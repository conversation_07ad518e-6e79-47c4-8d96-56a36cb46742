package com.hfut.xiaozu.incident.record;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReportIncidentDTO {

    /**
     * 事件标题
     */
    @NotBlank(message = "标题不能为空")
    private String title;

    /**
     * 事件描述
     */
    @NotBlank(message = "描述不能为空")
    private String description;

    /**
     * 分类类型: 1-环境卫生 2-设施损坏 3-安全问题 4-其他
     */
    private Integer categoryType ;

    /**
     * 优先级: 1-紧急 2-高 3-中 4-低
     */
    private Integer priority;

    /**
     * 位置描述
     */
    @NotBlank(message = "必须描述事件发生位置")
    private String locationDescription;

    /**
     * 位置GeoJSON
     */
    @NotNull(message = "必须描述事件发生坐标")
    private LocationGeojson  locationGeojson;

    /**
     * 所属小区ID
     */
    private Long communityId;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LocationGeojson {
        private Double latitude;
        private Double longitude;
    }
}
