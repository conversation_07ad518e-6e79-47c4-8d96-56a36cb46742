package com.hfut.xiaozu.family.info;

import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 业主家庭成员信息表
 * @TableName family_member
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class addFamilyMemberDTO {

    /**
     * 家人姓名
     */
    @NotBlank(message = "家人姓名不能为空")
    @Size(max = 50, message = "家人姓名长度不能超过50个字符")
    private String memberName;

    /**
     * 与业主关系:1-配偶 2-子女 3-父母 4-其他亲属
     */
    @NotNull(message = "与业主关系不能为空")
    @Min(value = 1, message = "与业主关系类型无效")
    @Max(value = 4, message = "与业主关系类型无效")
    private Integer relationship;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 是否主要家庭成员 0-否 1-是
     */
    @Min(value = 0, message = "是否主要家庭成员参数无效")
    @Max(value = 1, message = "是否主要家庭成员参数无效")
    private Integer isPrimary;
}
