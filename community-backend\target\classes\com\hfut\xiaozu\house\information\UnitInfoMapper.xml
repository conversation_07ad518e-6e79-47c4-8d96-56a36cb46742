<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hfut.xiaozu.house.information.UnitInfoMapper">

    <resultMap id="BaseResultMap" type="com.hfut.xiaozu.house.information.UnitInfo">
            <id property="id" column="id" />
            <result property="buildingId" column="building_id" />
            <result property="unitCode" column="unit_code" />
            <result property="floorCount" column="floor_count" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,building_id,unit_code,floor_count,create_time,update_time
    </sql>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from unit_info
        where  id = #{id}
    </select>

    <select id="listByBuildingId" resultType="com.hfut.xiaozu.house.information.UnitInfo">
        select
        <include refid="Base_Column_List" />
        from unit_info
        where  building_id = #{buildingId}
    </select>


    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.hfut.xiaozu.house.information.UnitInfo" useGeneratedKeys="true">
        insert into unit_info
        ( building_id,unit_code,floor_count)
        values (#{buildingId},#{unitCode},#{floorCount})
    </insert>

    <update id="update" parameterType="com.hfut.xiaozu.house.information.UnitInfo">
        update unit_info
        <set>
                <if test="buildingId != null">
                    building_id = #{buildingId},
                </if>
                <if test="unitCode != null">
                    unit_code = #{unitCode},
                </if>
                <if test="floorCount != null">
                    floor_count = #{floorCount},
                </if>
        </set>
        where   id = #{id}
    </update>

</mapper>
