# 自定义弹窗系统

## 概述

我们已经成功将前端系统中所有的浏览器原生 `alert`、`confirm` 弹窗替换为自定义设计的弹窗组件。这个系统提供了更好的用户体验、统一的视觉风格和更强的功能扩展性。

## 系统架构

### 1. 核心组件

#### CustomDialog.vue
- 位置：`src/components/CustomDialog.vue`
- 功能：自定义弹窗的核心组件
- 特性：
  - 支持多种类型（info、success、warning、error、confirm）
  - 支持输入框（text、textarea）
  - 支持加载状态
  - 美观的动画效果
  - 响应式设计

#### 对话框服务
- 位置：`src/utils/dialog.js`
- 功能：提供全局的弹窗调用接口
- 方法：
  - `showInfo()` - 信息提示
  - `showSuccess()` - 成功提示
  - `showWarning()` - 警告提示
  - `showError()` - 错误提示
  - `showConfirm()` - 确认对话框
  - `showPrompt()` - 输入对话框
  - `showTextareaPrompt()` - 文本域输入
  - `showLoading()` - 加载对话框

#### 插件系统
- 位置：`src/plugins/dialog.js`
- 功能：将对话框服务注册为全局插件
- 注册：在 `main.js` 中使用 `app.use(dialogPlugin)`

#### 组合式API
- 位置：`src/composables/useDialog.js`
- 功能：提供在组合式API中使用对话框的便捷方法
- 使用：`const dialog = useDialog()`

## 使用方法

### 1. 在组合式API中使用

```javascript
import { useDialog } from '@/composables/useDialog.js'

export default {
  setup() {
    const dialog = useDialog()
    
    const handleClick = async () => {
      // 信息提示
      await dialog.showInfo('操作成功', '提示')
      
      // 确认对话框
      const confirmed = await dialog.confirm('确定要删除吗？')
      if (confirmed) {
        // 执行删除操作
      }
      
      // 输入对话框
      const result = await dialog.showPrompt('请输入名称：', '输入')
      if (result.confirmed) {
        console.log('用户输入：', result.inputValue)
      }
    }
    
    return { handleClick }
  }
}
```

### 2. 在选项式API中使用

```javascript
export default {
  methods: {
    async handleClick() {
      // 使用全局属性
      await this.$showSuccess('操作成功！')
      
      // 使用兼容方法
      const confirmed = await this.$confirm('确定要继续吗？')
      if (confirmed) {
        // 执行操作
      }
    }
  }
}
```

### 3. 兼容性方法

为了方便迁移，我们提供了与原生方法兼容的接口：

```javascript
// 替代 alert()
await dialog.alert('这是一个提示')

// 替代 confirm()
const confirmed = await dialog.confirm('确定要删除吗？')

// 替代 prompt()
const input = await dialog.prompt('请输入名称：', '默认值')
```

## 已替换的文件

### 1. 事件管理页面
- 文件：`src/views/EventManagement.vue`
- 替换内容：
  - 权限提示弹窗
  - 完成事件确认对话框
  - 成功/失败提示
  - 指派网格员结果提示

### 2. 侧边栏布局
- 文件：`src/layouts/SidebarLayout.vue`
- 替换内容：
  - 退出登录确认对话框
  - 调试信息提示

### 3. 网格管理页面
- 文件：`src/views/GridManagement.vue`
- 替换内容：
  - 测试按钮提示
  - 操作结果提示
  - 错误信息提示

## 弹窗类型和样式

### 1. 信息类型
- **info**：蓝色主题，用于一般信息提示
- **success**：绿色主题，用于成功操作提示
- **warning**：橙色主题，用于警告信息
- **error**：红色主题，用于错误信息
- **confirm**：蓝色主题，用于确认操作

### 2. 交互功能
- **基础提示**：只有确定按钮
- **确认对话框**：确定和取消按钮
- **输入对话框**：包含输入框的对话框
- **加载对话框**：显示加载状态的对话框

### 3. 视觉特性
- 毛玻璃背景效果
- 平滑的进入/退出动画
- 响应式设计，适配移动端
- 统一的配色方案
- 清晰的图标和排版

## 演示页面

我们创建了一个演示页面来展示所有类型的弹窗：
- 路径：`/dialog-demo`
- 文件：`src/views/DialogDemo.vue`
- 功能：展示所有弹窗类型和使用方法

## 优势

### 1. 用户体验
- 统一的视觉风格
- 更好的动画效果
- 支持键盘操作
- 移动端友好

### 2. 开发体验
- 简单易用的API
- 完整的TypeScript支持
- 灵活的配置选项
- 良好的错误处理

### 3. 维护性
- 集中的样式管理
- 模块化的代码结构
- 易于扩展新功能
- 统一的调用方式

## 注意事项

1. **z-index**：自定义弹窗使用 z-index: 10000，确保在最顶层显示
2. **内存管理**：弹窗组件会自动清理，无需手动管理
3. **异步处理**：所有弹窗方法都返回Promise，支持async/await
4. **错误处理**：用户取消操作会抛出异常，需要适当处理

## 未来扩展

1. **主题系统**：支持多种主题切换
2. **国际化**：支持多语言
3. **更多类型**：添加更多弹窗类型
4. **高级功能**：支持拖拽、调整大小等
