<template>
  <div class="building-layer-test">
    <div class="page-header">
      <h1>建筑物图层测试</h1>
      <p>测试建筑物图层的显示和隐藏功能</p>
    </div>

    <div class="test-content">
      <div class="test-controls">
        <h3>测试控制</h3>
        <div class="control-group">
          <label class="checkbox-label">
            <input 
              type="checkbox" 
              v-model="showBuildings" 
              @change="toggleBuildings"
            >
            显示建筑物
          </label>
        </div>
        
        <div class="control-group">
          <label>缩放级别: {{ currentZoom }}</label>
          <input 
            type="range" 
            min="10" 
            max="20" 
            v-model="currentZoom" 
            @input="changeZoom"
            class="zoom-slider"
          >
        </div>

        <div class="control-buttons">
          <button @click="goToBeijing" class="test-btn">北京市中心</button>
          <button @click="goToShanghai" class="test-btn">上海市中心</button>
          <button @click="refreshMap" class="test-btn">刷新地图</button>
        </div>

        <div class="info-panel">
          <h4>说明</h4>
          <ul>
            <li>建筑物通常在缩放级别15以上才可见</li>
            <li>不同城市的建筑物数据可能不同</li>
            <li>切换建筑物图层后可能需要稍等片刻</li>
          </ul>
        </div>
      </div>

      <div class="map-section">
        <h3>地图显示</h3>
        <div class="map-wrapper">
          <MapComponent
            ref="mapComponent"
            map-id="building-test-map"
            :height="'600px'"
            :center="mapCenter"
            :zoom="parseInt(currentZoom)"
            :markers="[]"
            :polygons="[]"
            :show-controls="true"
            @map-ready="onMapReady"
            @layer-toggle="onLayerToggle"
          />
        </div>
      </div>
    </div>

    <div class="test-log">
      <h3>测试日志</h3>
      <div class="log-content">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import MapComponent from '../components/MapComponent.vue';

// 响应式数据
const mapComponent = ref(null);
const showBuildings = ref(true);
const currentZoom = ref(16);
const mapCenter = ref([117.198612, 31.774164]); // 治理端默认中心
const logs = ref([]);

// 预定义的测试位置
const testLocations = {
  beijing: [116.4074, 39.9042],
  shanghai: [121.4737, 31.2304]
};

// 添加日志
const addLog = (message) => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message: message
  });
  if (logs.value.length > 20) {
    logs.value.pop();
  }
};

// 地图就绪回调
const onMapReady = (map) => {
  addLog('地图初始化完成');
  
  // 监听缩放变化
  map.on('zoomchange', () => {
    const zoom = map.getZoom();
    currentZoom.value = zoom;
    addLog(`缩放级别变更为: ${zoom}`);
  });
  
  // 监听地图移动
  map.on('moveend', () => {
    const center = map.getCenter();
    addLog(`地图中心移动到: [${center.lng.toFixed(4)}, ${center.lat.toFixed(4)}]`);
  });
};

// 切换建筑物显示
const toggleBuildings = () => {
  if (mapComponent.value) {
    const map = mapComponent.value.getMap();
    if (map) {
      addLog(`${showBuildings.value ? '启用' : '禁用'}建筑物图层`);
      
      try {
        if (showBuildings.value) {
          map.setFeatures(['bg', 'road', 'building']);
          addLog('建筑物特性已启用');
        } else {
          map.setFeatures(['bg', 'road']);
          addLog('建筑物特性已禁用');
        }
        
        // 强制刷新
        setTimeout(() => {
          if (map) {
            map.setZoom(map.getZoom());
            addLog('地图已刷新');
          }
        }, 100);
        
      } catch (error) {
        addLog('切换建筑物图层失败: ' + error.message);
      }
    }
  }
};

// 改变缩放级别
const changeZoom = () => {
  if (mapComponent.value) {
    const map = mapComponent.value.getMap();
    if (map) {
      map.setZoom(parseInt(currentZoom.value));
      addLog(`手动设置缩放级别为: ${currentZoom.value}`);
    }
  }
};

// 跳转到北京
const goToBeijing = () => {
  mapCenter.value = testLocations.beijing;
  if (mapComponent.value) {
    const map = mapComponent.value.getMap();
    if (map) {
      map.setCenter(testLocations.beijing);
      addLog('跳转到北京市中心');
    }
  }
};

// 跳转到上海
const goToShanghai = () => {
  mapCenter.value = testLocations.shanghai;
  if (mapComponent.value) {
    const map = mapComponent.value.getMap();
    if (map) {
      map.setCenter(testLocations.shanghai);
      addLog('跳转到上海市中心');
    }
  }
};

// 刷新地图
const refreshMap = () => {
  if (mapComponent.value) {
    const map = mapComponent.value.getMap();
    if (map) {
      // 重新设置特性
      if (showBuildings.value) {
        map.setFeatures(['bg', 'road', 'building']);
      } else {
        map.setFeatures(['bg', 'road']);
      }
      
      // 触发重绘
      map.setZoom(map.getZoom());
      addLog('地图已手动刷新');
    }
  }
};

// 图层切换处理
const onLayerToggle = (layerKey, visible) => {
  addLog(`图层切换: ${layerKey} - ${visible ? '显示' : '隐藏'}`);

  // 特殊处理建筑物图层
  if (layerKey === 'buildings') {
    showBuildings.value = visible;
    toggleBuildings();
  }
};

// 页面加载
onMounted(() => {
  addLog('建筑物图层测试页面加载完成');
});
</script>

<style scoped>
.building-layer-test {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  color: #333;
  margin-bottom: 10px;
}

.page-header p {
  color: #666;
  font-size: 16px;
}

.test-content {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.test-controls {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  height: fit-content;
}

.test-controls h3 {
  margin-top: 0;
  color: #333;
  border-bottom: 2px solid #4a90e2;
  padding-bottom: 10px;
}

.control-group {
  margin-bottom: 20px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 16px;
}

.zoom-slider {
  width: 100%;
  margin-top: 10px;
}

.control-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.test-btn {
  background: #4a90e2;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s;
}

.test-btn:hover {
  background: #357abd;
}

.info-panel {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 5px;
  margin-top: 20px;
}

.info-panel h4 {
  margin-top: 0;
  color: #333;
}

.info-panel ul {
  margin: 0;
  padding-left: 20px;
}

.info-panel li {
  margin-bottom: 5px;
  font-size: 14px;
  color: #666;
}

.map-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.map-section h3 {
  margin-top: 0;
  color: #333;
  border-bottom: 2px solid #4a90e2;
  padding-bottom: 10px;
}

.test-log {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.test-log h3 {
  margin-top: 0;
  color: #333;
  border-bottom: 2px solid #4a90e2;
  padding-bottom: 10px;
}

.log-content {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 10px;
  background: #f8f9fa;
}

.log-item {
  display: flex;
  gap: 10px;
  padding: 3px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;
}

.log-time {
  color: #666;
  font-weight: bold;
  min-width: 80px;
}

.log-message {
  color: #333;
}

@media (max-width: 768px) {
  .test-content {
    grid-template-columns: 1fr;
  }
}
</style>
