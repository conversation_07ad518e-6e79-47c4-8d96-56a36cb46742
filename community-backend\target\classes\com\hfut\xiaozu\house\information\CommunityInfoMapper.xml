<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hfut.xiaozu.house.information.CommunityInfoMapper">

    <resultMap id="BaseResultMap" type="com.hfut.xiaozu.house.information.CommunityInfo">
            <id property="id" column="id" />
            <result property="communityName" column="community_name" />
            <result property="addressDetail" column="address_detail" />
            <result property="geoJson" column="geo_json" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,community_name,address_detail,geo_json,create_time,update_time
    </sql>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from community_info
        where  id = #{id}
    </select>

    <select id="list" resultType="com.hfut.xiaozu.house.information.CommunityInfo">
        select
        <include refid="Base_Column_List" />
        from community_info
    </select>

    <select id="getIdByName" resultType="java.lang.Long">
        SELECT id FROM community.community_info
                  WHERE community_name= #{communityName}
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.hfut.xiaozu.house.information.CommunityInfo" useGeneratedKeys="true">
        insert into community_info
        ( community_name,address_detail,geo_json)
        values (#{communityName},#{addressDetail},#{geoJson})
    </insert>

    <update id="update" parameterType="com.hfut.xiaozu.house.information.CommunityInfo">
        update community_info
        <set>
                <if test="communityName != null">
                    community_name = #{communityName},
                </if>
                <if test="addressDetail != null">
                    address_detail = #{addressDetail},
                </if>
                <if test="geoJson != null">
                    geo_json = #{geoJson},
                </if>
        </set>
        where   id = #{id}
    </update>

</mapper>
