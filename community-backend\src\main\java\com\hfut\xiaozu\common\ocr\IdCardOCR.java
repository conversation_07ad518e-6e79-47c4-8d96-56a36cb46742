package com.hfut.xiaozu.common.ocr;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;
import java.net.URLEncoder;

/**
 * <AUTHOR>
 * @date 2025-06-24
 */
@Component
@Slf4j
public class IdCardOCR {

    // 请求url
    public static final String url="https://aip.baidubce.com/rest/2.0/ocr/v1/idcard";

    @Resource
    IDOCRConfig idocrConfig;

    /**
     * 上传本地文件,从项目目录开始定位
     */
    public void  idcardLocalPic() {
        try {

            String filePath = "res\\ocrTest01Front.jpg";

            // 1. 确定项目根目录
            String projectRoot = System.getProperty("user.dir");

            // 2. 构造资源文件的绝对路径（在src同级的res目录）
            String imagePath = projectRoot + File.separator +filePath;

            System.out.println(imagePath);

            byte[] fileBytes = ResourceUtil.readBytes(imagePath);

            String base64Data = Base64.encode(fileBytes);

            // 注意这里仅为了简化编码每一次请求都去获取access_token，线上环境access_token有过期时间， 客户端可自行缓存，过期后重新获取。
            String accessToken = idocrConfig.getAccessToken();
            String fullUrl = "https://aip.baidubce.com/rest/2.0/ocr/v1/idcard?access_token=" + accessToken;

            String bodyParams = "id_card_side=front&image=" + URLEncoder.encode(base64Data, "UTF-8");

            // 4. 发送POST请求
            HttpResponse response = HttpRequest.post(fullUrl)
                    .contentType("application/x-www-form-urlencoded")
                    .body(bodyParams)
                    .timeout(5000)
                    .execute();

            if (response.isOk()) {
                System.out.println(response.body());
            } else {
                throw new RuntimeException("OCR请求失败：状态码=" + response.getStatus());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String  idcardFromUrl(String url) {
        try {
            String accessToken = idocrConfig.getAccessToken();
            String fullUrl = "https://aip.baidubce.com/rest/2.0/ocr/v1/idcard?access_token=" + accessToken;

            String bodyParams = "id_card_side=front&url="+url;

            HttpResponse response = HttpRequest.post(fullUrl)
                    .contentType("application/x-www-form-urlencoded")
                    .body(bodyParams)
                    .timeout(5000)
                    .execute();

            if (response.isOk()) {
                return response.body();
            } else {
                throw new RuntimeException("OCR请求失败：状态码=" + response.getStatus());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    public String[] IdCardInfoExtractor(String jsonStr){
        log.error(jsonStr);
        JSONObject json = JSONUtil.parseObj(jsonStr);
        JSONObject wordsResult = json.getJSONObject("words_result");

        String name = wordsResult.getJSONObject("姓名").getStr("words");
        String idNumber = wordsResult.getJSONObject("公民身份号码").getStr("words");

        return new String[]{name,idNumber};
    }
}
