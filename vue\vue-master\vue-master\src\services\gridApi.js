/**
 * 网格管理API服务
 * 处理网格相关的后端API调用
 * 使用HTTP拦截器自动处理token和错误
 */

import http from '../utils/httpInterceptor.js';
import { validateAndFormatGridData } from '../utils/gridJsonValidator.js';

/**
 * 获取社区列表
 * @returns {Promise<Object>} API响应
 */
export const getCommunities = async () => {
  console.log('🔧 API: 获取社区列表');

  try {
    // 使用HTTP拦截器发送请求（自动添加token）
    const response = await http.get('/grids/communities');

    console.log('✅ API: 社区列表获取成功', response);

    return {
      success: true,
      data: response.data,
      message: response.message || '社区列表获取成功'
    };

  } catch (error) {
    console.error('❌ API: 社区列表获取失败', error);
    throw error;
  }
};

/**
 * 创建新网格
 * @param {Object} gridData - 网格数据
 * @returns {Promise<Object>} API响应
 */
export const createGrid = async (gridData) => {
  console.log('🔧 API: 创建网格', gridData);

  try {
    // 转换为后端期望的格式 - 转换为对象格式
    const coordinates = gridData.coordinates.map(coord => ({
      longitude: coord[0],
      latitude: coord[1]
    }));

    const backendData = {
      gridName: gridData.name,
      responsibleId: gridData.responsibleId || 0,
      communityId: gridData.communityId,
      coordinates: coordinates
    };

    console.log('✅ 转换后的后端数据格式:', JSON.stringify(backendData, null, 2));

    // 验证数据格式
    console.log('📊 数据格式验证:');
    console.log('- 原始坐标格式 (数组):', gridData.coordinates);
    console.log('- 发送给后端的格式 (对象数组):', backendData.coordinates);
    console.log('- 预期格式: [{longitude: lng, latitude: lat}, ...]');

    // 使用HTTP拦截器发送请求（自动添加token）
    const response = await http.post('/grids', backendData);

    console.log('✅ API: 网格创建成功', response);

    // 标准化响应格式
    return {
      success: true,
      data: response.data,
      message: response.message || '网格创建成功'
    };

  } catch (error) {
    console.error('❌ API: 网格创建失败', error);

    // 在开发环境下返回模拟数据
    if (process.env.NODE_ENV === 'development' && error.code === 'NETWORK_ERROR') {
      console.log('🔧 开发模式: 返回模拟数据');
      await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟网络延迟

      return {
        success: true,
        data: {
          id: `grid_${Date.now()}`,
          name: gridData.name,
          status: 'active',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        message: '网格创建成功（模拟数据）'
      };
    }

    // 重新抛出错误，保持错误信息
    throw error;
  }
};

/**
 * 更新网格信息
 * @param {string} gridId - 网格ID
 * @param {Object} updateData - 更新数据
 * @returns {Promise<Object>} API响应
 */
export const updateGrid = async (gridId, updateData) => {
  console.log('🔧 API: 更新网格', gridId, updateData);

  try {
    // 使用HTTP拦截器发送请求
    const response = await http.put(`/grids/${gridId}`, updateData);

    console.log('✅ API: 网格更新成功', response);
    return {
      success: true,
      data: response.data,
      message: response.message || '网格更新成功'
    };
  } catch (error) {
    console.error('❌ API: 网格更新失败', error);
    throw error;
  }
};

/**
 * 删除网格
 * @param {string} gridId - 网格ID
 * @returns {Promise<Object>} API响应
 */
export const deleteGrid = async (gridId) => {
  console.log('🔧 API: 删除网格', gridId);

  try {
    // 使用HTTP拦截器发送请求
    const response = await http.delete(`/grids/${gridId}`);

    console.log('✅ API: 网格删除成功', response);
    return {
      success: true,
      data: response.data,
      message: response.message || '网格删除成功'
    };
  } catch (error) {
    console.error('❌ API: 网格删除失败', error);
    throw error;
  }
};

/**
 * 获取所有网格数据
 * @returns {Promise<Object>} API响应
 */
export const getAllGrids = async () => {
  console.log('🔧 API: 获取所有网格数据');

  try {
    // 使用HTTP拦截器发送请求（自动添加token）
    const response = await http.get('/grids/listAll');

    console.log('✅ API: 获取所有网格数据成功', response);

    return {
      success: true,
      data: response.data,
      message: response.message || '获取所有网格数据成功'
    };

  } catch (error) {
    console.error('❌ API: 获取所有网格数据失败', error);
    throw error;
  }
};

/**
 * 获取网格列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} API响应
 */
export const getGrids = async (params = {}) => {
  console.log('🔧 API: 获取网格列表', params);

  try {
    const queryString = new URLSearchParams(params).toString();
    const url = `/grids${queryString ? `?${queryString}` : ''}`;

    // 使用HTTP拦截器发送请求
    const response = await http.get(url);

    console.log('✅ API: 获取网格列表成功', response);
    return {
      success: true,
      data: response.data,
      total: response.total,
      message: response.message || '获取网格列表成功'
    };
  } catch (error) {
    console.error('❌ API: 获取网格列表失败', error);
    throw error;
  }
};

/**
 * 获取单个网格详情
 * @param {string} gridId - 网格ID
 * @returns {Promise<Object>} API响应
 */
export const getGridById = async (gridId) => {
  console.log('API: 获取网格详情', gridId);

  try {
    const response = await apiRequest(`/grids/${gridId}`, {
      method: 'GET'
    });

    return {
      success: true,
      data: response.data,
      message: response.message || '获取网格详情成功'
    };
  } catch (error) {
    console.error('API: 获取网格详情失败', error);
    throw error;
  }
};

/**
 * 批量操作网格
 * @param {string} action - 操作类型 (activate, deactivate, delete)
 * @param {Array} gridIds - 网格ID数组
 * @returns {Promise<Object>} API响应
 */
export const batchOperateGrids = async (action, gridIds) => {
  console.log('API: 批量操作网格', action, gridIds);

  try {
    const response = await apiRequest('/grids/batch', {
      method: 'POST',
      body: JSON.stringify({
        action,
        gridIds
      })
    });

    return {
      success: true,
      data: response.data,
      message: response.message || '批量操作成功'
    };
  } catch (error) {
    console.error('API: 批量操作失败', error);
    throw error;
  }
};

/**
 * 导出网格数据
 * @param {Object} params - 导出参数
 * @returns {Promise<Blob>} 文件数据
 */
export const exportGrids = async (params = {}) => {
  console.log('API: 导出网格数据', params);

  try {
    const queryString = new URLSearchParams(params).toString();
    const url = `/grids/export${queryString ? `?${queryString}` : ''}`;
    
    const response = await fetch(`${API_BASE_URL}${url}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.blob();
  } catch (error) {
    console.error('API: 导出网格数据失败', error);
    throw error;
  }
};

/**
 * 导入网格数据
 * @param {File} file - 导入文件
 * @returns {Promise<Object>} API响应
 */
export const importGrids = async (file) => {
  console.log('API: 导入网格数据', file.name);

  try {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch(`${API_BASE_URL}/grids/import`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`
      },
      body: formData
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    return {
      success: true,
      data: result.data,
      message: result.message || '导入成功'
    };
  } catch (error) {
    console.error('API: 导入网格数据失败', error);
    throw error;
  }
};
