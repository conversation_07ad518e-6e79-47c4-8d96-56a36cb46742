<template>
  <div class="vote-api-test">
    <div class="test-header">
      <h1>投票API测试页面</h1>
      <p>用于调试投票API连接问题</p>
    </div>

    <div class="test-content">
      <!-- 测试结果显示 -->
      <div class="test-results">
        <h3>测试结果</h3>
        <div class="result-item">
          <strong>Token状态:</strong>
          <span :class="tokenStatus.class">{{ tokenStatus.text }}</span>
        </div>
        <div class="result-item">
          <strong>后端连接:</strong>
          <span :class="backendStatus.class">{{ backendStatus.text }}</span>
        </div>
        <div class="result-item">
          <strong>投票API:</strong>
          <span :class="voteApiStatus.class">{{ voteApiStatus.text }}</span>
        </div>
      </div>

      <!-- 测试按钮 -->
      <div class="test-actions">
        <button @click="testToken" class="test-btn">测试Token</button>
        <button @click="testBackendConnection" class="test-btn">测试后端连接</button>
        <button @click="testVoteApi" class="test-btn">测试投票API</button>
        <button @click="runAllTests" class="test-btn primary">运行所有测试</button>
      </div>

      <!-- 详细日志 -->
      <div class="test-logs">
        <h3>详细日志</h3>
        <div class="log-container">
          <div
            v-for="(log, index) in logs"
            :key="index"
            :class="['log-item', log.type]"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
        <button @click="clearLogs" class="clear-btn">清除日志</button>
      </div>

      <!-- 原始响应数据 -->
      <div v-if="rawResponse" class="raw-response">
        <h3>原始响应数据</h3>
        <pre>{{ JSON.stringify(rawResponse, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { getToken } from '../utils/tokenManager.js';
import { getVoteList } from '../services/voteApi.js';

// 响应式数据
const logs = ref([]);
const rawResponse = ref(null);

const tokenStatus = reactive({
  text: '未测试',
  class: 'status-unknown'
});

const backendStatus = reactive({
  text: '未测试',
  class: 'status-unknown'
});

const voteApiStatus = reactive({
  text: '未测试',
  class: 'status-unknown'
});

// 添加日志
const addLog = (message, type = 'info') => {
  logs.value.push({
    time: new Date().toLocaleTimeString(),
    message,
    type
  });
};

// 清除日志
const clearLogs = () => {
  logs.value = [];
  rawResponse.value = null;
};

// 测试Token
const testToken = () => {
  addLog('开始测试Token...', 'info');
  
  const token = getToken();
  
  if (token) {
    tokenStatus.text = '有效';
    tokenStatus.class = 'status-success';
    addLog(`Token存在: ${token.substring(0, 20)}...`, 'success');
  } else {
    tokenStatus.text = '无效';
    tokenStatus.class = 'status-error';
    addLog('Token不存在或无效', 'error');
  }
};

// 测试后端连接
const testBackendConnection = async () => {
  addLog('开始测试后端连接...', 'info');
  
  try {
    const response = await fetch('http://localhost:8080/api/vote/list', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getToken()}`
      }
    });

    addLog(`HTTP状态码: ${response.status}`, 'info');
    
    if (response.ok) {
      backendStatus.text = '连接成功';
      backendStatus.class = 'status-success';
      addLog('后端连接成功', 'success');
      
      const data = await response.json();
      rawResponse.value = data;
      addLog(`响应数据: ${JSON.stringify(data)}`, 'info');
    } else {
      backendStatus.text = `连接失败 (${response.status})`;
      backendStatus.class = 'status-error';
      addLog(`后端连接失败: ${response.status} ${response.statusText}`, 'error');
      
      const errorData = await response.text();
      addLog(`错误响应: ${errorData}`, 'error');
    }
  } catch (error) {
    backendStatus.text = '连接失败';
    backendStatus.class = 'status-error';
    addLog(`后端连接异常: ${error.message}`, 'error');
    
    if (error.message.includes('fetch')) {
      addLog('可能的原因: 后端服务未启动或端口不正确', 'warning');
    }
  }
};

// 测试投票API
const testVoteApi = async () => {
  addLog('开始测试投票API...', 'info');
  
  try {
    const result = await getVoteList();
    
    if (result.success) {
      voteApiStatus.text = 'API正常';
      voteApiStatus.class = 'status-success';
      addLog('投票API调用成功', 'success');
      addLog(`返回数据: ${JSON.stringify(result)}`, 'info');
      rawResponse.value = result;
    } else {
      voteApiStatus.text = 'API异常';
      voteApiStatus.class = 'status-error';
      addLog(`投票API调用失败: ${result.message}`, 'error');
    }
  } catch (error) {
    voteApiStatus.text = 'API异常';
    voteApiStatus.class = 'status-error';
    addLog(`投票API调用异常: ${error.message}`, 'error');
  }
};

// 运行所有测试
const runAllTests = async () => {
  clearLogs();
  addLog('开始运行所有测试...', 'info');
  
  testToken();
  await testBackendConnection();
  await testVoteApi();
  
  addLog('所有测试完成', 'info');
};

// 页面加载时自动运行测试
onMounted(() => {
  addLog('页面加载完成，准备运行测试', 'info');
});
</script>

<style scoped>
.vote-api-test {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
}

.test-header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.test-content {
  display: grid;
  gap: 20px;
}

.test-results {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.result-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
}

.status-unknown {
  color: #6c757d;
}

.status-success {
  color: #28a745;
  font-weight: bold;
}

.status-error {
  color: #dc3545;
  font-weight: bold;
}

.test-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.test-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  background: #6c757d;
  color: white;
  transition: background-color 0.3s;
}

.test-btn:hover {
  background: #5a6268;
}

.test-btn.primary {
  background: #007bff;
}

.test-btn.primary:hover {
  background: #0056b3;
}

.test-logs {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 10px;
  background: #f8f9fa;
  margin-bottom: 10px;
}

.log-item {
  display: flex;
  gap: 10px;
  margin-bottom: 5px;
  font-family: monospace;
  font-size: 14px;
}

.log-time {
  color: #6c757d;
  min-width: 80px;
}

.log-item.info .log-message {
  color: #17a2b8;
}

.log-item.success .log-message {
  color: #28a745;
}

.log-item.error .log-message {
  color: #dc3545;
}

.log-item.warning .log-message {
  color: #ffc107;
}

.clear-btn {
  padding: 8px 16px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.clear-btn:hover {
  background: #5a6268;
}

.raw-response {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.raw-response pre {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
}
</style>
