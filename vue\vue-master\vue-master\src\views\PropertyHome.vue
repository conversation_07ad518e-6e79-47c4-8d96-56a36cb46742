<template>
  <div class="property-home">
    <div class="dashboard-header">
      <h1>社区治理端</h1>
      <p class="subtitle">综合管理平台 - 数据总览与功能导航</p>
    </div>
    
    <div class="dashboard-content">
      <!-- 数据概览卡片 -->
      <section class="overview-section">
        <h2 class="section-title">数据概览</h2>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">🏠</div>
            <div class="stat-info">
              <h3>总户数</h3>
              <p class="stat-number">1,248</p>
              <span class="stat-change positive">+12 本月</span>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">👥</div>
            <div class="stat-info">
              <h3>总人数</h3>
              <p class="stat-number">3,567</p>
              <span class="stat-change positive">+28 本月</span>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">📋</div>
            <div class="stat-info">
              <h3>待处理事件</h3>
              <p class="stat-number">23</p>
              <span class="stat-change negative">-5 今日</span>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">🚶</div>
            <div class="stat-info">
              <h3>巡查任务</h3>
              <p class="stat-number">8</p>
              <span class="stat-change neutral">进行中</span>
            </div>
          </div>
        </div>
      </section>

      <!-- 社区治理端功能模块 -->
      <section class="function-section">
        <h2 class="section-title">社区治理功能</h2>
        <div class="grid-container">
          <div class="grid-item" @click="navigateTo('/property/data-overview')">
            <div class="item-icon">📊</div>
            <h3>数据总览</h3>
            <p>总体情况展示, GIS地图展示</p>
          </div>
          
          <div class="grid-item" @click="navigateTo('/property/full-archive')">
            <div class="item-icon">📁</div>
            <h3>全息档案</h3>
            <p>档案信息管理与分类, 档案查询与展示, 重点监控人群, 档案导出</p>
          </div>
          
          <div class="grid-item" @click="navigateTo('/property/event-management')">
            <div class="item-icon">📋</div>
            <h3>事件管理</h3>
            <p>多源事件整合, 事件查询与状态跟踪, 超时预警</p>
          </div>
          
          <div class="grid-item" @click="navigateTo('/property/patrol-management')">
            <div class="item-icon">🚶</div>
            <h3>巡查管理</h3>
            <p>巡查任务编排, 巡查任务发布, 巡查任务执行情况</p>
          </div>
          
          <div class="grid-item" @click="navigateTo('/property/gis-grid')">
            <div class="item-icon">🗺️</div>
            <h3>GIS网格管理</h3>
            <p>网格边界编辑, 网格信息维护与绑定, 网格合并与拆分</p>
          </div>
          
          <div class="grid-item" @click="navigateTo('/property/info-collection')">
            <div class="item-icon">📝</div>
            <h3>实名信息采集</h3>
            <p>居民实名认证信息采集, 身份验证与管理</p>
          </div>

          <div class="grid-item" @click="navigateTo('/property/house-info-collection')">
            <div class="item-icon">🏠</div>
            <h3>房屋信息采集</h3>
            <p>房屋基础信息采集, 房屋档案建立与维护</p>
          </div>
        </div>
      </section>

      <!-- 公共模块 -->
      <section class="function-section">
        <h2 class="section-title">公共模块</h2>
        <div class="grid-container">
          <div class="grid-item" @click="navigateTo('/common/user-permission')">
            <div class="item-icon">👥</div>
            <h3>用户与权限服务</h3>
            <p>登录鉴权, 账号体系</p>
          </div>
          
          <div class="grid-item" @click="navigateTo('/common/file-storage')">
            <div class="item-icon">💾</div>
            <h3>文件存储服务</h3>
            <p>阿里OSS</p>
          </div>
          
          <div class="grid-item" @click="navigateTo('/common/message-notification')">
            <div class="item-icon">📢</div>
            <h3>消息通知服务</h3>
            <p>系统内通知</p>
          </div>
          
          <div class="grid-item" @click="navigateTo('/common/log-monitor')">
            <div class="item-icon">📈</div>
            <h3>日志与监控</h3>
            <p>操作日志, 错误告警, 系统性能监控</p>
          </div>

          <div class="grid-item" @click="navigateTo('/common/voice-demo')">
            <div class="item-icon">🎤</div>
            <h3>语音文字互转</h3>
            <p>文字转语音, 语音转文字, TTS演示</p>
          </div>
        </div>
      </section>

      <!-- 快速操作区域 -->
      <section class="quick-actions">
        <h2 class="section-title">快速操作</h2>
        <div class="action-buttons">
          <button class="action-btn primary" @click="navigateTo('/property/event-management')">
            <span class="btn-icon">📋</span>
            新建事件
          </button>
          <button class="action-btn secondary" @click="navigateTo('/property/patrol-management')">
            <span class="btn-icon">🚶</span>
            发布巡查任务
          </button>
          <button class="action-btn tertiary" @click="navigateTo('/property/full-archive')">
            <span class="btn-icon">📁</span>
            查看档案
          </button>
        </div>
      </section>
    </div>

    <!-- 语音助手 -->
    <VoiceAssistant ref="voiceAssistant" />
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';
import VoiceAssistant from '../components/VoiceAssistant.vue';

const router = useRouter();

const navigateTo = (path) => {
  router.push(path);
};
</script>

<style scoped>
.property-home {
  max-width: 1200px;
  margin: 0 auto;
  background: var(--main-bg);
  color: var(--main-text);
}

.dashboard-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px 0;
  background: var(--card-bg);
  border-radius: 12px;
  box-shadow: var(--shadow);
}

.dashboard-header h1 {
  font-size: 32px;
  color: var(--main-text);
  margin: 0 0 10px 0;
  font-weight: 700;
}

.subtitle {
  font-size: 16px;
  color: var(--main-text);
  margin: 0;
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.section-title {
  font-size: 24px;
  color: var(--main-text);
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 3px solid #FFB07C;
  font-weight: 600;
}

/* 数据概览样式 */
.overview-section {
  background: var(--card-bg);
  padding: 30px;
  border-radius: 12px;
  box-shadow: var(--shadow);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 25px;
  background: var(--main-bg);
  border-radius: 10px;
  border: 1px solid #e9ecef;
  transition: transform 0.3s, box-shadow 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 179, 102, 0.15);
}

.stat-icon {
  font-size: 36px;
  margin-right: 20px;
}

.stat-info h3 {
  font-size: 14px;
  color: var(--main-text);
  margin: 0 0 8px 0;
  font-weight: 500;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: var(--main-text);
  margin: 0 0 5px 0;
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 12px;
}

.stat-change.positive {
  color: var(--accent-green);
  background: var(--accent-green-lighter);
}

.stat-change.negative {
  color: var(--error);
  background: rgba(244, 67, 54, 0.1);
}

.stat-change.neutral {
  color: var(--accent-yellow);
  background: var(--accent-yellow-lighter);
}

/* 功能模块样式 */
.function-section {
  background: var(--card-bg);
  padding: 30px;
  border-radius: 12px;
  box-shadow: var(--shadow);
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.grid-item {
  background: var(--main-bg);
  color: var(--main-text);
  border: 1px solid #e9ecef;
  border-radius: 10px;
  padding: 25px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow);
}

.grid-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #FFB07C, #FFDAB9);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.grid-item:hover::before {
  transform: scaleX(1);
}

.grid-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(255, 179, 102, 0.15);
  border-color: #FFB07C;
  background: #FFEDCC;
}

.item-icon {
  font-size: 48px;
  margin-bottom: 15px;
  display: block;
}

.grid-item h3 {
  font-size: 18px;
  color: var(--main-text);
  margin: 0 0 10px 0;
  font-weight: 600;
}

.grid-item p {
  font-size: 14px;
  color: var(--main-text);
  margin: 0;
  line-height: 1.5;
}

/* 快速操作样式 */
.quick-actions {
  background: var(--card-bg);
  padding: 30px;
  border-radius: 12px;
  box-shadow: var(--shadow);
}

.action-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: #FFB07C;
  color: #5B5347;
}

.action-btn.primary:hover {
  background: #FDE1B6;
  transform: translateY(-2px);
}

.action-btn.secondary {
  background: #6c757d;
  color: white;
}

.action-btn.secondary:hover {
  background: #5a6268;
  transform: translateY(-2px);
}

.action-btn.tertiary {
  background: #e9ecef;
  color: #495057;
}

.action-btn.tertiary:hover {
  background: #dee2e6;
  transform: translateY(-2px);
}

.btn-icon {
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-header h1 {
    font-size: 24px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .grid-container {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-btn {
    justify-content: center;
  }
}
</style>
