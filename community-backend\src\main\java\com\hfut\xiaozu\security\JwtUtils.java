package com.hfut.xiaozu.security;

import cn.hutool.core.util.StrUtil;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTValidator;
import com.hfut.xiaozu.user.context.CurrentUser;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-06-23
 */
@Component
@Slf4j
public class JwtUtils {

    @Resource
    private JwtConfigProperties jwtConfigProperties;

    // 生成令牌
    public String generateToken(CurrentUser user) {
        //HS256(HmacSHA256)算法
        String token = JWT.create()
                .setPayload("userId",user.getUserId())
                .setPayload("role",user.getRole())
                .setExpiresAt(new Date(System.currentTimeMillis() + jwtConfigProperties.getExpiration()))
                .setKey(jwtConfigProperties.getSecret().getBytes(StandardCharsets.UTF_8))
                .sign();
        return token;
    }

    // 解析令牌
    public CurrentUser parseToken(String token) {
        if (StrUtil.isBlank(token)) {
            return null;
        }

        try {
            // 一次性完成签名验证+过期验证
            JWT jwt = JWT.of(token);
            JWTValidator.of(jwt).validateDate();

            // 安全获取并转换payload
            Long userId = parsePayloadAsLong(jwt.getPayload("userId"));
            Integer role = parsePayloadAsInteger(jwt.getPayload("role"));

            if (userId == null || role == null) {
                log.warn("JWT payload missing required fields");
                return null;
            }

            return new CurrentUser(userId, role);
        } catch (Exception e) {
            // 统一处理所有验证/解析异常
            log.debug("JWT validation failed: {}", e.getMessage());
            return null;
        }


    }
    private Long parsePayloadAsLong(Object payload) {
        if (payload instanceof Number) {
            return ((Number) payload).longValue();
        } else if (payload instanceof String) {
            try {
                return Long.parseLong((String) payload);
            } catch (NumberFormatException ignored) {
            }
        }
        return null;
    }

    private Integer parsePayloadAsInteger(Object payload) {
        if (payload instanceof Number) {
            return ((Number) payload).intValue();
        } else if (payload instanceof String) {
            try {
                return Integer.parseInt((String) payload);
            } catch (NumberFormatException ignored) {
            }
        }
        return null;
    }
}
