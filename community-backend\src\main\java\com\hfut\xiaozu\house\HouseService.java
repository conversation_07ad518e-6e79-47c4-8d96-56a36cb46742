package com.hfut.xiaozu.house;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageHelper;
import com.hfut.xiaozu.common.Result;
import com.hfut.xiaozu.house.binding.UserHouseBinding;
import com.hfut.xiaozu.house.binding.UserHouseBindingMapper;
import com.hfut.xiaozu.house.grid.*;
import com.hfut.xiaozu.house.information.*;
import com.hfut.xiaozu.user.context.CurrentUser;
import com.hfut.xiaozu.user.context.UserContextHolder;
import com.hfut.xiaozu.user.idv.IdentityVerifySubmitMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2025-06-25
 */
@Service
@Slf4j
public class HouseService {

    @Resource
    private CommunityInfoMapper communityInfoMapper;

    @Resource
    private BuildingInfoMapper buildingInfoMapper;

    @Resource
    private UnitInfoMapper unitInfoMapper;

    @Resource
    private HouseInfoMapper houseInfoMapper;

    @Autowired
    private UserHouseBindingMapper userHouseBindingMapper;

    @Resource
    private CommunityGridMapper communityGridMapper;

    @Autowired
    private GridItemArchiveMapper gridItemArchiveMapper;

    public Result<?> listAllcommunities() {

        List<CommunityInfo> list = communityInfoMapper.list();

        if(CollectionUtil.isEmpty(list)){
            return Result.ok("一个社区也没有");
        }

        List<CommunitiesVO> result=new ArrayList<>();

        for (CommunityInfo communityInfo : list) {
            CommunitiesVO vo = new CommunitiesVO();
            vo.setCommunityName(communityInfo.getCommunityName());
            vo.setCommunityId(communityInfo.getId());

            JSONObject json = JSONUtil.parseObj(communityInfo.getGeoJson());
            vo.setLatitude(json.getDouble("latitude"));
            vo.setLongitude(json.getDouble("longitude"));

            result.add(vo);
        }
        return Result.ok(result);
    }

    public Result<?> listAllBuildings(Long communityId) {

        List<BuildingInfo> list = buildingInfoMapper.listByCommunityId(communityId);

        if(CollectionUtil.isEmpty(list)){
            return Result.ok("一栋楼也没有");
        }

        List<BuildingsVO> result=new ArrayList<>();

        list.forEach( item->{
                BuildingsVO vo=new BuildingsVO();
                vo.setBuildingName(item.getBuildingName());
                vo.setBuildingsId(item.getId());

                JSONObject json = JSONUtil.parseObj(item.getGeoJson());
                vo.setLatitude(json.getDouble("latitude"));
                vo.setLongitude(json.getDouble("longitude"));
                result.add(vo);
            }
        );
        return Result.ok(result);
    }

    public Result<?> listAllUnits(Long buildingId) {

        List<UnitInfo> list = unitInfoMapper.listByBuildingId(buildingId);

        if(CollectionUtil.isEmpty(list)){
            return Result.ok("一个单元也没有");
        }

        List<UnitVO> result=new ArrayList<>();

        list.forEach( item->{
                    UnitVO vo=new UnitVO();

                    vo.setId(item.getId());
                    vo.setFloorCount(item.getFloorCount());
                    vo.setUnitCode(item.getUnitCode());

                    result.add(vo);
                }
        );
        return Result.ok(result);
    }

    public Result<?> listAllHouses(Long unitId) {

        List<HouseInfo> list = houseInfoMapper.listByUnitId(unitId);

        if(CollectionUtil.isEmpty(list)){
            return Result.ok("一个房屋也没有");
        }

        List<HouseVO> result = BeanUtil.copyToList(list, HouseVO.class);

        return Result.ok(result);
    }

    public Result<?> bindingHouseByHouseId(Long houseId, Integer relatioType) {

        if(!UserContextHolder.isCurrentUserRole(CurrentUser.Resident)){
            return Result.fail("只有居民才能申请绑定");
        }

        HouseInfo house = houseInfoMapper.getById(houseId);

        if(Objects.equals(house.getIsOccupied(), 1)) {
            return Result.fail("已经有人住了，绑定失败");
        }

        Long userId = UserContextHolder.getCurrentUser().getUserId();

        //生成申请绑定记录
        UserHouseBinding userHouseBinding = new UserHouseBinding();
        userHouseBinding.setHouseId(houseId);
        userHouseBinding.setUserId(userId);
        userHouseBinding.setRelationType(relatioType);
        userHouseBinding.setStatus(1);

        userHouseBindingMapper.insert(userHouseBinding);
        return Result.ok("申请绑定成功");
    }

    public Result<?> listBindingHouse(Integer status, Integer pageNum, Integer pageSize) {

        if(UserContextHolder.isCurrentUserRole(CurrentUser.Resident)){
            return Result.fail("居民不能查询房屋申请绑定记录列表");
        }

        PageHelper.startPage(pageNum, pageSize);
        List<UserHouseBinding> list=userHouseBindingMapper.listByStatus(status);

        if(CollectionUtil.isEmpty(list)){
            return Result.ok("当前状态不存在房屋绑定申请");
        }

        return Result.ok(list);
    }

    public Result<?> getBindingHouseRecordById(Long id) {

        //先将记录查询出来再鉴权
        UserHouseBinding result = userHouseBindingMapper.getById(id);

        CurrentUser currentUser = UserContextHolder.getCurrentUser();

        if(currentUser==null||(Objects.equals(currentUser.getRole(),CurrentUser.Resident)
                                && !Objects.equals(currentUser.getUserId(),result.getUserId()) ) ){
            return Result.fail("没有权限查看此记录");
        }

        return Result.ok(result);

    }
    @Transactional
    public Result<?> updateBindingHouseRecordById(Long id, UserHouseBinding dto) {

        if(UserContextHolder.isCurrentUserRole(CurrentUser.Resident)){
            return Result.fail("居民不能更新房屋申请绑定记录");
        }

        dto.setUpdatedBy(UserContextHolder.getCurrentUser().getUserId());
        dto.setId(id);

        if(!Objects.equals(dto.getStatus(),1)){
//            log.error("{},{}",dto.getHouseId(),dto.getStatus());
            houseInfoMapper.updateStatusById(dto.getHouseId(),1);
        }
        int i = userHouseBindingMapper.updateById(dto);

        if(i!=1){
            return Result.fail("更新异常");
        }
        return Result.ok("更新成功");
    }

    public Result<?> getHouseStatus(Long houseId) {

        Integer status = houseInfoMapper.getHouseStatusById(houseId);
        if(status==null){
            return Result.fail("不存在此房屋");
        }
        return Result.ok(status);
    }

    public Result<?> getBindingHouseRecordStatusMyself() {
        if(!UserContextHolder.isCurrentUserRole(CurrentUser.Resident)){
            return Result.fail("必须是居民才能查询自身状态");
        }
        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        List<UserHouseBinding> result = userHouseBindingMapper.getByUserId(currentUser.getUserId());

        if(result==null){
            return Result.fail("您未申请绑定");
        }
        return Result.ok(result);
    }

    @Transactional
    public Result<?> createGrid(CreateGridDTO dto) {

        if(!UserContextHolder.isCurrentUserRole(CurrentUser.Administrator)){
            return Result.fail("必须是管理员才能创建网格");
        }

        List<CreateGridDTO.point> points = dto.getCoordinates();

        //最后一个新增的点和第一个点一样，表示闭合多边形
        Coordinate[] polygonCoords = new Coordinate[points.size()+1];


        for (int i = 0; i < points.size(); i++) {

            polygonCoords[i]=new Coordinate(
                    points.get(i).getLongitude(),points.get(i).getLatitude());

        }

        polygonCoords[points.size()]=new Coordinate(
                points.get(0).getLongitude(),points.get(0).getLatitude());

        // 创建多边形
        GeometryFactory factory = new GeometryFactory();
        LinearRing ring = factory.createLinearRing(polygonCoords);
        Polygon polygon = factory.createPolygon(ring, null);

        //插入网格
        CommunityGridEntity grid = new CommunityGridEntity();

        grid.setGridName(dto.getGridName());
        grid.setCommunityId(dto.getCommunityId());
        grid.setResponsibleId(dto.getResponsibleId());
        grid.setBoundaryGeojson(JSONUtil.toJsonStr(points));

        log.error(grid.getBoundaryGeojson());
        communityGridMapper.insert(grid);

        //维护网格与小区楼栋关系,使用档案
        List<BuildingInfo> buildings=buildingInfoMapper.listByCommunityId(dto.getCommunityId());
        buildings.forEach(item->{

            JSONObject jsonObj = JSONUtil.parseObj(item.getGeoJson());

            double lng = jsonObj.getDouble("longitude"); // 经度
            double lat = jsonObj.getDouble("latitude");   // 纬度

            Point point = factory.createPoint(new Coordinate(lng, lat));

            boolean contains = polygon.contains(point);

            if(contains){

                GridItemArchive buildingArchive = new GridItemArchive();

                buildingArchive.setArchiveType(GridItemArchive.ARCHIVE_TYPE_BUILDING);
                buildingArchive.setGridId(grid.getId());
                buildingArchive.setRelatedId(item.getId());
                buildingArchive.setName("网格:"+grid.getId()+"--楼栋："+item.getId());

                gridItemArchiveMapper.insert(buildingArchive);
            }

        });

        return Result.ok();
    }

    public Result<?> listAllGrids() {

            if (!UserContextHolder.isCurrentUserRole(CurrentUser.Administrator)) {
                return Result.fail("权限不足");
            }

            List<CommunityGridEntity> grids = communityGridMapper.listAll();
        if (CollectionUtil.isEmpty(grids)) {
            return Result.fail("不存在网格");
        }

                List<Map<String,Object>> result=new ArrayList<>();
                grids.forEach(item-> {
                            HashMap<String, Object> grid = new HashMap<>();
                            grid.put("id",item.getId());
                            grid.put("gridName",item.getGridName());
                            grid.put("communityId",item.getCommunityId());
                            grid.put("responsibleId",item.getResponsibleId());
                            List<CreateGridDTO.point> points = JSONUtil.parseArray(item.getBoundaryGeojson())
                                                                    .toList(CreateGridDTO.point.class);
                            grid.put("coordinates",points);
                            result.add(grid);
                        }
                        );


            return Result.ok(result);
    }
}
