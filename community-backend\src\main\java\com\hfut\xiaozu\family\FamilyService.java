package com.hfut.xiaozu.family;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.hfut.xiaozu.common.Result;
import com.hfut.xiaozu.family.info.FamilyMemberEntity;
import com.hfut.xiaozu.family.info.FamilyMemberMapper;
import com.hfut.xiaozu.family.info.addFamilyMemberDTO;
import com.hfut.xiaozu.user.context.CurrentUser;
import com.hfut.xiaozu.user.context.UserContextHolder;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-06-28
 */
@Service
public class FamilyService {

    @Resource
    private FamilyMemberMapper familyMemberMapper;

    public Result<?> addFamilyMembers(List<addFamilyMemberDTO> familyMembers) {
        CurrentUser currentUser = UserContextHolder.getCurrentUser();

        List<FamilyMemberEntity> result=new ArrayList<>();

        familyMembers.forEach( item->{
            FamilyMemberEntity entity = new FamilyMemberEntity();

            BeanUtil.copyProperties(item,entity);
            entity.setIsDeleted(0);
            entity.setOwnerId(currentUser.getUserId());

            result.add(entity);
            }
        );

        int insert=0;
        for (FamilyMemberEntity entity : result) {
            insert += familyMemberMapper.insert(entity);
        }

        if(insert!=familyMembers.size()){
            return Result.fail("批量插入异常");
        }

        return  Result.ok("批量插入成功");
    }

    public Result<?> listMyFamilyMembers() {
        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        if(!Objects.equals(currentUser.getRole(),CurrentUser.Resident )){
            return Result.fail("只有居民才能查询自己的家属");
        }

        List<FamilyMemberEntity> result = familyMemberMapper.listByOwnerId(currentUser.getUserId());

        if(CollectionUtil.isEmpty(result)){
            return Result.fail("未添加家属");
        }

        return Result.ok(result);
    }

    public Result<?> updateFamilyMember(FamilyMemberEntity familyMember) {
        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        if(!Objects.equals(currentUser.getRole(),CurrentUser.Resident )){
            return Result.fail("只有居民修改家属信息");
        }

        if(familyMember.getOwnerId()==null|| !Objects.equals(familyMember.getOwnerId(),currentUser.getUserId()) ){
            return Result.fail("您不存在此家属");
        }

        int update = familyMemberMapper.update(familyMember);
        if(update!=1){
            return Result.fail("更新异常");
        }
        return Result.ok("更新成功");
    }

    public Result<?> deleteFamilyMemberById(Long id) {
        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        if(!Objects.equals(currentUser.getRole(),CurrentUser.Resident )){
            return Result.fail("只有居民才能删除家属");
        }

        FamilyMemberEntity entity = familyMemberMapper.getById(id);
        if(entity==null||!Objects.equals(entity.getOwnerId(),currentUser.getUserId()) ){
            return Result.fail("您不存在此家属");
        }

        int i = familyMemberMapper.deleteById(id);
        if(i!=1){
            return Result.fail("更新异常");
        }
        return Result.ok("更新成功");
    }
}
