<template>
  <div class="patrol-route-management">
    <div class="page-header">
      <h1>巡查路线管理</h1>
      <p class="subtitle">巡查路线规划、任务分配与执行监控</p>
    </div>

    <div class="management-content">
      <!-- 工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <button class="btn primary" @click="createRoute">
            <span class="btn-icon">➕</span>
            新建路线
          </button>
          <button class="btn secondary" @click="importRoutes">
            <span class="btn-icon">📥</span>
            导入路线
          </button>
          <button class="btn secondary" @click="exportRoutes">
            <span class="btn-icon">📤</span>
            导出路线
          </button>
        </div>
        <div class="toolbar-right">
          <div class="search-box">
            <input 
              type="text" 
              v-model="searchQuery" 
              placeholder="搜索路线名称..."
              class="search-input"
            >
            <button class="search-btn">🔍</button>
          </div>
          <select v-model="statusFilter" class="filter-select">
            <option value="">全部状态</option>
            <option value="active">活跃</option>
            <option value="inactive">非活跃</option>
          </select>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <div class="content-layout">
          <!-- 路线列表 -->
          <div class="route-list-section">
            <div class="section-header">
              <h2>巡查路线</h2>
              <span class="route-count">共 {{ filteredRoutes.length }} 条路线</span>
            </div>
            
            <div class="route-list">
              <div 
                v-for="route in filteredRoutes" 
                :key="route.id"
                :class="['route-item', { selected: selectedRoute?.id === route.id }]"
                @click="selectRoute(route)"
              >
                <div class="route-header">
                  <h3>{{ route.name }}</h3>
                  <span :class="['status-badge', route.status]">
                    {{ getStatusText(route.status) }}
                  </span>
                </div>
                <div class="route-info">
                  <div class="info-item">
                    <span class="label">负责人:</span>
                    <span class="value">{{ route.assignee }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">路线长度:</span>
                    <span class="value">{{ route.distance }}米</span>
                  </div>
                  <div class="info-item">
                    <span class="label">检查点:</span>
                    <span class="value">{{ route.checkpoints }}个</span>
                  </div>
                  <div class="info-item">
                    <span class="label">预计时长:</span>
                    <span class="value">{{ route.estimatedTime }}分钟</span>
                  </div>
                </div>
                <div class="route-actions">
                  <button class="action-btn edit" @click.stop="editRoute(route)">
                    <span class="btn-icon">✏️</span>
                    编辑
                  </button>
                  <button class="action-btn view" @click.stop="viewRouteDetails(route)">
                    <span class="btn-icon">👁️</span>
                    详情
                  </button>
                  <button class="action-btn start" @click.stop="startPatrol(route)">
                    <span class="btn-icon">▶️</span>
                    开始巡查
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 地图区域 -->
          <div class="map-section">
            <div class="section-header">
              <h2>路线地图</h2>
              <div class="map-tools">
                <!-- 地图模式切换 -->
                <div class="map-mode-controls">
                  <label>地图模式</label>
                  <div class="mode-buttons">
                    <button
                      :class="['mode-btn', { active: currentMapMode === 'normal' }]"
                      @click="switchMapMode('normal')"
                    >
                      标准
                    </button>
                    <button
                      :class="['mode-btn', { active: currentMapMode === 'satellite' }]"
                      @click="switchMapMode('satellite')"
                    >
                      卫星
                    </button>
                    <button
                      :class="['mode-btn', { active: currentMapMode === 'terrain' }]"
                      @click="switchMapMode('terrain')"
                    >
                      地形
                    </button>
                  </div>
                </div>

                <button
                  :class="['tool-btn', { active: drawingMode === 'route' }]"
                  @click="toggleDrawingMode('route')"
                >
                  <span class="btn-icon">📏</span>
                  绘制路线
                </button>
                <button
                  :class="['tool-btn', { active: drawingMode === 'checkpoint' }]"
                  @click="toggleDrawingMode('checkpoint')"
                >
                  <span class="btn-icon">📍</span>
                  添加检查点
                </button>
                <button class="tool-btn" @click="clearSelection">
                  <span class="btn-icon">🧹</span>
                  清除选择
                </button>
              </div>
            </div>
            
            <div class="map-container">
              <MapComponent
                ref="mapComponent"
                :height="'500px'"
                :center="mapCenter"
                :zoom="16"
                :markers="routeMarkers"
                :polygons="gridPolygons"
                :show-drawing-tools="true"
                @marker-click="onMarkerClick"
                @draw-created="onDrawCreated"
              />
            </div>
          </div>
        </div>

        <!-- 路线详情面板 -->
        <div v-if="selectedRoute" class="details-panel">
          <div class="panel-header">
            <h3>{{ selectedRoute.name }} - 详细信息</h3>
            <button class="close-btn" @click="closeDetails">×</button>
          </div>
          <div class="panel-content">
            <div class="detail-tabs">
              <button 
                v-for="tab in detailTabs" 
                :key="tab.key"
                :class="['tab-btn', { active: activeDetailTab === tab.key }]"
                @click="activeDetailTab = tab.key"
              >
                {{ tab.name }}
              </button>
            </div>
            
            <div class="tab-content">
              <!-- 基本信息 -->
              <div v-if="activeDetailTab === 'basic'" class="basic-info">
                <div class="info-grid">
                  <div class="info-item">
                    <label>路线名称:</label>
                    <span>{{ selectedRoute.name }}</span>
                  </div>
                  <div class="info-item">
                    <label>路线编号:</label>
                    <span>{{ selectedRoute.code }}</span>
                  </div>
                  <div class="info-item">
                    <label>负责人:</label>
                    <span>{{ selectedRoute.assignee }}</span>
                  </div>
                  <div class="info-item">
                    <label>联系电话:</label>
                    <span>{{ selectedRoute.phone }}</span>
                  </div>
                  <div class="info-item">
                    <label>路线长度:</label>
                    <span>{{ selectedRoute.distance }}米</span>
                  </div>
                  <div class="info-item">
                    <label>检查点数量:</label>
                    <span>{{ selectedRoute.checkpoints }}个</span>
                  </div>
                  <div class="info-item">
                    <label>预计时长:</label>
                    <span>{{ selectedRoute.estimatedTime }}分钟</span>
                  </div>
                  <div class="info-item">
                    <label>状态:</label>
                    <span :class="['status-text', selectedRoute.status]">
                      {{ getStatusText(selectedRoute.status) }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 检查点列表 -->
              <div v-if="activeDetailTab === 'checkpoints'" class="checkpoints-info">
                <div class="checkpoints-list">
                  <div v-for="(checkpoint, index) in selectedRoute.checkpointList" :key="checkpoint.id" class="checkpoint-item">
                    <div class="checkpoint-number">{{ index + 1 }}</div>
                    <div class="checkpoint-details">
                      <h4>{{ checkpoint.name }}</h4>
                      <p>{{ checkpoint.description }}</p>
                      <div class="checkpoint-meta">
                        <span class="meta-item">类型: {{ checkpoint.type }}</span>
                        <span class="meta-item">预计停留: {{ checkpoint.duration }}分钟</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 巡查记录 -->
              <div v-if="activeDetailTab === 'records'" class="records-info">
                <div class="records-list">
                  <div v-for="record in selectedRoute.patrolRecords" :key="record.id" class="record-item">
                    <div class="record-header">
                      <span class="record-date">{{ formatDate(record.date) }}</span>
                      <span :class="['record-status', record.status]">{{ getRecordStatusText(record.status) }}</span>
                    </div>
                    <div class="record-details">
                      <p><strong>巡查员:</strong> {{ record.patroller }}</p>
                      <p><strong>开始时间:</strong> {{ formatTime(record.startTime) }}</p>
                      <p><strong>结束时间:</strong> {{ formatTime(record.endTime) }}</p>
                      <p><strong>实际时长:</strong> {{ record.actualDuration }}分钟</p>
                      <p v-if="record.notes"><strong>备注:</strong> {{ record.notes }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 路线编辑对话框 -->
    <div v-if="showEditDialog" class="dialog-overlay" @click="closeEditDialog">
      <div class="dialog" @click.stop>
        <div class="dialog-header">
          <h3>{{ editingRoute.id ? '编辑路线' : '新建路线' }}</h3>
          <button class="close-btn" @click="closeEditDialog">×</button>
        </div>
        <div class="dialog-content">
          <form @submit.prevent="saveRoute">
            <div class="form-grid">
              <div class="form-group">
                <label>路线名称</label>
                <input type="text" v-model="editingRoute.name" required>
              </div>
              <div class="form-group">
                <label>路线编号</label>
                <input type="text" v-model="editingRoute.code" required>
              </div>
              <div class="form-group">
                <label>负责人</label>
                <input type="text" v-model="editingRoute.assignee" required>
              </div>
              <div class="form-group">
                <label>联系电话</label>
                <input type="tel" v-model="editingRoute.phone" required>
              </div>
              <div class="form-group">
                <label>预计时长(分钟)</label>
                <input type="number" v-model="editingRoute.estimatedTime" required>
              </div>
              <div class="form-group">
                <label>状态</label>
                <select v-model="editingRoute.status" required>
                  <option value="active">活跃</option>
                  <option value="inactive">非活跃</option>
                </select>
              </div>
            </div>
            <div class="form-actions">
              <button type="button" class="btn secondary" @click="closeEditDialog">取消</button>
              <button type="submit" class="btn primary">保存</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import MapComponent from '../components/MapComponent.vue';
import { getAllGrids } from '../services/gridApi.js';

const router = useRouter();

// 响应式数据
const mapComponent = ref(null);
const searchQuery = ref('');
const statusFilter = ref('');
const selectedRoute = ref(null);
const activeDetailTab = ref('basic');
const drawingMode = ref(null);
const showEditDialog = ref(false);
const editingRoute = reactive({});
const currentMapMode = ref('normal');

// 地图配置
const mapCenter = [117.198612, 31.774164]; // 社区管理系统地图中心点，高德地图格式：[经度, 纬度]

// 详情标签页
const detailTabs = [
  { key: 'basic', name: '基本信息' },
  { key: 'checkpoints', name: '检查点' },
  { key: 'records', name: '巡查记录' }
];

// 模拟巡查路线数据
const routes = ref([
  {
    id: 1,
    name: '芙蓉社区A区巡查路线',
    code: 'PR-A001',
    assignee: '张三',
    phone: '13800138001',
    distance: 800,
    checkpoints: 5,
    estimatedTime: 30,
    status: 'active',
    checkpointList: [
      { id: 1, name: '主入口', description: '检查门禁系统', type: '安全检查', duration: 5 },
      { id: 2, name: '停车场', description: '检查车辆停放', type: '秩序维护', duration: 8 },
      { id: 3, name: '儿童游乐区', description: '检查设施安全', type: '安全检查', duration: 10 },
      { id: 4, name: '垃圾收集点', description: '检查清洁状况', type: '环境检查', duration: 5 },
      { id: 5, name: '消防通道', description: '检查通道畅通', type: '安全检查', duration: 2 }
    ],
    patrolRecords: [
      {
        id: 1,
        date: new Date('2024-01-15'),
        patroller: '张三',
        startTime: '09:00',
        endTime: '09:28',
        actualDuration: 28,
        status: 'completed',
        notes: '一切正常'
      }
    ]
  },
  {
    id: 2,
    name: '芙蓉社区B区巡查路线',
    code: 'PR-B001',
    assignee: '李四',
    phone: '13800138002',
    distance: 600,
    checkpoints: 4,
    estimatedTime: 25,
    status: 'active',
    checkpointList: [
      { id: 1, name: '北门', description: '检查门禁', type: '安全检查', duration: 5 },
      { id: 2, name: '花园', description: '检查绿化', type: '环境检查', duration: 10 },
      { id: 3, name: '健身区', description: '检查器材', type: '设施检查', duration: 8 },
      { id: 4, name: '配电房', description: '检查设备', type: '设施检查', duration: 2 }
    ],
    patrolRecords: []
  }
]);

// 工具函数：计算多边形面积（简单估算）
const calculatePolygonArea = (coordinates) => {
  if (!coordinates || coordinates.length < 3) return 0;

  let area = 0;
  const n = coordinates.length;

  for (let i = 0; i < n; i++) {
    const j = (i + 1) % n;
    area += coordinates[i][0] * coordinates[j][1];
    area -= coordinates[j][0] * coordinates[i][1];
  }

  return Math.abs(area / 2) * 111000 * 111000; // 粗略转换为平方米
};

/**
 * 转换后端网格数据为巡查管理页面格式
 * @param {Object} backendGrid - 后端网格数据
 * @returns {Object} 巡查管理页面网格数据格式
 */
const transformBackendGridToPatrol = (backendGrid) => {
  try {
    // 新格式：后端直接返回坐标对象数组
    const coordinateObjects = backendGrid.coordinates;

    // 转换为前端期望的坐标数组格式 [lng, lat]
    const coordinates = coordinateObjects.map(coord => [
      coord.longitude,
      coord.latitude
    ]);

    // 计算多边形面积
    const area = calculatePolygonArea(coordinates);

    return {
      id: backendGrid.id,
      title: backendGrid.gridName,
      coordinates: coordinates,
      color: '#28a745', // 默认为活跃状态颜色
      fillColor: '#28a745',
      fillOpacity: 0.2,
      strokeWeight: 2,
      popup: `<div><h4>${backendGrid.gridName}</h4><p>面积：${Math.round(area)}m²</p><p>负责人：待分配</p><p>状态：活跃</p></div>`,
      properties: {
        gridId: backendGrid.id,
        name: backendGrid.gridName,
        area: Math.round(area),
        communityId: backendGrid.communityId,
        responsibleId: backendGrid.responsibleId
      }
    };
  } catch (error) {
    console.error('转换网格数据失败:', error, backendGrid);
    return null;
  }
};

/**
 * 从后端加载网格数据
 */
const loadGridsFromBackend = async () => {
  try {
    console.log('🔧 PatrolManagement: 开始从后端加载网格数据...');

    const response = await getAllGrids();

    if (response.success && response.data) {
      console.log('✅ PatrolManagement: 后端网格数据获取成功:', response.data.length, '个网格');

      // 转换数据格式
      const transformedGrids = response.data
        .map(transformBackendGridToPatrol)
        .filter(grid => grid !== null); // 过滤掉转换失败的数据

      console.log('✅ PatrolManagement: 网格数据转换完成:', transformedGrids.length, '个有效网格');
      return transformedGrids;
    } else {
      console.warn('⚠️ PatrolManagement: 后端返回数据为空，使用空数组');
      return [];
    }
  } catch (error) {
    console.error('❌ PatrolManagement: 从后端加载网格数据失败:', error);
    return [];
  }
};

// 网格数据
const gridPolygons = ref([]);
const isLoadingGrids = ref(false);

// 地图标记数据
const routeMarkers = ref([
  {
    lat: 31.774164,
    lng: 117.198612,
    title: '检查点1',
    popup: '主入口检查点',
    properties: { type: 'checkpoint' }
  }
]);

// 计算属性
const filteredRoutes = computed(() => {
  let filtered = routes.value;
  
  if (searchQuery.value) {
    filtered = filtered.filter(route => 
      route.name.includes(searchQuery.value) || 
      route.assignee.includes(searchQuery.value)
    );
  }
  
  if (statusFilter.value) {
    filtered = filtered.filter(route => route.status === statusFilter.value);
  }
  
  return filtered;
});

// 方法
const selectRoute = (route) => {
  selectedRoute.value = route;
};

const getStatusText = (status) => {
  return status === 'active' ? '活跃' : '非活跃';
};

const getRecordStatusText = (status) => {
  const statusMap = {
    'completed': '已完成',
    'in-progress': '进行中',
    'cancelled': '已取消'
  };
  return statusMap[status] || status;
};

const createRoute = () => {
  Object.assign(editingRoute, {
    id: null,
    name: '',
    code: '',
    assignee: '',
    phone: '',
    estimatedTime: 30,
    status: 'active'
  });
  showEditDialog.value = true;
};

const editRoute = (route) => {
  Object.assign(editingRoute, { ...route });
  showEditDialog.value = true;
};

const viewRouteDetails = (route) => {
  selectedRoute.value = route;
  activeDetailTab.value = 'basic';
};

const startPatrol = (route) => {
  console.log('开始巡查:', route.name);
  // 这里可以跳转到巡查执行页面
};

const saveRoute = () => {
  if (editingRoute.id) {
    // 更新现有路线
    const index = routes.value.findIndex(r => r.id === editingRoute.id);
    if (index > -1) {
      routes.value[index] = { ...editingRoute };
    }
  } else {
    // 创建新路线
    const newRoute = {
      ...editingRoute,
      id: Date.now(),
      distance: 0,
      checkpoints: 0,
      checkpointList: [],
      patrolRecords: []
    };
    routes.value.push(newRoute);
  }
  closeEditDialog();
};

const closeEditDialog = () => {
  showEditDialog.value = false;
  Object.keys(editingRoute).forEach(key => delete editingRoute[key]);
};

const closeDetails = () => {
  selectedRoute.value = null;
};

const toggleDrawingMode = (mode) => {
  drawingMode.value = drawingMode.value === mode ? null : mode;
};

const clearSelection = () => {
  selectedRoute.value = null;
};

const importRoutes = () => {
  console.log('导入巡查路线');
};

const exportRoutes = () => {
  console.log('导出巡查路线');
};

const onMarkerClick = (marker) => {
  console.log('点击标记:', marker);
};

const onDrawCreated = (feature) => {
  console.log('绘制完成:', feature);
};

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('zh-CN');
};

const formatTime = (time) => {
  return time;
};

const switchMapMode = (mode) => {
  currentMapMode.value = mode;
  if (mapComponent.value) {
    // 通过MapComponent的图层切换功能来切换地图模式
    switch (mode) {
      case 'normal':
        mapComponent.value.toggleLayer('satellite', false);
        mapComponent.value.toggleLayer('terrain', false);
        break;
      case 'satellite':
        mapComponent.value.toggleLayer('satellite', true);
        mapComponent.value.toggleLayer('terrain', false);
        break;
      case 'terrain':
        mapComponent.value.toggleLayer('satellite', false);
        mapComponent.value.toggleLayer('terrain', true);
        break;
    }
  }
};

/**
 * 刷新网格数据
 */
const refreshGridData = async () => {
  isLoadingGrids.value = true;
  try {
    const gridData = await loadGridsFromBackend();
    gridPolygons.value = gridData;

    console.log('✅ PatrolManagement: 网格数据刷新完成:', gridData.length, '个网格');
  } catch (error) {
    console.error('❌ PatrolManagement: 刷新网格数据失败:', error);
  } finally {
    isLoadingGrids.value = false;
  }
};

// 生命周期
onMounted(async () => {
  console.log('巡查路线管理页面初始化');

  // 加载网格数据
  await refreshGridData();
});
</script>

<style scoped>
.patrol-route-management {
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #FFF7E2, #FFB07C);
  color: white;
  padding: 30px;
  text-align: center;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 2.5em;
  font-weight: 600;
}

.subtitle {
  margin: 0;
  opacity: 0.9;
  font-size: 1.2em;
}

.management-content {
  padding: 30px;
}

.toolbar {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 30px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.btn {
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
}

.btn.primary {
  background: #FFB07C;
  color: #5B5347;
}

.btn.primary:hover {
  background: #FDE1B6;
}

.btn.secondary {
  background: #6c757d;
  color: white;
}

.btn.secondary:hover {
  background: #5a6268;
}

.btn-icon {
  font-size: 14px;
}

.search-box {
  display: flex;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.search-input {
  padding: 8px 12px;
  border: none;
  outline: none;
  width: 200px;
  font-size: 14px;
}

.search-btn {
  background: #f8f9fa;
  border: none;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
}

.main-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
  overflow: hidden;
}

.content-layout {
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  min-height: 600px;
}

.route-list-section {
  border-right: 1px solid #e1e8ed;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #e1e8ed;
  background: #f8f9fa;
}

.section-header h2 {
  margin: 0;
  font-size: 1.4em;
  color: #333;
}

.route-count {
  color: #666;
  font-size: 14px;
}

.route-list {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.route-item {
  background: #f8f9fa;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.route-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.route-item.selected {
  border-color: #FFB07C;
  background: #f0f7ff;
}

.route-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.route-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.2em;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.status-badge.active {
  background: #d4edda;
  color: #155724;
}

.status-badge.inactive {
  background: #f8d7da;
  color: #721c24;
}

.route-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-bottom: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.info-item .label {
  color: #666;
  font-weight: 500;
}

.info-item .value {
  color: #333;
  font-weight: 600;
}

.route-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  background: #f8f9fa;
  border: 1px solid #e1e8ed;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: #e9ecef;
}

.action-btn.edit:hover {
  border-color: #FFB07C;
  color: #FFB07C;
}

.action-btn.view:hover {
  border-color: #28a745;
  color: #28a745;
}

.action-btn.start:hover {
  border-color: #ffc107;
  color: #856404;
}

.map-section {
  display: flex;
  flex-direction: column;
}

.map-tools {
  display: flex;
  gap: 15px;
  align-items: center;
}

/* 地图模式切换样式 */
.map-mode-controls {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.map-mode-controls label {
  font-size: 12px;
  color: #666;
  font-weight: 600;
}

.mode-buttons {
  display: flex;
  gap: 2px;
  border: 1px solid #ddd;
  border-radius: 6px;
  overflow: hidden;
}

.mode-btn {
  background: white;
  color: #666;
  border: none;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
  border-right: 1px solid #ddd;
}

.mode-btn:last-child {
  border-right: none;
}

.mode-btn:hover {
  background: #f8f9fa;
  color: #333;
}

.mode-btn.active {
  background: #FFB07C;
  color: white;
}

.tool-btn {
  background: #f8f9fa;
  border: 1px solid #e1e8ed;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
}

.tool-btn:hover {
  background: #e9ecef;
  border-color: #FFB07C;
}

.tool-btn.active {
  background: #FFB07C;
  color: white;
  border-color: #FFB07C;
}

.map-container {
  flex: 1;
  padding: 20px;
}

.details-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: 400px;
  height: 100vh;
  background: white;
  box-shadow: -2px 0 12px rgba(0,0,0,0.15);
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e1e8ed;
  background: #f8f9fa;
}

.panel-header h3 {
  margin: 0;
  font-size: 1.2em;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.close-btn:hover {
  background: #e9ecef;
  color: #333;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
}

.detail-tabs {
  display: flex;
  border-bottom: 1px solid #e1e8ed;
}

.tab-btn {
  background: none;
  border: none;
  padding: 15px 20px;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  flex: 1;
}

.tab-btn:hover {
  color: #FFB07C;
  background: #f8f9fa;
}

.tab-btn.active {
  color: #FFB07C;
  border-bottom-color: #FFB07C;
  background: #f8f9fa;
}

.tab-content {
  padding: 20px;
}

.basic-info .info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
}

.basic-info .info-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.basic-info .info-item label {
  font-weight: 600;
  color: #666;
  font-size: 14px;
}

.basic-info .info-item span {
  color: #333;
  font-size: 16px;
}

.status-text.active {
  color: #28a745;
  font-weight: 600;
}

.status-text.inactive {
  color: #dc3545;
  font-weight: 600;
}

.checkpoints-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.checkpoint-item {
  display: flex;
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #FFB07C;
}

.checkpoint-number {
  background: #FFB07C;
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}

.checkpoint-details h4 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 16px;
}

.checkpoint-details p {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 14px;
}

.checkpoint-meta {
  display: flex;
  gap: 15px;
}

.meta-item {
  font-size: 12px;
  color: #666;
  background: white;
  padding: 4px 8px;
  border-radius: 4px;
}

.records-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.record-item {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #28a745;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.record-date {
  font-weight: 600;
  color: #333;
}

.record-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.record-status.completed {
  background: #d4edda;
  color: #155724;
}

.record-status.in-progress {
  background: #cce5ff;
  color: #004085;
}

.record-status.cancelled {
  background: #f8d7da;
  color: #721c24;
}

.record-details p {
  margin: 5px 0;
  font-size: 14px;
  color: #666;
}

/* 对话框样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e1e8ed;
  background: #f8f9fa;
  border-radius: 12px 12px 0 0;
}

.dialog-header h3 {
  margin: 0;
  font-size: 1.3em;
  color: #333;
}

.dialog-content {
  padding: 20px;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.form-group input,
.form-group select {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #FFB07C;
  box-shadow: 0 0 0 3px rgba(255, 176, 124, 0.1);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-layout {
    grid-template-columns: 1fr;
  }

  .route-list-section {
    border-right: none;
    border-bottom: 1px solid #e1e8ed;
  }

  .details-panel {
    width: 100%;
    position: relative;
    height: auto;
    box-shadow: none;
    border-top: 1px solid #e1e8ed;
  }
}

@media (max-width: 768px) {
  .management-content {
    padding: 15px;
  }

  .toolbar {
    flex-direction: column;
    align-items: stretch;
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }

  .search-input {
    width: 150px;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .detail-tabs {
    flex-direction: column;
  }

  .tab-btn {
    border-bottom: 1px solid #e1e8ed;
    border-right: none;
  }

  .tab-btn.active {
    border-bottom-color: #e1e8ed;
    border-left: 3px solid #FFB07C;
  }
}
</style>
