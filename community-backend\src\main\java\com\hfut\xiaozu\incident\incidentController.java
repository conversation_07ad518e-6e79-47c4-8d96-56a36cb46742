package com.hfut.xiaozu.incident;

import com.hfut.xiaozu.common.Result;
import com.hfut.xiaozu.incident.record.ReportIncidentDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-06-29
 */
@Tag(name = "事件管理")
@RestController
@RequestMapping("/api/incident")
public class incidentController {

    @Resource
    private incidentService incidentService;

    @Operation(summary = "事件上报")
    @PostMapping
    public Result<?> reportIncident(@Valid @RequestBody ReportIncidentDTO vo, BindingResult bindingResult){
        List<String> errors = new ArrayList<>();
        if(bindingResult.hasErrors()){
            errors.addAll(bindingResult.getAllErrors()
                    .stream()
                    .map(DefaultMessageSourceResolvable::getDefaultMessage)
                    .collect(Collectors.toList()));
        }

        if(!errors.isEmpty()){
            return Result.fail(errors.toString());
        }

        return incidentService.reportIncident(vo);
    }

    @Operation(summary = "按状态查询事件列表")
    @GetMapping("/list/{status}")
    public Result<?> listIncidentByStatus(@PathVariable Integer status){
        if(status==null||(status!=1&&status!=2&&status!=3&&status!=4)){
            return Result.fail("状态值错误，只能是1(待分派)、2(处理中)、3(已完成)、4(已关闭)");
        }
        return incidentService.listIncidentByStatus(status);
    }

    @Operation(summary = "查询所有事件列表")
    @GetMapping("/list")
    public Result<?> listAllIncidents(){
        return incidentService.listAllIncidents();
    }

    @Operation(summary = "根据事件ID查询事件详情")
    @GetMapping("/{incidentId}")
    public Result<?> getIncidentById(@PathVariable Long incidentId){

        if (incidentId == null) {
            return Result.fail("事件ID不能为空");
        }

        return incidentService.getIncidentById(incidentId);
    }

    @Operation(summary = "根据事件ID查询事件详细信息（包含处理记录）")
    @GetMapping("/{incidentId}/detail")
    public Result<?> getIncidentDetailById(@PathVariable Long incidentId){

        if (incidentId == null) {
            return Result.fail("事件ID不能为空");
        }

        return incidentService.getIncidentDetailById(incidentId);
    }

    @Operation(summary = "根据事件ID分派事件")
    @GetMapping("/{incidentId}/assign")
    public Result<?> assignIncidentById(@PathVariable Long incidentId,@RequestParam Long handlerId){

        if (incidentId == null) {
            return Result.fail("事件ID不能为空");
        }

        return incidentService.assignIncidentById(incidentId,handlerId);
    }

    @Operation(summary = "根据事件ID推荐网格员")
    @GetMapping("/{incidentId}/recommendedGridWorker")
    public Result<?> recommendedGridWorkerByIncidentId(@PathVariable Long incidentId){

        if (incidentId == null) {
            return Result.fail("事件ID不能为空");
        }

        return incidentService.recommendedGridWorkerByIncidentId(incidentId);
    }

    @Operation(summary = "网格员完成任务")
    @PutMapping("/{incidentId}/complete")
    public Result<?> completeIncidentById(@PathVariable Long incidentId){

        if (incidentId == null) {
            return Result.fail("事件ID不能为空");
        }

        return incidentService.completeIncidentById(incidentId);
    }
}
