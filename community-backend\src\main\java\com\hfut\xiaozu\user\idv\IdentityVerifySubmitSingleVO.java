package com.hfut.xiaozu.user.idv;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IdentityVerifySubmitSingleVO {
    /**
     * 认证提交ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户昵称
     */
    private String userName;

    /**
     * 提交的姓名
     */
    private String submittedName;

    /**
     * 提交的身份证号
     */
    private String submittedIdCard;

    /**
     * 当前状态: 0-待审核,1-审核失败,2-审核通过
     */
    private Integer status;

    /**
     * 审核结果详情（如失败原因）
     */
    private String reviewRemark;

    /**
     * 审核人ID（关联用户表）
     */
    private Long reviewerId;

    /**
     * 提交时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
