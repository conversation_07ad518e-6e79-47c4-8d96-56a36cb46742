package com.hfut.xiaozu.family;

import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.PhoneUtil;
import com.hfut.xiaozu.common.Result;
import com.hfut.xiaozu.family.info.FamilyMemberEntity;
import com.hfut.xiaozu.family.info.addFamilyMemberDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-06-28
 */
@Tag(name = "家人管理")
@RequestMapping("/api/family")
@RestController
public class FamilyController {

    @Resource
    private FamilyService familyService;

    @Operation(summary = "批量插入家人信息")
    @PostMapping
    public Result<?> batchInsertFamilyMembers(@Valid @RequestBody List<addFamilyMemberDTO> familyMembers, BindingResult bindingResult) {
        List<String> errors = new ArrayList<>();
        if(bindingResult.hasErrors()){
            errors.addAll(bindingResult.getAllErrors()
                    .stream()
                    .map(DefaultMessageSourceResolvable::getDefaultMessage)
                    .collect(Collectors.toList()));
        }

        familyMembers.forEach(item->{
            if(!PhoneUtil.isPhone(item.getContactPhone())&&!errors.contains("手机号格式错误")){
                errors.add("手机号格式错误");
            }
            if(!IdcardUtil.isValidCard(item.getIdCard())&&!errors.contains("身份证格式错误")){
                errors.add("身份证格式错误");
            }
        });

        if(!errors.isEmpty()){
            return Result.fail(errors.toString());
        }

        return familyService.addFamilyMembers(familyMembers);
    }

    @Operation(summary = "列出自身信息")
    @GetMapping("/list/me")
    public Result<?> listMyFamilyMembers() {
        return familyService.listMyFamilyMembers();
    }

    @Operation(summary = "更新家人信息")
    @PutMapping
    public Result<?> updateFamilyMember(@RequestBody FamilyMemberEntity familyMember) {
        return familyService.updateFamilyMember(familyMember);
    }

    @Operation(summary = "删除家人")
    @DeleteMapping
    public Result<?> updateFamilyMember(@RequestParam Long id) {
        return familyService.deleteFamilyMemberById(id);
    }
}
