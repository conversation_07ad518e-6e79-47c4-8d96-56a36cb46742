-- 创建数据库
CREATE DATABASE IF NOT EXISTS community CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE community;

-- 创建用户账户表
CREATE TABLE IF NOT EXISTS user_account (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  user_name VARCHAR(50) NOT NULL COMMENT '用户名（唯一标识）',
  phone VARCHAR(20) NOT NULL COMMENT '手机号码',
  password VARCHAR(200) NOT NULL COMMENT '密码',
  real_name VARCHAR(50) COMMENT '真实姓名',
  id_card VARCHAR(18) COMMENT '身份证号码',
  avatar_url VARCHAR(255) COMMENT '头像URL',
  is_verified TINYINT UNSIGNED DEFAULT 0 COMMENT '是否实名认证 0-否 1-是',
  is_deleted TINYINT UNSIGNED DEFAULT 0 COMMENT '是否删除 0-否 1-是',
  user_type TINYINT UNSIGNED DEFAULT 1 COMMENT '用户类型 1-居民 2-网格员 3-社区管理员',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  -- 添加唯一索引
  UNIQUE KEY uk_user_name (user_name),
  UNIQUE KEY uk_phone (phone),
  
  -- 添加普通索引
  KEY idx_user_type (user_type),
  KEY idx_create_time (create_time)
) COMMENT '用户账户信息表';

-- 插入测试数据（可选）
INSERT IGNORE INTO user_account (user_name, phone, password, user_type) VALUES
('admin', '***********', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyiLZOiCsIrRHEQ7Ej.x8yKKGKu', 3),
('grid001', '***********', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyiLZOiCsIrRHEQ7Ej.x8yKKGKu', 2),
('resident001', '***********', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyiLZOiCsIrRHEQ7Ej.x8yKKGKu', 1);

-- 查看表结构
DESCRIBE user_account;

-- 查看数据
SELECT id, user_name, phone, user_type, create_time FROM user_account;
