<template>
  <div class="event-management">
    <div class="page-header">
      <h1>事件管理</h1>
      <p class="subtitle">社区事件全生命周期管理</p>
    </div>

    <div class="management-content">
      <!-- 功能导航 -->
      <div class="function-nav">
        <div class="nav-tabs">
          <button 
            v-for="tab in tabs" 
            :key="tab.key"
            :class="['tab-btn', { active: activeTab === tab.key }]"
            @click="activeTab = tab.key"
          >
            <span class="tab-icon">{{ tab.icon }}</span>
            {{ tab.name }}
          </button>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 事件列表 -->
        <div v-if="activeTab === 'list'" class="content-panel">
          <div class="panel-header">
            <h2>事件列表</h2>
            <div class="panel-actions">
              <button class="action-btn primary" @click="createEvent">
                <span class="btn-icon">➕</span>
                新建事件
              </button>
              <button class="action-btn" @click="refreshEvents">
                <span class="btn-icon">🔄</span>
                刷新
              </button>
              <button class="action-btn" @click="exportEvents">
                <span class="btn-icon">📤</span>
                导出
              </button>
            </div>
          </div>

          <!-- 筛选控制 -->
          <div class="filter-section">
            <div class="filter-controls">
              <div class="filter-group">
                <label>搜索</label>
                <input 
                  type="text" 
                  v-model="searchQuery" 
                  placeholder="搜索事件ID、位置或描述..."
                  class="search-input"
                >
              </div>
              <div class="filter-group">
                <label>事件类型</label>
                <select v-model="typeFilter" class="filter-select">
                  <option value="">全部类型</option>
                  <option v-for="type in eventTypes" :key="type.key" :value="type.key">
                    {{ type.name }}
                  </option>
                </select>
              </div>
              <div class="filter-group">
                <label>状态</label>
                <select v-model="statusFilter" class="filter-select">
                  <option value="">全部状态</option>
                  <option v-for="status in eventStatuses" :key="status.key" :value="status.key">
                    {{ status.name }}
                  </option>
                </select>
              </div>
              <div class="filter-group">
                <label>时间范围</label>
                <select v-model="timeFilter" class="filter-select">
                  <option value="all">全部时间</option>
                  <option value="today">今天</option>
                  <option value="week">本周</option>
                  <option value="month">本月</option>
                </select>
              </div>
            </div>
          </div>

          <!-- 事件表格 -->
          <div class="events-table-container">
            <table class="events-table">
              <thead>
                <tr>
                  <th>事件ID</th>
                  <th>类型</th>
                  <th>状态</th>
                  <th>位置</th>
                  <th>报告人</th>
                  <th>创建时间</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="event in filteredEvents" :key="event.id" class="event-row">
                  <td class="event-id">{{ event.id }}</td>
                  <td>
                    <span :class="['type-badge', event.type]">
                      {{ getEventTypeName(event.type) }}
                    </span>
                  </td>
                  <td>
                    <span :class="['status-badge', event.status]">
                      {{ getEventStatusName(event.status) }}
                    </span>
                  </td>
                  <td>{{ event.location }}</td>
                  <td>{{ event.reporter }}</td>
                  <td>{{ formatDate(event.createdAt) }}</td>
                  <td class="actions">
                    <button class="btn-small primary" @click="viewEvent(event)">查看</button>
                    <button class="btn-small" @click="editEvent(event)">编辑</button>
                    <button class="btn-small secondary" @click="viewOnMap(event)">地图</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 事件地图 -->
        <div v-if="activeTab === 'map'" class="content-panel">
          <div class="panel-header">
            <h2>事件地图</h2>
            <div class="panel-actions">
              <button class="action-btn" @click="navigateToEventMap">
                <span class="btn-icon">🗺️</span>
                打开完整地图
              </button>
            </div>
          </div>
          <div class="map-container">
            <MapComponent
              ref="mapComponent"
              map-id="event-management-map"
              :height="'500px'"
              :center="mapCenter"
              :zoom="15"
              :markers="eventMarkers"
              :polygons="[]"
              :show-controls="true"
              :show-drawing-tools="false"
              @marker-click="onEventMarkerClick"
            />
          </div>
        </div>

        <!-- 统计分析 -->
        <div v-if="activeTab === 'statistics'" class="content-panel">
          <div class="panel-header">
            <h2>统计分析</h2>
          </div>
          <div class="statistics-content">
            <div class="stats-grid">
              <div class="stat-card">
                <div class="stat-icon">📊</div>
                <div class="stat-info">
                  <h3>总事件数</h3>
                  <p class="stat-number">{{ eventStats.total }}</p>
                </div>
              </div>
              <div class="stat-card">
                <div class="stat-icon">⏳</div>
                <div class="stat-info">
                  <h3>待处理</h3>
                  <p class="stat-number">{{ eventStats.pending }}</p>
                </div>
              </div>
              <div class="stat-card">
                <div class="stat-icon">🔄</div>
                <div class="stat-info">
                  <h3>处理中</h3>
                  <p class="stat-number">{{ eventStats.processing }}</p>
                </div>
              </div>
              <div class="stat-card">
                <div class="stat-icon">✅</div>
                <div class="stat-info">
                  <h3>已完成</h3>
                  <p class="stat-number">{{ eventStats.completed }}</p>
                </div>
              </div>
            </div>

            <!-- 事件类型分布图表 -->
            <div class="chart-section">
              <h3>事件类型分布</h3>
              <div class="chart-container">
                <div class="chart-placeholder">
                  📈 图表区域 (可集成Chart.js等图表库)
                </div>
              </div>
            </div>

            <!-- 事件趋势图表 -->
            <div class="chart-section">
              <h3>事件处理趋势</h3>
              <div class="chart-container">
                <div class="chart-placeholder">
                  📉 趋势图表区域
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 事件详情弹窗 -->
    <div v-if="selectedEvent" class="event-detail-modal" @click.self="closeEventDetail">
      <div class="modal-content">
        <div class="modal-header">
          <h3>{{ selectedEvent.id }} - 事件详情</h3>
          <button class="close-btn" @click="closeEventDetail">×</button>
        </div>
        <div class="modal-body">
          <div class="detail-grid">
            <div class="detail-item">
              <label>事件类型</label>
              <span :class="['type-badge', selectedEvent.type]">
                {{ getEventTypeName(selectedEvent.type) }}
              </span>
            </div>
            <div class="detail-item">
              <label>状态</label>
              <span :class="['status-badge', selectedEvent.status]">
                {{ getEventStatusName(selectedEvent.status) }}
              </span>
            </div>
            <div class="detail-item">
              <label>位置</label>
              <span>{{ selectedEvent.location }}</span>
            </div>
            <div class="detail-item">
              <label>报告人</label>
              <span>{{ selectedEvent.reporter }}</span>
            </div>
            <div class="detail-item">
              <label>创建时间</label>
              <span>{{ formatDate(selectedEvent.createdAt) }}</span>
            </div>
            <div class="detail-item full-width">
              <label>描述</label>
              <p>{{ selectedEvent.description }}</p>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="action-btn primary" @click="editEvent(selectedEvent)">编辑事件</button>
          <button class="action-btn secondary" @click="viewOnMap(selectedEvent)">在地图上查看</button>
          <button class="action-btn" @click="closeEventDetail">关闭</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import MapComponent from '../components/MapComponent.vue';

const router = useRouter();

// 响应式数据
const activeTab = ref('list');
const mapComponent = ref(null);
const selectedEvent = ref(null);
const searchQuery = ref('');
const typeFilter = ref('');
const statusFilter = ref('');
const timeFilter = ref('all');

// 地图配置
const mapCenter = [117.198612, 31.774164]; // 社区管理系统地图中心点，高德地图格式：[经度, 纬度]

// 标签页配置
const tabs = [
  { key: 'list', name: '事件列表', icon: '📋' },
  { key: 'map', name: '事件地图', icon: '🗺️' },
  { key: 'statistics', name: '统计分析', icon: '📊' }
];

// 事件类型配置
const eventTypes = [
  { key: 'maintenance', name: '维修事件', color: '#ffc107' },
  { key: 'security', name: '安全事件', color: '#dc3545' },
  { key: 'environment', name: '环境事件', color: '#28a745' },
  { key: 'emergency', name: '紧急事件', color: '#fd7e14' }
];

// 事件状态配置
const eventStatuses = [
  { key: 'pending', name: '待处理', color: '#6c757d' },
  { key: 'processing', name: '处理中', color: '#007bff' },
  { key: 'completed', name: '已完成', color: '#28a745' }
];

// 模拟事件数据
const events = ref([
  {
    id: 'E001',
    type: 'maintenance',
    status: 'pending',
    location: 'A区1号楼电梯',
    lat: 31.774164,
    lng: 117.198612,
    reporter: '张三',
    description: '电梯故障，无法正常运行，需要紧急维修',
    createdAt: new Date('2024-01-15T10:30:00')
  },
  {
    id: 'E002',
    type: 'security',
    status: 'processing',
    location: 'B区停车场',
    lat: 31.775164,
    lng: 117.199612,
    reporter: '李四',
    description: '发现可疑人员在停车场徘徊，已通知保安',
    createdAt: new Date('2024-01-15T09:15:00')
  },
  {
    id: 'E003',
    type: 'environment',
    status: 'completed',
    location: 'C区花园',
    lat: 31.773164,
    lng: 117.197612,
    reporter: '王五',
    description: '垃圾桶满溢，需要清理',
    createdAt: new Date('2024-01-14T16:45:00')
  },
  {
    id: 'E004',
    type: 'emergency',
    status: 'processing',
    location: 'D区主入口',
    lat: 31.768427,
    lng: 117.200184,
    reporter: '赵六',
    description: '水管爆裂，大量积水',
    createdAt: new Date('2024-01-15T14:20:00')
  }
]);

// 计算属性
const filteredEvents = computed(() => {
  return events.value.filter(event => {
    // 搜索过滤
    const searchMatch = !searchQuery.value ||
      event.id.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      event.location.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      event.description.toLowerCase().includes(searchQuery.value.toLowerCase());

    // 类型过滤
    const typeMatch = !typeFilter.value || event.type === typeFilter.value;

    // 状态过滤
    const statusMatch = !statusFilter.value || event.status === statusFilter.value;

    // 时间过滤
    let timeMatch = true;
    if (timeFilter.value !== 'all') {
      const now = new Date();
      const eventDate = new Date(event.createdAt);

      switch (timeFilter.value) {
        case 'today':
          timeMatch = eventDate.toDateString() === now.toDateString();
          break;
        case 'week':
          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          timeMatch = eventDate >= weekAgo;
          break;
        case 'month':
          const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          timeMatch = eventDate >= monthAgo;
          break;
      }
    }

    return searchMatch && typeMatch && statusMatch && timeMatch;
  });
});

const eventMarkers = computed(() => {
  return filteredEvents.value.map(event => ({
    lat: event.lat,
    lng: event.lng,
    title: `${event.id} - ${getEventTypeName(event.type)}`,
    popup: `<div><h4>${event.location}</h4><p>${event.description}</p></div>`,
    properties: {
      eventId: event.id,
      type: event.type,
      status: event.status
    }
  }));
});

const eventStats = computed(() => {
  const stats = {
    total: events.value.length,
    pending: 0,
    processing: 0,
    completed: 0
  };

  events.value.forEach(event => {
    stats[event.status]++;
  });

  return stats;
});

// 方法
const getEventTypeName = (type) => {
  const typeObj = eventTypes.find(t => t.key === type);
  return typeObj ? typeObj.name : type;
};

const getEventStatusName = (status) => {
  const statusObj = eventStatuses.find(s => s.key === status);
  return statusObj ? statusObj.name : status;
};

const formatDate = (date) => {
  return new Date(date).toLocaleString('zh-CN');
};

const createEvent = () => {
  router.push('/property/event-management/create');
};

const viewEvent = (event) => {
  selectedEvent.value = event;
};

const editEvent = (event) => {
  router.push(`/property/event-management/edit/${event.id}`);
};

const viewOnMap = (event) => {
  router.push({
    path: '/property/event-map',
    query: { eventId: event.id }
  });
};

const closeEventDetail = () => {
  selectedEvent.value = null;
};

const refreshEvents = () => {
  console.log('刷新事件数据');
};

const exportEvents = () => {
  console.log('导出事件数据');
};

const navigateToEventMap = () => {
  router.push('/property/event-map');
};

const onEventMarkerClick = (marker) => {
  const eventId = marker.properties.eventId;
  const event = events.value.find(e => e.id === eventId);
  if (event) {
    selectedEvent.value = event;
  }
};

// 生命周期
onMounted(() => {
  console.log('事件管理页面初始化');
});
</script>

<style scoped>
.event-management {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 30px;
  text-align: center;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 2.2em;
  font-weight: 600;
}

.subtitle {
  margin: 0;
  opacity: 0.9;
  font-size: 1.1em;
}

.management-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.function-nav {
  background: white;
  border-bottom: 1px solid #e1e8ed;
  padding: 0 30px;
}

.nav-tabs {
  display: flex;
  gap: 0;
}

.tab-btn {
  background: none;
  border: none;
  padding: 15px 25px;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tab-btn:hover {
  color: #4a90e2;
  background: #f8f9fa;
}

.tab-btn.active {
  color: #4a90e2;
  border-bottom-color: #4a90e2;
  background: #f8f9fa;
}

.tab-icon {
  font-size: 16px;
}

.main-content {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
}

.content-panel {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #e1e8ed;
  background: #fafbfc;
}

.panel-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.5em;
}

.panel-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  background: #4a90e2;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: background 0.3s ease;
}

.action-btn:hover {
  background: #357abd;
}

.action-btn.primary {
  background: #28a745;
}

.action-btn.primary:hover {
  background: #218838;
}

.action-btn.secondary {
  background: #6c757d;
}

.action-btn.secondary:hover {
  background: #545b62;
}

.btn-icon {
  font-size: 14px;
}

/* 筛选控制样式 */
.filter-section {
  padding: 20px 30px;
  border-bottom: 1px solid #e1e8ed;
  background: #f8f9fa;
}

.filter-controls {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 20px;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.filter-group label {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.search-input,
.filter-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.search-input:focus,
.filter-select:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

/* 表格样式 */
.events-table-container {
  overflow-x: auto;
}

.events-table {
  width: 100%;
  border-collapse: collapse;
}

.events-table th,
.events-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #e1e8ed;
}

.events-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.event-row:hover {
  background: #f8f9fa;
}

.event-id {
  font-family: monospace;
  font-weight: 600;
  color: #4a90e2;
}

.type-badge,
.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  color: white;
}

.type-badge.maintenance { background: #ffc107; color: #000; }
.type-badge.security { background: #dc3545; }
.type-badge.environment { background: #28a745; }
.type-badge.emergency { background: #fd7e14; }

.status-badge.pending { background: #6c757d; }
.status-badge.processing { background: #007bff; }
.status-badge.completed { background: #28a745; }

.actions {
  display: flex;
  gap: 5px;
}

.btn-small {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background 0.3s ease;
}

.btn-small.primary {
  background: #4a90e2;
  color: white;
}

.btn-small.primary:hover {
  background: #357abd;
}

.btn-small.secondary {
  background: #6c757d;
  color: white;
}

.btn-small.secondary:hover {
  background: #545b62;
}

.btn-small:not(.primary):not(.secondary) {
  background: #e9ecef;
  color: #333;
}

.btn-small:not(.primary):not(.secondary):hover {
  background: #dee2e6;
}

/* 地图容器样式 */
.map-container {
  height: 500px;
  position: relative;
}

/* 统计样式 */
.statistics-content {
  padding: 30px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: #f8f9fa;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.stat-icon {
  font-size: 2em;
  margin-bottom: 10px;
}

.stat-info h3 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 14px;
}

.stat-number {
  margin: 0;
  font-size: 2em;
  font-weight: 600;
  color: #4a90e2;
}

.chart-section {
  margin-bottom: 30px;
}

.chart-section h3 {
  margin: 0 0 15px 0;
  color: #333;
}

.chart-container {
  background: #f8f9fa;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  color: #666;
  font-size: 16px;
}

/* 事件详情弹窗样式 */
.event-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #e1e8ed;
  background: #fafbfc;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.3s ease;
}

.close-btn:hover {
  background: #e9ecef;
}

.modal-body {
  padding: 30px;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-item label {
  font-size: 14px;
  font-weight: 600;
  color: #666;
}

.detail-item span,
.detail-item p {
  color: #333;
  margin: 0;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px 30px;
  border-top: 1px solid #e1e8ed;
  background: #fafbfc;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-controls {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .detail-grid {
    grid-template-columns: 1fr;
  }

  .events-table {
    font-size: 12px;
  }

  .events-table th,
  .events-table td {
    padding: 8px 10px;
  }
}
</style>
