# Vue语音文字互转功能说明

## 概述

本项目实现了基于Web自带TTS（Text-to-Speech）和语音识别API的语音文字互转功能，包含两个主要组件：

1. **VoiceTextConverter.vue** - 完整的语音文字互转组件
2. **VoiceAssistant.vue** - 简化的语音助手组件

## 功能特点

### 文字转语音 (TTS)
- ✅ 支持多种语音选择
- ✅ 可调节语速、音调、音量
- ✅ 播放控制（播放、暂停、停止、继续）
- ✅ 最大支持500字符
- ✅ 实时状态反馈

### 语音转文字 (STT)
- ✅ 支持多种语言识别（中文、英文、日文、韩文）
- ✅ 实时显示识别结果
- ✅ 支持连续识别模式
- ✅ 临时结果显示
- ✅ 复制到剪贴板功能

### 互转功能
- ✅ 识别结果可直接朗读
- ✅ 文字复制功能
- ✅ 错误处理和提示
- ✅ 浏览器兼容性检测

## 组件使用

### VoiceTextConverter 完整组件

```vue
<template>
  <div>
    <VoiceTextConverter ref="voiceConverter" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import VoiceTextConverter from '@/components/VoiceTextConverter.vue'

const voiceConverter = ref(null)

// 调用组件方法
const speakText = (text) => {
  voiceConverter.value.speakText(text)
}

const startRecognition = () => {
  voiceConverter.value.startRecognition()
}
</script>
```

### VoiceAssistant 简化组件

```vue
<template>
  <div>
    <!-- 页面内容 -->
    <VoiceAssistant ref="voiceAssistant" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import VoiceAssistant from '@/components/VoiceAssistant.vue'

const voiceAssistant = ref(null)

// 调用组件方法
const speakWelcome = () => {
  voiceAssistant.value.speak('欢迎使用系统')
}
</script>
```

## API 方法

### VoiceTextConverter 方法

| 方法名 | 参数 | 说明 |
|--------|------|------|
| `speakText(text)` | text: string | 朗读指定文字 |
| `stopSpeaking()` | - | 停止语音播放 |
| `startRecognition()` | - | 开始语音识别 |
| `stopRecognition()` | - | 停止语音识别 |
| `getRecognizedText()` | - | 获取识别结果 |
| `clearAll()` | - | 清空所有内容 |

### VoiceAssistant 方法

| 方法名 | 参数 | 说明 |
|--------|------|------|
| `speak(text)` | text: string | 朗读指定文字 |
| `stop()` | - | 停止语音播放 |
| `show()` | - | 显示语音面板 |
| `hide()` | - | 隐藏语音面板 |

## 浏览器兼容性

| 浏览器 | TTS支持 | STT支持 | 推荐度 |
|--------|---------|---------|--------|
| Chrome | ✅ 完全支持 | ✅ 完全支持 | ⭐⭐⭐⭐⭐ |
| Edge | ✅ 完全支持 | ✅ 完全支持 | ⭐⭐⭐⭐⭐ |
| Safari | ✅ 完全支持 | ⚠️ 部分支持 | ⭐⭐⭐⭐ |
| Firefox | ✅ 完全支持 | ❌ 不支持 | ⭐⭐⭐ |

### 兼容性说明

1. **语音合成 (TTS)**: 所有现代浏览器都支持
2. **语音识别 (STT)**: 主要支持基于Chromium的浏览器
3. **权限要求**: 语音识别需要麦克风权限

## 配置选项

### TTS 配置

```javascript
// 语音参数
const speechConfig = {
  rate: 1,        // 语速 (0.5-2)
  pitch: 1,       // 音调 (0-2)
  volume: 1,      // 音量 (0-1)
  voice: null     // 语音选择
}
```

### STT 配置

```javascript
// 识别参数
const recognitionConfig = {
  lang: 'zh-CN',           // 识别语言
  continuous: true,        // 连续识别
  interimResults: true,    // 临时结果
  maxAlternatives: 1       // 最大候选数
}
```

## 错误处理

### 常见错误类型

| 错误类型 | 说明 | 解决方案 |
|----------|------|----------|
| `no-speech` | 未检测到语音 | 检查麦克风或重新说话 |
| `audio-capture` | 无法访问麦克风 | 检查麦克风连接和权限 |
| `not-allowed` | 权限被拒绝 | 在浏览器设置中允许麦克风权限 |
| `network` | 网络错误 | 检查网络连接 |

### 错误处理示例

```javascript
// 监听错误事件
speechRecognition.onerror = (event) => {
  switch (event.error) {
    case 'no-speech':
      console.log('未检测到语音输入')
      break
    case 'not-allowed':
      console.log('麦克风权限被拒绝')
      break
    default:
      console.log('识别错误:', event.error)
  }
}
```

## 最佳实践

### 1. 权限处理
```javascript
// 检查麦克风权限
const checkMicrophonePermission = async () => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
    stream.getTracks().forEach(track => track.stop())
    return true
  } catch (error) {
    return false
  }
}
```

### 2. 语音质量优化
- 使用清晰的语音输入
- 避免背景噪音
- 保持适当的说话速度
- 使用标准普通话或英语

### 3. 用户体验优化
- 提供清晰的状态反馈
- 支持键盘快捷键
- 添加加载状态指示
- 提供错误恢复机制

## 部署注意事项

### 1. HTTPS 要求
语音识别功能需要在HTTPS环境下运行，本地开发可以使用localhost。

### 2. 权限策略
确保网站的权限策略允许访问麦克风：

```html
<meta http-equiv="Permissions-Policy" content="microphone=()">
```

### 3. 性能优化
- 避免长时间连续识别
- 及时释放语音资源
- 控制识别文本长度

## 示例代码

### 完整使用示例

```vue
<template>
  <div class="voice-demo">
    <h2>语音功能演示</h2>
    
    <!-- 文字输入 -->
    <div class="input-section">
      <textarea v-model="inputText" placeholder="输入要朗读的文字"></textarea>
      <button @click="speakText">🎵 朗读</button>
    </div>
    
    <!-- 语音识别 -->
    <div class="recognition-section">
      <button @click="toggleRecognition" :class="{ active: isListening }">
        {{ isListening ? '🔴 停止识别' : '🎤 开始识别' }}
      </button>
      <div v-if="recognizedText" class="result">
        识别结果: {{ recognizedText }}
      </div>
    </div>
    
    <!-- 语音助手 -->
    <VoiceAssistant ref="assistant" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import VoiceAssistant from '@/components/VoiceAssistant.vue'

const inputText = ref('')
const recognizedText = ref('')
const isListening = ref(false)
const assistant = ref(null)

const speakText = () => {
  if (assistant.value && inputText.value) {
    assistant.value.speak(inputText.value)
  }
}

const toggleRecognition = () => {
  // 实现语音识别逻辑
}
</script>
```

## 更新日志

### v1.0.0 (2024-12-29)
- ✅ 实现基础TTS功能
- ✅ 实现基础STT功能
- ✅ 添加浏览器兼容性检测
- ✅ 完善错误处理机制
- ✅ 创建演示页面

## 技术支持

如有问题或建议，请联系开发团队或提交Issue。

---

**注意**: 此功能依赖于浏览器的Web Speech API，不同浏览器的支持程度可能有所差异。建议在生产环境中进行充分测试。
