<template>
  <div class="placeholder-page">
    <div class="placeholder-content">
      <div class="placeholder-icon">🚧</div>
      <h1>{{ pageTitle }}</h1>
      <p class="placeholder-message">此功能页面正在开发中...</p>
      <div class="placeholder-info">
        <p><strong>当前路径:</strong> {{ $route.path }}</p>
        <p><strong>用户类型:</strong> {{ userTypeText }}</p>
        <p><strong>页面类型:</strong> {{ pageTypeText }}</p>
      </div>
      <button @click="goBack" class="back-button">返回上一页</button>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useUserStore } from '../../stores/userStore';

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();

const pageTitle = computed(() => {
  return route.meta.title || '功能页面';
});

const userTypeText = computed(() => {
  if (userStore.isProperty) return '物业管理端';
  if (userStore.isResident) return '居民端';
  return '未知';
});

const pageTypeText = computed(() => {
  const path = route.path;
  if (path.includes('/property/')) return '物业管理功能';
  if (path.includes('/resident/')) return '居民服务功能';
  if (path.includes('/common/')) return '公共模块功能';
  return '其他功能';
});

const goBack = () => {
  router.go(-1);
};
</script>

<style scoped>
.placeholder-page {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40px 20px;
}

.placeholder-content {
  text-align: center;
  max-width: 500px;
  padding: 40px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  border: 2px dashed #e0e0e0;
}

.placeholder-icon {
  font-size: 4em;
  margin-bottom: 20px;
  display: block;
}

.placeholder-content h1 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.8em;
  font-weight: bold;
}

.placeholder-message {
  margin: 0 0 30px 0;
  color: #666;
  font-size: 1.1em;
  line-height: 1.5;
}

.placeholder-info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
  text-align: left;
}

.placeholder-info p {
  margin: 8px 0;
  color: #555;
  font-size: 0.95em;
}

.placeholder-info strong {
  color: #333;
}

.back-button {
  background: linear-gradient(135deg, #d4a574, #c49660);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1em;
  font-weight: bold;
  transition: all 0.2s;
}

.back-button:hover {
  background: linear-gradient(135deg, #c49660, #b8854c);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.back-button:active {
  transform: translateY(0);
}
</style>
