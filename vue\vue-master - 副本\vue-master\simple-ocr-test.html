<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化OCR测试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="file"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .request-preview {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 简化OCR测试工具</h1>
        
        <div class="form-group">
            <label for="tokenInput">Authorization Token:</label>
            <input type="text" id="tokenInput" placeholder="输入Bearer token (不包含Bearer前缀)">
            <small>示例: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...</small>
        </div>

        <div class="form-group">
            <label for="sessionInput">Session ID:</label>
            <input type="text" id="sessionInput" value="qweqweqweqw" placeholder="输入session_id">
        </div>

        <div class="form-group">
            <label for="imageInput">身份证图片:</label>
            <input type="file" id="imageInput" accept="image/*">
        </div>

        <div class="form-group">
            <button onclick="testOCR()" id="testBtn">🚀 测试OCR</button>
            <button onclick="clearResults()">🗑️ 清除结果</button>
            <button onclick="loadStoredToken()">🔑 加载存储的Token</button>
        </div>

        <div id="requestPreview" class="request-preview" style="display: none;">
            <strong>请求预览:</strong><br>
            <span id="previewContent"></span>
        </div>

        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_URL = 'http://localhost:8080/api/auth/ocr';

        // 加载存储的token
        function loadStoredToken() {
            let token = null;
            
            // 尝试从auth_data获取
            try {
                const authData = localStorage.getItem('auth_data') || sessionStorage.getItem('auth_data');
                if (authData) {
                    const parsed = JSON.parse(authData);
                    token = parsed.token;
                }
            } catch (e) {
                console.warn('解析auth_data失败:', e);
            }
            
            // 备用方案
            if (!token) {
                token = localStorage.getItem('auth_token') || localStorage.getItem('userToken');
            }
            
            if (token) {
                document.getElementById('tokenInput').value = token;
                showResult(`✅ 已加载存储的Token\n\nToken预览: ${token.substring(0, 50)}...`, 'success');
            } else {
                showResult('❌ 未找到存储的Token', 'error');
            }
        }

        // 显示请求预览
        function showRequestPreview(token, sessionId, fileName) {
            const preview = `URL: ${API_URL}
Method: POST
Content-Type: multipart/form-data

Headers:
Authorization: Bearer ${token.substring(0, 30)}...

Form Data:
session_id: ${sessionId}
image: ${fileName}`;
            
            document.getElementById('previewContent').textContent = preview;
            document.getElementById('requestPreview').style.display = 'block';
        }

        // 显示结果
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }

        // 清除结果
        function clearResults() {
            document.getElementById('result').style.display = 'none';
            document.getElementById('requestPreview').style.display = 'none';
        }

        // 测试OCR
        async function testOCR() {
            const token = document.getElementById('tokenInput').value.trim();
            const sessionId = document.getElementById('sessionInput').value.trim();
            const fileInput = document.getElementById('imageInput');
            const file = fileInput.files[0];
            const testBtn = document.getElementById('testBtn');

            // 验证输入
            if (!token) {
                showResult('❌ 请输入Authorization Token', 'error');
                return;
            }

            if (!sessionId) {
                showResult('❌ 请输入Session ID', 'error');
                return;
            }

            if (!file) {
                showResult('❌ 请选择身份证图片', 'error');
                return;
            }

            // 显示请求预览
            showRequestPreview(token, sessionId, file.name);

            // 禁用按钮
            testBtn.disabled = true;
            testBtn.textContent = '🔄 测试中...';

            try {
                console.log('🚀 开始OCR测试:', {
                    url: API_URL,
                    sessionId: sessionId,
                    fileName: file.name,
                    fileSize: file.size,
                    tokenPreview: token.substring(0, 20) + '...'
                });

                // 创建FormData - 严格按照API文档要求
                const formData = new FormData();
                formData.append('session_id', sessionId);  // 注意：使用下划线
                formData.append('image', file);

                // 发送请求 - 严格按照API文档要求
                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`  // 严格按照Bearer格式
                        // 不设置Content-Type，让浏览器自动设置multipart/form-data
                    },
                    body: formData
                });

                console.log('📡 响应状态:', response.status, response.statusText);
                console.log('📡 响应头:', Object.fromEntries(response.headers.entries()));

                // 处理响应
                const responseText = await response.text();
                console.log('📦 原始响应:', responseText);

                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (e) {
                    result = { raw: responseText };
                }

                if (response.ok) {
                    let successMessage = `✅ OCR请求成功!\n\n状态码: ${response.status}\n\n`;
                    
                    if (result.data) {
                        successMessage += `识别结果:\n`;
                        if (result.data.name || result.data.ocrName) {
                            successMessage += `姓名: ${result.data.name || result.data.ocrName}\n`;
                        }
                        if (result.data.idcard || result.data.ocrIdNumber) {
                            successMessage += `身份证号: ${result.data.idcard || result.data.ocrIdNumber}\n`;
                        }
                        successMessage += `\n`;
                    }
                    
                    successMessage += `完整响应:\n${JSON.stringify(result, null, 2)}`;
                    showResult(successMessage, 'success');
                } else {
                    let errorMessage = `❌ OCR请求失败\n\n状态码: ${response.status}\n`;
                    
                    if (result.msg || result.message) {
                        errorMessage += `错误信息: ${result.msg || result.message}\n`;
                    }
                    
                    errorMessage += `\n完整响应:\n${JSON.stringify(result, null, 2)}`;
                    showResult(errorMessage, 'error');
                }

            } catch (error) {
                console.error('❌ 请求异常:', error);
                showResult(`❌ 请求异常\n\n错误信息: ${error.message}\n\n请检查:\n1. 网络连接\n2. 后端服务状态\n3. Token有效性\n4. CORS配置`, 'error');
            } finally {
                // 恢复按钮
                testBtn.disabled = false;
                testBtn.textContent = '🚀 测试OCR';
            }
        }

        // 页面加载时自动加载token
        window.addEventListener('load', function() {
            loadStoredToken();
        });
    </script>
</body>
</html>
