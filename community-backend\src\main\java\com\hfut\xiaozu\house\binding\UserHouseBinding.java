package com.hfut.xiaozu.house.binding;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户房屋绑定关系表（核心关系状态）
 * @TableName user_house_binding
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserHouseBinding {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 房屋ID
     */
    private Long houseId;

    /**
     * 关系类型: 1-业主 2-租客
     */
    private Integer relationType;

    /**
     * 绑定状态: 1-待审核 2-已生效 3-已拒绝 4-已解绑
     */
    private Integer status;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 最后更新人ID
     */
    private Long updatedBy;

    /**
     * 审核备注(拒绝原因等)
     */
    private String remark;
}
