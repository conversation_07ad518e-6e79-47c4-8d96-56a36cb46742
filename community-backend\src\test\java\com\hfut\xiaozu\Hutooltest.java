package com.hfut.xiaozu;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTPayload;
import cn.hutool.jwt.JWTUtil;
import cn.hutool.jwt.JWTValidator;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-06-23
 */
@SpringBootTest
@Slf4j
public class Hutooltest {
    @Test
    public void createTest() {
        byte[] key = "helloworld".getBytes();
        Map<String, Object> map = new HashMap<String, Object>() {
            private static final long serialVersionUID = 1L;

            {
                put("uid", Integer.parseInt("123"));
                // 10 秒后失效
                put(JWTPayload.EXPIRES_AT, System.currentTimeMillis() + 1000 * 30);

            }
        };
        String token = JWTUtil.createToken(map, key);
        Console.log(token);
    }

    @Test
    public void parseTest() {
        String token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1aWQiOjEyMywiZXhwIjoxNzUwNjgyMDE1MTE2fQ.netusxLlkxy5c-KjirRrJK2gA2qBeuq3tDGSz9i7Cjo";
        final JWT jwt = JWTUtil.parseToken(token);
        //header
        JSONObject headers = jwt.getHeaders();
        Console.log(headers);
        //payload
        JSONObject payloads = jwt.getPayloads();
        Console.log(payloads);
    }

    @Test
    public void verifyTest() {
        String token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1aWQiOjEyMywiZXhwIjoxNzUwNjgyMDE1MTE2fQ.netusxLlkxy5c-KjirRrJK2gA2qBeuq3tDGSz9i7Cjo";
        final boolean verify = JWTUtil.verify(token, "helloworld".getBytes());
        System.out.println(verify);
    }

    @Test
    public void validateTest() {
        JWT jwt = JWT.create()
                .setPayload("test", "test")
                .setKey("helloworld".getBytes(StandardCharsets.UTF_8))
                .setExpiresAt(DateUtil.parse("2025-06-23 20:37:00"));
        String token = jwt.sign();
        ThreadUtil.safeSleep(1000 * 10);
        JWTValidator.of(token).validateDate();
    }

}
