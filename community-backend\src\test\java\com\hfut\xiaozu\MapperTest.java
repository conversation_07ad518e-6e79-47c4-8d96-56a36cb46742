package com.hfut.xiaozu;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hfut.xiaozu.common.Result;
import com.hfut.xiaozu.house.information.CommunitiesVO;
import com.hfut.xiaozu.house.information.CommunityInfo;
import com.hfut.xiaozu.house.information.CommunityInfoMapper;
import com.hfut.xiaozu.incident.record.IncidentRecordEntity;
import com.hfut.xiaozu.incident.record.IncidentRecordMapper;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-25
 */
@SpringBootTest
@Slf4j
public class MapperTest {
    @Resource
    CommunityInfoMapper communityInfoMapper;

    @Resource
    private IncidentRecordMapper incidentRecordMapper;

    @Test
    public void listAllcommunities() {

        List<CommunityInfo> list = communityInfoMapper.list();

        if(CollectionUtil.isEmpty(list)){
            log.error("null");
        }

        List<CommunitiesVO> result=new ArrayList<>();

        for (CommunityInfo communityInfo : list) {
            log.info(communityInfo.toString());
        }

    }

    @Test
    public void test1(){
        IncidentRecordEntity a = incidentRecordMapper.getById(Long.valueOf(1));
        log.info(a.toString());
        JSONObject jsonObject = JSONUtil.parseObj(a.getLocationGeojson());
        Object latitude = jsonObject.get("latitude");
        Object longitude = jsonObject.get("longitude");
        log.info(latitude.toString());
        log.info(longitude.toString());

    }
}
