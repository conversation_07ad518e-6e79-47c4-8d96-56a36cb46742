<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="file"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .preview {
            max-width: 100%;
            max-height: 300px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 OCR API测试</h1>
        <p>测试身份证OCR识别接口</p>
        
        <div class="form-group">
            <label for="imageFile">选择身份证图片:</label>
            <input type="file" id="imageFile" accept="image/*" onchange="previewImage()">
            <img id="imagePreview" class="preview" style="display: none;">
        </div>
        
        <button onclick="testOCR()" id="ocrBtn">🔍 测试OCR识别</button>
        <button onclick="clearResult()">🗑️ 清空结果</button>
    </div>

    <div id="result"></div>

    <script>
        const API_BASE_URL = 'http://localhost:8080/api';

        // 预览选择的图片
        function previewImage() {
            const fileInput = document.getElementById('imageFile');
            const preview = document.getElementById('imagePreview');
            
            if (fileInput.files && fileInput.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(fileInput.files[0]);
            } else {
                preview.style.display = 'none';
            }
        }

        // 显示结果
        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="container"><div class="result ${type}">${message}</div></div>`;
        }

        // 清空结果
        function clearResult() {
            document.getElementById('result').innerHTML = '';
        }

        // 测试OCR接口
        async function testOCR() {
            const fileInput = document.getElementById('imageFile');
            const ocrBtn = document.getElementById('ocrBtn');
            
            if (!fileInput.files || !fileInput.files[0]) {
                showResult('❌ 请先选择一张身份证图片', 'error');
                return;
            }

            const file = fileInput.files[0];
            
            // 检查文件类型
            if (!file.type.startsWith('image/')) {
                showResult('❌ 请选择图片文件', 'error');
                return;
            }

            // 检查文件大小 (5MB限制)
            if (file.size > 5 * 1024 * 1024) {
                showResult('❌ 图片文件过大，请选择小于5MB的图片', 'error');
                return;
            }

            ocrBtn.disabled = true;
            ocrBtn.textContent = '识别中...';

            try {
                // 创建FormData
                const formData = new FormData();
                formData.append('image', file);

                console.log('🚀 发送OCR请求...');
                console.log('📁 文件信息:', {
                    name: file.name,
                    size: file.size,
                    type: file.type
                });

                const response = await fetch(`${API_BASE_URL}/auth/ocr`, {
                    method: 'POST',
                    body: formData
                    // 注意：不要设置Content-Type，让浏览器自动设置multipart/form-data
                });

                console.log('📡 响应状态:', response.status, response.statusText);

                const result = await response.text();
                console.log('📦 响应内容:', result);

                let parsedResult;
                try {
                    parsedResult = JSON.parse(result);
                } catch (e) {
                    parsedResult = { raw: result };
                }

                if (response.ok) {
                    showResult(`✅ OCR识别成功！\n\nHTTP状态: ${response.status}\n\n识别结果:\n${JSON.stringify(parsedResult, null, 2)}`, 'success');
                } else {
                    showResult(`❌ OCR识别失败\n\nHTTP状态: ${response.status} ${response.statusText}\n\n错误信息:\n${JSON.stringify(parsedResult, null, 2)}`, 'error');
                }

            } catch (error) {
                console.error('❌ 请求失败:', error);
                showResult(`❌ 网络错误\n\n错误信息: ${error.message}\n\n可能原因:\n1. 后端服务未启动\n2. 网络连接问题\n3. CORS跨域问题\n4. JWT拦截器问题`, 'error');
            } finally {
                ocrBtn.disabled = false;
                ocrBtn.textContent = '🔍 测试OCR识别';
            }
        }

        // 页面加载时的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔍 OCR测试页面已加载');
            showResult('📋 请选择一张身份证图片进行OCR识别测试\n\n注意事项:\n1. 支持jpg、png等常见图片格式\n2. 文件大小不超过5MB\n3. 建议使用清晰的身份证正面照片', 'info');
        });
    </script>
</body>
</html>
