<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            text-align: center;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏘️ 社区管理系统注册测试</h1>
        
        <form id="registerForm">
            <div class="form-group">
                <label for="userType">用户类型</label>
                <select id="userType" required>
                    <option value="">请选择用户类型</option>
                    <option value="1">🏠 社区居民</option>
                    <option value="2">👮 网格员</option>
                    <option value="3">🏢 社区管理员</option>
                </select>
            </div>

            <div class="form-group">
                <label for="userName">用户名</label>
                <input type="text" id="userName" required placeholder="请输入用户名（2-50个字符）">
            </div>

            <div class="form-group">
                <label for="phone">手机号码</label>
                <input type="tel" id="phone" required placeholder="请输入11位手机号码" maxlength="11">
            </div>

            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" required placeholder="请输入密码（6-200个字符）">
            </div>

            <div class="form-group">
                <label for="confirmPassword">确认密码</label>
                <input type="password" id="confirmPassword" required placeholder="请再次输入密码">
            </div>

            <button type="submit" id="submitBtn">立即注册</button>
            <button type="button" id="testBtn" style="background-color: #28a745; margin-top: 10px;">测试连接</button>
        </form>

        <div id="result" class="result"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080/api';

        // 测试连接按钮事件
        document.getElementById('testBtn').addEventListener('click', async function() {
            const resultDiv = document.getElementById('result');
            showResult('正在测试连接...', 'loading');

            try {
                // 测试基本连接
                const response = await fetch(`${API_BASE_URL}/auth/test`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    showResult('连接成功！后端服务正常运行', 'success');
                } else {
                    showResult(`连接失败：HTTP ${response.status}`, 'error');
                }
            } catch (error) {
                console.error('连接测试错误:', error);
                showResult(`连接失败：${error.message}`, 'error');

                // 提供详细的错误信息和解决建议
                setTimeout(() => {
                    showResult(`
                        连接失败详情：${error.message}

                        可能的解决方案：
                        1. 确保后端服务已启动（端口8080）
                        2. 检查防火墙设置
                        3. 确认数据库连接正常
                        4. 查看浏览器控制台的详细错误信息
                    `, 'error');
                }, 1000);
            }
        });
        
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const resultDiv = document.getElementById('result');
            
            // 获取表单数据
            const formData = {
                userName: document.getElementById('userName').value.trim(),
                phone: document.getElementById('phone').value.trim(),
                password: document.getElementById('password').value,
                userType: parseInt(document.getElementById('userType').value)
            };
            
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            // 前端验证
            if (!formData.userType) {
                showResult('请选择用户类型', 'error');
                return;
            }
            
            if (formData.userName.length < 2 || formData.userName.length > 50) {
                showResult('用户名长度需在2-50个字符之间', 'error');
                return;
            }
            
            if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
                showResult('请输入正确的手机号码', 'error');
                return;
            }
            
            if (formData.password.length < 6 || formData.password.length > 200) {
                showResult('密码长度需在6-200个字符之间', 'error');
                return;
            }
            
            if (formData.password !== confirmPassword) {
                showResult('两次输入的密码不一致', 'error');
                return;
            }
            
            // 显示加载状态
            submitBtn.disabled = true;
            submitBtn.textContent = '注册中...';
            showResult('正在注册，请稍候...', 'loading');
            
            try {
                console.log('发送注册请求:', formData);
                
                const response = await fetch(`${API_BASE_URL}/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                const result = await response.json();
                console.log('注册响应:', result);
                
                if (result.code === 200) {
                    showResult('注册成功！' + (result.msg || ''), 'success');
                    document.getElementById('registerForm').reset();
                } else {
                    showResult('注册失败：' + (result.msg || '未知错误'), 'error');
                }
                
            } catch (error) {
                console.error('注册错误:', error);
                showResult('注册失败：网络错误或服务器异常', 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '立即注册';
            }
        });
        
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
            
            if (type === 'success') {
                setTimeout(() => {
                    resultDiv.style.display = 'none';
                }, 5000);
            }
        }
    </script>
</body>
</html>
