<template>
  <div class="sidebar-layout">
    <!-- 顶部标题栏 -->
    <header class="header">
      <div class="header-content">
        <h1 class="system-title">芙蓉社区管理系统</h1>
        <div class="header-info">
          <span class="current-time">{{ currentTime }}</span>
          <button @click="goToProfile" class="profile-btn" title="个人信息">
            <span class="profile-icon">👤</span>
            <span class="profile-text">个人信息</span>
          </button>
          <div class="user-info">
            <span class="user-name">{{ user?.username || '用户' }}</span>
            <button @click="debugUserState" class="logout-btn" style="margin-right: 10px;">调试</button>
            <button @click="logout" class="logout-btn">退出</button>
          </div>
        </div>
      </div>
    </header>

    <div class="main-container">
      <!-- 左侧菜单栏 -->
      <aside class="sidebar">
        <nav class="sidebar-nav">
          <!-- 主页链接 -->
          <div class="nav-section">
            <router-link 
              :to="homeRoute" 
              class="nav-item home-item"
              :class="{ active: isHomeActive }"
            >
              <span class="nav-icon">🏠</span>
              <span class="nav-text">{{ homeTitle }}</span>
            </router-link>
          </div>

          <!-- 功能模块 -->
          <div class="nav-section">
            <h3 class="section-title">{{ sectionTitle }}</h3>
            <div class="nav-items">
              <template v-for="item in menuItems" :key="item.path || item.name">
                <!-- 普通菜单项 -->
                <router-link
                  v-if="!item.isDropdown"
                  :to="item.path"
                  class="nav-item"
                  :class="{ active: $route.path === item.path }"
                >
                  <span class="nav-icon">{{ item.icon }}</span>
                  <span class="nav-text">{{ item.name }}</span>
                </router-link>

                <!-- 下拉菜单项 -->
                <div v-else class="nav-dropdown">
                  <div
                    class="nav-item dropdown-toggle"
                    :class="{
                      active: isDropdownActive(item),
                      expanded: expandedDropdowns.includes(item.name)
                    }"
                    @click="toggleDropdown(item.name)"
                  >
                    <span class="nav-icon">{{ item.icon }}</span>
                    <span class="nav-text">{{ item.name }}</span>
                    <span class="dropdown-arrow">
                      {{ expandedDropdowns.includes(item.name) ? '▼' : '▶' }}
                    </span>
                  </div>
                  <div
                    class="dropdown-content"
                    :class="{ expanded: expandedDropdowns.includes(item.name) }"
                  >
                    <router-link
                      v-for="child in item.children"
                      :key="child.path"
                      :to="child.path"
                      class="nav-item nav-sub-item"
                      :class="{ active: $route.path === child.path }"
                    >
                      <span class="nav-icon">{{ child.icon }}</span>
                      <span class="nav-text">{{ child.name }}</span>
                    </router-link>
                  </div>
                </div>
              </template>
            </div>
          </div>

          <!-- 公共模块 -->
          <div class="nav-section">
            <h3 class="section-title">公共模块</h3>
            <div class="nav-items">
              <router-link
                v-for="item in commonItems"
                :key="item.path"
                :to="item.path"
                class="nav-item"
                :class="{ active: $route.path === item.path }"
              >
                <span class="nav-icon">{{ item.icon }}</span>
                <span class="nav-text">{{ item.name }}</span>
              </router-link>
            </div>
          </div>
        </nav>
      </aside>

      <!-- 主内容区域 -->
      <main class="content">
        <router-view />
      </main>
    </div>

    <!-- 版权栏 -->
    <footer class="copyright-footer">
      <div class="copyright-content">
        <div class="company-info">
          <GongdaLogo :size="32" class="company-logo" />
          <span class="company-name">软工tech</span>
          <span class="copyright-text">版权所有</span>
        </div>
        <div class="legal-notice">
          <span class="warning-text">侵犯必究</span>
          <span class="year">© {{ currentYear }}</span>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useUserStore } from '../stores/userStore';
import GongdaLogo from '../components/GongdaLogo.vue';

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();

const currentTime = ref('');
const expandedDropdowns = ref([]);

// 计算属性
const user = computed(() => userStore.user);
const isProperty = computed(() => userStore.isProperty);
const isResident = computed(() => userStore.isResident);
const currentYear = computed(() => new Date().getFullYear());

// 根据用户类型确定主页路由和标题
const homeRoute = computed(() => {
  return isProperty.value ? '/property-home' : '/resident-home';
});

const homeTitle = computed(() => {
  return isProperty.value ? '首页 (数据总览)' : '首页 (居民服务)';
});

const sectionTitle = computed(() => {
  return isProperty.value ? '社区治理端' : '居民端';
});

const isHomeActive = computed(() => {
  return route.path === homeRoute.value;
});

// 物业管理菜单项
const propertyMenuItems = [
  { path: '/property/data-overview', name: '数据总览', icon: '📊' },
  { path: '/property/full-archive', name: '全息档案', icon: '📁' },
  { path: '/property/event-management', name: '事件管理', icon: '📋' },
  { path: '/property/patrol-management', name: '巡查管理', icon: '🚶' },
  { path: '/property/gis-grid', name: 'GIS网格管理', icon: '🗺️' },
  {
    name: '信息采集',
    icon: '📝',
    isDropdown: true,
    children: [
      { path: '/property/info-collection', name: '实名信息采集', icon: '🆔' },
      { path: '/property/house-info-collection', name: '房屋信息采集', icon: '🏠' },
      { path: '/property/vehicle-info-collection', name: '车辆信息采集', icon: '🚗' }
    ]
  }
];

// 居民端菜单项
const residentMenuItems = [
  { path: '/resident/real-name-auth', name: '实名认证', icon: '🆔' },
  { path: '/resident/house-management', name: '房屋管理', icon: '🏠' },
  { path: '/resident/vehicle-management', name: '车辆管理', icon: '🚗' },
  { path: '/resident/family-management', name: '家人管理', icon: '👨‍👩‍👧‍👦' },
  { path: '/resident/issue-report', name: '问题上报', icon: '📢' },
  { path: '/resident/budget-vote', name: '预算支出投票', icon: '🗳️' }
];

// 公共模块菜单项
const commonItems = [
  { path: '/common/user-permission', name: '用户与权限服务', icon: '👥' },
  { path: '/common/file-storage', name: '文件存储服务', icon: '💾' },
  { path: '/common/message-notification', name: '消息通知服务', icon: '📢' },
  { path: '/common/log-monitor', name: '日志与监控', icon: '📈' }
];

// 根据用户类型选择菜单项
const menuItems = computed(() => {
  console.log('🔍 menuItems computed - 用户状态详情:', {
    userType: userStore.userType,
    isResident: userStore.isResident,
    isProperty: userStore.isProperty,
    'isProperty.value': isProperty.value,
    'isResident.value': isResident.value,
    'user': userStore.user,
    'localStorage.userType': localStorage.getItem('userType'),
    'localStorage.userData': localStorage.getItem('userData')
  });

  const selectedMenu = isProperty.value ? propertyMenuItems : residentMenuItems;
  console.log('🔍 选择的菜单类型:', isProperty.value ? 'property (治理端)' : 'resident (居民端)');
  console.log('🔍 菜单项:', selectedMenu);

  return selectedMenu;
});

// 更新时间
const updateTime = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  
  currentTime.value = `${year}年${month}月${day}日  ${hours}:${minutes}:${seconds}`;
};

// 跳转到个人信息页面
const goToProfile = () => {
  // 根据用户类型跳转到对应的个人信息页面
  if (isResident.value) {
    router.push('/resident-home/profile');
  } else if (isProperty.value) {
    router.push('/property-home/profile');
  }
};

// 下拉菜单相关方法
const toggleDropdown = (dropdownName) => {
  const index = expandedDropdowns.value.indexOf(dropdownName);
  if (index > -1) {
    expandedDropdowns.value.splice(index, 1);
  } else {
    expandedDropdowns.value.push(dropdownName);
  }
};

const isDropdownActive = (item) => {
  if (!item.children) return false;
  return item.children.some(child => route.path === child.path);
};

// 调试用户状态
const debugUserState = () => {
  const debugInfo = {
    'userStore.userType': userStore.userType,
    'userStore.isResident': userStore.isResident,
    'userStore.isProperty': userStore.isProperty,
    'userStore.user': userStore.user,
    'localStorage.userType': localStorage.getItem('userType'),
    'localStorage.userData': localStorage.getItem('userData'),
    'localStorage.userToken': localStorage.getItem('userToken'),
    'isProperty.value': isProperty.value,
    'isResident.value': isResident.value,
    'menuItems': menuItems.value,
    'homeTitle': homeTitle.value,
    'sectionTitle': sectionTitle.value
  };

  console.log('🔍 调试用户状态:', debugInfo);
  alert('调试信息已输出到控制台，请查看Console标签页');
};

// 退出登录
const logout = () => {
  if (confirm('确定要退出登录吗？')) {
    userStore.logout();
    router.push('/login');
  }
};

let timeInterval;

onMounted(() => {
  updateTime();
  timeInterval = setInterval(updateTime, 1000);

  // 确保用户状态已初始化
  userStore.initializeUser();
  console.log('🔍 SidebarLayout mounted - 用户状态:', {
    userType: userStore.userType,
    isResident: userStore.isResident,
    isProperty: userStore.isProperty,
    isAuthenticated: userStore.isAuthenticated
  });

  // 延迟检查，确保响应式更新完成
  setTimeout(() => {
    console.log('🔍 SidebarLayout mounted (延迟检查) - 用户状态:', {
      userType: userStore.userType,
      isResident: userStore.isResident,
      isProperty: userStore.isProperty,
      isAuthenticated: userStore.isAuthenticated,
      'localStorage.userType': localStorage.getItem('userType'),
      'localStorage.userData': localStorage.getItem('userData')
    });
  }, 100);
});

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
  }
});
</script>

<style scoped>
.sidebar-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--light-gray);
}

.header {
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-dark));
  color: white;
  padding: 0;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);
  z-index: 1000;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 30px;
  max-width: 100%;
}

.system-title {
  font-size: 24px;
  font-weight: bold;
  margin: 0;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.current-time {
  font-size: 16px;
  font-weight: 500;
}

.profile-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.profile-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.profile-icon {
  font-size: 16px;
}

.profile-text {
  font-weight: 500;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.user-name {
  font-size: 16px;
  font-weight: 500;
}

.logout-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.main-container {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.sidebar {
  width: 250px;
  background: linear-gradient(180deg, var(--white) 0%, var(--light-gray) 80%, var(--primary-blue-lighter) 100%);
  color: var(--text-dark);
  overflow-y: auto;
  box-shadow: 2px 0 8px rgba(33, 150, 243, 0.08);
  border-right: 1px solid var(--medium-gray);
}

.sidebar-nav {
  padding: 20px 0;
}

.nav-section {
  margin-bottom: 30px;
}

.section-title {
  color: #bdc3c7;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin: 0 20px 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid #34495e;
}

.nav-items {
  display: flex;
  flex-direction: column;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: var(--text-dark);
  text-decoration: none;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
  margin: 2px 8px;
  border-radius: 8px;
}

.nav-item:hover {
  background-color: var(--primary-blue-lighter);
  border-left-color: var(--primary-blue);
  color: var(--primary-blue-dark);
}

.nav-item.active {
  background-color: var(--primary-blue-lighter);
  border-left-color: var(--primary-blue);
  color: var(--primary-blue-dark);
}

.nav-item.home-item {
  background-color: var(--primary-blue);
  margin: 0 10px 20px;
  border-radius: 8px;
  border-left: none;
  font-weight: 600;
  color: white;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
}

.nav-item.home-item:hover,
.nav-item.home-item.active {
  background-color: var(--primary-blue-dark);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4);
}

.nav-icon {
  font-size: 18px;
  margin-right: 12px;
  width: 20px;
  text-align: center;
}

.nav-text {
  font-size: 14px;
  font-weight: 500;
}

/* 下拉菜单样式 */
.nav-dropdown {
  margin-bottom: 4px;
}

.dropdown-toggle {
  position: relative;
  cursor: pointer;
  justify-content: space-between;
}

.dropdown-toggle.expanded {
  background: var(--light-gray);
  color: var(--primary-blue);
}

.dropdown-arrow {
  font-size: 12px;
  transition: transform 0.3s ease;
  margin-left: auto;
}

.dropdown-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  background: var(--light-gray);
  border-radius: 0 0 8px 8px;
  margin-top: -4px;
}

.dropdown-content.expanded {
  max-height: 300px;
}

.nav-sub-item {
  padding: 12px 20px 12px 50px;
  margin: 0;
  border-radius: 0;
  background: transparent;
  border-left: 3px solid transparent;
}

.nav-sub-item:hover {
  background: rgba(33, 150, 243, 0.1);
  border-left-color: var(--primary-blue);
}

.nav-sub-item.active {
  background: var(--primary-blue);
  color: white;
  border-left-color: var(--primary-blue-dark);
  transform: none;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.content {
  flex: 1;
  padding: 30px;
  background-color: #ffffff;
  overflow-y: auto;
}

/* 版权栏样式 */
.copyright-footer {
  background: linear-gradient(135deg, var(--primary-blue-dark), var(--primary-blue));
  color: white;
  padding: 15px 0;
  border-top: 1px solid var(--primary-blue-light);
  box-shadow: 0 -2px 8px rgba(33, 150, 243, 0.1);
}

.copyright-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 30px;
}

.company-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.company-logo {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.company-name {
  font-size: 18px;
  font-weight: 700;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.copyright-text {
  font-size: 14px;
  font-weight: 500;
  opacity: 0.9;
}

.legal-notice {
  display: flex;
  align-items: center;
  gap: 20px;
}

.warning-text {
  font-size: 14px;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.year {
  font-size: 14px;
  font-weight: 500;
  opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    width: 200px;
  }

  .header-content {
    padding: 10px 20px;
  }

  .header-info {
    gap: 15px;
  }

  .profile-btn {
    padding: 8px 12px;
    font-size: 12px;
  }

  .profile-text {
    display: none;
  }

  .profile-icon {
    font-size: 18px;
  }

  .system-title {
    font-size: 20px;
  }

  .content {
    padding: 20px;
  }

  .copyright-content {
    flex-direction: column;
    gap: 10px;
    text-align: center;
    padding: 0 20px;
  }

  .company-info,
  .legal-notice {
    gap: 10px;
  }

  .company-name,
  .warning-text {
    font-size: 16px;
  }

  .copyright-text,
  .year {
    font-size: 12px;
  }
}
</style>
