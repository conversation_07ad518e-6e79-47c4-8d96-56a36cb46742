package com.hfut.xiaozu.security;

import cn.hutool.core.collection.CollectionUtil;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-24
 */
@ConfigurationProperties("jwt")
@Configuration
@Setter
@Getter
@ToString
@Slf4j
public class JwtConfigProperties {

    // JWT 密钥
    private String secret;

    // JWT 过期时间（毫秒）,默认半小时
    private long expiration = 1800000;

    // HTTP 请求头变量名
    private String header = "Authorization";

    // 排除路径列表
    private List<String> excludedPaths;
    private static final List<String> DEFAULT_EXCLUDED_PATHS = Arrays.asList(
            "/api/auth/login",
            "/api/auth/register"
    );

    // 在属性绑定后执行验证
    @PostConstruct
    public void validate() {
        // 确保密钥不为空
        if (secret == null || secret.isBlank()) {
            throw new IllegalArgumentException("JWT secret must be configured in application.yml");
        }

        // 确保排除路径包含必要项
        if (CollectionUtil.isEmpty(excludedPaths)) {
            excludedPaths = DEFAULT_EXCLUDED_PATHS;
        } else {
            // 确保登录路径总是被排除
            if (!excludedPaths.contains("/api/auth/login")) {
                throw new IllegalArgumentException(
                        "JWT excludedPaths must include /api/auth/login to prevent system lockout"
                );
            }
        }
    }
}
