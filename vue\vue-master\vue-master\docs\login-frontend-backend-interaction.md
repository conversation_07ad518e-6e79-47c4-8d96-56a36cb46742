# 登录前后端交互完整示例

## 概述

本文档展示了一个完整的登录认证系统，包括前端登录组件、后端API、token管理、自动刷新和在其他请求中自动携带token的完整流程。

## 1. 前端登录流程

### 1.1 用户登录

```javascript
// LoginForm.vue - 用户登录处理
async function handleLogin() {
  try {
    isLoading.value = true;
    
    // 1. 表单验证
    if (!validateForm()) {
      return;
    }
    
    console.log('🔐 开始登录流程...');
    
    // 2. 调用认证store的登录方法
    await authStore.login({
      username: loginForm.username,
      password: loginForm.password,
      rememberMe: loginForm.rememberMe
    });
    
    console.log('✅ 登录成功!');
    
    // 3. 跳转到目标页面
    const redirectPath = router.currentRoute.value.query.redirect || '/dashboard';
    router.push(redirectPath);
    
  } catch (error) {
    console.error('❌ 登录失败:', error);
    
    // 4. 处理不同类型的错误
    if (error.code === 'INVALID_CREDENTIALS') {
      loginError.value = '用户名或密码错误';
    } else if (error.code === 'ACCOUNT_LOCKED') {
      loginError.value = '账户已被锁定，请联系管理员';
    } else {
      loginError.value = error.message || '登录失败，请稍后重试';
    }
    
    // 5. 清空密码
    loginForm.password = '';
    
  } finally {
    isLoading.value = false;
  }
}
```

### 1.2 认证状态管理

```javascript
// auth.js - Pinia认证store
export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref(null);
  const token = ref('');
  const refreshToken = ref('');
  const isLoading = ref(false);
  
  // 计算属性
  const isAuthenticated = computed(() => {
    return !!token.value && !!user.value && !isTokenExpired.value;
  });
  
  // 登录方法
  async function login(credentials) {
    try {
      isLoading.value = true;
      
      console.log('🔐 发起登录请求...');
      
      // 调用登录API
      const response = await authApi.login({
        username: credentials.username,
        password: credentials.password
      });
      
      if (!response.success) {
        throw new Error(response.message || '登录失败');
      }
      
      const { data } = response;
      
      // 保存认证信息
      await setAuthData({
        user: data.user,
        token: data.token,
        refreshToken: data.refreshToken,
        expiresAt: data.expiresAt,
        rememberMe: credentials.rememberMe
      });
      
      // 设置token自动刷新
      scheduleTokenRefresh();
      
      return data;
      
    } catch (error) {
      await clearAuthData();
      throw error;
    } finally {
      isLoading.value = false;
    }
  }
  
  // 保存认证数据
  async function setAuthData(authData) {
    user.value = authData.user;
    token.value = authData.token;
    refreshToken.value = authData.refreshToken;
    tokenExpiry.value = authData.expiresAt;
    rememberMe.value = authData.rememberMe;
    
    // 保存到本地存储
    saveToStorage();
  }
  
  // 保存到本地存储
  function saveToStorage() {
    const authData = {
      user: user.value,
      token: token.value,
      refreshToken: refreshToken.value,
      tokenExpiry: tokenExpiry.value,
      rememberMe: rememberMe.value
    };
    
    const dataString = JSON.stringify(authData);
    
    if (rememberMe.value) {
      // 记住我：保存到localStorage
      localStorage.setItem('auth_data', dataString);
      sessionStorage.removeItem('auth_data');
    } else {
      // 不记住我：保存到sessionStorage
      sessionStorage.setItem('auth_data', dataString);
      localStorage.removeItem('auth_data');
    }
  }
  
  return {
    user, token, isAuthenticated,
    login, logout, refreshAuthToken
  };
});
```

## 2. 后端登录API

### 2.1 登录接口

```javascript
// 后端登录API
app.post('/api/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    // 1. 查找用户
    const user = await User.findOne({ username }).select('+password');
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误',
        error: { code: 'INVALID_CREDENTIALS' }
      });
    }
    
    // 2. 验证密码
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误',
        error: { code: 'INVALID_CREDENTIALS' }
      });
    }
    
    // 3. 生成token
    const tokenPayload = {
      userId: user._id,
      username: user.username,
      role: user.role,
      permissions: user.permissions
    };
    
    const accessToken = generateToken(tokenPayload, JWT_SECRET, '15m');
    const refreshToken = await generateRefreshToken(user._id);
    
    // 4. 返回成功响应
    res.json({
      success: true,
      message: '登录成功',
      data: {
        user: {
          id: user._id,
          username: user.username,
          email: user.email,
          role: user.role,
          permissions: user.permissions
        },
        token: accessToken,
        refreshToken: refreshToken,
        expiresAt: new Date(Date.now() + 15 * 60 * 1000).toISOString(),
        tokenType: 'Bearer'
      }
    });
    
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: { code: 'INTERNAL_ERROR' }
    });
  }
});
```

### 2.2 Token刷新接口

```javascript
// Token刷新API
app.post('/api/auth/refresh', async (req, res) => {
  try {
    const { refreshToken } = req.body;
    
    // 1. 验证刷新token
    const decoded = verifyToken(refreshToken, JWT_REFRESH_SECRET);
    
    // 2. 检查token是否在数据库中存在
    const storedToken = await RefreshToken.findOne({
      token: refreshToken,
      userId: decoded.userId,
      isRevoked: false
    });
    
    if (!storedToken) {
      return res.status(401).json({
        success: false,
        message: '刷新令牌已失效',
        error: { code: 'REFRESH_TOKEN_REVOKED' }
      });
    }
    
    // 3. 获取用户信息
    const user = await User.findById(decoded.userId);
    
    // 4. 生成新的访问token
    const tokenPayload = {
      userId: user._id,
      username: user.username,
      role: user.role,
      permissions: user.permissions
    };
    
    const newAccessToken = generateToken(tokenPayload, JWT_SECRET, '15m');
    
    // 5. 返回新token
    res.json({
      success: true,
      message: 'Token刷新成功',
      data: {
        token: newAccessToken,
        expiresAt: new Date(Date.now() + 15 * 60 * 1000).toISOString(),
        tokenType: 'Bearer'
      }
    });
    
  } catch (error) {
    res.status(401).json({
      success: false,
      message: '无效的刷新令牌',
      error: { code: 'INVALID_REFRESH_TOKEN' }
    });
  }
});
```

## 3. HTTP请求拦截器

### 3.1 自动添加Token

```javascript
// httpInterceptor.js - 自动在请求中添加token
class HttpInterceptor {
  async prepareRequestConfig(options, authStore) {
    const config = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      },
      ...options
    };

    // 添加认证token
    if (authStore.isAuthenticated && authStore.token) {
      // 检查token是否即将过期
      if (authStore.isTokenExpired) {
        console.log('🔄 Token已过期，尝试刷新...');
        await this.handleTokenRefresh(authStore);
      }
      
      config.headers['Authorization'] = `Bearer ${authStore.token}`;
      console.log(`🔑 已添加Authorization头`);
    }

    return config;
  }
  
  async handleAuthError(error, url, config, authStore) {
    // 如果是401错误，尝试刷新token
    if (authStore.refreshToken && !this.isRefreshing) {
      try {
        console.log('🔄 尝试刷新token...');
        await this.handleTokenRefresh(authStore);
        
        // token刷新成功，可以重试原请求
        error.canRetry = true;
        
      } catch (refreshError) {
        console.error('❌ Token刷新失败:', refreshError);
        await authStore.clearAuthData();
        this.redirectToLogin();
      }
    }
  }
}

// 使用示例
const http = new HttpInterceptor();

// 所有API请求都会自动添加token
const response = await http.post('/api/grids', gridData);
```

### 3.2 网格API使用拦截器

```javascript
// gridApi.js - 使用HTTP拦截器
import http from '../utils/httpInterceptor.js';

export const createGrid = async (gridData) => {
  try {
    // 使用HTTP拦截器发送请求（自动添加token）
    const response = await http.post('/grids', gridData);
    
    return {
      success: true,
      data: response.data,
      message: response.message || '网格创建成功'
    };
    
  } catch (error) {
    console.error('❌ API: 网格创建失败', error);
    throw error;
  }
};

export const getGrids = async (params = {}) => {
  try {
    const queryString = new URLSearchParams(params).toString();
    const url = `/grids${queryString ? `?${queryString}` : ''}`;
    
    // 自动添加token
    const response = await http.get(url);
    
    return {
      success: true,
      data: response.data,
      total: response.total
    };
    
  } catch (error) {
    console.error('❌ API: 获取网格列表失败', error);
    throw error;
  }
};
```

## 4. 路由守卫

### 4.1 认证路由守卫

```javascript
// main.js - 设置路由守卫
import { useAuthStore } from './stores/auth.js'

// 恢复认证状态
const authStore = useAuthStore()
authStore.restoreAuthState()

// 设置路由守卫
router.beforeEach((to, from, next) => {
  console.log(`🧭 路由导航: ${from.path} → ${to.path}`)
  
  // 检查路由是否需要认证
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  
  if (requiresAuth) {
    if (authStore.isAuthenticated) {
      console.log('✅ 用户已认证，允许访问')
      next()
    } else {
      console.log('❌ 用户未认证，跳转到登录页')
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
    }
  } else {
    // 如果是登录页面且用户已登录，跳转到首页
    if (to.path === '/login' && authStore.isAuthenticated) {
      console.log('✅ 用户已登录，跳转到首页')
      next('/dashboard')
    } else {
      next()
    }
  }
})
```

### 4.2 路由配置

```javascript
// router/index.js - 路由配置
const routes = [
  {
    path: '/login',
    name: 'Login',
    component: LoginForm,
    meta: {
      title: '用户登录',
      requiresAuth: false
    }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: {
      title: '仪表板',
      requiresAuth: true,
      permissions: ['dashboard:view']
    }
  },
  {
    path: '/property/gis-grid',
    name: 'GridManagement',
    component: GridManagement,
    meta: {
      title: '网格管理',
      requiresAuth: true,
      permissions: ['grid:view']
    }
  }
]
```

## 5. 完整交互流程

### 5.1 登录流程时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant LoginForm as 登录组件
    participant AuthStore as 认证Store
    participant AuthAPI as 认证API
    participant Storage as 本地存储
    
    User->>LoginForm: 输入用户名密码
    LoginForm->>LoginForm: 表单验证
    LoginForm->>AuthStore: 调用login()
    AuthStore->>AuthAPI: POST /api/auth/login
    AuthAPI->>AuthAPI: 验证用户名密码
    AuthAPI->>AuthStore: 返回token和用户信息
    AuthStore->>Storage: 保存认证数据
    AuthStore->>AuthStore: 设置自动刷新
    AuthStore->>LoginForm: 登录成功
    LoginForm->>User: 跳转到目标页面
```

### 5.2 API请求流程

```mermaid
sequenceDiagram
    participant Component as 组件
    participant HttpInterceptor as HTTP拦截器
    participant AuthStore as 认证Store
    participant API as 后端API
    
    Component->>HttpInterceptor: 发起API请求
    HttpInterceptor->>AuthStore: 获取token
    HttpInterceptor->>HttpInterceptor: 检查token是否过期
    
    alt Token即将过期
        HttpInterceptor->>AuthStore: 刷新token
        AuthStore->>API: POST /api/auth/refresh
        API->>AuthStore: 返回新token
        AuthStore->>HttpInterceptor: 返回新token
    end
    
    HttpInterceptor->>API: 发送请求(带token)
    API->>API: 验证token
    
    alt Token有效
        API->>HttpInterceptor: 返回数据
        HttpInterceptor->>Component: 返回结果
    else Token无效
        API->>HttpInterceptor: 401错误
        HttpInterceptor->>AuthStore: 清除认证数据
        HttpInterceptor->>Component: 跳转登录页
    end
```

## 6. 测试示例

### 6.1 登录测试

```javascript
// 测试登录功能
async function testLogin() {
  const authStore = useAuthStore();
  
  try {
    // 测试正确的用户名密码
    await authStore.login({
      username: 'admin',
      password: '123456',
      rememberMe: true
    });
    
    console.log('✅ 登录测试通过');
    console.log('👤 当前用户:', authStore.user);
    console.log('🔑 Token:', authStore.token.substring(0, 20) + '...');
    
  } catch (error) {
    console.error('❌ 登录测试失败:', error);
  }
}
```

### 6.2 API请求测试

```javascript
// 测试带token的API请求
async function testApiWithToken() {
  try {
    // 这个请求会自动添加token
    const response = await http.get('/api/grids');
    console.log('✅ API请求成功:', response);
    
  } catch (error) {
    console.error('❌ API请求失败:', error);
  }
}
```

## 7. 关键特性

1. **自动Token管理**: 自动在请求中添加token，无需手动处理
2. **Token自动刷新**: 在token即将过期时自动刷新
3. **错误处理**: 完善的401错误处理和重试机制
4. **持久化存储**: 支持"记住我"功能，数据持久化
5. **路由守卫**: 自动检查认证状态，保护需要登录的页面
6. **状态管理**: 使用Pinia统一管理认证状态
7. **安全性**: JWT token + 刷新token双重保护

这套完整的认证系统确保了前后端交互的安全性和用户体验的流畅性。
