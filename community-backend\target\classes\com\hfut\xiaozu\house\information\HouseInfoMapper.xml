<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hfut.xiaozu.house.information.HouseInfoMapper">

    <resultMap id="BaseResultMap" type="com.hfut.xiaozu.house.information.HouseInfo">
            <id property="id" column="id" />
            <result property="unitId" column="unit_id" />
            <result property="houseNumber" column="house_number" />
            <result property="areaSize" column="area_size" />
            <result property="isOccupied" column="is_occupied" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,unit_id,house_number,area_size,is_occupied,create_time,
        update_time
    </sql>

    <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from house_info
        where  id = #{id}
    </select>

    <select id="listByUnitId" resultType="com.hfut.xiaozu.house.information.HouseInfo">
        select
        <include refid="Base_Column_List" />
        from house_info
        where  unit_id = #{unitId}
    </select>

    <select id="getHouseStatusById" resultType="java.lang.Integer">
        select is_occupied
        from community.house_info
        where  id = #{houseId}
    </select>


    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.hfut.xiaozu.house.information.HouseInfo" useGeneratedKeys="true">
        insert into community.house_info
        ( unit_id,house_number,area_size,is_occupied)
        values (#{unitId},#{houseNumber},#{areaSize},#{isOccupied})
    </insert>


    <update id="update" parameterType="com.hfut.xiaozu.house.information.HouseInfo">
        update house_info
        <set>
                <if test="unitId != null">
                    unit_id = #{unitId},
                </if>
                <if test="houseNumber != null">
                    house_number = #{houseNumber},
                </if>
                <if test="areaSize != null">
                    area_size = #{areaSize},
                </if>
                <if test="isOccupied != null">
                    is_occupied = #{isOccupied},
                </if>
        </set>
        where   id = #{id}
    </update>

    <update id="updateStatusById">
        update community.house_info SET  is_occupied = #{status}
        where   id = #{houseId}
    </update>

    <select id="getCommunityIdByHouseId" parameterType="java.lang.Long" resultType="java.lang.Long">
        select b.community_id
        from house_info h
        join unit_info u on h.unit_id = u.id
        join building_info b on u.building_id = b.id
        where h.id = #{houseId}
    </select>

</mapper>
