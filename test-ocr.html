<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR API 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="file"], input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f8f9fa;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
    </style>
</head>
<body>
    <h1>OCR API 测试</h1>
    
    <form id="ocrForm">
        <div class="form-group">
            <label for="sessionId">Session ID:</label>
            <input type="text" id="sessionId" name="sessionId" value="test-session-123" required>
        </div>
        
        <div class="form-group">
            <label for="imageFile">选择身份证图片:</label>
            <input type="file" id="imageFile" name="imageFile" accept="image/*" required>
        </div>
        
        <button type="submit">上传并识别</button>
    </form>
    
    <div id="result" class="result" style="display: none;">
        <h3>识别结果:</h3>
        <div id="resultContent"></div>
    </div>

    <script>
        document.getElementById('ocrForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const sessionId = document.getElementById('sessionId').value;
            const imageFile = document.getElementById('imageFile').files[0];
            
            if (!imageFile) {
                alert('请选择图片文件');
                return;
            }
            
            const formData = new FormData();
            formData.append('session_id', sessionId);
            formData.append('image', imageFile);
            
            const resultDiv = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            
            try {
                resultContent.innerHTML = '<p>正在处理中...</p>';
                resultDiv.style.display = 'block';
                
                const response = await fetch('http://localhost:8080/api/auth/ocr', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    resultContent.innerHTML = `
                        <div class="success">
                            <h4>识别成功!</h4>
                            <p><strong>姓名:</strong> ${result.data.name || '未识别'}</p>
                            <p><strong>身份证号:</strong> ${result.data.idNumber || '未识别'}</p>
                        </div>
                    `;
                } else {
                    resultContent.innerHTML = `
                        <div class="error">
                            <h4>识别失败</h4>
                            <p>错误信息: ${result.msg || '未知错误'}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultContent.innerHTML = `
                    <div class="error">
                        <h4>请求失败</h4>
                        <p>错误信息: ${error.message}</p>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
