<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试百度OCR Token有效性</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .current-token {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            word-break: break-all;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 测试百度OCR Token有效性</h1>
        
        <div class="current-token">
            <h3>📋 当前Access Token</h3>
            <p><strong>Token:</strong> 24.949b65e4adfab79b2ceedc21a414701.2592000.1753412317.282335-119337225</p>
            <p><strong>理论过期时间:</strong> 2025年7月25日</p>
            <p><strong>当前时间:</strong> <span id="currentTime"></span></p>
        </div>

        <button onclick="testToken()" id="testBtn">🧪 测试Token有效性</button>

        <div id="result"></div>
    </div>

    <script>
        // 显示当前时间
        document.getElementById('currentTime').textContent = new Date().toLocaleString('zh-CN');

        async function testToken() {
            const token = '24.949b65e4adfab79b2ceedc21a414701.2592000.1753412317.282335-119337225';
            const resultDiv = document.getElementById('result');
            const btn = document.getElementById('testBtn');
            
            btn.disabled = true;
            btn.textContent = '🔄 测试中...';
            resultDiv.innerHTML = '<div class="result info">🔄 正在测试Token有效性...</div>';
            
            try {
                // 使用一个测试图片URL来验证token
                const testImageUrl = 'https://community233.oss-cn-nanjing.aliyuncs.com/images/20250624225425877.jpg';
                const apiUrl = `https://aip.baidubce.com/rest/2.0/ocr/v1/idcard?access_token=${token}`;
                
                console.log('🌐 测试URL:', apiUrl);
                console.log('📸 测试图片:', testImageUrl);
                
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: `id_card_side=front&url=${encodeURIComponent(testImageUrl)}`
                });
                
                const data = await response.json();
                console.log('📦 响应数据:', data);
                
                if (data.error_code) {
                    // 有错误代码
                    if (data.error_code === 110 || data.error_msg?.includes('Access token invalid')) {
                        resultDiv.innerHTML = `
<div class="result error">
❌ Token确实已失效！

错误代码: ${data.error_code}
错误信息: ${data.error_msg}

🔍 分析:
虽然理论过期时间是2025年7月25日，但实际上token已经失效。
可能的原因：
1. 百度可能提前使token失效
2. 配置文件中的token可能不是最新的
3. API密钥可能被重置过

💡 解决方案:
需要重新获取新的Access Token
</div>`;
                    } else {
                        resultDiv.innerHTML = `
<div class="result error">
❌ 其他API错误

错误代码: ${data.error_code}
错误信息: ${data.error_msg}

这可能是图片问题或其他API限制，但token本身可能是有效的。
</div>`;
                    }
                } else if (data.words_result) {
                    // 成功识别
                    resultDiv.innerHTML = `
<div class="result success">
✅ Token有效！OCR识别成功

识别结果:
${JSON.stringify(data, null, 2)}

🎉 Token工作正常，问题可能在其他地方。
</div>`;
                } else {
                    // 未知响应
                    resultDiv.innerHTML = `
<div class="result info">
🤔 未知响应格式

响应内容:
${JSON.stringify(data, null, 2)}

需要进一步分析响应内容。
</div>`;
                }
                
            } catch (error) {
                console.error('❌ 请求失败:', error);
                resultDiv.innerHTML = `
<div class="result error">
❌ 网络请求失败: ${error.message}

💡 可能的原因:
1. CORS跨域限制（浏览器安全策略）
2. 网络连接问题
3. 百度API服务不可用

🔧 建议:
由于CORS限制，建议使用后端测试代码来验证token有效性。
</div>`;
            } finally {
                btn.disabled = false;
                btn.textContent = '🧪 测试Token有效性';
            }
        }
    </script>
</body>
</html>
