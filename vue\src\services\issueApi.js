/**
 * 问题上报相关API服务
 */

import http from '../utils/httpInterceptor.js'

/**
 * 提交问题上报
 * @param {Object} issueData - 问题数据
 * @returns {Promise<Object>} API响应
 */
export const submitIssueReport = async (issueData) => {
  console.log('🔧 API: 提交问题上报', issueData);

  try {
    // 验证必要字段
    if (!issueData.title || !issueData.description) {
      throw new Error('标题和描述不能为空');
    }

    if (!issueData.categoryType || !issueData.priority) {
      throw new Error('问题类型和优先级不能为空');
    }

    if (!issueData.locationGeojson || !issueData.locationGeojson.latitude || !issueData.locationGeojson.longitude) {
      throw new Error('请在地图上选择问题位置');
    }

    // 使用HTTP拦截器发送请求（自动添加token）
    const response = await http.post('/incident', issueData);

    console.log('✅ API: 问题上报成功', response);

    return {
      success: true,
      data: response.data,
      message: response.msg || '问题上报成功'
    };

  } catch (error) {
    console.error('❌ API: 问题上报失败', error);
    throw error;
  }
};

/**
 * 获取问题类型列表
 * @returns {Array} 问题类型列表
 */
export const getIssueCategories = () => {
  return [
    { value: '1', label: '环境卫生', icon: '🧹', description: '垃圾清理、环境污染等' },
    { value: '2', label: '设施损坏', icon: '🔧', description: '公共设施、道路损坏等' },
    { value: '3', label: '安全问题', icon: '⚠️', description: '安全隐患、治安问题等' },
    { value: '4', label: '其他', icon: '📝', description: '其他类型问题' }
  ];
};

/**
 * 获取优先级列表
 * @returns {Array} 优先级列表
 */
export const getPriorityLevels = () => {
  return [
    { value: '1', label: '紧急', icon: '🔴', color: '#e74c3c', description: '需要立即处理' },
    { value: '2', label: '高', icon: '🟠', color: '#f39c12', description: '需要尽快处理' },
    { value: '3', label: '中', icon: '🟡', color: '#f1c40f', description: '正常处理' },
    { value: '4', label: '低', icon: '🟢', color: '#27ae60', description: '可延后处理' }
  ];
};

/**
 * 获取我的问题上报列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} API响应
 */
export const getMyIssueReports = async (params = {}) => {
  console.log('🔧 API: 获取我的问题上报列表', params);

  try {
    const response = await http.get('/incident/my', { params });

    console.log('✅ API: 获取问题上报列表成功', response);

    return {
      success: true,
      data: response.data,
      message: response.msg || '获取成功'
    };

  } catch (error) {
    console.error('❌ API: 获取问题上报列表失败', error);
    throw error;
  }
};

/**
 * 获取问题详情
 * @param {number} issueId - 问题ID
 * @returns {Promise<Object>} API响应
 */
export const getIssueDetail = async (issueId) => {
  console.log('🔧 API: 获取问题详情', issueId);

  try {
    const response = await http.get(`/incident/${issueId}`);

    console.log('✅ API: 获取问题详情成功', response);

    return {
      success: true,
      data: response.data,
      message: response.msg || '获取成功'
    };

  } catch (error) {
    console.error('❌ API: 获取问题详情失败', error);
    throw error;
  }
};

/**
 * 取消问题上报
 * @param {number} issueId - 问题ID
 * @returns {Promise<Object>} API响应
 */
export const cancelIssueReport = async (issueId) => {
  console.log('🔧 API: 取消问题上报', issueId);

  try {
    const response = await http.put(`/incident/${issueId}/cancel`);

    console.log('✅ API: 取消问题上报成功', response);

    return {
      success: true,
      data: response.data,
      message: response.msg || '取消成功'
    };

  } catch (error) {
    console.error('❌ API: 取消问题上报失败', error);
    throw error;
  }
};

/**
 * 格式化问题类型显示
 * @param {number|string} categoryType - 问题类型值
 * @returns {Object} 格式化后的类型信息
 */
export const formatCategoryType = (categoryType) => {
  const categories = getIssueCategories();
  const category = categories.find(c => c.value === String(categoryType));
  return category || { value: categoryType, label: '未知类型', icon: '❓' };
};

/**
 * 格式化优先级显示
 * @param {number|string} priority - 优先级值
 * @returns {Object} 格式化后的优先级信息
 */
export const formatPriority = (priority) => {
  const priorities = getPriorityLevels();
  const priorityInfo = priorities.find(p => p.value === String(priority));
  return priorityInfo || { value: priority, label: '未知优先级', icon: '❓', color: '#95a5a6' };
};

/**
 * 验证问题上报数据
 * @param {Object} issueData - 问题数据
 * @returns {Object} 验证结果
 */
export const validateIssueData = (issueData) => {
  const errors = [];

  // 验证标题
  if (!issueData.title || !issueData.title.trim()) {
    errors.push('问题标题不能为空');
  } else if (issueData.title.trim().length > 100) {
    errors.push('问题标题不能超过100个字符');
  }

  // 验证描述
  if (!issueData.description || !issueData.description.trim()) {
    errors.push('问题描述不能为空');
  } else if (issueData.description.trim().length > 500) {
    errors.push('问题描述不能超过500个字符');
  }

  // 验证问题类型
  if (!issueData.categoryType) {
    errors.push('请选择问题类型');
  } else {
    const validCategories = ['1', '2', '3', '4'];
    if (!validCategories.includes(String(issueData.categoryType))) {
      errors.push('问题类型无效');
    }
  }

  // 验证优先级
  if (!issueData.priority) {
    errors.push('请选择优先级');
  } else {
    const validPriorities = ['1', '2', '3', '4'];
    if (!validPriorities.includes(String(issueData.priority))) {
      errors.push('优先级无效');
    }
  }

  // 验证位置信息
  if (!issueData.locationGeojson) {
    errors.push('请在地图上选择问题位置');
  } else {
    if (typeof issueData.locationGeojson.latitude !== 'number' || 
        typeof issueData.locationGeojson.longitude !== 'number') {
      errors.push('位置坐标格式无效');
    }
  }

  // 验证小区ID
  if (!issueData.communityId) {
    errors.push('小区ID不能为空');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * 获取问题状态显示信息
 * @param {number|string} status - 状态值
 * @returns {Object} 状态信息
 */
export const getStatusInfo = (status) => {
  const statusMap = {
    '0': { label: '待处理', color: '#f39c12', icon: '⏳' },
    '1': { label: '处理中', color: '#3498db', icon: '🔄' },
    '2': { label: '已完成', color: '#27ae60', icon: '✅' },
    '3': { label: '已取消', color: '#95a5a6', icon: '❌' },
    '4': { label: '已拒绝', color: '#e74c3c', icon: '🚫' }
  };

  return statusMap[String(status)] || { label: '未知状态', color: '#95a5a6', icon: '❓' };
};

export default {
  submitIssueReport,
  getIssueCategories,
  getPriorityLevels,
  getMyIssueReports,
  getIssueDetail,
  cancelIssueReport,
  formatCategoryType,
  formatPriority,
  validateIssueData,
  getStatusInfo
};
