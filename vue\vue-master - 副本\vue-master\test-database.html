<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #555;
        }
        button {
            padding: 12px 24px;
            margin: 8px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-primary:hover { background-color: #0056b3; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-success:hover { background-color: #1e7e34; }
        .btn-warning { background-color: #ffc107; color: black; }
        .btn-warning:hover { background-color: #e0a800; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-danger:hover { background-color: #c82333; }
        .btn-info { background-color: #17a2b8; color: white; }
        .btn-info:hover { background-color: #138496; }
        
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
            border-left: 4px solid #ccc;
        }
        .success { 
            background-color: #d4edda; 
            color: #155724; 
            border-left-color: #28a745;
        }
        .error { 
            background-color: #f8d7da; 
            color: #721c24; 
            border-left-color: #dc3545;
        }
        .info { 
            background-color: #d1ecf1; 
            color: #0c5460; 
            border-left-color: #17a2b8;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }
        
        .config-info {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .config-info h4 {
            margin-top: 0;
            color: #495057;
        }
        .config-item {
            margin: 8px 0;
            font-family: monospace;
        }
        .config-label {
            font-weight: bold;
            color: #6c757d;
            display: inline-block;
            width: 120px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ 数据库连接测试工具</h1>
        
        <div class="config-info">
            <h4>📋 当前数据库配置</h4>
            <div class="config-item">
                <span class="config-label">数据库类型:</span> MySQL
            </div>
            <div class="config-item">
                <span class="config-label">服务器地址:</span> localhost:3306
            </div>
            <div class="config-item">
                <span class="config-label">数据库名:</span> community
            </div>
            <div class="config-item">
                <span class="config-label">用户名:</span> root
            </div>
            <div class="config-item">
                <span class="config-label">连接池:</span> Druid
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔌 基础连接测试</h3>
            <p>测试后端服务是否能够连接到数据库</p>
            <button class="btn-primary" onclick="testBasicConnection()">测试API连接</button>
            <button class="btn-success" onclick="testDatabaseConnection()">测试数据库连接</button>
            <div id="basic-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📊 数据库操作测试</h3>
            <p>测试数据库的基本CRUD操作</p>
            <button class="btn-info" onclick="testSelectOperation()">测试查询操作</button>
            <button class="btn-warning" onclick="testInsertOperation()">测试插入操作</button>
            <button class="btn-danger" onclick="testTableStructure()">检查表结构</button>
            <div id="operation-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔍 连接池监控</h3>
            <p>查看Druid连接池的状态和监控信息</p>
            <button class="btn-info" onclick="openDruidMonitor()">打开Druid监控页面</button>
            <button class="btn-warning" onclick="testDruidStatus()">获取连接池状态</button>
            <div id="monitor-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🛠️ 问题诊断</h3>
            <p>诊断常见的数据库连接问题</p>
            <button class="btn-warning" onclick="diagnoseDatabaseIssues()">诊断连接问题</button>
            <button class="btn-info" onclick="showTroubleshootingGuide()">故障排除指南</button>
            <div id="diagnosis-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080/api';
        
        // 显示结果的通用函数
        function showResult(elementId, message, type) {
            const resultDiv = document.getElementById(elementId);
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }

        // 1. 测试基础API连接
        async function testBasicConnection() {
            showResult('basic-result', '正在测试API连接...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/test`, {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                const data = await response.json();
                showResult('basic-result', 
                    `✅ API连接成功！\n状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`, 
                    'success');
            } catch (error) {
                showResult('basic-result', 
                    `❌ API连接失败！\n错误: ${error.message}\n\n请检查:\n1. 后端服务是否启动\n2. 端口8080是否可访问`, 
                    'error');
            }
        }

        // 2. 测试数据库连接
        async function testDatabaseConnection() {
            showResult('basic-result', '正在测试数据库连接...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/test-db`, {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                const data = await response.json();
                
                if (data.code === 200) {
                    showResult('basic-result', 
                        `✅ 数据库连接成功！\n${data.msg}\n响应详情: ${JSON.stringify(data, null, 2)}`, 
                        'success');
                } else {
                    showResult('basic-result', 
                        `⚠️ 数据库连接有问题\n错误信息: ${data.msg}\n响应详情: ${JSON.stringify(data, null, 2)}`, 
                        'warning');
                }
            } catch (error) {
                showResult('basic-result', 
                    `❌ 数据库连接测试失败！\n错误: ${error.message}`, 
                    'error');
            }
        }

        // 3. 测试查询操作
        async function testSelectOperation() {
            showResult('operation-result', '正在测试查询操作...', 'info');
            
            try {
                // 尝试查询一个不存在的用户，测试查询功能
                const response = await fetch(`${API_BASE_URL}/auth/register`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        userName: '',
                        phone: '',
                        password: '',
                        userType: 1
                    })
                });
                
                const data = await response.json();
                showResult('operation-result', 
                    `📊 查询操作测试完成\n这是通过注册接口测试数据库查询的结果\n响应: ${JSON.stringify(data, null, 2)}`, 
                    'info');
            } catch (error) {
                showResult('operation-result', 
                    `❌ 查询操作测试失败！\n错误: ${error.message}`, 
                    'error');
            }
        }

        // 4. 测试插入操作
        async function testInsertOperation() {
            showResult('operation-result', '正在测试插入操作...', 'info');
            
            const testData = {
                userName: 'dbtest_' + Date.now(),
                phone: '13900000' + String(Date.now()).slice(-3),
                password: 'test123456',
                userType: 1
            };
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/register`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testData)
                });
                
                const data = await response.json();
                
                if (data.code === 200) {
                    showResult('operation-result', 
                        `✅ 插入操作成功！\n测试数据: ${JSON.stringify(testData, null, 2)}\n响应: ${JSON.stringify(data, null, 2)}`, 
                        'success');
                } else {
                    showResult('operation-result', 
                        `⚠️ 插入操作失败\n测试数据: ${JSON.stringify(testData, null, 2)}\n错误: ${data.msg}`, 
                        'warning');
                }
            } catch (error) {
                showResult('operation-result', 
                    `❌ 插入操作测试失败！\n错误: ${error.message}`, 
                    'error');
            }
        }

        // 5. 检查表结构
        async function testTableStructure() {
            showResult('operation-result', 
                `📋 表结构信息\n\n根据配置，应该存在以下表:\n\n` +
                `表名: user_account\n` +
                `字段:\n` +
                `- id (BIGINT, 主键, 自增)\n` +
                `- user_name (VARCHAR(50), 用户名)\n` +
                `- phone (VARCHAR(20), 手机号)\n` +
                `- password (VARCHAR(200), 密码)\n` +
                `- user_type (TINYINT, 用户类型)\n` +
                `- create_time (DATETIME, 创建时间)\n` +
                `- update_time (DATETIME, 更新时间)\n\n` +
                `如果注册功能正常工作，说明表结构正确。`, 
                'info');
        }

        // 6. 打开Druid监控页面
        function openDruidMonitor() {
            const monitorUrl = 'http://localhost:8080/druid/index.html';
            window.open(monitorUrl, '_blank');
            showResult('monitor-result', 
                `🔗 已尝试打开Druid监控页面\n地址: ${monitorUrl}\n\n如果页面无法打开，请检查:\n1. 后端服务是否启动\n2. Druid监控是否启用`, 
                'info');
        }

        // 7. 测试Druid状态
        async function testDruidStatus() {
            showResult('monitor-result', '正在获取Druid连接池状态...', 'info');
            
            try {
                const response = await fetch('http://localhost:8080/druid/datasource.json');
                const data = await response.json();
                
                showResult('monitor-result', 
                    `✅ Druid连接池状态获取成功！\n${JSON.stringify(data, null, 2)}`, 
                    'success');
            } catch (error) {
                showResult('monitor-result', 
                    `⚠️ 无法获取Druid状态\n错误: ${error.message}\n\n可能原因:\n1. Druid监控未启用\n2. 访问权限限制`, 
                    'warning');
            }
        }

        // 8. 诊断数据库问题
        async function diagnoseDatabaseIssues() {
            showResult('diagnosis-result', '正在诊断数据库连接问题...', 'info');
            
            let diagnosis = [];
            
            // 检查API连接
            try {
                await fetch(`${API_BASE_URL}/auth/test`);
                diagnosis.push('✅ API服务连接正常');
            } catch (error) {
                diagnosis.push('❌ API服务连接失败: ' + error.message);
            }
            
            // 检查数据库连接
            try {
                const response = await fetch(`${API_BASE_URL}/auth/test-db`);
                const data = await response.json();
                if (data.code === 200) {
                    diagnosis.push('✅ 数据库连接正常');
                } else {
                    diagnosis.push('❌ 数据库连接异常: ' + data.msg);
                }
            } catch (error) {
                diagnosis.push('❌ 数据库连接测试失败: ' + error.message);
            }
            
            showResult('diagnosis-result', diagnosis.join('\n'), 'info');
        }

        // 9. 显示故障排除指南
        function showTroubleshootingGuide() {
            const guide = `🛠️ 数据库连接故障排除指南

常见问题及解决方案:

1. 【连接被拒绝】
   - 检查MySQL服务是否启动
   - 确认端口3306是否开放
   - 验证用户名密码是否正确

2. 【数据库不存在】
   - 创建community数据库
   - 执行初始化SQL脚本
   - 检查数据库名称拼写

3. 【表不存在】
   - 执行建表SQL语句
   - 检查表名是否正确
   - 确认用户权限

4. 【连接池配置问题】
   - 检查Druid配置
   - 调整连接池参数
   - 查看连接池监控

5. 【权限问题】
   - 确认数据库用户权限
   - 检查防火墙设置
   - 验证网络连接

解决步骤:
1. 启动MySQL服务
2. 创建community数据库
3. 执行建表语句
4. 重启后端服务
5. 测试连接`;

            showResult('diagnosis-result', guide, 'info');
        }
    </script>
</body>
</html>
