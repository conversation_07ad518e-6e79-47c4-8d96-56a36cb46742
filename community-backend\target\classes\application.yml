spring:
  application:
    name: xia<PERSON>u

  datasource:
    name: community-db
    druid:
      # 基本连接信息
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: **********************************************************************************************************************
      username: root
      password: a13716842307

      # --- 连接池核心参数 ---
      initial-size: 5                        # 初始化连接数量（预热连接池）
      min-idle: 5                            # 最小空闲连接数（保持基本连接可用）
      max-active: 50                         # 最大活跃连接数（按服务器资源调整）
      max-wait: 10000                        # 获取连接最大等待时间(ms)
      test-on-borrow: false                  # 获取连接时是否测试可用性（影响性能，不推荐）
      test-on-return: false                  # 归还连接时是否测试可用性（影响性能，不推荐）
      test-while-idle: true                  # 空闲时定期检测连接有效性（重要！）
      time-between-eviction-runs-millis: 60000  # 空闲连接检测周期(ms)
      min-evictable-idle-time-millis: 300000    # 连接空闲最小生存时间(ms)（5分钟）
      keep-alive: true                       # 启用保活机制（避免长时间空闲断开）

      # --- 过滤器配置 ---
      filters: stat,wall,slf4j,config         # 启用统计、防火墙、日志、配置过滤器
      filter:
      # 统计过滤器配置
        stat:
          log-slow-sql: true                 # 记录慢SQL日志
          slow-sql-millis: 1000              # 慢SQL阈值(ms)（大于此值视为慢查询）
          merge-sql: true                    # 合并相似SQL的统计（减少日志量）
        # SQL防火墙配置
        wall:
          enabled: true                      # 启用SQL防火墙
          config:
            drop-table-allow: true          # 禁止DROP TABLE操作
            delete-allow: true              # 禁止不带WHERE的DELETE
            update-where-none-check: true    # 检查UPDATE必须有WHERE条件
            select-into-allow: false         # 禁止SELECT INTO
            create-table-allow: true        # 禁止CREATE TABLE
        # 日志过滤器
        slf4j:
          enabled: true                      # 启用SQL日志
          statement-executable-sql-log-enable: true  # 记录可执行SQL

      # --- 监控配置 ---
      # 内置监控服务器配置（访问路径 /druid/*）
      stat-view-servlet:
        enabled: true                        # 启用监控页面
        url-pattern: /druid/*                # 监控页面访问路径
        reset-enable: false                  # 禁用重置功能（生产环境推荐）
#        login-username: admin                # 监控页面管理员用户（建议修改）
#        login-password: 123456    # 监控页面密码（从环境变量获取）
      # Web关联监控（统计Web请求）
      web-stat-filter:
        enabled: true                        # 启用Web请求统计
        url-pattern: /*                      # 监控所有URL请求
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*" # 排除静态资源
        session-stat-enable: true             # 开启Session统计
        profile-enable: true                  # 监控单个URL调用的SQL列表


  servlet:
    multipart:
      max-file-size: 1MB # 单个文件大小
      max-request-size: 5MB      # 设置总上传的文件大小

mybatis:
  mapper-locations:
    - classpath*:com/hfut/xiaozu/**/*.xml
    - classpath*:mapper/**/*.xml
  configuration:
    map-underscore-to-camel-case: true  #开启驼峰命名映射
    log-prefix: "[MYBATIS]"                   # MyBatis日志前缀
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl  # 控制台日志

# PageHelper配置
pagehelper:
  helper-dialect: mysql         # 数据库方言
  reasonable: true              # 分页合理化（超出最大页时返回最后一页）
  support-methods-arguments: true # 支持接口参数



server:
  port: 8080

# springdoc-openapi项目配置,访问地址：http://127.0.0.1:8080/doc.html
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 'default'
      paths-to-match: '/**'
      packages-to-scan: com.hfut.xiaozu
# knife4j的增强配置，不需要增强可以不配
knife4j:
  enable: true
  setting:
    language: zh_cn
logging:
  level:
    com.hfut.xiaozu: debug
    org.mybatis: debug
    org.apache.ibatis: debug
    com.github.pagehelper: DEBUG  # 查看PageHelper日志


jwt:
  secret: 114514233 #jwt密钥
  expiration:  3600000000000000     # jwt过期时间  60分钟
  header: Authorization
  excludedPaths:
    - /api/auth/login
    - /api/auth/register
    - /**/v3/api-docs/**
    - /doc.html
    - /swagger-ui.html
    - /swagger-ui/**
    - /webjars/**
    - /swagger-resources/**
    - /swagger/**
    - /**/*.js
    - /**/*.css
    - /**/*.png
    - /**/*.ico

aliyun:
  oss:
    endpoint: oss-cn-nanjing.aliyuncs.com
    bucketName: qiaozekai
