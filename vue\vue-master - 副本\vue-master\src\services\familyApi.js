/**
 * 家人管理API服务
 * 处理家人相关的后端API调用
 * 使用HTTP拦截器自动处理token和错误
 */

import http from '../utils/httpInterceptor.js';

/**
 * 批量添加家人
 * @param {Array} familyMembers - 家人数据数组
 * @returns {Promise<Object>} API响应
 */
export const addFamilyMembers = async (familyMembers) => {
  console.log('🔧 API: 批量添加家人', familyMembers);

  try {
    // 使用HTTP拦截器发送请求（自动添加token）
    const response = await http.post('/family', familyMembers);

    console.log('✅ API: 家人添加成功', response);

    return {
      success: true,
      data: response.data,
      message: response.msg || '家人添加成功'
    };

  } catch (error) {
    console.error('❌ API: 家人添加失败', error);
    throw error;
  }
};

/**
 * 获取当前用户的家人列表
 * @returns {Promise<Object>} API响应
 */
export const getMyFamilyMembers = async () => {
  console.log('🔧 API: 获取我的家人列表');

  try {
    // 使用HTTP拦截器发送请求（自动添加token）
    const response = await http.get('/family/list/me');

    console.log('✅ API: 家人列表获取成功', response);

    return {
      success: true,
      data: response.data,
      message: response.msg || '家人列表获取成功'
    };

  } catch (error) {
    console.error('❌ API: 家人列表获取失败', error);
    throw error;
  }
};

/**
 * 更新家人信息
 * @param {Object} familyMember - 家人数据
 * @returns {Promise<Object>} API响应
 */
export const updateFamilyMember = async (familyMember) => {
  console.log('🔧 API: 更新家人信息', familyMember);

  try {
    // 使用HTTP拦截器发送请求（自动添加token）
    const response = await http.put('/family', familyMember);

    console.log('✅ API: 家人信息更新成功', response);

    return {
      success: true,
      data: response.data,
      message: response.msg || '家人信息更新成功'
    };

  } catch (error) {
    console.error('❌ API: 家人信息更新失败', error);
    throw error;
  }
};

/**
 * 删除家人信息
 * @param {number} familyId - 家人ID
 * @returns {Promise<Object>} API响应
 */
export const deleteFamilyMember = async (familyId) => {
  console.log('🔧 API: 删除家人信息', familyId);

  try {
    // 尝试使用URL参数方式
    const response = await http.delete(`/family?id=${familyId}`);

    console.log('✅ API: 家人信息删除成功', response);

    return {
      success: true,
      data: response.data,
      message: response.msg || '家人信息删除成功'
    };

  } catch (error) {
    console.error('❌ API: 家人信息删除失败', error);

    // 如果URL参数方式失败，尝试请求体方式
    if (error.status === 400) {
      console.log('🔄 尝试使用请求体方式删除...');
      try {
        const requestData = { id: familyId };
        const response = await http.delete('/family', {
          data: requestData
        });

        console.log('✅ API: 家人信息删除成功（请求体方式）', response);

        return {
          success: true,
          data: response.data,
          message: response.msg || '家人信息删除成功'
        };
      } catch (bodyError) {
        console.error('❌ API: 请求体方式也失败', bodyError);
        throw bodyError;
      }
    }

    throw error;
  }
};
