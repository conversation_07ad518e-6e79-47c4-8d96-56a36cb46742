<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup>
import { onMounted } from 'vue';
import { useUserStore } from './stores/userStore';

const userStore = useUserStore();

// 应用启动时初始化用户状态
onMounted(() => {
  userStore.initializeUser();
});
</script>

<style>
/* 全局样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: var(--text-dark);
  background-color: var(--light-gray);
}

#app {
  min-height: 100vh;
}

/* 全局滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--light-gray);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-blue-light);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-blue-dark);
}

/* 全局链接样式 */
a {
  color: var(--primary-blue);
  text-decoration: none;
}

a:hover {
  color: var(--primary-blue-dark);
  text-decoration: underline;
}

/* 全局按钮样式重置 */
button {
  font-family: inherit;
}

/* 响应式字体大小 */
@media (max-width: 768px) {
  body {
    font-size: 14px;
  }
}
</style>