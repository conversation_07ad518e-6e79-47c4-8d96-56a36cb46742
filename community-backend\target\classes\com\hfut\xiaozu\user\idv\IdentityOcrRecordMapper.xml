<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hfut.xiaozu.user.idv.IdentityOcrRecordMapper">

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.hfut.xiaozu.user.idv.IdentityOcrRecordEntity" useGeneratedKeys="true">
        insert into community.identity_ocr_record(user_id,session_id,id_card_front_url,ocr_data,ocr_name,ocr_id_number,ocr_status,error_message)
        values (#{userId},#{sessionId},#{idCardFrontUrl},#{ocrData},#{ocrName},#{ocrIdNumber},#{ocrStatus},#{errorMessage})
    </insert>

    <update id="updateOcrRecord">
        UPDATE community.identity_ocr_record
        SET
            ocr_name = #{ocrName},
            ocr_data = #{ocrData},
            ocr_id_number = #{ocrIdNumber},
            ocr_status = #{ocrStatus},
            error_message = #{errorMessage}
        WHERE id = #{id}
    </update>
</mapper>
