package com.hfut.xiaozu.house.grid;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【community_grid(小区网格划分表)】的数据库操作Mapper
* @createDate 2025-06-27 10:36:49
* @Entity com.hfut.xiaozu.house.grid.CommunityGrid
*/
@Mapper
public interface CommunityGridMapper {

    int insert(CommunityGridEntity record);

    CommunityGridEntity getById(Long id);

    int deleteById(Long id);

    int update(CommunityGridEntity record);

    List<CommunityGridEntity> listAll();

    CommunityGridEntity getByResponsibleId(Long responsibleId);
}
