package com.hfut.xiaozu.car.bind;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 车辆-房屋绑定关系表（支持多房屋绑定）
 * @TableName vehicle_house_binding
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class VehicleHouseBindingEntity {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 车辆ID
     */
    private Long vehicleId;

    /**
     * 房屋ID
     */
    private Long houseId;

    /**
     * 绑定状态: 1-待审核 2-已生效 3-已拒绝 4-已解绑
     */
    private Integer status;

    /**
     * 车位编号
     */
    private String spaceNumber;

    /**
     * 车位类型: 1-固定车位 2-临时车位
     */
    private Integer spaceType;

    /**
     * 审核人ID
     */
    private Long approvedBy;

    /**
     * 审核备注
     */
    private String approvalRemark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
