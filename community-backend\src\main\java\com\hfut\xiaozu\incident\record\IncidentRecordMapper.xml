<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hfut.xiaozu.incident.record.IncidentRecordMapper">

    <resultMap id="BaseResultMap" type="com.hfut.xiaozu.incident.record.IncidentRecordEntity">
            <id property="id" column="id" />
            <result property="reporterId" column="reporter_id" />
            <result property="title" column="title" />
            <result property="description" column="description" />
            <result property="categoryType" column="category_type" />
            <result property="priority" column="priority" />
            <result property="locationDescription" column="location_description" />
            <result property="locationGeojson" column="location_geojson" />
            <result property="communityId" column="community_id" />
            <result property="status" column="status" />
            <result property="handlerId" column="handler_id" />
            <result property="createTime" column="create_time" />
            <result property="assignTime" column="assign_time" />
            <result property="completeTime" column="complete_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,reporter_id,title,description,category_type,priority,
        location_description,location_geojson,community_id,status,handler_id,
        create_time,assign_time,complete_time,update_time
    </sql>

    <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from incident_record
        where  id = #{id}
    </select>


    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.hfut.xiaozu.incident.record.IncidentRecordEntity" useGeneratedKeys="true">
        insert into incident_record
        ( reporter_id,title,description,category_type,priority,
        location_description,location_geojson,community_id,status,handler_id,
        assign_time,complete_time)
        values (#{reporterId},#{title},#{description},#{categoryType},#{priority},
        #{locationDescription},#{locationGeojson},#{communityId},#{status},#{handlerId},
        #{assignTime},#{completeTime})
    </insert>

    <update id="updateById" parameterType="com.hfut.xiaozu.incident.record.IncidentRecordEntity">
        update incident_record
        <set>
                <if test="reporterId != null">
                    reporter_id = #{reporterId},
                </if>
                <if test="title != null">
                    title = #{title},
                </if>
                <if test="description != null">
                    description = #{description},
                </if>
                <if test="categoryType != null">
                    category_type = #{categoryType},
                </if>
                <if test="priority != null">
                    priority = #{priority},
                </if>
                <if test="locationDescription != null">
                    location_description = #{locationDescription},
                </if>
                <if test="locationGeojson != null">
                    location_geojson = #{locationGeojson},
                </if>
                <if test="communityId != null">
                    community_id = #{communityId},
                </if>
                <if test="status != null">
                    status = #{status},
                </if>
                <if test="handlerId != null">
                    handler_id = #{handlerId},
                </if>

                <if test="assignTime != null">
                    assign_time = #{assignTime},
                </if>
                <if test="completeTime != null">
                    complete_time = #{completeTime},
                </if>

        </set>
        where   id = #{id}
    </update>

    <!-- 管理员查询：按状态查询所有事件 -->
    <select id="listByStatus" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from incident_record
        where status = #{status}
        order by create_time desc
    </select>

    <!-- 居民查询：按状态和上报人查询事件 -->
    <select id="listByStatusAndReporter" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from incident_record
        where status = #{status} and reporter_id = #{reporterId}
        order by create_time desc
    </select>

    <!-- 网格员查询：按状态查询自己上报的和被分配的事件 -->
    <select id="listByStatusForGridWorker" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from incident_record
        where status = #{status} and (reporter_id = #{userId} or handler_id = #{userId})
        order by create_time desc
    </select>

    <!-- 管理员查询：查询所有事件 -->
    <select id="listAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from incident_record
        order by create_time desc
    </select>

    <!-- 居民查询：查询自己上报的所有事件 -->
    <select id="listByReporter" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from incident_record
        where reporter_id = #{reporterId}
        order by create_time desc
    </select>

    <!-- 网格员查询：查询自己上报的和被分配的所有事件 -->
    <select id="listForGridWorker" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from incident_record
        where reporter_id = #{userId} or handler_id = #{userId}
        order by create_time desc
    </select>

</mapper>
