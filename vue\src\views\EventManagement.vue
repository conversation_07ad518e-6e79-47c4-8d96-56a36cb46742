<template>
  <div class="event-management">
    <div class="page-header">
      <h1 class="page-title">
        <span class="title-icon">🎯</span>
        事件管理
      </h1>
      <p class="page-description">社区事件全生命周期管理</p>
    </div>

    <div class="management-content">
      <!-- 状态筛选导航 -->
      <div class="status-nav">
        <div class="nav-tabs">
          <button
            v-for="status in statusTabs"
            :key="status.value"
            :class="['tab-btn', { active: currentStatus === status.value }]"
            @click="switchStatus(status.value)"
            :disabled="loadingEvents"
          >
            <span class="tab-icon">{{ status.icon }}</span>
            <span class="tab-text">{{ status.label }}</span>
            <span v-if="eventCounts[status.value] !== undefined" class="tab-count">
              {{ eventCounts[status.value] }}
            </span>
          </button>
        </div>
        <div class="nav-actions">
          <button class="action-btn" @click="refreshEvents" :disabled="loadingEvents">
            <span class="btn-icon">🔄</span>
            刷新
          </button>
          <button class="action-btn primary" @click="toggleView">
            <span class="btn-icon">{{ viewMode === 'list' ? '🗺️' : '📋' }}</span>
            {{ viewMode === 'list' ? '地图视图' : '列表视图' }}
          </button>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 加载状态 -->
        <div v-if="loadingEvents" class="loading-section">
          <div class="loading-spinner"></div>
          <p>正在加载事件数据...</p>
        </div>

        <!-- 错误提示 -->
        <div v-else-if="errorMessage" class="error-section">
          <div class="error-icon">❌</div>
          <h3>加载失败</h3>
          <p>{{ errorMessage }}</p>
          <button class="retry-btn" @click="refreshEvents">重试</button>
        </div>

        <!-- 事件列表视图 -->
        <div v-else-if="viewMode === 'list'" class="list-view">
          <div class="list-header">
            <h2>{{ getCurrentStatusLabel() }} ({{ filteredEvents.length }})</h2>
            <div class="search-controls">
              <div class="search-box">
                <input
                  v-model="searchQuery"
                  type="text"
                  placeholder="搜索事件标题、描述或位置..."
                  class="search-input"
                />
                <span class="search-icon">🔍</span>
              </div>
              <select v-model="categoryFilter" class="category-filter">
                <option value="">所有类型</option>
                <option value="1">环境卫生</option>
                <option value="2">设施损坏</option>
                <option value="3">安全问题</option>
                <option value="4">其他</option>
              </select>
            </div>
          </div>

          <!-- 事件列表 -->
          <div class="events-list">
            <div v-if="filteredEvents.length === 0" class="empty-state">
              <div class="empty-icon">📭</div>
              <h3>暂无事件</h3>
              <p>当前状态下没有找到相关事件</p>
            </div>

            <div v-else class="event-cards">
              <div
                v-for="event in filteredEvents"
                :key="event.id"
                class="event-card"
                @click="selectEvent(event)"
              >
                <div class="card-header">
                  <div class="event-id">#{{ event.id }}</div>
                  <div class="event-category" :style="{ backgroundColor: getCategoryColor(event.categoryType) }">
                    {{ getCategoryIcon(event.categoryType) }} {{ getCategoryName(event.categoryType) }}
                  </div>
                  <div class="event-priority" :class="getPriorityClass(event.priority)">
                    {{ getPriorityIcon(event.priority) }} {{ getPriorityName(event.priority) }}
                  </div>
                </div>

                <div class="card-body">
                  <h3 class="event-title">{{ event.title }}</h3>
                  <p class="event-description">{{ event.description }}</p>
                  <div class="event-location">
                    <span class="location-icon">📍</span>
                    {{ event.locationDescription || '未指定位置' }}
                  </div>
                </div>

                <div class="card-footer">
                  <div class="event-time">
                    <span class="time-icon">🕒</span>
                    {{ formatDateTime(event.createTime) }}
                  </div>
                  <div class="event-actions">
                    <button class="action-btn small" @click.stop="viewOnMap(event)">
                      📍 地图
                    </button>
                    <button class="action-btn small primary" @click.stop="handleEvent(event)">
                      {{ getActionText(event.status) }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 地图视图 -->
        <div v-else-if="viewMode === 'map'" class="map-view">
          <div class="map-header">
            <h2>事件地图 - {{ getCurrentStatusLabel() }}</h2>
            <div class="map-controls">
              <div class="legend">
                <h4>图例</h4>
                <div class="legend-items">
                  <div class="legend-item" v-for="category in categoryTypes" :key="category.value">
                    <div class="legend-color" :style="{ backgroundColor: category.color }"></div>
                    <span>{{ category.icon }} {{ category.label }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="map-container">
            <div v-if="loadingEvents" class="map-loading">
              <div class="loading-spinner"></div>
              <p>加载地图数据中...</p>
            </div>
            <div v-else-if="errorMessage" class="map-error">
              <p>{{ errorMessage }}</p>
              <button @click="fetchEvents(currentStatus)" class="retry-btn">重试</button>
            </div>
            <MapComponent
              v-else
              ref="mapComponent"
              map-id="event-management-map"
              :height="'600px'"
              :center="mapCenter"
              :zoom="15"
              :markers="eventMarkers"
              :show-controls="true"
              :show-drawing-tools="false"
              @map-ready="onMapReady"
              @marker-clicked="onMarkerClicked"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 事件详情弹窗 -->
    <div v-if="selectedEvent" class="event-detail-modal" @click.self="closeEventDetail">
      <div class="modal-content">
        <div class="modal-header">
          <h3>事件详情 #{{ selectedEvent.id }}</h3>
          <button class="close-btn" @click="closeEventDetail">×</button>
        </div>

        <div class="modal-body">
          <div class="detail-grid">
            <div class="detail-section">
              <h4>基本信息</h4>
              <div class="detail-row">
                <label>事件标题</label>
                <span class="value">{{ selectedEvent.title }}</span>
              </div>
              <div class="detail-row">
                <label>事件类型</label>
                <span class="category-badge" :style="{ backgroundColor: getCategoryColor(selectedEvent.categoryType) }">
                  {{ getCategoryIcon(selectedEvent.categoryType) }} {{ getCategoryName(selectedEvent.categoryType) }}
                </span>
              </div>
              <div class="detail-row">
                <label>优先级</label>
                <span class="priority-badge" :class="getPriorityClass(selectedEvent.priority)">
                  {{ getPriorityIcon(selectedEvent.priority) }} {{ getPriorityName(selectedEvent.priority) }}
                </span>
              </div>
              <div class="detail-row">
                <label>当前状态</label>
                <span class="status-badge" :class="getStatusClass(selectedEvent.status)">
                  {{ getStatusIcon(selectedEvent.status) }} {{ getStatusName(selectedEvent.status) }}
                </span>
              </div>
            </div>

            <div class="detail-section">
              <h4>位置信息</h4>
              <div class="detail-row">
                <label>位置描述</label>
                <span class="value">{{ selectedEvent.locationDescription || '未指定' }}</span>
              </div>
              <div class="detail-row">
                <label>坐标信息</label>
                <span class="value">{{ getLocationCoordinates(selectedEvent.locationGeojson) }}</span>
              </div>
            </div>

            <div class="detail-section full-width">
              <h4>详细描述</h4>
              <div class="description-content">
                {{ selectedEvent.description }}
              </div>
            </div>

            <div class="detail-section">
              <h4>时间信息</h4>
              <div class="detail-row">
                <label>创建时间</label>
                <span class="value">{{ formatDateTime(selectedEvent.createTime) }}</span>
              </div>
              <div class="detail-row" v-if="selectedEvent.assignTime">
                <label>分派时间</label>
                <span class="value">{{ formatDateTime(selectedEvent.assignTime) }}</span>
              </div>
              <div class="detail-row" v-if="selectedEvent.completeTime">
                <label>完成时间</label>
                <span class="value">{{ formatDateTime(selectedEvent.completeTime) }}</span>
              </div>
              <div class="detail-row">
                <label>更新时间</label>
                <span class="value">{{ formatDateTime(selectedEvent.updateTime) }}</span>
              </div>
            </div>

            <div class="detail-section">
              <h4>处理信息</h4>
              <div class="detail-row">
                <label>上报人ID</label>
                <span class="value">{{ selectedEvent.reporterId }}</span>
              </div>
              <div class="detail-row">
                <label>处理人ID</label>
                <span class="value">{{ selectedEvent.handlerId || '未分派' }}</span>
              </div>
              <div class="detail-row">
                <label>社区ID</label>
                <span class="value">{{ selectedEvent.communityId }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button class="action-btn" @click="viewOnMap(selectedEvent)">
            📍 在地图上查看
          </button>
          <button class="action-btn primary" @click="handleEvent(selectedEvent)">
            {{ getActionText(selectedEvent.status) }}
          </button>
          <button class="action-btn secondary" @click="closeEventDetail">
            关闭
          </button>
        </div>
      </div>
    </div>

    <!-- 指派网格员弹窗 -->
    <div v-if="showAssignModal" class="modal-overlay" @click="closeAssignModal">
      <div class="modal-content assign-modal" @click.stop>
        <div class="modal-header">
          <h3>指派网格员</h3>
          <button class="close-btn" @click="closeAssignModal">×</button>
        </div>

        <div class="modal-body">
          <div class="event-info">
            <h4>事件信息</h4>
            <div class="info-item">
              <span class="label">事件ID:</span>
              <span class="value">#{{ assigningEvent?.id }}</span>
            </div>
            <div class="info-item">
              <span class="label">事件标题:</span>
              <span class="value">{{ assigningEvent?.title }}</span>
            </div>
            <div class="info-item">
              <span class="label">事件类型:</span>
              <span class="value">{{ getCategoryName(assigningEvent?.categoryType) }}</span>
            </div>
            <div class="info-item">
              <span class="label">优先级:</span>
              <span class="value">{{ getPriorityName(assigningEvent?.priority) }}</span>
            </div>
          </div>

          <div class="grid-worker-selection">
            <h4>选择网格员</h4>
            <div v-if="loadingGridWorkers" class="loading-text">
              正在加载网格员列表...
            </div>
            <div v-else-if="gridWorkers.length === 0" class="empty-text">
              暂无可用的网格员
            </div>
            <div v-else class="grid-worker-list">
              <div
                v-for="worker in gridWorkers"
                :key="worker.id"
                :class="['worker-item', { selected: selectedWorkerId === worker.id }]"
                @click="selectWorker(worker.id)"
              >
                <div class="worker-info">
                  <div class="worker-name">{{ worker.userName }}</div>
                  <div class="worker-id">ID: {{ worker.id }}</div>
                </div>
                <div class="worker-status">
                  <span class="status-indicator available"></span>
                  可用
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button class="action-btn secondary" @click="closeAssignModal">
            取消
          </button>
          <button
            class="action-btn primary"
            @click="confirmAssign"
            :disabled="!selectedWorkerId || assigningWorker"
          >
            <span v-if="assigningWorker">指派中...</span>
            <span v-else>确认指派</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import MapComponent from '../components/MapComponent.vue'
import http from '../utils/httpInterceptor.js'
import { authApi } from '../services/authApi.js'
import { assignGridWorker } from '../services/eventApi.js'

// 响应式数据
const mapComponent = ref(null)
const selectedEvent = ref(null)
const searchQuery = ref('')
const categoryFilter = ref('')
const currentStatus = ref(1) // 默认显示待分派事件
const viewMode = ref('list') // 'list' 或 'map'
const loadingEvents = ref(false)
const errorMessage = ref('')
const events = ref([])
const eventCounts = ref({})

// 指派网格员相关数据
const showAssignModal = ref(false)
const assigningEvent = ref(null)
const gridWorkers = ref([])
const loadingGridWorkers = ref(false)
const selectedWorkerId = ref(null)
const assigningWorker = ref(false)

// 地图配置
const mapCenter = [117.198612, 31.774164] // 社区管理系统地图中心点，高德地图格式：[经度, 纬度]

// 状态标签页配置
const statusTabs = [
  { value: 1, label: '待分派', icon: '⏳' },
  { value: 2, label: '处理中', icon: '🔄' },
  { value: 3, label: '已完成', icon: '✅' },
  { value: 4, label: '已关闭', icon: '🔒' }
]

// 事件类型配置
const categoryTypes = [
  { value: 1, label: '环境卫生', icon: '🧹', color: '#28a745' },
  { value: 2, label: '设施损坏', icon: '🔧', color: '#ffc107' },
  { value: 3, label: '安全问题', icon: '⚠️', color: '#dc3545' },
  { value: 4, label: '其他', icon: '📝', color: '#6c757d' }
]

// 优先级配置
const priorityLevels = [
  { value: 1, label: '紧急', icon: '🔴', class: 'priority-urgent' },
  { value: 2, label: '高', icon: '🟠', class: 'priority-high' },
  { value: 3, label: '中', icon: '🟡', class: 'priority-medium' },
  { value: 4, label: '低', icon: '🟢', class: 'priority-low' }
]

// 计算属性
const filteredEvents = computed(() => {
  let filtered = events.value

  // 按搜索关键词过滤
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(event =>
      event.title.toLowerCase().includes(query) ||
      event.description.toLowerCase().includes(query) ||
      (event.locationDescription && event.locationDescription.toLowerCase().includes(query))
    )
  }

  // 按类型过滤
  if (categoryFilter.value) {
    filtered = filtered.filter(event => event.categoryType === parseInt(categoryFilter.value))
  }

  return filtered
})

// 事件标记点（用于地图显示）
const eventMarkers = computed(() => {
  console.log('🗺️ 计算事件标记点，事件数量:', filteredEvents.value.length)

  const markers = filteredEvents.value.map(event => {
    console.log('处理事件:', event.id, '位置数据:', event.locationGeojson)

    const location = parseLocationGeojson(event.locationGeojson)
    if (!location) {
      console.warn('事件位置解析失败:', event.id)
      return null
    }

    // 验证坐标是否有效
    if (isNaN(location.longitude) || isNaN(location.latitude) ||
        !isFinite(location.longitude) || !isFinite(location.latitude)) {
      console.warn('事件坐标无效:', event.id, location)
      return null
    }

    const marker = {
      id: event.id,
      position: [location.longitude, location.latitude],
      title: event.title,
      content: `
        <div class="marker-popup">
          <h4>${event.title}</h4>
          <p><strong>类型:</strong> ${getCategoryName(event.categoryType)}</p>
          <p><strong>优先级:</strong> ${getPriorityName(event.priority)}</p>
          <p><strong>描述:</strong> ${event.description}</p>
          <p><strong>位置:</strong> ${event.locationDescription || '未指定'}</p>
          <p><strong>时间:</strong> ${formatDateTime(event.createTime)}</p>
        </div>
      `,
      icon: {
        type: 'custom',
        content: `
          <div class="event-marker" style="background-color: ${getCategoryColor(event.categoryType)}">
            <span class="marker-icon">${getCategoryIcon(event.categoryType)}</span>
          </div>
        `
      },
      data: event
    }

    console.log('创建标记点:', marker.id, marker.position)
    return marker
  }).filter(marker => marker !== null)

  console.log('✅ 有效标记点数量:', markers.length)
  return markers
})

// API调用方法
const fetchEvents = async (status) => {
  loadingEvents.value = true
  errorMessage.value = ''

  try {
    console.log(`🔧 获取状态为 ${status} 的事件列表`)

    const response = await http.get(`/incident/list/${status}`)

    if (response && response.code === 200) {
      events.value = response.data || []
      console.log(`✅ 成功获取 ${events.value.length} 个事件`)
    } else {
      throw new Error(response?.msg || '获取事件列表失败')
    }
  } catch (error) {
    console.error('❌ 获取事件列表失败:', error)
    errorMessage.value = error.message || '网络错误，请稍后重试'
    events.value = []
  } finally {
    loadingEvents.value = false
  }
}

// 获取所有状态的事件数量
const fetchEventCounts = async () => {
  try {
    const counts = {}
    for (const status of statusTabs) {
      try {
        const response = await http.get(`/incident/list/${status.value}`)
        if (response && response.code === 200) {
          counts[status.value] = response.data?.length || 0
        }
      } catch (error) {
        console.warn(`获取状态 ${status.value} 的事件数量失败:`, error)
        counts[status.value] = 0
      }
    }
    eventCounts.value = counts
  } catch (error) {
    console.error('获取事件数量失败:', error)
  }
}

// 工具方法
const parseLocationGeojson = (geojsonStr) => {
  try {
    console.log('🔍 解析位置信息:', geojsonStr)

    if (!geojsonStr) {
      console.warn('位置信息为空')
      return null
    }

    const location = JSON.parse(geojsonStr)
    console.log('📍 解析后的位置对象:', location)

    if (location.latitude && location.longitude) {
      const lat = parseFloat(location.latitude)
      const lng = parseFloat(location.longitude)

      console.log('🌍 转换后的坐标:', { lat, lng })

      // 验证坐标范围是否合理
      if (isNaN(lat) || isNaN(lng) || !isFinite(lat) || !isFinite(lng)) {
        console.warn('❌ 坐标值无效:', { lat, lng })
        return null
      }

      // 验证坐标范围（中国境内大致范围）
      if (lat < 3 || lat > 54 || lng < 73 || lng > 136) {
        console.warn('⚠️ 坐标超出合理范围:', { lat, lng })
        return null
      }

      const result = {
        latitude: lat,
        longitude: lng
      }
      console.log('✅ 位置解析成功:', result)
      return result
    }

    console.warn('❌ 位置对象缺少latitude或longitude字段')
    return null
  } catch (error) {
    console.warn('❌ 解析位置信息失败:', error, '原始数据:', geojsonStr)
    return null
  }
}

const getCategoryName = (categoryType) => {
  const category = categoryTypes.find(c => c.value === categoryType)
  return category ? category.label : '未知类型'
}

const getCategoryIcon = (categoryType) => {
  const category = categoryTypes.find(c => c.value === categoryType)
  return category ? category.icon : '❓'
}

const getCategoryColor = (categoryType) => {
  const category = categoryTypes.find(c => c.value === categoryType)
  return category ? category.color : '#6c757d'
}

const getPriorityName = (priority) => {
  const level = priorityLevels.find(p => p.value === priority)
  return level ? level.label : '未知优先级'
}

const getPriorityIcon = (priority) => {
  const level = priorityLevels.find(p => p.value === priority)
  return level ? level.icon : '❓'
}

const getPriorityClass = (priority) => {
  const level = priorityLevels.find(p => p.value === priority)
  return level ? level.class : 'priority-unknown'
}

const getStatusName = (status) => {
  const statusTab = statusTabs.find(s => s.value === status)
  return statusTab ? statusTab.label : '未知状态'
}

const getStatusIcon = (status) => {
  const statusTab = statusTabs.find(s => s.value === status)
  return statusTab ? statusTab.icon : '❓'
}

const getStatusClass = (status) => {
  const statusMap = {
    1: 'status-pending',
    2: 'status-processing',
    3: 'status-completed',
    4: 'status-closed'
  }
  return statusMap[status] || 'status-unknown'
}

const getCurrentStatusLabel = () => {
  const statusTab = statusTabs.find(s => s.value === currentStatus.value)
  return statusTab ? statusTab.label : '未知状态'
}

const getActionText = (status) => {
  const actionMap = {
    1: '分派处理',
    2: '标记完成',
    3: '重新处理',
    4: '重新开启'
  }
  return actionMap[status] || '处理'
}

const getLocationCoordinates = (geojsonStr) => {
  const location = parseLocationGeojson(geojsonStr)
  if (location) {
    return `${location.latitude.toFixed(6)}, ${location.longitude.toFixed(6)}`
  }
  return '无坐标信息'
}

const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '未知时间'
  try {
    const date = new Date(dateTimeStr)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch (error) {
    return dateTimeStr
  }
}

// 事件处理方法
const switchStatus = async (status) => {
  if (currentStatus.value === status) return

  currentStatus.value = status
  await fetchEvents(status)
}

const refreshEvents = async () => {
  await Promise.all([
    fetchEvents(currentStatus.value),
    fetchEventCounts()
  ])
}

const toggleView = () => {
  viewMode.value = viewMode.value === 'list' ? 'map' : 'list'
}

const selectEvent = (event) => {
  selectedEvent.value = event
}

const closeEventDetail = () => {
  selectedEvent.value = null
}

const viewOnMap = (event) => {
  viewMode.value = 'map'

  // 等待地图组件加载完成后定位到事件位置
  setTimeout(() => {
    const location = parseLocationGeojson(event.locationGeojson)
    if (location && mapComponent.value) {
      // 设置地图中心到事件位置
      const mapInstance = mapComponent.value.getMap()
      if (mapInstance) {
        mapInstance.setZoomAndCenter(18, [location.longitude, location.latitude])
      }
    }
  }, 100)
}

const handleEvent = (event) => {
  console.log('处理事件:', event)

  // 根据事件状态决定操作
  if (event.status === 1) {
    // 待分派状态：打开指派网格员弹窗
    openAssignModal(event)
  } else {
    // 其他状态的处理逻辑
    alert(`处理事件 #${event.id}: ${event.title}`)
  }
}

// 指派网格员相关方法
const openAssignModal = async (event) => {
  assigningEvent.value = event
  showAssignModal.value = true
  selectedWorkerId.value = null

  // 加载网格员列表
  await loadGridWorkers()
}

const closeAssignModal = () => {
  showAssignModal.value = false
  assigningEvent.value = null
  selectedWorkerId.value = null
  gridWorkers.value = []
}

const loadGridWorkers = async () => {
  loadingGridWorkers.value = true

  try {
    console.log('🔧 加载网格员列表...')
    const response = await authApi.getGridWorkers()

    if (response.success) {
      gridWorkers.value = response.data
      console.log('✅ 网格员列表加载成功:', gridWorkers.value.length, '个网格员')
    } else {
      console.error('❌ 加载网格员列表失败:', response.message)
      gridWorkers.value = []
    }
  } catch (error) {
    console.error('❌ 加载网格员列表异常:', error)
    gridWorkers.value = []
  } finally {
    loadingGridWorkers.value = false
  }
}

const selectWorker = (workerId) => {
  selectedWorkerId.value = workerId
  console.log('选择网格员:', workerId)
}

const confirmAssign = async () => {
  if (!selectedWorkerId.value || !assigningEvent.value) {
    return
  }

  assigningWorker.value = true

  try {
    console.log('🔧 指派网格员:', {
      incidentId: assigningEvent.value.id,
      handlerId: selectedWorkerId.value
    })

    const response = await assignGridWorker(assigningEvent.value.id, selectedWorkerId.value)

    if (response.success) {
      console.log('✅ 指派成功:', response.message)
      alert('指派成功！')

      // 关闭弹窗
      closeAssignModal()

      // 刷新事件列表
      await fetchEvents(currentStatus.value)
    } else {
      console.error('❌ 指派失败:', response.message)
      alert('指派失败：' + response.message)
    }
  } catch (error) {
    console.error('❌ 指派异常:', error)
    alert('指派失败：' + error.message)
  } finally {
    assigningWorker.value = false
  }
}

// 地图事件处理
const onMapReady = () => {
  console.log('✅ 事件管理地图初始化完成')
  console.log('📊 当前事件数量:', events.value.length)
  console.log('🗺️ 当前标记点数量:', eventMarkers.value.length)

  // 测试：添加一个固定的测试标记点
  const testLocation = "{\"latitude\": 31.774985, \"longitude\": 117.197904}"
  const testResult = parseLocationGeojson(testLocation)
  console.log('🧪 测试位置解析结果:', testResult)
}

const onMarkerClicked = (marker) => {
  if (marker.data) {
    selectEvent(marker.data)
  }
}

// 监听状态变化
watch(currentStatus, (newStatus) => {
  fetchEvents(newStatus)
})

// 组件挂载时初始化
onMounted(async () => {
  console.log('事件管理页面初始化')
  await Promise.all([
    fetchEvents(currentStatus.value),
    fetchEventCounts()
  ])
})
</script>

<style scoped>
/* 页面基础样式 */
.event-management {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 10px 0;
}

.title-icon {
  font-size: 2rem;
}

.page-description {
  color: #6c757d;
  font-size: 1.1rem;
  margin: 0;
}

/* 状态导航样式 */
.status-nav {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.nav-tabs {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: 2px solid #e1e8ed;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  position: relative;
}

.tab-btn:hover {
  border-color: #007bff;
  background: #f8f9ff;
}

.tab-btn.active {
  border-color: #007bff;
  background: #007bff;
  color: white;
}

.tab-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.tab-icon {
  font-size: 1.2rem;
}

.tab-count {
  background: rgba(255,255,255,0.2);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
}

.tab-btn.active .tab-count {
  background: rgba(255,255,255,0.3);
}

.nav-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 1px solid #dee2e6;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  text-decoration: none;
  color: #495057;
}

.action-btn:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.action-btn.primary {
  background: #007bff;
  border-color: #007bff;
  color: white;
}

.action-btn.primary:hover {
  background: #0056b3;
  border-color: #0056b3;
}

.action-btn.secondary {
  background: #6c757d;
  border-color: #6c757d;
  color: white;
}

.action-btn.secondary:hover {
  background: #545b62;
  border-color: #545b62;
}

.action-btn.small {
  padding: 6px 12px;
  font-size: 0.875rem;
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-icon {
  font-size: 1rem;
}

/* 加载和错误状态 */
.loading-section,
.error-section {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 15px;
}

.retry-btn {
  margin-top: 15px;
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
}

.retry-btn:hover {
  background: #0056b3;
}

/* 列表视图样式 */
.list-view {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  overflow: hidden;
}

.list-header {
  padding: 20px;
  border-bottom: 1px solid #e1e8ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.list-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.search-controls {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
}

.search-box {
  position: relative;
  min-width: 300px;
}

.search-input {
  width: 100%;
  padding: 10px 40px 10px 15px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

.search-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 1rem;
}

.category-filter {
  padding: 10px 15px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  cursor: pointer;
}

.category-filter:focus {
  outline: none;
  border-color: #007bff;
}

/* 事件列表样式 */
.events-list {
  padding: 20px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 15px;
}

.empty-state h3 {
  margin: 0 0 10px 0;
  color: #495057;
}

.empty-state p {
  margin: 0;
}

.event-cards {
  display: grid;
  gap: 20px;
}

.event-card {
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  padding: 20px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.event-card:hover {
  border-color: #007bff;
  box-shadow: 0 4px 15px rgba(0,123,255,0.1);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.event-id {
  font-weight: 600;
  color: #6c757d;
  font-size: 0.9rem;
}

.event-category,
.event-priority {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  color: white;
}

.event-priority.priority-urgent {
  background: #dc3545;
}

.event-priority.priority-high {
  background: #fd7e14;
}

.event-priority.priority-medium {
  background: #ffc107;
  color: #212529;
}

.event-priority.priority-low {
  background: #28a745;
}

.card-body {
  margin-bottom: 15px;
}

.event-title {
  margin: 0 0 10px 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
}

.event-description {
  margin: 0 0 10px 0;
  color: #6c757d;
  line-height: 1.5;
}

.event-location {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #495057;
  font-size: 0.9rem;
}

.location-icon {
  color: #007bff;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15px;
  border-top: 1px solid #f1f3f4;
}

.event-time {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #6c757d;
  font-size: 0.85rem;
}

.time-icon {
  color: #007bff;
}

.event-actions {
  display: flex;
  gap: 8px;
}

/* 地图视图样式 */
.map-view {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  overflow: hidden;
}

.map-header {
  padding: 20px;
  border-bottom: 1px solid #e1e8ed;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 20px;
}

.map-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.map-controls {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.legend {
  background: #f8f9fa;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  padding: 15px;
  min-width: 200px;
}

.legend h4 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 0.9rem;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.85rem;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 1px solid rgba(0,0,0,0.1);
}

.map-container {
  position: relative;
  background: #f8f9fa;
}

.map-loading, .map-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 600px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.map-error p {
  color: #dc3545;
  margin-bottom: 15px;
}

.retry-btn {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.retry-btn:hover {
  background: #0056b3;
}

/* 事件详情弹窗样式 */
.event-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #e1e8ed;
  background: #f8f9fa;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6c757d;
  padding: 5px;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.modal-body {
  padding: 30px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
}

.detail-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.detail-section.full-width {
  grid-column: 1 / -1;
}

.detail-section h4 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 1rem;
  font-weight: 600;
  border-bottom: 2px solid #dee2e6;
  padding-bottom: 8px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.detail-row:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.detail-row label {
  font-weight: 600;
  color: #6c757d;
  font-size: 0.9rem;
  min-width: 80px;
}

.detail-row .value {
  color: #495057;
  font-weight: 500;
  text-align: right;
  flex: 1;
  margin-left: 15px;
}

.description-content {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 15px;
  color: #495057;
  line-height: 1.6;
  min-height: 60px;
}

.category-badge,
.priority-badge,
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  color: white;
}

.status-badge.status-pending {
  background: #6c757d;
}

.status-badge.status-processing {
  background: #007bff;
}

.status-badge.status-completed {
  background: #28a745;
}

.status-badge.status-closed {
  background: #dc3545;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px 30px;
  border-top: 1px solid #e1e8ed;
  background: #f8f9fa;
}

/* 地图标记样式 */
.event-marker {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 2px 6px rgba(0,0,0,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.event-marker:hover {
  transform: scale(1.2);
}

.marker-popup {
  max-width: 250px;
  padding: 10px;
}

.marker-popup h4 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 1rem;
}

.marker-popup p {
  margin: 4px 0;
  font-size: 0.85rem;
  color: #6c757d;
}

.marker-popup strong {
  color: #495057;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .event-management {
    padding: 10px;
  }

  .status-nav {
    flex-direction: column;
    align-items: stretch;
  }

  .nav-tabs {
    justify-content: center;
  }

  .tab-btn {
    flex: 1;
    min-width: 0;
  }

  .nav-actions {
    justify-content: center;
  }

  .list-header {
    flex-direction: column;
    align-items: stretch;
  }

  .search-controls {
    flex-direction: column;
  }

  .search-box {
    min-width: auto;
  }

  .event-cards {
    gap: 15px;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .card-footer {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }

  .event-actions {
    justify-content: space-between;
  }

  .map-header {
    flex-direction: column;
  }

  .legend {
    min-width: auto;
    width: 100%;
  }

  .detail-grid {
    grid-template-columns: 1fr;
  }

  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .detail-row .value {
    text-align: left;
    margin-left: 0;
  }

  .modal-content {
    margin: 10px;
    max-height: calc(100vh - 20px);
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 15px 20px;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 2rem;
  }

  .tab-btn {
    padding: 10px 12px;
    font-size: 0.85rem;
  }

  .tab-text {
    display: none;
  }

  .action-btn {
    padding: 8px 12px;
    font-size: 0.85rem;
  }

  .event-card {
    padding: 15px;
  }

  .event-title {
    font-size: 1.1rem;
  }
}

/* 指派网格员弹窗样式 */
.assign-modal {
  max-width: 600px;
  width: 90%;
}

.event-info {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.event-info h4 {
  margin: 0 0 12px 0;
  color: #495057;
  font-size: 16px;
  font-weight: 600;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
}

.info-item .label {
  font-weight: 500;
  color: #6c757d;
  min-width: 80px;
  margin-right: 12px;
}

.info-item .value {
  color: #495057;
  flex: 1;
}

.grid-worker-selection h4 {
  margin: 0 0 16px 0;
  color: #495057;
  font-size: 16px;
  font-weight: 600;
}

.loading-text, .empty-text {
  text-align: center;
  color: #6c757d;
  padding: 20px;
  font-style: italic;
}

.grid-worker-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 8px;
}

.worker-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
  cursor: pointer;
  transition: all 0.2s ease;
}

.worker-item:last-child {
  border-bottom: none;
}

.worker-item:hover {
  background-color: #f8f9fa;
}

.worker-item.selected {
  background-color: #e3f2fd;
  border-color: #2196f3;
}

.worker-info {
  flex: 1;
}

.worker-name {
  font-weight: 500;
  color: #495057;
  margin-bottom: 4px;
}

.worker-id {
  font-size: 12px;
  color: #6c757d;
}

.worker-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #28a745;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-indicator.available {
  background-color: #28a745;
}

.status-indicator.busy {
  background-color: #ffc107;
}

.status-indicator.offline {
  background-color: #6c757d;
}
</style>
