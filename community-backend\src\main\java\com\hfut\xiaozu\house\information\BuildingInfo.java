package com.hfut.xiaozu.house.information;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 楼栋信息表
 * @TableName building_info
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class BuildingInfo {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 小区ID
     */
    private Long communityId;

    /**
     * 楼栋编号
     */
    private String buildingCode;

    /**
     * 楼栋名称
     */
    private String buildingName;

    /**
     * 坐标GeoJSON数据
     */
    private Object geoJson;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
