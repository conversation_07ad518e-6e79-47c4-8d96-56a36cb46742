<template>
  <div class="event-map-view">
    <div class="page-header">
      <h1>事件地图</h1>
      <p class="subtitle">实时事件位置展示与状态跟踪</p>
    </div>

    <div class="map-content">
      <!-- 控制面板 -->
      <div class="control-panel">
        <div class="panel-section">
          <h3>事件筛选</h3>
          <div class="filter-controls">
            <div class="filter-group">
              <label>事件类型</label>
              <div class="checkbox-group">
                <label v-for="type in eventTypes" :key="type.key" class="checkbox-label">
                  <input 
                    type="checkbox" 
                    v-model="selectedTypes" 
                    :value="type.key"
                    @change="updateEventDisplay"
                  >
                  <span class="checkbox-text">{{ type.name }}</span>
                  <span :class="['type-indicator', type.key]"></span>
                </label>
              </div>
            </div>
            
            <div class="filter-group">
              <label>事件状态</label>
              <div class="checkbox-group">
                <label v-for="status in eventStatuses" :key="status.key" class="checkbox-label">
                  <input 
                    type="checkbox" 
                    v-model="selectedStatuses" 
                    :value="status.key"
                    @change="updateEventDisplay"
                  >
                  <span class="checkbox-text">{{ status.name }}</span>
                  <span :class="['status-indicator', status.key]"></span>
                </label>
              </div>
            </div>

            <div class="filter-group">
              <label>时间范围</label>
              <select v-model="timeRange" @change="updateEventDisplay">
                <option value="today">今天</option>
                <option value="week">本周</option>
                <option value="month">本月</option>
                <option value="all">全部</option>
              </select>
            </div>
          </div>
        </div>

        <div class="panel-section">
          <h3>事件统计</h3>
          <div class="stats-list">
            <div class="stat-item">
              <span class="stat-label">总事件数</span>
              <span class="stat-value">{{ eventStats.total }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">待处理</span>
              <span class="stat-value pending">{{ eventStats.pending }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">处理中</span>
              <span class="stat-value processing">{{ eventStats.processing }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">已完成</span>
              <span class="stat-value completed">{{ eventStats.completed }}</span>
            </div>
          </div>
        </div>

        <div class="panel-section">
          <h3>快速操作</h3>
          <div class="action-buttons">
            <button class="action-btn primary" @click="createEvent">
              <span class="btn-icon">➕</span>
              新建事件
            </button>
            <button class="action-btn secondary" @click="refreshEvents">
              <span class="btn-icon">🔄</span>
              刷新数据
            </button>
            <button class="action-btn secondary" @click="exportEvents">
              <span class="btn-icon">📤</span>
              导出事件
            </button>
          </div>
        </div>
      </div>

      <!-- 地图区域 -->
      <div class="map-area">
        <div class="map-header">
          <h2>事件分布地图</h2>
          <div class="map-controls">
            <!-- 地图模式切换 -->
            <div class="map-mode-controls">
              <label>地图模式</label>
              <div class="mode-buttons">
                <button
                  :class="['mode-btn', { active: currentMapMode === 'normal' }]"
                  @click="switchMapMode('normal')"
                >
                  标准
                </button>
                <button
                  :class="['mode-btn', { active: currentMapMode === 'satellite' }]"
                  @click="switchMapMode('satellite')"
                >
                  卫星
                </button>
                <button
                  :class="['mode-btn', { active: currentMapMode === 'terrain' }]"
                  @click="switchMapMode('terrain')"
                >
                  地形
                </button>
              </div>
            </div>

            <button class="control-btn" @click="centerMap">
              <span class="btn-icon">🎯</span>
              居中显示
            </button>
            <button class="control-btn" @click="toggleHeatmap">
              <span class="btn-icon">🔥</span>
              {{ showHeatmap ? '关闭' : '开启' }}热力图
            </button>
          </div>
        </div>
        
        <div class="map-container">
          <MapComponent
            ref="mapComponent"
            :height="'600px'"
            :center="mapCenter"
            :zoom="15"
            :markers="filteredEventMarkers"
            :polygons="gridPolygons"
            :show-controls="true"
            :show-drawing-tools="false"
            @marker-click="onEventClick"
            @layer-toggle="onLayerToggle"
          />
        </div>

        <!-- 图例 -->
        <div class="map-legend">
          <h4>图例</h4>
          <div class="legend-items">
            <div v-for="type in eventTypes" :key="type.key" class="legend-item">
              <span :class="['legend-marker', type.key]"></span>
              <span class="legend-text">{{ type.name }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 事件详情弹窗 -->
    <div v-if="selectedEvent" class="event-detail-modal" @click="closeEventDetail">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>事件详情</h3>
          <button class="close-btn" @click="closeEventDetail">×</button>
        </div>
        <div class="modal-body">
          <div class="event-info">
            <div class="info-row">
              <span class="label">事件编号:</span>
              <span class="value">{{ selectedEvent.id }}</span>
            </div>
            <div class="info-row">
              <span class="label">事件类型:</span>
              <span :class="['value', 'type-badge', selectedEvent.type]">
                {{ getEventTypeName(selectedEvent.type) }}
              </span>
            </div>
            <div class="info-row">
              <span class="label">事件状态:</span>
              <span :class="['value', 'status-badge', selectedEvent.status]">
                {{ getEventStatusName(selectedEvent.status) }}
              </span>
            </div>
            <div class="info-row">
              <span class="label">发生位置:</span>
              <span class="value">{{ selectedEvent.location }}</span>
            </div>
            <div class="info-row">
              <span class="label">报告人:</span>
              <span class="value">{{ selectedEvent.reporter }}</span>
            </div>
            <div class="info-row">
              <span class="label">创建时间:</span>
              <span class="value">{{ formatDate(selectedEvent.createdAt) }}</span>
            </div>
            <div class="info-row">
              <span class="label">事件描述:</span>
              <span class="value description">{{ selectedEvent.description }}</span>
            </div>
          </div>
          
          <div class="event-actions">
            <button class="action-btn primary" @click="editEvent(selectedEvent)">
              <span class="btn-icon">✏️</span>
              编辑事件
            </button>
            <button class="action-btn secondary" @click="viewEventHistory(selectedEvent)">
              <span class="btn-icon">📋</span>
              查看历史
            </button>
            <button 
              v-if="selectedEvent.status !== 'completed'"
              class="action-btn success" 
              @click="completeEvent(selectedEvent)"
            >
              <span class="btn-icon">✅</span>
              标记完成
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import MapComponent from '../components/MapComponent.vue';
// 临时移除外部数据导入，直接在组件内定义

const router = useRouter();

// 响应式数据
const mapComponent = ref(null);
const selectedEvent = ref(null);
const selectedTypes = ref(['maintenance', 'security', 'environment', 'emergency']);
const selectedStatuses = ref(['pending', 'processing', 'completed']);
const timeRange = ref('week');
const showHeatmap = ref(false);
const currentMapMode = ref('normal');

// 地图配置
const mapCenter = [117.198612, 31.774164]; // 社区管理系统地图中心点，高德地图格式：[经度, 纬度]

// 事件类型配置
const eventTypes = [
  { key: 'maintenance', name: '设施维护', color: '#007bff', icon: '🔧' },
  { key: 'security', name: '安全事件', color: '#dc3545', icon: '🛡️' },
  { key: 'environment', name: '环境卫生', color: '#28a745', icon: '🌱' },
  { key: 'emergency', name: '紧急事件', color: '#fd7e14', icon: '🚨' }
];

// 事件状态配置
const eventStatuses = [
  { key: 'pending', name: '待处理', color: '#ffc107' },
  { key: 'processing', name: '处理中', color: '#17a2b8' },
  { key: 'completed', name: '已完成', color: '#28a745' }
];

// 网格数据
const gridPolygons = ref([
  {
    id: 1,
    title: '芙蓉社区A区',
    coordinates: [[117.201184, 31.769427], [117.203184, 31.769427], [117.203184, 31.771427], [117.201184, 31.771427]],
    color: '#28a745',
    fillColor: '#28a745',
    fillOpacity: 0.2,
    strokeWeight: 2,
    popup: '<div><h4>芙蓉社区A区</h4><p>面积：15000m²</p><p>负责人：张三</p><p>状态：活跃</p></div>'
  },
  {
    id: 2,
    title: '芙蓉社区B区',
    coordinates: [[117.203184, 31.769427], [117.205184, 31.769427], [117.205184, 31.770927], [117.203184, 31.770927]],
    color: '#28a745',
    fillColor: '#28a745',
    fillOpacity: 0.2,
    strokeWeight: 2,
    popup: '<div><h4>芙蓉社区B区</h4><p>面积：12000m²</p><p>负责人：李四</p><p>状态：活跃</p></div>'
  },
  {
    id: 3,
    title: '芙蓉社区C区',
    coordinates: [[117.200184, 31.771427], [117.202684, 31.771427], [117.202684, 31.773927], [117.200184, 31.773927]],
    color: '#6c757d',
    fillColor: '#6c757d',
    fillOpacity: 0.2,
    strokeWeight: 2,
    popup: '<div><h4>芙蓉社区C区</h4><p>面积：18000m²</p><p>负责人：王五</p><p>状态：非活跃</p></div>'
  }
]);

// 事件数据
const events = ref([
  {
    id: 'EVT001',
    type: 'maintenance',
    title: '电梯故障',
    description: '3号楼电梯停运，需要维修',
    location: '芙蓉社区A区3号楼',
    status: 'pending',
    priority: 'high',
    lat: 31.774164,
    lng: 117.198612,
    reportTime: new Date('2024-01-20T09:30:00'),
    assignee: '张三'
  },
  {
    id: 'EVT002',
    type: 'security',
    title: '可疑人员',
    description: '发现可疑人员在小区内徘徊',
    location: '芙蓉社区B区门口',
    status: 'processing',
    priority: 'high',
    lat: 31.775164,
    lng: 117.199612,
    reportTime: new Date('2024-01-20T14:15:00'),
    assignee: '李四'
  },
  {
    id: 'EVT003',
    type: 'environment',
    title: '垃圾清理',
    description: '垃圾桶已满，需要及时清理',
    location: '芙蓉社区C区垃圾站',
    status: 'completed',
    priority: 'medium',
    lat: 31.773164,
    lng: 117.197612,
    reportTime: new Date('2024-01-19T16:45:00'),
    assignee: '王五'
  },
  {
    id: 'EVT004',
    type: 'emergency',
    title: '水管爆裂',
    description: '地下水管爆裂，影响供水',
    location: '芙蓉社区地下室',
    status: 'processing',
    priority: 'urgent',
    lat: 31.769427,
    lng: 117.200684,
    reportTime: new Date('2024-01-20T11:10:00'),
    assignee: '孙七'
  }
]);

// 计算属性
const filteredEvents = computed(() => {
  return events.value.filter(event => {
    const typeMatch = selectedTypes.value.includes(event.type);
    const statusMatch = selectedStatuses.value.includes(event.status);

    let timeMatch = true;
    const now = new Date();
    const eventDate = new Date(event.reportTime);

    switch (timeRange.value) {
      case 'today':
        timeMatch = eventDate.toDateString() === now.toDateString();
        break;
      case 'week':
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        timeMatch = eventDate >= weekAgo;
        break;
      case 'month':
        const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        timeMatch = eventDate >= monthAgo;
        break;
    }

    return typeMatch && statusMatch && timeMatch;
  });
});

const filteredEventMarkers = computed(() => {
  return filteredEvents.value.map(event => {
    const eventType = eventTypes.find(type => type.key === event.type);
    const eventStatus = eventStatuses.find(status => status.key === event.status);

    return {
      lat: event.lat,
      lng: event.lng,
      title: `${event.id} - ${eventType?.name || event.type}`,
      popup: `
        <div style="min-width: 200px;">
          <h4 style="margin: 0 0 8px 0; color: ${eventType?.color || '#333'};">
            ${eventType?.icon || '📋'} ${event.title}
          </h4>
          <p style="margin: 4px 0; font-size: 13px;"><strong>位置：</strong>${event.location}</p>
          <p style="margin: 4px 0; font-size: 13px;"><strong>描述：</strong>${event.description}</p>
          <p style="margin: 4px 0; font-size: 13px;">
            <strong>状态：</strong>
            <span style="color: ${eventStatus?.color || '#333'};">${eventStatus?.name || event.status}</span>
          </p>
          <p style="margin: 4px 0; font-size: 13px;"><strong>负责人：</strong>${event.assignee}</p>
          <p style="margin: 4px 0; font-size: 12px; color: #666;">
            ${event.reportTime.toLocaleString('zh-CN')}
          </p>
        </div>
      `,
      properties: {
        eventId: event.id,
        type: event.type,
        status: event.status,
        priority: event.priority
      }
    };
  });
});

const eventStats = computed(() => {
  const stats = {
    total: filteredEvents.value.length,
    pending: 0,
    processing: 0,
    completed: 0
  };

  filteredEvents.value.forEach(event => {
    stats[event.status]++;
  });

  return stats;
});

// 方法
const updateEventDisplay = () => {
  console.log('更新事件显示');
};

const onEventClick = (marker) => {
  const eventId = marker.properties.eventId;
  selectedEvent.value = events.value.find(e => e.id === eventId);
  console.log('点击事件:', eventId, selectedEvent.value);
};

const closeEventDetail = () => {
  selectedEvent.value = null;
};

const getEventTypeName = (type) => {
  const typeObj = eventTypes.find(t => t.key === type);
  return typeObj ? typeObj.name : type;
};

const getEventStatusName = (status) => {
  const statusObj = eventStatuses.find(s => s.key === status);
  return statusObj ? statusObj.name : status;
};

const formatDate = (date) => {
  return new Date(date).toLocaleString('zh-CN');
};

const createEvent = () => {
  router.push('/property/event-management/create');
};

const editEvent = (event) => {
  router.push(`/property/event-management/edit/${event.id}`);
};

const viewEventHistory = (event) => {
  router.push(`/property/event-management/history/${event.id}`);
};

const completeEvent = (event) => {
  if (confirm(`确定要将事件 "${event.id}" 标记为已完成吗？`)) {
    event.status = 'completed';
    selectedEvent.value = null;
  }
};

const refreshEvents = () => {
  console.log('刷新事件数据');
};

const exportEvents = () => {
  console.log('导出事件数据');
};

const centerMap = () => {
  if (mapComponent.value) {
    // 居中显示所有事件
    console.log('居中显示地图');
  }
};

const toggleHeatmap = () => {
  showHeatmap.value = !showHeatmap.value;
  console.log('切换热力图:', showHeatmap.value);
};

const onLayerToggle = (layerKey, visible) => {
  console.log(`图层切换: ${layerKey}`, visible);
  // 这里可以添加特定的图层处理逻辑
};

const switchMapMode = (mode) => {
  currentMapMode.value = mode;
  if (mapComponent.value) {
    // 通过MapComponent的图层切换功能来切换地图模式
    switch (mode) {
      case 'normal':
        mapComponent.value.toggleLayer('satellite', false);
        mapComponent.value.toggleLayer('terrain', false);
        break;
      case 'satellite':
        mapComponent.value.toggleLayer('satellite', true);
        mapComponent.value.toggleLayer('terrain', false);
        break;
      case 'terrain':
        mapComponent.value.toggleLayer('satellite', false);
        mapComponent.value.toggleLayer('terrain', true);
        break;
    }
  }
};

// 生命周期
onMounted(() => {
  console.log('事件地图页面初始化');
});
</script>

<style scoped>
.event-map-view {
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  text-align: center;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 2.5em;
  font-weight: 600;
}

.subtitle {
  margin: 0;
  opacity: 0.9;
  font-size: 1.2em;
}

.map-content {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 30px;
  padding: 30px;
  height: calc(100vh - 200px);
}

.control-panel {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
  overflow-y: auto;
}

.panel-section {
  padding: 20px;
  border-bottom: 1px solid #e1e8ed;
}

.panel-section:last-child {
  border-bottom: none;
}

.panel-section h3 {
  margin: 0 0 15px 0;
  font-size: 1.2em;
  color: #333;
}

.filter-controls {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.filter-group label {
  font-weight: 600;
  color: #666;
  font-size: 14px;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #666;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
}

.checkbox-text {
  flex: 1;
}

.type-indicator,
.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
}

.type-indicator.maintenance {
  background: #ffc107;
}

.type-indicator.security {
  background: #dc3545;
}

.type-indicator.environment {
  background: #28a745;
}

.type-indicator.emergency {
  background: #fd7e14;
}

.status-indicator.pending {
  background: #ffc107;
}

.status-indicator.processing {
  background: #007bff;
}

.status-indicator.completed {
  background: #28a745;
}

.filter-group select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.stats-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.stat-value {
  font-weight: 600;
  font-size: 16px;
  color: #333;
}

.stat-value.pending {
  color: #ffc107;
}

.stat-value.processing {
  color: #007bff;
}

.stat-value.completed {
  color: #28a745;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.action-btn {
  background: #4a90e2;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background 0.3s ease;
}

.action-btn:hover {
  background: #357abd;
}

.action-btn.secondary {
  background: #6c757d;
}

.action-btn.secondary:hover {
  background: #5a6268;
}

.action-btn.success {
  background: #28a745;
}

.action-btn.success:hover {
  background: #218838;
}

.btn-icon {
  font-size: 14px;
}

.map-area {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #e1e8ed;
  background: #f8f9fa;
}

.map-header h2 {
  margin: 0;
  font-size: 1.4em;
  color: #333;
}

.map-controls {
  display: flex;
  gap: 15px;
  align-items: center;
}

/* 地图模式切换样式 */
.map-mode-controls {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.map-mode-controls label {
  font-size: 12px;
  color: #666;
  font-weight: 600;
}

.mode-buttons {
  display: flex;
  gap: 2px;
  border: 1px solid #ddd;
  border-radius: 6px;
  overflow: hidden;
}

.mode-btn {
  background: white;
  color: #666;
  border: none;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
  border-right: 1px solid #ddd;
}

.mode-btn:last-child {
  border-right: none;
}

.mode-btn:hover {
  background: #f8f9fa;
  color: #333;
}

.mode-btn.active {
  background: #4a90e2;
  color: white;
}

.control-btn {
  background: #4a90e2;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: background 0.3s ease;
}

.control-btn:hover {
  background: #357abd;
}

.map-container {
  flex: 1;
  padding: 20px;
}

.map-legend {
  padding: 15px 25px;
  border-top: 1px solid #e1e8ed;
  background: #f8f9fa;
}

.map-legend h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #333;
}

.legend-items {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #666;
}

.legend-marker {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
}

.legend-marker.maintenance {
  background: #ffc107;
}

.legend-marker.security {
  background: #dc3545;
}

.legend-marker.environment {
  background: #28a745;
}

.legend-marker.emergency {
  background: #fd7e14;
}

/* 事件详情弹窗 */
.event-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e1e8ed;
  background: #f8f9fa;
  border-radius: 12px 12px 0 0;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.3em;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.close-btn:hover {
  background: #e9ecef;
  color: #333;
}

.modal-body {
  padding: 20px;
}

.event-info {
  margin-bottom: 20px;
}

.info-row {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
}

.info-row .label {
  font-weight: 600;
  color: #666;
  width: 100px;
  flex-shrink: 0;
  font-size: 14px;
}

.info-row .value {
  color: #333;
  font-size: 14px;
  flex: 1;
}

.info-row .value.description {
  line-height: 1.5;
}

.type-badge,
.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.type-badge.maintenance {
  background: #fff3cd;
  color: #856404;
}

.type-badge.security {
  background: #f8d7da;
  color: #721c24;
}

.type-badge.environment {
  background: #d4edda;
  color: #155724;
}

.type-badge.emergency {
  background: #ffeaa7;
  color: #d63031;
}

.status-badge.pending {
  background: #fff3cd;
  color: #856404;
}

.status-badge.processing {
  background: #cce5ff;
  color: #004085;
}

.status-badge.completed {
  background: #d4edda;
  color: #155724;
}

.event-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .map-content {
    grid-template-columns: 1fr;
    height: auto;
  }

  .control-panel {
    order: 2;
  }

  .map-area {
    order: 1;
    height: 500px;
  }
}

@media (max-width: 768px) {
  .map-content {
    padding: 15px;
    gap: 15px;
  }

  .legend-items {
    flex-direction: column;
    gap: 8px;
  }

  .event-actions {
    flex-direction: column;
  }
}
</style>
