package com.hfut.xiaozu.vote.record;

import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【vote_record(投票记录表)】的数据库操作Mapper
* @createDate 2025-07-01 10:19:42
* @Entity com.hfut.xiaozu.vote.VoteRecord
*/
@Mapper
public interface VoteRecordMapper {

    int deleteById(Long id);

    int insert(VoteRecordEntity record);

    VoteRecordEntity getById(Long id);

    List<VoteRecordEntity> listByVoteId(Long voteId);

    Integer countByOptionId(Long voteId,Long optionId);

    List<VoteRecordEntity> listByVoteIdAndUserId(Long voteId, Long userId);
}
