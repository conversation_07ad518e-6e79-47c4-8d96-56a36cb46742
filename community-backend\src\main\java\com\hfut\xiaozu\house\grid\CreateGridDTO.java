package com.hfut.xiaozu.house.grid;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-06-27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateGridDTO {

    @NotBlank(message = "网格名不能为空")
    private String gridName;

    /**
     * 负责人id
     */
    private Long responsibleId;

    /**
     * 所属小区id
     */
    @NotNull(message = "必须要求所属小区id")
    @Min(value = 1,message = "小区id非法")
    private Long communityId;

    @NotNull(message = "地理信息不能为空")
    private List<point> coordinates;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class point{
        Double longitude;
        Double latitude;
    }
}
