<template>
  <div class="voice-demo-page">
    <div class="demo-header">
      <h1 class="demo-title">语音文字互转演示</h1>
      <p class="demo-description">
        基于Web自带的TTS（Text-to-Speech）和语音识别API实现的语音文字互转功能
      </p>
    </div>

    <div class="demo-content">
      <!-- 语音转换组件 -->
      <VoiceTextConverter ref="voiceConverter" />

      <!-- 功能说明 -->
      <div class="feature-info">
        <h3>功能特点</h3>
        <div class="feature-grid">
          <div class="feature-item">
            <div class="feature-icon">🎵</div>
            <h4>文字转语音</h4>
            <ul>
              <li>支持多种语音选择</li>
              <li>可调节语速、音调、音量</li>
              <li>支持播放控制（播放、暂停、停止）</li>
              <li>最大支持500字符</li>
            </ul>
          </div>

          <div class="feature-item">
            <div class="feature-icon">🎤</div>
            <h4>语音转文字</h4>
            <ul>
              <li>支持多种语言识别</li>
              <li>实时显示识别结果</li>
              <li>支持连续识别模式</li>
              <li>可复制识别结果</li>
            </ul>
          </div>

          <div class="feature-item">
            <div class="feature-icon">🔄</div>
            <h4>互转功能</h4>
            <ul>
              <li>识别结果可直接朗读</li>
              <li>支持文字复制到剪贴板</li>
              <li>实时错误提示</li>
              <li>浏览器兼容性检测</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 使用说明 -->
      <div class="usage-guide">
        <h3>使用说明</h3>
        <div class="guide-sections">
          <div class="guide-section">
            <h4>📝 文字转语音使用步骤：</h4>
            <ol>
              <li>在文本框中输入要转换的文字</li>
              <li>选择合适的语音、调节语速和音调</li>
              <li>点击"开始播放"按钮</li>
              <li>可以随时暂停、继续或停止播放</li>
            </ol>
          </div>

          <div class="guide-section">
            <h4>🎙️ 语音转文字使用步骤：</h4>
            <ol>
              <li>切换到"语音转文字"标签页</li>
              <li>选择识别语言和相关设置</li>
              <li>点击"开始识别"按钮</li>
              <li>对着麦克风说话，系统会实时识别</li>
              <li>识别完成后可复制文字或直接朗读</li>
            </ol>
          </div>
        </div>
      </div>

      <!-- 浏览器兼容性 -->
      <div class="compatibility-info">
        <h3>浏览器兼容性</h3>
        <div class="browser-grid">
          <div class="browser-item supported">
            <div class="browser-icon">🌐</div>
            <h4>Chrome</h4>
            <p>完全支持</p>
          </div>
          <div class="browser-item supported">
            <div class="browser-icon">🌐</div>
            <h4>Edge</h4>
            <p>完全支持</p>
          </div>
          <div class="browser-item supported">
            <div class="browser-icon">🌐</div>
            <h4>Safari</h4>
            <p>部分支持</p>
          </div>
          <div class="browser-item limited">
            <div class="browser-icon">🌐</div>
            <h4>Firefox</h4>
            <p>有限支持</p>
          </div>
        </div>
        <p class="compatibility-note">
          <strong>注意：</strong>语音识别功能需要麦克风权限，首次使用时浏览器会请求权限。
          建议使用最新版本的Chrome或Edge浏览器以获得最佳体验。
        </p>
      </div>

      <!-- 快速测试 -->
      <div class="quick-test">
        <h3>快速测试</h3>
        <div class="test-buttons">
          <button @click="testTTS" class="test-btn">
            🎵 测试文字转语音
          </button>
          <button @click="testSTT" class="test-btn">
            🎤 测试语音转文字
          </button>
          <button @click="clearAll" class="test-btn secondary">
            🗑️ 清空所有内容
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import VoiceTextConverter from '../components/VoiceTextConverter.vue'

const voiceConverter = ref(null)

// 测试文字转语音
const testTTS = () => {
  const testText = '欢迎使用语音文字互转功能！这是一个基于Web API的演示程序。'
  if (voiceConverter.value) {
    voiceConverter.value.speakText(testText)
  }
}

// 测试语音转文字
const testSTT = () => {
  if (voiceConverter.value) {
    voiceConverter.value.startRecognition()
  }
}

// 清空所有内容
const clearAll = () => {
  if (voiceConverter.value) {
    voiceConverter.value.clearAll()
  }
}
</script>

<style scoped>
.voice-demo-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.demo-header {
  text-align: center;
  margin-bottom: 40px;
}

.demo-title {
  font-size: 36px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 15px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.demo-description {
  font-size: 18px;
  color: #7f8c8d;
  margin: 0;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.demo-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.feature-info, .usage-guide, .compatibility-info, .quick-test {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.feature-info h3, .usage-guide h3, .compatibility-info h3, .quick-test h3 {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 25px 0;
  text-align: center;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
}

.feature-item {
  text-align: center;
  padding: 20px;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.feature-item:hover {
  border-color: #20B2AA;
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(32, 178, 170, 0.15);
}

.feature-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.feature-item h4 {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 15px 0;
}

.feature-item ul {
  text-align: left;
  margin: 0;
  padding-left: 20px;
  color: #666;
}

.feature-item li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.guide-sections {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
}

.guide-section {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #20B2AA;
}

.guide-section h4 {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 15px 0;
}

.guide-section ol {
  margin: 0;
  padding-left: 20px;
  color: #666;
}

.guide-section li {
  margin-bottom: 10px;
  line-height: 1.6;
}

.browser-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.browser-item {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.browser-item.supported {
  background: rgba(40, 167, 69, 0.1);
  border: 2px solid #28a745;
}

.browser-item.limited {
  background: rgba(255, 193, 7, 0.1);
  border: 2px solid #ffc107;
}

.browser-icon {
  font-size: 32px;
  margin-bottom: 10px;
}

.browser-item h4 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 5px 0;
}

.browser-item p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.compatibility-note {
  background: #e9ecef;
  padding: 15px;
  border-radius: 6px;
  margin: 0;
  color: #495057;
  line-height: 1.6;
}

.test-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.test-btn {
  padding: 15px 25px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #20B2AA;
  color: white;
}

.test-btn:hover {
  background: #1a9a93;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(32, 178, 170, 0.3);
}

.test-btn.secondary {
  background: #6c757d;
}

.test-btn.secondary:hover {
  background: #5a6268;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .voice-demo-page {
    padding: 15px;
  }
  
  .demo-title {
    font-size: 28px;
  }
  
  .demo-description {
    font-size: 16px;
  }
  
  .feature-grid {
    grid-template-columns: 1fr;
  }
  
  .guide-sections {
    grid-template-columns: 1fr;
  }
  
  .browser-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .test-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .test-btn {
    width: 250px;
  }
}
</style>
