<template>
  <div class="budget-vote">
    <div class="page-header">
      <h1>预算支出投票</h1>
      <p class="subtitle">参与社区决策，投出您的宝贵一票</p>
    </div>

    <div class="vote-content">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-container">
        <div class="loading-spinner"></div>
        <p>正在加载投票数据...</p>
      </div>

      <!-- 错误提示 -->
      <div v-else-if="errorMessage" class="error-message">
        {{ errorMessage }}
        <div class="error-actions">
          <button @click="loadVotes" class="retry-btn">重试</button>
          <button @click="toggleDebugMode" class="debug-btn">
            {{ showDebugInfo ? '隐藏' : '显示' }}调试信息
          </button>
        </div>

        <!-- 调试信息 -->
        <div v-if="showDebugInfo" class="debug-info">
          <h4>调试信息</h4>
          <div class="debug-item">
            <strong>Token状态:</strong>
            <span :class="debugInfo.tokenStatus.class">{{ debugInfo.tokenStatus.text }}</span>
          </div>
          <div class="debug-item">
            <strong>API地址:</strong>
            <span>{{ debugInfo.apiUrl }}</span>
          </div>
          <div class="debug-item">
            <strong>请求时间:</strong>
            <span>{{ debugInfo.requestTime }}</span>
          </div>
          <div class="debug-item">
            <strong>错误详情:</strong>
            <pre>{{ debugInfo.errorDetails }}</pre>
          </div>
          <div class="debug-actions">
            <button @click="testApiConnection" class="test-btn">测试API连接</button>
            <button @click="checkBackendStatus" class="test-btn">检查后端状态</button>
          </div>
        </div>
      </div>

      <!-- 投票列表 -->
      <div v-else-if="votes.length > 0" class="votes-container">
        <div class="view-controls">
          <div class="view-mode-selector">
            <button
              v-for="mode in viewModes"
              :key="mode.value"
              @click="currentViewMode = mode.value"
              :class="['mode-btn', { active: currentViewMode === mode.value }]"
            >
              <span class="mode-icon">{{ mode.icon }}</span>
              <span class="mode-text">{{ mode.label }}</span>
            </button>
          </div>
        </div>

        <div class="votes-list">
          <div
            v-for="vote in votes"
            :key="vote.id"
            class="vote-card"
          >
            <div class="vote-header">
              <h3>{{ vote.title }}</h3>
              <div class="vote-status">
                <span :class="['status-badge', getVoteStatus(vote).class]">
                  {{ getVoteStatus(vote).text }}
                </span>
              </div>
            </div>

            <div class="vote-info">
              <p class="vote-description">{{ vote.description }}</p>
              
              <div class="vote-meta">
                <div class="meta-item">
                  <span class="meta-label">开始时间:</span>
                  <span class="meta-value">{{ formatDateTime(vote.startTime) }}</span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">结束时间:</span>
                  <span class="meta-value">{{ formatDateTime(vote.endTime) }}</span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">最大选择:</span>
                  <span class="meta-value">{{ vote.maxChoices }}项</span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">投票方式:</span>
                  <span class="meta-value">{{ vote.isAnonymous ? '匿名' : '实名' }}</span>
                </div>
              </div>
            </div>

            <!-- 投票结果展示 -->
            <div class="vote-results">
              <h4>投票结果</h4>
              
              <!-- 列表视图 -->
              <div v-if="currentViewMode === 'list'" class="results-list">
                <div
                  v-for="option in vote.options"
                  :key="option.id || option.sortOrder"
                  class="result-item"
                >
                  <div class="option-info">
                    <span class="option-content">{{ option.content }}</span>
                    <span class="option-votes">{{ option.number }}票</span>
                    <span v-if="showDebugInfo" class="option-debug">
                      (ID: {{ option.id || option.sortOrder }})
                    </span>
                  </div>
                  <div class="progress-bar">
                    <div
                      class="progress-fill"
                      :style="{ width: getOptionPercentage(option, vote.options) + '%' }"
                    ></div>
                  </div>
                  <span class="percentage">{{ getOptionPercentage(option, vote.options).toFixed(1) }}%</span>
                </div>
              </div>

              <!-- 饼状图视图 -->
              <div v-else-if="currentViewMode === 'pie'" class="chart-container">
                <canvas
                  :ref="el => setPieChartRef(vote.id, el)"
                  class="pie-chart"
                ></canvas>
              </div>

              <!-- 柱状图视图 -->
              <div v-else-if="currentViewMode === 'bar'" class="chart-container">
                <canvas
                  :ref="el => setBarChartRef(vote.id, el)"
                  class="bar-chart"
                ></canvas>
              </div>
            </div>

            <!-- 投票操作 -->
            <div v-if="canVote(vote)" class="vote-actions">
              <button
                @click="() => { try { openVoteModal(vote); } catch (error) { handleError(error, '打开投票弹窗'); } }"
                class="vote-btn"
              >
                <span class="btn-icon">🗳️</span>
                立即投票
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <div class="empty-icon">🗳️</div>
        <h3>暂无投票活动</h3>
        <p>当前没有进行中的投票活动，请稍后再来查看。</p>
      </div>
    </div>

    <!-- 投票弹窗 -->
    <div v-if="showVoteModal" class="modal-overlay" @click="closeVoteModal">
      <div class="vote-modal" @click.stop>
        <div class="modal-header">
          <h3>{{ selectedVote?.title }}</h3>
          <button @click="closeVoteModal" class="close-btn">✕</button>
        </div>

        <div class="modal-body">
          <p class="vote-description">{{ selectedVote?.description }}</p>
          
          <div class="vote-options">
            <h4>请选择您的投票选项（最多选择{{ selectedVote?.maxChoices }}项）：</h4>
            
            <div class="options-list">
              <label
                v-for="option in selectedVote?.options"
                :key="option.id || option.sortOrder"
                class="option-label"
              >
                <input
                  v-if="selectedVote?.maxChoices === 1"
                  v-model="selectedOptions"
                  type="radio"
                  :value="option.id || option.sortOrder"
                  name="voteOption"
                />
                <input
                  v-else
                  v-model="selectedOptions"
                  type="checkbox"
                  :value="option.id || option.sortOrder"
                  :disabled="selectedOptions.length >= selectedVote?.maxChoices && !selectedOptions.includes(option.id || option.sortOrder)"
                />
                <span class="option-text">{{ option.content }}</span>
                <span class="option-debug" v-if="showDebugInfo">
                  (ID: {{ option.id || option.sortOrder }})
                </span>
              </label>
            </div>
          </div>

          <!-- 调试信息 -->
          <div v-if="showDebugInfo" class="vote-debug-info">
            <h5>调试信息</h5>
            <p><strong>当前选择:</strong> {{ JSON.stringify(selectedOptions) }}</p>
            <p><strong>投票ID:</strong> {{ selectedVote?.id }}</p>
            <p><strong>最大选择数:</strong> {{ selectedVote?.maxChoices }}</p>
            <p><strong>可用选项:</strong></p>
            <ul>
              <li v-for="option in selectedVote?.options" :key="option.id || option.sortOrder">
                ID: {{ option.id || option.sortOrder }}, 内容: {{ option.content }}
              </li>
            </ul>
          </div>
        </div>

        <div class="modal-footer">
          <button @click="closeVoteModal" class="btn secondary">取消</button>
          <button
            @click="submitUserVote"
            :disabled="selectedOptions.length === 0 || isSubmittingVote"
            class="btn primary"
          >
            <span v-if="isSubmittingVote">提交中...</span>
            <span v-else>确认投票</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 成功提示模态框 -->
    <div v-if="showSuccessModalFlag" class="modal-overlay" @click="closeModal">
      <div class="message-modal success" @click.stop>
        <div class="modal-icon">✅</div>
        <h3>{{ modalTitle }}</h3>
        <p>{{ modalMessage }}</p>
        <button @click="closeModal" class="modal-btn success">确定</button>
      </div>
    </div>

    <!-- 错误提示模态框 -->
    <div v-if="showErrorModalFlag" class="modal-overlay" @click="closeModal">
      <div class="message-modal error" @click.stop>
        <div class="modal-icon">❌</div>
        <h3>{{ modalTitle }}</h3>
        <p>{{ modalMessage }}</p>
        <button @click="closeModal" class="modal-btn error">确定</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, watch } from 'vue';
import { getVoteList, submitVote } from '../../services/voteApi.js';
import { getToken } from '../../utils/tokenManager.js';
import {
  Chart,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PieController,
  BarController
} from 'chart.js';

// 注册Chart.js组件
Chart.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PieController,
  BarController
);

// 响应式数据
const isLoading = ref(true);
const errorMessage = ref('');
const votes = ref([]);
const currentViewMode = ref('list');
const showVoteModal = ref(false);
const selectedVote = ref(null);
const selectedOptions = ref([]);
const isSubmittingVote = ref(false);

// 调试相关
const showDebugInfo = ref(false);
const debugInfo = reactive({
  tokenStatus: { text: '未检查', class: 'status-unknown' },
  apiUrl: 'http://localhost:8080/api/vote/list',
  requestTime: '',
  errorDetails: ''
});

// 提示模态框
const showSuccessModalFlag = ref(false);
const showErrorModalFlag = ref(false);
const modalTitle = ref('');
const modalMessage = ref('');

// 图表引用和实例
const pieChartRefs = reactive({});
const barChartRefs = reactive({});
const chartInstances = reactive({});

// 视图模式选项
const viewModes = [
  { value: 'list', label: '列表视图', icon: '📋' },
  { value: 'pie', label: '饼状图', icon: '🥧' },
  { value: 'bar', label: '柱状图', icon: '📊' }
];

// 设置图表引用
const setPieChartRef = (voteId, el) => {
  if (el) {
    pieChartRefs[voteId] = el;
  }
};

const setBarChartRef = (voteId, el) => {
  if (el) {
    barChartRefs[voteId] = el;
  }
};

// 加载投票数据
const loadVotes = async () => {
  isLoading.value = true;
  errorMessage.value = '';

  try {
    console.log('🔧 开始加载投票列表');

    // 检查token
    const token = getToken();
    console.log('🔑 当前token状态:', token ? '存在' : '不存在');

    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    console.log('📡 调用投票API...');
    const result = await getVoteList();
    console.log('📦 API响应结果:', result);

    if (result.success) {
      // 过滤和验证投票数据
      const rawVotes = result.data || [];
      const validVotes = [];

      console.log('📦 原始投票数据:', rawVotes);

      rawVotes.forEach((vote, index) => {
        if (!vote) {
          console.warn(`⚠️ 投票${index + 1}为null或undefined，跳过`);
          return;
        }

        console.log(`📊 验证投票${index + 1}:`, {
          id: vote.id,
          title: vote.title,
          maxChoices: vote.maxChoices,
          optionsCount: vote.options?.length || 0,
          options: vote.options?.map(opt => opt ? {
            id: opt.id,
            sortOrder: opt.sortOrder,
            content: opt.content
          } : null).filter(Boolean) || []
        });

        // 检查数据完整性
        let isValid = true;
        if (!vote.id) {
          console.warn(`⚠️ 投票${index + 1}缺少ID:`, vote);
          isValid = false;
        }
        if (!vote.title) {
          console.warn(`⚠️ 投票${index + 1}缺少标题:`, vote);
          isValid = false;
        }
        if (!vote.options || !Array.isArray(vote.options) || vote.options.length === 0) {
          console.warn(`⚠️ 投票${index + 1}缺少有效选项:`, vote.options);
          isValid = false;
        } else {
          // 过滤无效选项
          vote.options = vote.options.filter(option => {
            if (!option) {
              console.warn(`⚠️ 投票${index + 1}包含null选项`);
              return false;
            }
            if (!option.content) {
              console.warn(`⚠️ 投票${index + 1}选项缺少内容:`, option);
              return false;
            }
            return true;
          });

          if (vote.options.length === 0) {
            console.warn(`⚠️ 投票${index + 1}过滤后无有效选项`);
            isValid = false;
          }
        }

        if (isValid) {
          validVotes.push(vote);
        } else {
          console.warn(`❌ 投票${index + 1}数据无效，已跳过`);
        }
      });

      votes.value = validVotes;
      console.log('✅ 投票列表加载成功，共', votes.value.length, '个有效投票');

      // 等待DOM更新后渲染图表
      await nextTick();
      renderCharts();
    } else {
      console.error('❌ API返回失败状态:', result);
      throw new Error(result.message || '投票列表加载失败');
    }

  } catch (error) {
    console.error('❌ 投票列表加载失败:', error);

    // 更新调试信息
    debugInfo.requestTime = new Date().toLocaleString();
    debugInfo.errorDetails = JSON.stringify({
      message: error.message,
      stack: error.stack,
      name: error.name
    }, null, 2);

    // 更详细的错误信息
    let detailedError = error.message || '投票列表加载失败，请稍后重试';

    if (error.message?.includes('fetch')) {
      detailedError = '无法连接到服务器，请检查网络连接或确认后端服务是否启动';
    } else if (error.message?.includes('401')) {
      detailedError = '认证失败，请重新登录';
    } else if (error.message?.includes('404')) {
      detailedError = '投票接口不存在，请联系管理员';
    } else if (error.message?.includes('500')) {
      detailedError = '服务器内部错误，请稍后重试';
    }

    errorMessage.value = detailedError;
  } finally {
    isLoading.value = false;
  }
};

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '';
  
  const date = new Date(dateTimeStr);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 获取投票状态
const getVoteStatus = (vote) => {
  const now = new Date();
  const startTime = new Date(vote.startTime);
  const endTime = new Date(vote.endTime);

  if (now < startTime) {
    return { text: '未开始', class: 'pending' };
  } else if (now > endTime) {
    return { text: '已结束', class: 'ended' };
  } else {
    return { text: '进行中', class: 'active' };
  }
};

// 判断是否可以投票
const canVote = (vote) => {
  const status = getVoteStatus(vote);
  return status.class === 'active';
};

// 计算选项百分比
const getOptionPercentage = (option, allOptions) => {
  const totalVotes = allOptions.reduce((sum, opt) => sum + (opt.number || 0), 0);
  if (totalVotes === 0) return 0;
  return ((option.number || 0) / totalVotes) * 100;
};

// 打开投票弹窗
const openVoteModal = (vote) => {
  console.log('🔧 打开投票弹窗，投票数据:', vote);

  // 验证投票数据完整性
  if (!vote) {
    console.error('❌ 投票数据为空');
    showErrorModal('错误', '投票数据无效，请刷新页面重试');
    return;
  }

  if (!vote.id) {
    console.error('❌ 投票ID缺失:', vote);
    showErrorModal('错误', '投票ID缺失，无法进行投票');
    return;
  }

  if (!vote.options || !Array.isArray(vote.options) || vote.options.length === 0) {
    console.error('❌ 投票选项数据无效:', vote.options);
    showErrorModal('错误', '投票选项数据无效，无法进行投票');
    return;
  }

  // 检查选项是否有有效的ID
  const hasValidIds = vote.options.some(option => option.id || option.sortOrder);
  if (!hasValidIds) {
    console.error('❌ 投票选项缺少有效ID:', vote.options);
    showErrorModal('错误', '投票选项数据不完整，无法进行投票');
    return;
  }

  console.log('✅ 投票数据验证通过');
  console.log('📊 投票选项:', vote.options.map(opt => ({
    id: opt.id || opt.sortOrder,
    content: opt.content,
    sortOrder: opt.sortOrder
  })));

  selectedVote.value = vote;
  selectedOptions.value = vote.maxChoices === 1 ? null : [];
  showVoteModal.value = true;
};

// 关闭投票弹窗
const closeVoteModal = () => {
  showVoteModal.value = false;
  selectedVote.value = null;
  selectedOptions.value = [];
};

// 显示成功模态框
const showSuccessModal = (title, message) => {
  modalTitle.value = title;
  modalMessage.value = message;
  showSuccessModalFlag.value = true;
};

// 显示错误模态框
const showErrorModal = (title, message) => {
  modalTitle.value = title;
  modalMessage.value = message;
  showErrorModalFlag.value = true;
};

// 关闭提示模态框
const closeModal = () => {
  showSuccessModalFlag.value = false;
  showErrorModalFlag.value = false;
  modalTitle.value = '';
  modalMessage.value = '';
};

// 提交投票
const submitUserVote = async () => {
  if (!selectedVote.value || isSubmittingVote.value) return;

  // 构建选项ID数组
  let optionIds;
  if (selectedVote.value.maxChoices === 1) {
    // 单选：selectedOptions是单个值
    optionIds = selectedOptions.value ? [selectedOptions.value] : [];
  } else {
    // 多选：selectedOptions是数组
    optionIds = Array.isArray(selectedOptions.value) ? selectedOptions.value : [];
  }

  // 验证选择
  if (!optionIds || optionIds.length === 0) {
    alert('请选择投票选项');
    return;
  }

  // 验证选择数量
  if (optionIds.length > selectedVote.value.maxChoices) {
    alert(`最多只能选择${selectedVote.value.maxChoices}个选项`);
    return;
  }

  // 验证选项ID有效性
  const validOptionIds = selectedVote.value.options.map(opt => opt.id || opt.sortOrder);
  const invalidOptions = optionIds.filter(id => !validOptionIds.includes(id));
  if (invalidOptions.length > 0) {
    console.error('❌ 无效的选项ID:', invalidOptions);
    alert('选择的选项无效，请重新选择');
    return;
  }

  isSubmittingVote.value = true;

  try {
    console.log('🔧 开始提交投票');
    console.log('📊 投票信息:', {
      voteId: selectedVote.value.id,
      voteTitle: selectedVote.value.title,
      maxChoices: selectedVote.value.maxChoices,
      selectedOptions: selectedOptions.value,
      optionIds: optionIds,
      availableOptions: selectedVote.value.options
    });

    const result = await submitVote(selectedVote.value.id, optionIds);

    if (result.success) {
      console.log('✅ 投票提交成功:', result);

      // 使用自定义成功提示
      showSuccessModal('投票成功', '您的投票已成功提交，感谢您的参与！');

      closeVoteModal();

      // 重新加载投票数据以显示最新结果
      await loadVotes();
    } else {
      throw new Error(result.message || '投票提交失败');
    }

  } catch (error) {
    console.error('❌ 投票提交失败:', error);

    // 更友好的错误提示
    let errorMsg = error.message || '投票提交失败，请稍后重试';
    if (error.message?.includes('已经投过票')) {
      errorMsg = '您已经参与过此次投票，无法重复投票';
    } else if (error.message?.includes('投票已结束')) {
      errorMsg = '投票活动已结束，无法继续投票';
    } else if (error.message?.includes('认证失败')) {
      errorMsg = '登录状态已过期，请重新登录后再试';
    }

    showErrorModal('投票失败', errorMsg);
  } finally {
    isSubmittingVote.value = false;
  }
};

// 渲染图表
const renderCharts = () => {
  votes.value.forEach(vote => {
    if (currentViewMode.value === 'pie') {
      renderPieChart(vote);
    } else if (currentViewMode.value === 'bar') {
      renderBarChart(vote);
    }
  });
};

// 渲染饼状图
const renderPieChart = (vote) => {
  const canvas = pieChartRefs[vote.id];
  if (!canvas) return;

  // 销毁现有图表
  if (chartInstances[`pie-${vote.id}`]) {
    chartInstances[`pie-${vote.id}`].destroy();
  }

  const ctx = canvas.getContext('2d');
  const data = vote.options.map(option => option.number || 0);
  const labels = vote.options.map(option => option.content);

  const colors = [
    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
    '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
  ];

  chartInstances[`pie-${vote.id}`] = new Chart(ctx, {
    type: 'pie',
    data: {
      labels: labels,
      datasets: [{
        data: data,
        backgroundColor: colors.slice(0, data.length),
        borderWidth: 2,
        borderColor: '#fff'
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom',
          labels: {
            padding: 20,
            usePointStyle: true
          }
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              const total = context.dataset.data.reduce((a, b) => a + b, 0);
              const percentage = total > 0 ? ((context.parsed / total) * 100).toFixed(1) : 0;
              return `${context.label}: ${context.parsed}票 (${percentage}%)`;
            }
          }
        }
      }
    }
  });
};

// 渲染柱状图
const renderBarChart = (vote) => {
  const canvas = barChartRefs[vote.id];
  if (!canvas) return;

  // 销毁现有图表
  if (chartInstances[`bar-${vote.id}`]) {
    chartInstances[`bar-${vote.id}`].destroy();
  }

  const ctx = canvas.getContext('2d');
  const data = vote.options.map(option => option.number || 0);
  const labels = vote.options.map(option => option.content);

  chartInstances[`bar-${vote.id}`] = new Chart(ctx, {
    type: 'bar',
    data: {
      labels: labels,
      datasets: [{
        label: '票数',
        data: data,
        backgroundColor: 'rgba(52, 152, 219, 0.8)',
        borderColor: 'rgba(52, 152, 219, 1)',
        borderWidth: 2,
        borderRadius: 4
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              return `${context.parsed.y}票`;
            }
          }
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            stepSize: 1
          }
        },
        x: {
          ticks: {
            maxRotation: 45,
            minRotation: 0
          }
        }
      }
    }
  });
};

// 监听视图模式变化
watch(currentViewMode, async () => {
  await nextTick();
  renderCharts();
});

// 调试方法
const toggleDebugMode = () => {
  showDebugInfo.value = !showDebugInfo.value;
  if (showDebugInfo.value) {
    updateDebugInfo();
  }
};

const updateDebugInfo = () => {
  const token = getToken();
  debugInfo.tokenStatus = token
    ? { text: '存在', class: 'status-success' }
    : { text: '不存在', class: 'status-error' };
  debugInfo.requestTime = new Date().toLocaleString();
};

const testApiConnection = async () => {
  try {
    const response = await fetch(debugInfo.apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getToken()}`
      }
    });

    const data = await response.json();
    debugInfo.errorDetails = `HTTP ${response.status}: ${JSON.stringify(data, null, 2)}`;

    if (response.ok) {
      alert('API连接测试成功！');
    } else {
      alert(`API连接测试失败：HTTP ${response.status}`);
    }
  } catch (error) {
    debugInfo.errorDetails = `连接异常: ${error.message}`;
    alert(`API连接测试异常：${error.message}`);
  }
};

const checkBackendStatus = async () => {
  try {
    const response = await fetch('http://localhost:8080/api/grids/communities', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getToken()}`
      }
    });

    if (response.ok) {
      alert('后端服务正常运行！');
    } else {
      alert(`后端服务异常：HTTP ${response.status}`);
    }
  } catch (error) {
    alert(`后端服务连接失败：${error.message}\n\n请检查：\n1. 后端服务是否启动\n2. 端口8080是否正确\n3. 网络连接是否正常`);
  }
};

// 全局错误处理
const handleError = (error, context = '') => {
  console.error(`❌ ${context}错误:`, error);

  // 记录到调试信息
  debugInfo.requestTime = new Date().toLocaleString();
  debugInfo.errorDetails = JSON.stringify({
    context,
    message: error.message,
    stack: error.stack,
    name: error.name
  }, null, 2);

  // 显示用户友好的错误信息
  let userMessage = error.message || '发生未知错误';
  if (context) {
    userMessage = `${context}: ${userMessage}`;
  }

  showErrorModal('操作失败', userMessage);
};

// 生命周期
onMounted(() => {
  try {
    loadVotes();
  } catch (error) {
    handleError(error, '页面初始化');
  }
});
</script>

<style scoped>
.budget-vote {
  background: #f5f7fa;
  min-height: 100vh;
  padding: 20px;
}

.page-header {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  text-align: center;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 28px;
  font-weight: 600;
}

.subtitle {
  margin: 0;
  color: #7f8c8d;
  font-size: 16px;
}

.vote-content {
  max-width: 1200px;
  margin: 0 auto;
}

.loading-container {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
  color: #e74c3c;
  border-left: 4px solid #e74c3c;
}

.retry-btn {
  margin-top: 15px;
  padding: 10px 20px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
}

.retry-btn:hover {
  background: #2980b9;
}

.error-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.debug-btn {
  padding: 10px 20px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
}

.debug-btn:hover {
  background: #5a6268;
}

.debug-info {
  margin-top: 20px;
  padding: 20px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
}

.debug-info h4 {
  margin: 0 0 15px 0;
  color: #495057;
}

.debug-item {
  display: flex;
  margin-bottom: 10px;
  align-items: flex-start;
}

.debug-item strong {
  min-width: 100px;
  color: #495057;
}

.debug-item span {
  margin-left: 10px;
}

.debug-item pre {
  margin-left: 10px;
  background: white;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
}

.debug-actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

.test-btn {
  padding: 8px 16px;
  background: #17a2b8;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.test-btn:hover {
  background: #138496;
}

.status-unknown {
  color: #6c757d;
}

.status-success {
  color: #28a745;
  font-weight: bold;
}

.status-error {
  color: #dc3545;
  font-weight: bold;
}

/* 提示模态框样式 */
.message-modal {
  background: white;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.message-modal .modal-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.message-modal h3 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 20px;
}

.message-modal p {
  margin: 0 0 25px 0;
  color: #34495e;
  line-height: 1.6;
}

.modal-btn {
  padding: 12px 30px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s;
}

.modal-btn.success {
  background: #27ae60;
  color: white;
}

.modal-btn.success:hover {
  background: #229954;
}

.modal-btn.error {
  background: #e74c3c;
  color: white;
}

.modal-btn.error:hover {
  background: #c0392b;
}

.option-debug {
  font-size: 12px;
  color: #6c757d;
  margin-left: 8px;
}

.vote-debug-info {
  margin-top: 20px;
  padding: 15px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 14px;
}

.vote-debug-info h5 {
  margin: 0 0 10px 0;
  color: #495057;
}

.vote-debug-info p {
  margin: 5px 0;
  color: #6c757d;
}

.vote-debug-info ul {
  margin: 5px 0;
  padding-left: 20px;
}

.vote-debug-info li {
  color: #6c757d;
  font-size: 12px;
}

.view-controls {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.view-mode-selector {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.mode-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: 2px solid #e9ecef;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.mode-btn:hover {
  border-color: #3498db;
  background: #f8f9fa;
}

.mode-btn.active {
  border-color: #3498db;
  background: #3498db;
  color: white;
}

.mode-icon {
  font-size: 18px;
}

.votes-list {
  display: grid;
  gap: 20px;
}

.vote-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.vote-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.vote-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #e9ecef;
}

.vote-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.pending {
  background: #f39c12;
  color: white;
}

.status-badge.active {
  background: #27ae60;
  color: white;
}

.status-badge.ended {
  background: #95a5a6;
  color: white;
}

.vote-info {
  padding: 20px 30px;
}

.vote-description {
  margin: 0 0 20px 0;
  color: #34495e;
  line-height: 1.6;
}

.vote-meta {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.meta-item {
  display: flex;
  justify-content: space-between;
}

.meta-label {
  color: #7f8c8d;
  font-weight: 500;
}

.meta-value {
  color: #2c3e50;
  font-weight: 600;
}

.vote-results {
  padding: 20px 30px;
  border-top: 1px solid #e9ecef;
}

.vote-results h4 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.results-list {
  display: grid;
  gap: 15px;
}

.result-item {
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: 15px;
  align-items: center;
}

.option-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.option-content {
  color: #2c3e50;
  font-weight: 500;
}

.option-votes {
  color: #7f8c8d;
  font-size: 14px;
}

.progress-bar {
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  min-width: 100px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2980b9);
  transition: width 0.3s ease;
}

.percentage {
  color: #34495e;
  font-weight: 600;
  font-size: 14px;
  min-width: 50px;
  text-align: right;
}

.chart-container {
  display: flex;
  justify-content: center;
  padding: 20px;
}

.pie-chart,
.bar-chart {
  max-width: 400px;
  max-height: 300px;
}

.vote-actions {
  padding: 20px 30px;
  border-top: 1px solid #e9ecef;
  text-align: center;
}

.vote-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: #27ae60;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.vote-btn:hover {
  background: #229954;
}

.btn-icon {
  font-size: 18px;
}

.empty-state {
  text-align: center;
  padding: 80px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.empty-state h3 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 24px;
}

.empty-state p {
  margin: 0;
  color: #7f8c8d;
  font-size: 16px;
}

/* 投票弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.vote-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
}

.close-btn {
  width: 30px;
  height: 30px;
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #7f8c8d;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.close-btn:hover {
  background: #f8f9fa;
  color: #2c3e50;
}

.modal-body {
  padding: 30px;
}

.modal-body .vote-description {
  margin: 0 0 30px 0;
  color: #34495e;
  line-height: 1.6;
  font-size: 16px;
}

.vote-options h4 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.options-list {
  display: grid;
  gap: 15px;
}

.option-label {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px 20px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.option-label:hover {
  border-color: #3498db;
  background: #f8f9fa;
}

.option-label input[type="radio"],
.option-label input[type="checkbox"] {
  margin: 0;
  transform: scale(1.2);
}

.option-label input[type="radio"]:checked,
.option-label input[type="checkbox"]:checked {
  accent-color: #3498db;
}

.option-label:has(input:checked) {
  border-color: #3498db;
  background: #e3f2fd;
}

.option-text {
  color: #2c3e50;
  font-weight: 500;
  font-size: 16px;
}

.modal-footer {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  padding: 20px 30px;
  border-top: 1px solid #e9ecef;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn.primary {
  background: #3498db;
  color: white;
}

.btn.primary:hover {
  background: #2980b9;
}

.btn.primary:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.btn.secondary {
  background: #ecf0f1;
  color: #34495e;
}

.btn.secondary:hover {
  background: #d5dbdb;
}

@media (max-width: 768px) {
  .budget-vote {
    padding: 10px;
  }

  .page-header {
    padding: 20px;
  }

  .vote-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .vote-info,
  .vote-results,
  .vote-actions {
    padding: 15px 20px;
  }

  .vote-meta {
    grid-template-columns: 1fr;
  }

  .meta-item {
    flex-direction: column;
    gap: 5px;
  }

  .result-item {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .view-mode-selector {
    flex-direction: column;
  }

  .modal-overlay {
    padding: 10px;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 20px;
  }

  .modal-footer {
    flex-direction: column;
  }
}
</style>
