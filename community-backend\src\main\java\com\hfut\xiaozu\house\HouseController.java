package com.hfut.xiaozu.house;

import com.hfut.xiaozu.common.Result;
import com.hfut.xiaozu.house.binding.UserHouseBinding;
import com.hfut.xiaozu.house.grid.CreateGridDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-06-25
 */
@Tag( name = "房屋信息")
@RestController
@RequestMapping("/api/grids")
public class HouseController {

    @Resource
    HouseService houseService;

    @Operation(summary = "查询所有社区")
    @GetMapping("/communities")
    public Result<?> listAllCommunities(){
        return houseService.listAllcommunities();
    }

    @Operation(summary = "查询社区所有楼栋")
    @GetMapping("/buildings")
    public Result<?> listAllBuildingsByCommunityId(@RequestParam Long communityId){
        return houseService.listAllBuildings(communityId);
    }

    @Operation(summary = "查询楼栋所有单元")
    @GetMapping("/units")
    public Result<?> listAllUnitsByBuildingId(@RequestParam Long buildingId){
        return houseService.listAllUnits(buildingId);
    }

    @Operation(summary = "查询单元所有房屋")
    @GetMapping("/houses")
    public Result<?> listAllHousesByUnitId(@RequestParam Long unitId){
        return houseService.listAllHouses(unitId);
    }

    @Operation(summary = "绑定指定房屋")
    @PutMapping("/houses/binding/{houseId}")
    public Result<?> bindingHouseByHouseId(@RequestParam Integer relatioType, @PathVariable Long houseId){
        return houseService.bindingHouseByHouseId(houseId,relatioType);
    }

    @Operation(summary = "查询申请绑定房屋列表")
    @GetMapping("/houses/binding/list")
    public Result<?> listBindingHouse(@RequestParam Integer status,
                                      @RequestParam(defaultValue = "1") Integer pageNum,
                                      @RequestParam(defaultValue = "10") Integer pageSize
                                      ){

        if (status != 1 && status != 2 && status != 3 && status != 4) {
            return Result.fail("房屋申请状态值无效");
        }

        return houseService.listBindingHouse(status,pageNum,pageSize);
    }

    @Operation(summary = "查询指定房屋居住情况")
    @GetMapping("/houses/status/{id}")
    public Result<?> getHouseStatus(@PathVariable Long id){

        return houseService.getHouseStatus(id);
    }

    @Operation(summary = "查询指定申请绑定房屋记录详情")
    @GetMapping("/houses/binding/{id}")
    public Result<?> listBindingHouse(@PathVariable Long id){

        return houseService.getBindingHouseRecordById(id);
    }

    @Operation(summary = "更新指定房屋绑定申请记录")
    @PutMapping("/houses/binding/update/{id}")
    public Result<?> updateBindingHouseRecordByid(@PathVariable Long id, @RequestBody UserHouseBinding dto){

        return houseService.updateBindingHouseRecordById(id,dto);
    }

    @Operation(summary = "查询自身申请绑定房屋记录状态")
    @GetMapping("/houses/binding/me")
    public Result<?> get(){
        return houseService.getBindingHouseRecordStatusMyself();
    }

    @Operation(summary = "创建网格")
    @PostMapping
    public Result<?> createNewGrid(@RequestBody CreateGridDTO dto,
                                   BindingResult bindingResult){
        List<String> errors = new ArrayList<>();

        if (bindingResult.hasErrors()) {
            errors.addAll(bindingResult.getAllErrors()
                    .stream()
                    .map(DefaultMessageSourceResolvable::getDefaultMessage)
                    .collect(Collectors.toList()));
        }

        if(!errors.isEmpty()){
            return Result.fail(errors.toString());
        }

        return houseService.createGrid(dto);
    }

    @Operation(summary = "列出所有网格")
    @GetMapping("/listAll")
    public Result<?> listAllGrid(){
        return houseService.listAllGrids();
    }
}
