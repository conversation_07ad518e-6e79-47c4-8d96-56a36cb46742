package com.hfut.xiaozu.common.oss;

import cn.hutool.core.date.DateUtil;
import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.util.Date;

@Component
@Slf4j
public class AliOssUtil {

    @Resource
    private OSS ossClient;

    @Resource
    private OssConfig ossConfig;

    public String upload(byte[] bytes, String objectName) {
        String formattedObjectName = DateUtil.format(new Date(), "yyyy/MM/dd") + "/" + objectName;

        try {
            // 创建PutObject请求。
            ossClient.putObject(ossConfig.getBucketName(), formattedObjectName, new ByteArrayInputStream(bytes));
        } catch (OSSException oe) {
            log.error("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            log.error("Error Message:" + oe.getErrorMessage());
            log.error("Error Code:" + oe.getErrorCode());
            log.error("Request ID:" + oe.getRequestId());
            log.error("Host ID:" + oe.getHostId());
        } catch (ClientException ce) {
            log.error("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            log.error("Error Message:" + ce.getMessage());
        }

        //文件访问路径规则 https://BucketName.Endpoint/ObjectName
        StringBuilder stringBuilder = new StringBuilder("https://");
        stringBuilder
                .append(ossConfig.getBucketName())
                .append(".")
                .append(ossConfig.getEndpoint())
                .append("/")
                .append(formattedObjectName);

        log.info("文件上传到:{}", stringBuilder.toString());

        return stringBuilder.toString();
    }
}
