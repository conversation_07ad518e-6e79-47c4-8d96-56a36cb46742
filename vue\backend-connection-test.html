<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后端连接测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #495057;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        button:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .result.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255,255,255,0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 后端连接测试</h1>
        
        <div class="test-section">
            <h3>1. 基础连接测试</h3>
            <p>测试是否能连接到后端服务器</p>
            <button onclick="testBasicConnection()">测试连接</button>
            <div id="basic-result" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>2. API端点测试</h3>
            <p>测试具体的API端点是否可用</p>
            <button onclick="testApiEndpoint()">测试API端点</button>
            <div id="api-result" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 代理测试</h3>
            <p>测试Vite开发服务器的代理配置</p>
            <button onclick="testProxy()">测试代理</button>
            <div id="proxy-result" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>4. 问题上报API测试</h3>
            <p>测试问题上报API是否可用（需要登录）</p>
            <button onclick="testIncidentApi()">测试问题上报API</button>
            <div id="incident-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 显示结果
        function showResult(elementId, message, type) {
            const resultDiv = document.getElementById(elementId);
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }
        
        // 设置按钮加载状态
        function setButtonLoading(button, loading) {
            if (loading) {
                button.disabled = true;
                if (!button.dataset.originalText) {
                    button.dataset.originalText = button.innerHTML;
                }
                button.innerHTML = '<span class="loading"></span>' + button.dataset.originalText;
            } else {
                button.disabled = false;
                if (button.dataset.originalText) {
                    button.innerHTML = button.dataset.originalText;
                }
            }
        }
        
        // 1. 基础连接测试
        async function testBasicConnection() {
            const button = event.target;
            setButtonLoading(button, true);
            
            try {
                showResult('basic-result', '正在测试连接...', 'info');
                
                const response = await fetch('http://localhost:8080', {
                    method: 'GET',
                    mode: 'no-cors'
                });
                
                showResult('basic-result', '✅ 基础连接成功！\n后端服务器正在运行', 'success');
            } catch (error) {
                showResult('basic-result', `❌ 连接失败！\n错误: ${error.message}\n\n可能原因:\n1. 后端服务器未启动\n2. 端口8080被占用\n3. 防火墙阻止连接`, 'error');
            } finally {
                setButtonLoading(button, false);
            }
        }
        
        // 2. API端点测试
        async function testApiEndpoint() {
            const button = event.target;
            setButtonLoading(button, true);
            
            try {
                showResult('api-result', '正在测试API端点...', 'info');
                
                const response = await fetch('http://localhost:8080/api/auth/test', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('api-result', `✅ API端点测试成功！\n状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult('api-result', `⚠️ API端点响应异常\n状态码: ${response.status}\n状态文本: ${response.statusText}`, 'error');
                }
            } catch (error) {
                showResult('api-result', `❌ API端点测试失败！\n错误: ${error.message}`, 'error');
            } finally {
                setButtonLoading(button, false);
            }
        }
        
        // 3. 代理测试
        async function testProxy() {
            const button = event.target;
            setButtonLoading(button, true);
            
            try {
                showResult('proxy-result', '正在测试代理...', 'info');
                
                const response = await fetch('/api/auth/test', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('proxy-result', `✅ 代理测试成功！\n状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult('proxy-result', `⚠️ 代理响应异常\n状态码: ${response.status}\n状态文本: ${response.statusText}`, 'error');
                }
            } catch (error) {
                showResult('proxy-result', `❌ 代理测试失败！\n错误: ${error.message}`, 'error');
            } finally {
                setButtonLoading(button, false);
            }
        }
        
        // 4. 问题上报API测试
        async function testIncidentApi() {
            const button = event.target;
            setButtonLoading(button, true);
            
            try {
                showResult('incident-result', '正在测试问题上报API...', 'info');
                
                // 测试数据
                const testData = {
                    title: "测试问题",
                    description: "这是一个测试问题",
                    categoryType: 1,
                    priority: 3,
                    locationDescription: "测试位置",
                    locationGeojson: {
                        latitude: 31.774308,
                        longitude: 117.19853
                    },
                    communityId: 1
                };
                
                const response = await fetch('/api/incident', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer test-token'
                    },
                    body: JSON.stringify(testData)
                });
                
                const responseText = await response.text();
                
                if (response.ok) {
                    showResult('incident-result', `✅ 问题上报API测试成功！\n状态码: ${response.status}\n响应: ${responseText}`, 'success');
                } else {
                    showResult('incident-result', `⚠️ 问题上报API响应异常\n状态码: ${response.status}\n状态文本: ${response.statusText}\n响应内容: ${responseText}`, 'error');
                }
            } catch (error) {
                showResult('incident-result', `❌ 问题上报API测试失败！\n错误: ${error.message}`, 'error');
            } finally {
                setButtonLoading(button, false);
            }
        }
    </script>
</body>
</html>
