package com.hfut.xiaozu.user.idv;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-24
 */
@Mapper
public interface IdentityVerifySubmitMapper {
    void insert(IdentityVerifySubmitEntity identityVerifySubmitEntity);

    List<IdentityVerifySubmitEntity> listByStatus(Integer status);

    IdentityVerifySubmitEntity getById(Long id);

    int updateStatusById(IdentityVerifySubmitEntity identityVerifySubmitEntity);

    List<IdentityVerifySubmitEntity> getByUserId(Long userId);
}
