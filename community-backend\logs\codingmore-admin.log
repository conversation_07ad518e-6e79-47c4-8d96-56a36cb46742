2025-07-01 09:22:26.317 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 53 ] - Starting XiaozuApplication using Java 17.0.2 with PID 24220 (D:\gitlab\xiangmu\community-backend\target\classes started by joy0531 in D:\gitlab\xiangmu\community-backend)
2025-07-01 09:22:26.328 [ restartedMain ] - [ DEBUG ] [ com.hfut.xiaozu.XiaozuApplication : 54 ] - Running with Spring Boot v3.4.7, Spring v6.2.8
2025-07-01 09:22:26.328 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 652 ] - No active profile set, falling back to 1 default profile: "default"
2025-07-01 09:22:26.367 [ restartedMain ] - [ INFO  ] [ o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor : 252 ] - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-01 09:22:26.367 [ restartedMain ] - [ INFO  ] [ o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor : 252 ] - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-01 09:22:26.953 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 247 ] - Searching for mappers annotated with @Mapper
2025-07-01 09:22:26.954 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 251 ] - Using auto-configuration base package 'com.hfut.xiaozu'
2025-07-01 09:22:27.104 [ restartedMain ] - [ DEBUG ] [ org.apache.ibatis.logging.LogFactory : 112 ] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-01 09:22:27.115 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.class]
2025-07-01 09:22:27.115 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.class]
2025-07-01 09:22:27.115 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.class]
2025-07-01 09:22:27.115 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.class]
2025-07-01 09:22:27.115 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.class]
2025-07-01 09:22:27.116 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.class]
2025-07-01 09:22:27.116 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.class]
2025-07-01 09:22:27.116 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.class]
2025-07-01 09:22:27.116 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.class]
2025-07-01 09:22:27.116 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.class]
2025-07-01 09:22:27.116 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.class]
2025-07-01 09:22:27.116 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.class]
2025-07-01 09:22:27.116 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.class]
2025-07-01 09:22:27.116 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.class]
2025-07-01 09:22:27.116 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.class]
2025-07-01 09:22:27.117 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleHouseBindingMapper' and 'com.hfut.xiaozu.car.bind.VehicleHouseBindingMapper' mapperInterface
2025-07-01 09:22:27.118 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleInfoMapper' and 'com.hfut.xiaozu.car.info.VehicleInfoMapper' mapperInterface
2025-07-01 09:22:27.119 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'familyMemberMapper' and 'com.hfut.xiaozu.family.info.FamilyMemberMapper' mapperInterface
2025-07-01 09:22:27.119 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userHouseBindingMapper' and 'com.hfut.xiaozu.house.binding.UserHouseBindingMapper' mapperInterface
2025-07-01 09:22:27.119 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityGridMapper' and 'com.hfut.xiaozu.house.grid.CommunityGridMapper' mapperInterface
2025-07-01 09:22:27.119 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'gridItemArchiveMapper' and 'com.hfut.xiaozu.house.grid.GridItemArchiveMapper' mapperInterface
2025-07-01 09:22:27.121 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'buildingInfoMapper' and 'com.hfut.xiaozu.house.information.BuildingInfoMapper' mapperInterface
2025-07-01 09:22:27.121 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityInfoMapper' and 'com.hfut.xiaozu.house.information.CommunityInfoMapper' mapperInterface
2025-07-01 09:22:27.121 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'houseInfoMapper' and 'com.hfut.xiaozu.house.information.HouseInfoMapper' mapperInterface
2025-07-01 09:22:27.121 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'unitInfoMapper' and 'com.hfut.xiaozu.house.information.UnitInfoMapper' mapperInterface
2025-07-01 09:22:27.122 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentProcessRecordMapper' and 'com.hfut.xiaozu.incident.process.IncidentProcessRecordMapper' mapperInterface
2025-07-01 09:22:27.122 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentRecordMapper' and 'com.hfut.xiaozu.incident.record.IncidentRecordMapper' mapperInterface
2025-07-01 09:22:27.123 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userMapper' and 'com.hfut.xiaozu.user.account.UserMapper' mapperInterface
2025-07-01 09:22:27.123 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityOcrRecordMapper' and 'com.hfut.xiaozu.user.idv.IdentityOcrRecordMapper' mapperInterface
2025-07-01 09:22:27.123 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityVerifySubmitMapper' and 'com.hfut.xiaozu.user.idv.IdentityVerifySubmitMapper' mapperInterface
2025-07-01 09:22:27.475 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 111 ] - Tomcat initialized with port 8080 (http)
2025-07-01 09:22:27.485 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardService : 168 ] - Starting service [Tomcat]
2025-07-01 09:22:27.487 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardEngine : 168 ] - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-01 09:22:27.529 [ restartedMain ] - [ INFO  ] [ o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] : 168 ] - Initializing Spring embedded WebApplicationContext
2025-07-01 09:22:27.530 [ restartedMain ] - [ INFO  ] [ o.s.b.w.s.c.ServletWebServerApplicationContext : 301 ] - Root WebApplicationContext: initialization completed in 1162 ms
2025-07-01 09:22:27.591 [ restartedMain ] - [ INFO  ] [ c.a.d.s.b.a.DruidDataSourceAutoConfigure : 55 ] - Init DruidDataSource
2025-07-01 09:22:27.936 [ restartedMain ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 1009 ] - {dataSource-1} inited
2025-07-01 09:22:28.087 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.xml]'
2025-07-01 09:22:28.096 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.xml]'
2025-07-01 09:22:28.106 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.xml]'
2025-07-01 09:22:28.115 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.xml]'
2025-07-01 09:22:28.123 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.xml]'
2025-07-01 09:22:28.130 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.xml]'
2025-07-01 09:22:28.143 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.xml]'
2025-07-01 09:22:28.151 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.xml]'
2025-07-01 09:22:28.160 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.xml]'
2025-07-01 09:22:28.167 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.xml]'
2025-07-01 09:22:28.174 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.xml]'
2025-07-01 09:22:28.183 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.xml]'
2025-07-01 09:22:28.191 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.xml]'
2025-07-01 09:22:28.196 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.xml]'
2025-07-01 09:22:28.201 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.xml]'
2025-07-01 09:22:28.714 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 319 ] - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-01 09:22:28.879 [ restartedMain ] - [ INFO  ] [ o.s.b.d.autoconfigure.OptionalLiveReloadServer : 59 ] - LiveReload server is running on port 35729
2025-07-01 09:22:28.924 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 243 ] - Tomcat started on port 8080 (http) with context path '/'
2025-07-01 09:22:28.932 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 59 ] - Started XiaozuApplication in 2.973 seconds (process running for 4.177)
2025-07-01 09:36:52.209 [ http-nio-8080-exec-1 ] - [ INFO  ] [ o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] : 168 ] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-01 09:36:52.209 [ http-nio-8080-exec-1 ] - [ INFO  ] [ org.springframework.web.servlet.DispatcherServlet : 532 ] - Initializing Servlet 'dispatcherServlet'
2025-07-01 09:36:52.210 [ http-nio-8080-exec-1 ] - [ INFO  ] [ org.springframework.web.servlet.DispatcherServlet : 554 ] - Completed initialization in 1 ms
2025-07-01 09:36:54.624 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/houses/binding/me
2025-07-01 09:36:54.633 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 09:36:54.674 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/communities
2025-07-01 09:36:54.680 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-07-01 09:36:58.889 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/buildings
2025-07-01 09:36:58.893 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/buildings
2025-07-01 09:37:02.705 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/buildings
2025-07-01 09:37:02.710 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/buildings
2025-07-01 09:37:04.384 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/units
2025-07-01 09:37:04.390 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/units
2025-07-01 09:37:05.947 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/buildings
2025-07-01 09:37:05.950 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/buildings
2025-07-01 09:37:07.355 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/units
2025-07-01 09:37:07.359 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/units
2025-07-01 09:37:08.545 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/houses
2025-07-01 09:37:08.549 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses
2025-07-01 09:37:10.622 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/houses
2025-07-01 09:37:10.626 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses
2025-07-01 09:37:13.689 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/units
2025-07-01 09:37:13.694 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/units
2025-07-01 09:37:15.092 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/buildings
2025-07-01 09:37:16.566 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/units
2025-07-01 09:37:16.570 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/units
2025-07-01 09:37:17.860 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/houses
2025-07-01 09:37:17.864 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses
2025-07-01 09:37:19.157 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/houses/status/2613
2025-07-01 09:37:19.161 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/status/2613
2025-07-01 09:37:22.308 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/houses/binding/2613
2025-07-01 09:37:22.312 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: PUT /api/grids/houses/binding/2613
2025-07-01 09:37:24.041 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 09:39:57.323 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-01 09:39:57.855 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-01 09:39:58.202 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 09:39:58.215 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-07-01 09:39:59.432 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-01 09:39:59.822 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-01 09:40:00.176 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 09:40:00.187 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-07-01 09:52:51.080 [ File Watcher ] - [ INFO  ] [ o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener : 211 ] - Restarting due to 100 class path changes (0 additions, 100 deletions, 0 modifications)
2025-07-01 09:52:51.089 [ Thread-5 ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 54 ] - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-01 09:52:51.239 [ tomcat-shutdown ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 76 ] - Graceful shutdown complete
2025-07-01 09:52:51.266 [ Thread-5 ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2174 ] - {dataSource-1} closing ...
2025-07-01 09:52:51.282 [ Thread-5 ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2247 ] - {dataSource-1} closed
2025-07-01 09:52:57.133 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 53 ] - Starting XiaozuApplication using Java 17.0.2 with PID 24220 (D:\gitlab\xiangmu\community-backend\target\classes started by joy0531 in D:\gitlab\xiangmu\community-backend)
2025-07-01 09:52:57.134 [ restartedMain ] - [ DEBUG ] [ com.hfut.xiaozu.XiaozuApplication : 54 ] - Running with Spring Boot v3.4.7, Spring v6.2.8
2025-07-01 09:52:57.134 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 652 ] - No active profile set, falling back to 1 default profile: "default"
2025-07-01 09:52:57.633 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 247 ] - Searching for mappers annotated with @Mapper
2025-07-01 09:52:57.633 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 251 ] - Using auto-configuration base package 'com.hfut.xiaozu'
2025-07-01 09:52:57.791 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.class]
2025-07-01 09:52:57.791 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.class]
2025-07-01 09:52:57.792 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.class]
2025-07-01 09:52:57.792 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.class]
2025-07-01 09:52:57.792 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.class]
2025-07-01 09:52:57.792 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.class]
2025-07-01 09:52:57.792 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.class]
2025-07-01 09:52:57.792 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.class]
2025-07-01 09:52:57.792 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.class]
2025-07-01 09:52:57.792 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.class]
2025-07-01 09:52:57.793 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.class]
2025-07-01 09:52:57.793 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.class]
2025-07-01 09:52:57.793 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.class]
2025-07-01 09:52:57.793 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.class]
2025-07-01 09:52:57.795 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.class]
2025-07-01 09:52:57.795 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleHouseBindingMapper' and 'com.hfut.xiaozu.car.bind.VehicleHouseBindingMapper' mapperInterface
2025-07-01 09:52:57.796 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleInfoMapper' and 'com.hfut.xiaozu.car.info.VehicleInfoMapper' mapperInterface
2025-07-01 09:52:57.797 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'familyMemberMapper' and 'com.hfut.xiaozu.family.info.FamilyMemberMapper' mapperInterface
2025-07-01 09:52:57.797 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userHouseBindingMapper' and 'com.hfut.xiaozu.house.binding.UserHouseBindingMapper' mapperInterface
2025-07-01 09:52:57.798 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityGridMapper' and 'com.hfut.xiaozu.house.grid.CommunityGridMapper' mapperInterface
2025-07-01 09:52:57.799 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'gridItemArchiveMapper' and 'com.hfut.xiaozu.house.grid.GridItemArchiveMapper' mapperInterface
2025-07-01 09:52:57.799 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'buildingInfoMapper' and 'com.hfut.xiaozu.house.information.BuildingInfoMapper' mapperInterface
2025-07-01 09:52:57.800 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityInfoMapper' and 'com.hfut.xiaozu.house.information.CommunityInfoMapper' mapperInterface
2025-07-01 09:52:57.800 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'houseInfoMapper' and 'com.hfut.xiaozu.house.information.HouseInfoMapper' mapperInterface
2025-07-01 09:52:57.801 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'unitInfoMapper' and 'com.hfut.xiaozu.house.information.UnitInfoMapper' mapperInterface
2025-07-01 09:52:57.803 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentProcessRecordMapper' and 'com.hfut.xiaozu.incident.process.IncidentProcessRecordMapper' mapperInterface
2025-07-01 09:52:57.803 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentRecordMapper' and 'com.hfut.xiaozu.incident.record.IncidentRecordMapper' mapperInterface
2025-07-01 09:52:57.804 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userMapper' and 'com.hfut.xiaozu.user.account.UserMapper' mapperInterface
2025-07-01 09:52:57.805 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityOcrRecordMapper' and 'com.hfut.xiaozu.user.idv.IdentityOcrRecordMapper' mapperInterface
2025-07-01 09:52:57.806 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityVerifySubmitMapper' and 'com.hfut.xiaozu.user.idv.IdentityVerifySubmitMapper' mapperInterface
2025-07-01 09:52:57.937 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 111 ] - Tomcat initialized with port 8080 (http)
2025-07-01 09:52:57.938 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardService : 168 ] - Starting service [Tomcat]
2025-07-01 09:52:57.938 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardEngine : 168 ] - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-01 09:52:57.975 [ restartedMain ] - [ INFO  ] [ o.a.c.c.ContainerBase.[Tomcat-2].[localhost].[/] : 168 ] - Initializing Spring embedded WebApplicationContext
2025-07-01 09:52:57.975 [ restartedMain ] - [ INFO  ] [ o.s.b.w.s.c.ServletWebServerApplicationContext : 301 ] - Root WebApplicationContext: initialization completed in 837 ms
2025-07-01 09:52:58.009 [ restartedMain ] - [ INFO  ] [ c.a.d.s.b.a.DruidDataSourceAutoConfigure : 55 ] - Init DruidDataSource
2025-07-01 09:52:58.226 [ restartedMain ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 1009 ] - {dataSource-2} inited
2025-07-01 09:52:58.290 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.xml]'
2025-07-01 09:52:58.299 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.xml]'
2025-07-01 09:52:58.306 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.xml]'
2025-07-01 09:52:58.314 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.xml]'
2025-07-01 09:52:58.322 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.xml]'
2025-07-01 09:52:58.329 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.xml]'
2025-07-01 09:52:58.335 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.xml]'
2025-07-01 09:52:58.345 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.xml]'
2025-07-01 09:52:58.355 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.xml]'
2025-07-01 09:52:58.362 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.xml]'
2025-07-01 09:52:58.379 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.xml]'
2025-07-01 09:52:58.390 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.xml]'
2025-07-01 09:52:58.398 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.xml]'
2025-07-01 09:52:58.405 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.xml]'
2025-07-01 09:52:58.408 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.xml]'
2025-07-01 09:52:58.707 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 319 ] - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-01 09:52:58.794 [ restartedMain ] - [ INFO  ] [ o.s.b.d.autoconfigure.OptionalLiveReloadServer : 59 ] - LiveReload server is running on port 35729
2025-07-01 09:52:58.849 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 243 ] - Tomcat started on port 8080 (http) with context path '/'
2025-07-01 09:52:58.855 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 59 ] - Started XiaozuApplication in 1.797 seconds (process running for 1834.101)
2025-07-01 09:52:58.859 [ restartedMain ] - [ INFO  ] [ o.s.b.d.a.ConditionEvaluationDeltaLoggingListener : 63 ] - Condition evaluation unchanged
2025-07-01 09:53:13.239 [ File Watcher ] - [ INFO  ] [ o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener : 211 ] - Restarting due to 100 class path changes (0 additions, 100 deletions, 0 modifications)
2025-07-01 09:53:13.250 [ Thread-7 ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 54 ] - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-01 09:53:13.405 [ tomcat-shutdown ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 76 ] - Graceful shutdown complete
2025-07-01 09:53:13.422 [ Thread-7 ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2174 ] - {dataSource-2} closing ...
2025-07-01 09:53:13.427 [ Thread-7 ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2247 ] - {dataSource-2} closed
2025-07-01 09:53:19.219 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 53 ] - Starting XiaozuApplication using Java 17.0.2 with PID 24220 (D:\gitlab\xiangmu\community-backend\target\classes started by joy0531 in D:\gitlab\xiangmu\community-backend)
2025-07-01 09:53:19.219 [ restartedMain ] - [ DEBUG ] [ com.hfut.xiaozu.XiaozuApplication : 54 ] - Running with Spring Boot v3.4.7, Spring v6.2.8
2025-07-01 09:53:19.219 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 652 ] - No active profile set, falling back to 1 default profile: "default"
2025-07-01 09:53:19.573 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 247 ] - Searching for mappers annotated with @Mapper
2025-07-01 09:53:19.574 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 251 ] - Using auto-configuration base package 'com.hfut.xiaozu'
2025-07-01 09:53:19.700 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.class]
2025-07-01 09:53:19.700 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.class]
2025-07-01 09:53:19.700 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.class]
2025-07-01 09:53:19.700 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.class]
2025-07-01 09:53:19.700 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.class]
2025-07-01 09:53:19.700 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.class]
2025-07-01 09:53:19.700 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.class]
2025-07-01 09:53:19.701 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.class]
2025-07-01 09:53:19.701 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.class]
2025-07-01 09:53:19.701 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.class]
2025-07-01 09:53:19.701 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.class]
2025-07-01 09:53:19.701 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.class]
2025-07-01 09:53:19.701 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.class]
2025-07-01 09:53:19.701 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.class]
2025-07-01 09:53:19.701 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.class]
2025-07-01 09:53:19.702 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleHouseBindingMapper' and 'com.hfut.xiaozu.car.bind.VehicleHouseBindingMapper' mapperInterface
2025-07-01 09:53:19.703 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleInfoMapper' and 'com.hfut.xiaozu.car.info.VehicleInfoMapper' mapperInterface
2025-07-01 09:53:19.703 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'familyMemberMapper' and 'com.hfut.xiaozu.family.info.FamilyMemberMapper' mapperInterface
2025-07-01 09:53:19.704 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userHouseBindingMapper' and 'com.hfut.xiaozu.house.binding.UserHouseBindingMapper' mapperInterface
2025-07-01 09:53:19.705 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityGridMapper' and 'com.hfut.xiaozu.house.grid.CommunityGridMapper' mapperInterface
2025-07-01 09:53:19.705 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'gridItemArchiveMapper' and 'com.hfut.xiaozu.house.grid.GridItemArchiveMapper' mapperInterface
2025-07-01 09:53:19.706 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'buildingInfoMapper' and 'com.hfut.xiaozu.house.information.BuildingInfoMapper' mapperInterface
2025-07-01 09:53:19.707 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityInfoMapper' and 'com.hfut.xiaozu.house.information.CommunityInfoMapper' mapperInterface
2025-07-01 09:53:19.707 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'houseInfoMapper' and 'com.hfut.xiaozu.house.information.HouseInfoMapper' mapperInterface
2025-07-01 09:53:19.708 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'unitInfoMapper' and 'com.hfut.xiaozu.house.information.UnitInfoMapper' mapperInterface
2025-07-01 09:53:19.710 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentProcessRecordMapper' and 'com.hfut.xiaozu.incident.process.IncidentProcessRecordMapper' mapperInterface
2025-07-01 09:53:19.711 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentRecordMapper' and 'com.hfut.xiaozu.incident.record.IncidentRecordMapper' mapperInterface
2025-07-01 09:53:19.711 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userMapper' and 'com.hfut.xiaozu.user.account.UserMapper' mapperInterface
2025-07-01 09:53:19.712 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityOcrRecordMapper' and 'com.hfut.xiaozu.user.idv.IdentityOcrRecordMapper' mapperInterface
2025-07-01 09:53:19.713 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityVerifySubmitMapper' and 'com.hfut.xiaozu.user.idv.IdentityVerifySubmitMapper' mapperInterface
2025-07-01 09:53:19.841 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 111 ] - Tomcat initialized with port 8080 (http)
2025-07-01 09:53:19.843 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardService : 168 ] - Starting service [Tomcat]
2025-07-01 09:53:19.843 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardEngine : 168 ] - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-01 09:53:19.880 [ restartedMain ] - [ INFO  ] [ o.a.c.c.ContainerBase.[Tomcat-4].[localhost].[/] : 168 ] - Initializing Spring embedded WebApplicationContext
2025-07-01 09:53:19.880 [ restartedMain ] - [ INFO  ] [ o.s.b.w.s.c.ServletWebServerApplicationContext : 301 ] - Root WebApplicationContext: initialization completed in 657 ms
2025-07-01 09:53:19.913 [ restartedMain ] - [ INFO  ] [ c.a.d.s.b.a.DruidDataSourceAutoConfigure : 55 ] - Init DruidDataSource
2025-07-01 09:53:20.098 [ restartedMain ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 1009 ] - {dataSource-3} inited
2025-07-01 09:53:20.162 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.xml]'
2025-07-01 09:53:20.170 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.xml]'
2025-07-01 09:53:20.176 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.xml]'
2025-07-01 09:53:20.185 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.xml]'
2025-07-01 09:53:20.191 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.xml]'
2025-07-01 09:53:20.198 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.xml]'
2025-07-01 09:53:20.204 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.xml]'
2025-07-01 09:53:20.212 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.xml]'
2025-07-01 09:53:20.222 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.xml]'
2025-07-01 09:53:20.230 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.xml]'
2025-07-01 09:53:20.237 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.xml]'
2025-07-01 09:53:20.245 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.xml]'
2025-07-01 09:53:20.253 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.xml]'
2025-07-01 09:53:20.259 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.xml]'
2025-07-01 09:53:20.266 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.xml]'
2025-07-01 09:53:20.510 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 319 ] - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-01 09:53:20.588 [ restartedMain ] - [ INFO  ] [ o.s.b.d.autoconfigure.OptionalLiveReloadServer : 59 ] - LiveReload server is running on port 35729
2025-07-01 09:53:20.639 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 243 ] - Tomcat started on port 8080 (http) with context path '/'
2025-07-01 09:53:20.647 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 59 ] - Started XiaozuApplication in 1.488 seconds (process running for 1855.892)
2025-07-01 09:53:20.649 [ restartedMain ] - [ INFO  ] [ o.s.b.d.a.ConditionEvaluationDeltaLoggingListener : 63 ] - Condition evaluation unchanged
2025-07-01 09:59:42.770 [ http-nio-8080-exec-1 ] - [ INFO  ] [ o.a.c.c.ContainerBase.[Tomcat-4].[localhost].[/] : 168 ] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-01 09:59:42.770 [ http-nio-8080-exec-1 ] - [ INFO  ] [ org.springframework.web.servlet.DispatcherServlet : 532 ] - Initializing Servlet 'dispatcherServlet'
2025-07-01 09:59:42.770 [ http-nio-8080-exec-1 ] - [ INFO  ] [ org.springframework.web.servlet.DispatcherServlet : 554 ] - Completed initialization in 0 ms
2025-07-01 09:59:45.005 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 09:59:45.023 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-07-01 09:59:45.703 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-01 10:03:22.365 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:03:22.383 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-07-01 10:03:24.221 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/auth/me
2025-07-01 10:03:24.229 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/me
2025-07-01 10:03:24.243 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:05:28.936 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/me
2025-07-01 10:05:28.982 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:05:50.555 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/me
2025-07-01 10:05:50.572 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:06:00.703 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-01 10:07:05.781 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/auth/identity/me
2025-07-01 10:07:05.785 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: PUT /api/auth/identity/me
2025-07-01 10:07:54.024 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/auth/ocr
2025-07-01 10:07:54.064 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/auth/ocr
2025-07-01 10:07:54.710 [ http-nio-8080-exec-5 ] - [ INFO  ] [ com.hfut.xiaozu.common.oss.AliOssUtil : 53 ] - 文件上传到:https://qiaozekai.oss-cn-nanjing.aliyuncs.com/2025/07/01/100754075..jpg
2025-07-01 10:07:55.580 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.common.ocr.IdCardOCR : 99 ] - {"words_result":{"姓名":{"words":"天童爱丽丝","location":{"top":59,"left":101,"width":113,"height":25}},"民族":{"words":"","location":{"top":0,"left":0,"width":0,"height":0}},"住址":{"words":"沃托斯市千禧年科技学院研究学习区游戏开发部","location":{"top":190,"left":99,"width":209,"height":53}},"公民身份号码":{"words":"110381202103250420","location":{"top":301,"left":173,"width":330,"height":22}},"出生":{"words":"20210325","location":{"top":148,"left":102,"width":204,"height":20}},"性别":{"words":"女","location":{"top":105,"left":99,"width":18,"height":22}}},"words_result_num":6,"idcard_number_type":1,"image_status":"normal","log_id":1939868543084637731}
2025-07-01 10:07:59.773 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/auth/identity-verification
2025-07-01 10:07:59.778 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/auth/identity-verification
2025-07-01 10:08:02.331 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: PUT /api/auth/identity/me
2025-07-01 10:08:05.307 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:08:05.323 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-07-01 10:08:06.379 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:08:09.909 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/buildings
2025-07-01 10:08:09.912 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/buildings
2025-07-01 10:08:10.675 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/units
2025-07-01 10:08:10.679 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/units
2025-07-01 10:08:11.450 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/houses
2025-07-01 10:08:11.454 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses
2025-07-01 10:08:12.212 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/houses/status/2832
2025-07-01 10:08:12.217 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/status/2832
2025-07-01 10:08:14.347 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/houses/binding/2832
2025-07-01 10:08:14.351 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: PUT /api/grids/houses/binding/2832
2025-07-01 10:08:16.368 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:08:17.415 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-01 10:08:17.871 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-01 10:17:09.477 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-01 10:18:26.798 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:18:26.810 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-07-01 10:18:28.055 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-01 10:18:29.289 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:18:29.300 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-07-01 10:25:06.951 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:25:06.965 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-07-01 10:25:07.968 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:25:07.983 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-07-01 10:25:08.583 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:25:09.916 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-01 10:25:10.395 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-01 10:25:12.143 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-01 10:25:13.228 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/me
2025-07-01 10:25:13.242 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:26:43.915 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/me
2025-07-01 10:26:43.956 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:28:15.811 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/me
2025-07-01 10:28:15.920 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:28:38.700 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/me
2025-07-01 10:28:38.719 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:28:41.927 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/auth/logout
2025-07-01 10:28:41.931 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/auth/logout
2025-07-01 10:28:41.966 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /error
2025-07-01 10:29:39.076 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/auth/logout
2025-07-01 10:29:39.084 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /error
2025-07-01 10:32:37.201 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/me
2025-07-01 10:32:37.216 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:32:50.475 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-01 10:33:00.954 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-01 10:33:03.118 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-07-01 10:33:03.119 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-01 10:33:42.191 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/grids
2025-07-01 10:33:42.226 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.house.HouseService : 276 ] - [{"longitude":117.195533,"latitude":31.776427},{"longitude":117.196949,"latitude":31.775059},{"longitude":117.199331,"latitude":31.775862},{"longitude":117.200597,"latitude":31.777728},{"longitude":117.200376,"latitude":31.779542},{"longitude":117.195613,"latitude":31.779617}]
2025-07-01 10:33:42.245 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-01 10:33:43.830 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-01 10:34:33.799 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-07-01 10:34:33.821 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-07-01 10:34:33.847 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-07-01 10:34:33.866 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-07-01 10:34:33.882 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-07-01 10:34:35.355 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-01 10:34:37.221 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-01 10:34:37.896 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-01 10:34:42.130 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-01 10:34:44.463 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/auth/list
2025-07-01 10:34:44.468 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/list
2025-07-01 10:34:48.994 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/auth/update/17
2025-07-01 10:34:48.998 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: PUT /api/auth/update/17
2025-07-01 10:34:49.042 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/list
2025-07-01 10:34:51.613 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/houses/binding/list
2025-07-01 10:34:51.624 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/list
2025-07-01 10:34:56.866 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/houses/binding/update/16
2025-07-01 10:34:56.870 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: PUT /api/grids/houses/binding/update/16
2025-07-01 10:34:56.886 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/list
2025-07-01 10:34:58.348 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/car/binding/list/1
2025-07-01 10:34:58.356 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/binding/list/1
2025-07-01 10:35:01.604 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/car/binding/list/2
2025-07-01 10:35:01.607 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/binding/list/2
2025-07-01 10:35:01.996 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/car/binding/list/3
2025-07-01 10:35:01.999 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/binding/list/3
2025-07-01 10:35:02.419 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/binding/list/1
2025-07-01 10:35:05.337 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-01 10:35:38.761 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/me
2025-07-01 10:35:38.773 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:37:49.604 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/me
2025-07-01 10:37:49.622 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/houses/binding/me
2025-07-01 10:37:49.626 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:38:30.196 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:38:30.211 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/communities
2025-07-01 10:38:30.215 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-07-01 10:39:02.117 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/me
2025-07-01 10:39:02.130 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:39:20.188 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-01 10:39:30.348 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/me
2025-07-01 10:39:30.361 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:39:46.140 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-01 10:42:01.311 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/me
2025-07-01 10:42:01.326 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:42:03.492 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-01 10:42:23.374 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/car/add
2025-07-01 10:42:26.888 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-01 10:42:37.325 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:42:43.215 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/car/binding
2025-07-01 10:42:44.359 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-01 10:42:47.678 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-01 10:43:14.110 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/family
2025-07-01 10:43:15.411 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-01 10:43:59.573 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/me
2025-07-01 10:43:59.587 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:44:03.048 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/me
2025-07-01 10:44:03.099 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:44:05.875 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-01 10:44:08.465 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-01 10:44:09.177 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:44:09.191 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-07-01 10:44:12.835 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/me
2025-07-01 10:44:12.848 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:44:16.261 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-01 10:44:20.477 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/me
2025-07-01 10:44:20.489 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:44:25.108 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-01 10:44:26.922 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/me
2025-07-01 10:44:26.935 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:44:38.566 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 10:44:38.566 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 10:44:38.940 [ http-nio-8080-exec-8 ] - [ INFO  ] [ org.springdoc.api.AbstractOpenApiResource : 390 ] - Init duration for springdoc-openapi is: 322 ms
2025-07-01 10:47:22.795 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 10:47:22.795 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 10:47:29.625 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 10:47:29.625 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 10:48:03.186 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 10:48:03.186 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 10:48:50.769 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 10:48:50.769 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 10:48:51.367 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 10:48:51.367 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 10:48:52.571 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 10:48:52.571 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 10:49:00.825 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-01 10:49:02.558 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:49:02.571 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-07-01 10:49:06.347 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-01 10:49:23.403 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/family
2025-07-01 10:49:31.204 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-01 10:51:45.929 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-01 10:51:59.414 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/car/add
2025-07-01 10:52:00.411 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-01 10:52:17.086 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/me
2025-07-01 10:52:17.103 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:52:52.198 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/me
2025-07-01 10:52:52.213 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:52:59.132 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 10:52:59.132 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 10:53:02.359 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 10:53:02.359 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 10:53:03.138 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 10:53:03.139 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 10:53:09.220 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 10:53:09.220 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 10:53:09.653 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 10:53:09.653 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 10:53:11.041 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 10:53:11.041 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 10:53:11.489 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 10:53:11.489 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 10:53:12.019 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 10:53:12.019 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 10:53:12.408 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 10:53:12.408 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 10:53:16.038 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/me
2025-07-01 10:53:16.052 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:53:19.124 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 10:53:19.125 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 10:53:23.251 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 10:53:23.251 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 10:53:36.293 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/me
2025-07-01 10:53:36.306 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:53:36.312 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-01 10:53:36.313 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-01 10:53:59.452 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-01 10:54:04.329 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-01 10:54:08.615 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-01 10:54:47.339 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-01 10:55:15.869 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-01 10:55:28.716 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-01 10:55:45.315 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-01 10:55:46.916 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-01 10:55:47.415 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-01 10:55:47.808 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-01 10:55:48.376 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:55:48.398 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-07-01 10:55:49.676 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 10:55:49.693 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-07-01 10:55:50.027 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-01 10:55:50.428 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-01 11:01:02.007 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-01 11:01:06.016 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-01 11:01:07.880 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: DELETE /api/family
2025-07-01 11:01:07.894 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-01 11:01:11.084 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-01 11:01:47.809 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/family
2025-07-01 11:01:47.830 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-01 11:01:51.582 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: DELETE /api/family
2025-07-01 11:01:51.598 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-01 11:04:16.917 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-01 11:04:17.326 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 11:04:17.340 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-07-01 11:04:29.542 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-01 11:04:29.962 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-01 11:04:30.847 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-01 11:04:30.847 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-07-01 11:05:00.704 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/grids
2025-07-01 11:05:00.708 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.house.HouseService : 276 ] - [{"longitude":117.205796,"latitude":31.774596},{"longitude":117.205874,"latitude":31.773411},{"longitude":117.20672,"latitude":31.773437},{"longitude":117.206657,"latitude":31.774586}]
2025-07-01 11:05:00.743 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-01 11:05:00.769 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-01 11:11:22.221 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-01 11:11:25.673 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-07-01 11:11:25.676 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-01 11:11:31.746 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-01 11:11:34.704 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-01 11:18:08.702 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-07-01 11:18:08.712 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-07-01 11:18:08.760 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-07-01 11:18:08.773 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-07-01 11:18:08.788 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-07-01 11:18:09.121 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-01 11:18:10.345 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-07-01 11:18:10.352 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-07-01 11:18:10.384 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-07-01 11:18:10.398 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-07-01 11:18:10.411 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-07-01 11:18:11.147 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-01 11:18:11.533 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          2025-07-01 14:53:10.876 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 53 ] - Starting XiaozuApplication using Java 17.0.2 with PID 9596 (D:\gitlab\xiangmu\community-backend\target\classes started by joy0531 in D:\gitlab\xiangmu\community-backend)
2025-07-01 14:53:10.878 [ restartedMain ] - [ DEBUG ] [ com.hfut.xiaozu.XiaozuApplication : 54 ] - Running with Spring Boot v3.4.7, Spring v6.2.8
2025-07-01 14:53:10.879 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 652 ] - No active profile set, falling back to 1 default profile: "default"
2025-07-01 14:53:10.929 [ restartedMain ] - [ INFO  ] [ o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor : 252 ] - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-01 14:53:10.929 [ restartedMain ] - [ INFO  ] [ o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor : 252 ] - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-01 14:53:11.757 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 247 ] - Searching for mappers annotated with @Mapper
2025-07-01 14:53:11.757 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 251 ] - Using auto-configuration base package 'com.hfut.xiaozu'
2025-07-01 14:53:12.014 [ restartedMain ] - [ DEBUG ] [ org.apache.ibatis.logging.LogFactory : 112 ] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-01 14:53:12.034 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.class]
2025-07-01 14:53:12.035 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.class]
2025-07-01 14:53:12.035 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.class]
2025-07-01 14:53:12.035 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.class]
2025-07-01 14:53:12.035 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.class]
2025-07-01 14:53:12.035 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.class]
2025-07-01 14:53:12.035 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.class]
2025-07-01 14:53:12.035 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.class]
2025-07-01 14:53:12.036 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.class]
2025-07-01 14:53:12.036 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.class]
2025-07-01 14:53:12.036 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.class]
2025-07-01 14:53:12.036 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.class]
2025-07-01 14:53:12.036 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.class]
2025-07-01 14:53:12.036 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.class]
2025-07-01 14:53:12.036 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.class]
2025-07-01 14:53:12.038 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\info\VoteMapper.class]
2025-07-01 14:53:12.038 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\info\VoteOptionMapper.class]
2025-07-01 14:53:12.038 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\info\VoteScopeMapper.class]
2025-07-01 14:53:12.038 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\record\VoteRecordMapper.class]
2025-07-01 14:53:12.039 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleHouseBindingMapper' and 'com.hfut.xiaozu.car.bind.VehicleHouseBindingMapper' mapperInterface
2025-07-01 14:53:12.041 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleInfoMapper' and 'com.hfut.xiaozu.car.info.VehicleInfoMapper' mapperInterface
2025-07-01 14:53:12.043 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'familyMemberMapper' and 'com.hfut.xiaozu.family.info.FamilyMemberMapper' mapperInterface
2025-07-01 14:53:12.044 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userHouseBindingMapper' and 'com.hfut.xiaozu.house.binding.UserHouseBindingMapper' mapperInterface
2025-07-01 14:53:12.045 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityGridMapper' and 'com.hfut.xiaozu.house.grid.CommunityGridMapper' mapperInterface
2025-07-01 14:53:12.045 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'gridItemArchiveMapper' and 'com.hfut.xiaozu.house.grid.GridItemArchiveMapper' mapperInterface
2025-07-01 14:53:12.047 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'buildingInfoMapper' and 'com.hfut.xiaozu.house.information.BuildingInfoMapper' mapperInterface
2025-07-01 14:53:12.049 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityInfoMapper' and 'com.hfut.xiaozu.house.information.CommunityInfoMapper' mapperInterface
2025-07-01 14:53:12.050 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'houseInfoMapper' and 'com.hfut.xiaozu.house.information.HouseInfoMapper' mapperInterface
2025-07-01 14:53:12.051 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'unitInfoMapper' and 'com.hfut.xiaozu.house.information.UnitInfoMapper' mapperInterface
2025-07-01 14:53:12.051 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentProcessRecordMapper' and 'com.hfut.xiaozu.incident.process.IncidentProcessRecordMapper' mapperInterface
2025-07-01 14:53:12.052 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentRecordMapper' and 'com.hfut.xiaozu.incident.record.IncidentRecordMapper' mapperInterface
2025-07-01 14:53:12.053 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userMapper' and 'com.hfut.xiaozu.user.account.UserMapper' mapperInterface
2025-07-01 14:53:12.053 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityOcrRecordMapper' and 'com.hfut.xiaozu.user.idv.IdentityOcrRecordMapper' mapperInterface
2025-07-01 14:53:12.055 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityVerifySubmitMapper' and 'com.hfut.xiaozu.user.idv.IdentityVerifySubmitMapper' mapperInterface
2025-07-01 14:53:12.056 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'voteMapper' and 'com.hfut.xiaozu.vote.info.VoteMapper' mapperInterface
2025-07-01 14:53:12.057 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'voteOptionMapper' and 'com.hfut.xiaozu.vote.info.VoteOptionMapper' mapperInterface
2025-07-01 14:53:12.057 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'voteScopeMapper' and 'com.hfut.xiaozu.vote.info.VoteScopeMapper' mapperInterface
2025-07-01 14:53:12.059 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'voteRecordMapper' and 'com.hfut.xiaozu.vote.record.VoteRecordMapper' mapperInterface
2025-07-01 14:53:12.654 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 111 ] - Tomcat initialized with port 8080 (http)
2025-07-01 14:53:12.673 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardService : 168 ] - Starting service [Tomcat]
2025-07-01 14:53:12.674 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardEngine : 168 ] - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-01 14:53:12.738 [ restartedMain ] - [ INFO  ] [ o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] : 168 ] - Initializing Spring embedded WebApplicationContext
2025-07-01 14:53:12.739 [ restartedMain ] - [ INFO  ] [ o.s.b.w.s.c.ServletWebServerApplicationContext : 301 ] - Root WebApplicationContext: initialization completed in 1809 ms
2025-07-01 14:53:12.840 [ restartedMain ] - [ INFO  ] [ c.a.d.s.b.a.DruidDataSourceAutoConfigure : 55 ] - Init DruidDataSource
2025-07-01 14:53:13.392 [ restartedMain ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 1009 ] - {dataSource-1} inited
2025-07-01 14:53:13.577 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.xml]'
2025-07-01 14:53:13.588 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.xml]'
2025-07-01 14:53:13.599 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.xml]'
2025-07-01 14:53:13.612 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.xml]'
2025-07-01 14:53:13.621 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.xml]'
2025-07-01 14:53:13.633 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.xml]'
2025-07-01 14:53:13.645 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.xml]'
2025-07-01 14:53:13.656 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.xml]'
2025-07-01 14:53:13.667 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.xml]'
2025-07-01 14:53:13.676 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.xml]'
2025-07-01 14:53:13.684 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.xml]'
2025-07-01 14:53:13.693 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.xml]'
2025-07-01 14:53:13.701 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.xml]'
2025-07-01 14:53:13.707 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.xml]'
2025-07-01 14:53:13.713 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.xml]'
2025-07-01 14:53:13.720 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\info\VoteMapper.xml]'
2025-07-01 14:53:13.727 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\info\VoteOptionMapper.xml]'
2025-07-01 14:53:13.735 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\info\VoteScopeMapper.xml]'
2025-07-01 14:53:13.744 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\record\VoteRecordMapper.xml]'
2025-07-01 14:53:14.514 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 319 ] - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-01 14:53:14.750 [ restartedMain ] - [ INFO  ] [ o.s.b.d.autoconfigure.OptionalLiveReloadServer : 59 ] - LiveReload server is running on port 35729
2025-07-01 14:53:14.806 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 243 ] - Tomcat started on port 8080 (http) with context path '/'
2025-07-01 14:53:14.816 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 59 ] - Started XiaozuApplication in 4.381 seconds (process running for 6.227)
2025-07-01 14:56:43.597 [ http-nio-8080-exec-1 ] - [ INFO  ] [ o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] : 168 ] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-01 14:56:43.597 [ http-nio-8080-exec-1 ] - [ INFO  ] [ org.springframework.web.servlet.DispatcherServlet : 532 ] - Initializing Servlet 'dispatcherServlet'
2025-07-01 14:56:43.598 [ http-nio-8080-exec-1 ] - [ INFO  ] [ org.springframework.web.servlet.DispatcherServlet : 554 ] - Completed initialization in 1 ms
2025-07-01 14:56:44.312 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 14:56:44.315 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 14:56:44.840 [ http-nio-8080-exec-5 ] - [ INFO  ] [ org.springdoc.api.AbstractOpenApiResource : 390 ] - Init duration for springdoc-openapi is: 415 ms
2025-07-01 14:57:48.679 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 14:57:48.680 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 14:57:49.766 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 14:57:49.767 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 14:57:50.393 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 14:57:50.393 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 14:58:46.226 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 14:58:46.227 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 15:00:00.298 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 15:00:00.298 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 15:00:01.738 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 15:00:01.738 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 15:00:02.074 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 15:00:02.075 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 15:04:32.503 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 15:04:32.503 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 15:04:33.649 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 15:04:33.649 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 15:04:48.227 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 15:04:48.228 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 15:11:50.382 [ SpringApplicationShutdownHook ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 54 ] - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-01 15:11:50.477 [ tomcat-shutdown ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 76 ] - Graceful shutdown complete
2025-07-01 15:11:50.486 [ SpringApplicationShutdownHook ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2174 ] - {dataSource-1} closing ...
2025-07-01 15:11:50.495 [ SpringApplicationShutdownHook ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2247 ] - {dataSource-1} closed
2025-07-01 15:15:16.305 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 53 ] - Starting XiaozuApplication using Java 17.0.2 with PID 19740 (D:\gitlab\xiangmu\community-backend\target\classes started by joy0531 in D:\gitlab\xiangmu\community-backend)
2025-07-01 15:15:16.307 [ restartedMain ] - [ DEBUG ] [ com.hfut.xiaozu.XiaozuApplication : 54 ] - Running with Spring Boot v3.4.7, Spring v6.2.8
2025-07-01 15:15:16.308 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 652 ] - No active profile set, falling back to 1 default profile: "default"
2025-07-01 15:15:16.342 [ restartedMain ] - [ INFO  ] [ o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor : 252 ] - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-01 15:15:16.342 [ restartedMain ] - [ INFO  ] [ o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor : 252 ] - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-01 15:15:16.833 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 247 ] - Searching for mappers annotated with @Mapper
2025-07-01 15:15:16.833 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 251 ] - Using auto-configuration base package 'com.hfut.xiaozu'
2025-07-01 15:15:16.959 [ restartedMain ] - [ DEBUG ] [ org.apache.ibatis.logging.LogFactory : 112 ] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-01 15:15:16.970 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.class]
2025-07-01 15:15:16.970 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.class]
2025-07-01 15:15:16.970 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.class]
2025-07-01 15:15:16.970 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.class]
2025-07-01 15:15:16.970 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.class]
2025-07-01 15:15:16.970 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.class]
2025-07-01 15:15:16.970 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.class]
2025-07-01 15:15:16.971 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.class]
2025-07-01 15:15:16.971 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.class]
2025-07-01 15:15:16.971 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.class]
2025-07-01 15:15:16.971 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.class]
2025-07-01 15:15:16.971 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.class]
2025-07-01 15:15:16.971 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.class]
2025-07-01 15:15:16.971 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.class]
2025-07-01 15:15:16.971 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.class]
2025-07-01 15:15:16.971 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\info\VoteMapper.class]
2025-07-01 15:15:16.971 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\info\VoteOptionMapper.class]
2025-07-01 15:15:16.971 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\info\VoteScopeMapper.class]
2025-07-01 15:15:16.971 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\record\VoteRecordMapper.class]
2025-07-01 15:15:16.972 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleHouseBindingMapper' and 'com.hfut.xiaozu.car.bind.VehicleHouseBindingMapper' mapperInterface
2025-07-01 15:15:16.974 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleInfoMapper' and 'com.hfut.xiaozu.car.info.VehicleInfoMapper' mapperInterface
2025-07-01 15:15:16.974 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'familyMemberMapper' and 'com.hfut.xiaozu.family.info.FamilyMemberMapper' mapperInterface
2025-07-01 15:15:16.975 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userHouseBindingMapper' and 'com.hfut.xiaozu.house.binding.UserHouseBindingMapper' mapperInterface
2025-07-01 15:15:16.975 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityGridMapper' and 'com.hfut.xiaozu.house.grid.CommunityGridMapper' mapperInterface
2025-07-01 15:15:16.975 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'gridItemArchiveMapper' and 'com.hfut.xiaozu.house.grid.GridItemArchiveMapper' mapperInterface
2025-07-01 15:15:16.976 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'buildingInfoMapper' and 'com.hfut.xiaozu.house.information.BuildingInfoMapper' mapperInterface
2025-07-01 15:15:16.976 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityInfoMapper' and 'com.hfut.xiaozu.house.information.CommunityInfoMapper' mapperInterface
2025-07-01 15:15:16.976 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'houseInfoMapper' and 'com.hfut.xiaozu.house.information.HouseInfoMapper' mapperInterface
2025-07-01 15:15:16.977 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'unitInfoMapper' and 'com.hfut.xiaozu.house.information.UnitInfoMapper' mapperInterface
2025-07-01 15:15:16.977 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentProcessRecordMapper' and 'com.hfut.xiaozu.incident.process.IncidentProcessRecordMapper' mapperInterface
2025-07-01 15:15:16.977 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentRecordMapper' and 'com.hfut.xiaozu.incident.record.IncidentRecordMapper' mapperInterface
2025-07-01 15:15:16.979 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userMapper' and 'com.hfut.xiaozu.user.account.UserMapper' mapperInterface
2025-07-01 15:15:16.979 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityOcrRecordMapper' and 'com.hfut.xiaozu.user.idv.IdentityOcrRecordMapper' mapperInterface
2025-07-01 15:15:16.980 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityVerifySubmitMapper' and 'com.hfut.xiaozu.user.idv.IdentityVerifySubmitMapper' mapperInterface
2025-07-01 15:15:16.980 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'voteMapper' and 'com.hfut.xiaozu.vote.info.VoteMapper' mapperInterface
2025-07-01 15:15:16.980 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'voteOptionMapper' and 'com.hfut.xiaozu.vote.info.VoteOptionMapper' mapperInterface
2025-07-01 15:15:16.981 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'voteScopeMapper' and 'com.hfut.xiaozu.vote.info.VoteScopeMapper' mapperInterface
2025-07-01 15:15:16.981 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'voteRecordMapper' and 'com.hfut.xiaozu.vote.record.VoteRecordMapper' mapperInterface
2025-07-01 15:15:17.294 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 111 ] - Tomcat initialized with port 8080 (http)
2025-07-01 15:15:17.303 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardService : 168 ] - Starting service [Tomcat]
2025-07-01 15:15:17.303 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardEngine : 168 ] - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-01 15:15:17.341 [ restartedMain ] - [ INFO  ] [ o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] : 168 ] - Initializing Spring embedded WebApplicationContext
2025-07-01 15:15:17.343 [ restartedMain ] - [ INFO  ] [ o.s.b.w.s.c.ServletWebServerApplicationContext : 301 ] - Root WebApplicationContext: initialization completed in 1000 ms
2025-07-01 15:15:17.400 [ restartedMain ] - [ INFO  ] [ c.a.d.s.b.a.DruidDataSourceAutoConfigure : 55 ] - Init DruidDataSource
2025-07-01 15:15:17.720 [ restartedMain ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 1009 ] - {dataSource-1} inited
2025-07-01 15:15:17.842 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.xml]'
2025-07-01 15:15:17.851 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.xml]'
2025-07-01 15:15:17.860 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.xml]'
2025-07-01 15:15:17.868 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.xml]'
2025-07-01 15:15:17.875 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.xml]'
2025-07-01 15:15:17.883 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.xml]'
2025-07-01 15:15:17.889 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.xml]'
2025-07-01 15:15:17.894 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.xml]'
2025-07-01 15:15:17.906 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.xml]'
2025-07-01 15:15:17.912 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.xml]'
2025-07-01 15:15:17.917 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.xml]'
2025-07-01 15:15:17.924 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.xml]'
2025-07-01 15:15:17.930 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.xml]'
2025-07-01 15:15:17.935 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.xml]'
2025-07-01 15:15:17.940 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.xml]'
2025-07-01 15:15:17.946 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\info\VoteMapper.xml]'
2025-07-01 15:15:17.951 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\info\VoteOptionMapper.xml]'
2025-07-01 15:15:17.958 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\info\VoteScopeMapper.xml]'
2025-07-01 15:15:17.964 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\record\VoteRecordMapper.xml]'
2025-07-01 15:15:18.586 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 319 ] - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-01 15:15:18.825 [ restartedMain ] - [ INFO  ] [ o.s.b.d.autoconfigure.OptionalLiveReloadServer : 59 ] - LiveReload server is running on port 35729
2025-07-01 15:15:18.885 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 243 ] - Tomcat started on port 8080 (http) with context path '/'
2025-07-01 15:15:18.895 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 59 ] - Started XiaozuApplication in 2.885 seconds (process running for 3.588)
2025-07-01 15:15:25.698 [ http-nio-8080-exec-1 ] - [ INFO  ] [ o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] : 168 ] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-01 15:15:25.698 [ http-nio-8080-exec-1 ] - [ INFO  ] [ org.springframework.web.servlet.DispatcherServlet : 532 ] - Initializing Servlet 'dispatcherServlet'
2025-07-01 15:15:25.699 [ http-nio-8080-exec-1 ] - [ INFO  ] [ org.springframework.web.servlet.DispatcherServlet : 554 ] - Completed initialization in 1 ms
2025-07-01 15:15:26.154 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 15:15:26.158 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 15:15:26.653 [ http-nio-8080-exec-2 ] - [ INFO  ] [ org.springdoc.api.AbstractOpenApiResource : 390 ] - Init duration for springdoc-openapi is: 403 ms
2025-07-01 15:15:31.706 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 15:15:31.706 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 15:15:38.608 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 15:15:38.609 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 15:15:56.318 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/vote
2025-07-01 15:16:04.707 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 15:16:04.708 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 15:16:11.217 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 15:16:11.218 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 15:16:16.081 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-01 15:17:46.624 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 15:17:46.625 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 15:22:00.126 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 15:22:00.127 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 15:22:00.656 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-01 15:22:00.656 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-01 15:26:01.390 [ SpringApplicationShutdownHook ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 54 ] - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-01 15:26:01.503 [ tomcat-shutdown ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 76 ] - Graceful shutdown complete
2025-07-01 15:26:01.517 [ SpringApplicationShutdownHook ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2174 ] - {dataSource-1} closing ...
2025-07-01 15:26:01.534 [ SpringApplicationShutdownHook ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2247 ] - {dataSource-1} closed
