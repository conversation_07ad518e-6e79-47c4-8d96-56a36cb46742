<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hfut.xiaozu.vote.info.VoteScopeMapper">

    <resultMap id="BaseResultMap" type="com.hfut.xiaozu.vote.info.VoteScopeEntity">
            <id property="id" column="id" />
            <result property="voteId" column="vote_id" />
            <result property="targetType" column="target_type" />
            <result property="targetId" column="target_id" />
    </resultMap>

    <sql id="Base_Column_List">
        id,vote_id,target_type,target_id
    </sql>

    <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from vote_scope
        where  id = #{id}
    </select>

    <select id="listByVoteId" resultType="com.hfut.xiaozu.vote.info.VoteScopeEntity">
        select
        <include refid="Base_Column_List" />
        from vote_scope
        where  vote_id = #{voteId}
    </select>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from vote_scope
        where  id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.hfut.xiaozu.vote.info.VoteScopeEntity" useGeneratedKeys="true">
        insert into vote_scope
        ( vote_id,target_type,target_id)
        values (#{voteId},#{targetType},#{targetId})
    </insert>


    <update id="updateById" parameterType="com.hfut.xiaozu.vote.info.VoteScopeEntity">
        update vote_scope
        <set>
                <if test="voteId != null">
                    vote_id = #{voteId},
                </if>
                <if test="targetType != null">
                    target_type = #{targetType},
                </if>
                <if test="targetId != null">
                    target_id = #{targetId},
                </if>
        </set>
        where   id = #{id}
    </update>

</mapper>
