package com.hfut.xiaozu.vote;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageInfo;
import com.hfut.xiaozu.common.Result;
import com.hfut.xiaozu.house.HouseService;
import com.hfut.xiaozu.user.context.CurrentUser;
import com.hfut.xiaozu.user.context.UserContextHolder;
import com.hfut.xiaozu.vote.info.*;
import com.hfut.xiaozu.vote.record.VoteRecordEntity;
import com.hfut.xiaozu.vote.record.VoteRecordMapper;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Array;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-07-01
 */
@Service
public class VoteService {

    @Resource
    private VoteMapper voteMapper;

    @Resource
    private VoteOptionMapper voteOptionMapper;

    @Resource
    private VoteScopeMapper voteScopeMapper;

    @Resource
    private HouseService houseService;
    @Autowired
    private VoteRecordMapper voteRecordMapper;

    @Transactional
    public Result<?> createVote(CreateVoteDTO dto) {
        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        if(currentUser==null||!Objects.equals(currentUser.getRole(),CurrentUser.Administrator)){
            return Result.fail("权限不足");
        }

        VoteEntity vote = new VoteEntity();
        BeanUtil.copyProperties(dto,vote);
        vote.setCreatorId(currentUser.getUserId());


        //先插入投票，才能获取投票id，供投票选项和投票范围实体绑定
        int insert = voteMapper.insert(vote);
        if(insert!=1){
            return Result.fail("创建投票失败");
        }

        dto.getOptions().forEach(item-> {
            VoteOptionEntity op = new VoteOptionEntity();
            op.setContent(item.getContent());
            op.setSortOrder(item.getSortOrder());
            op.setVoteId(vote.getId());

            voteOptionMapper.insert(op);
        });

        dto.getScopes().forEach(item-> {
            VoteScopeEntity scope = new VoteScopeEntity();
            scope.setTargetId(item.getTargetId());
            scope.setTargetType(item.getTargetType());
            scope.setVoteId(vote.getId());

            voteScopeMapper.insert(scope);
        });


        return Result.ok("创建投票成功");
    }

    public Result<?> listVote() {
        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        if(currentUser==null||Objects.equals(currentUser.getRole(),CurrentUser.GridWorker)){
            return Result.fail("用户未登录,网格员无投票板块");
        }

        List<VoteVO> result =new ArrayList<>();

        List<VoteEntity> vote = voteMapper.listAll();

        for (VoteEntity item : vote) {
            VoteVO vo = new VoteVO();
            BeanUtil.copyProperties(item,vo);

            //添加选项
            List<VoteOptionEntity> options = voteOptionMapper.listByVoteId(item.getId());

            List<VoteVO.Option> r= new ArrayList<>();

            for (VoteOptionEntity option : options) {
                Integer count = voteRecordMapper.countByOptionId(item.getId(), Long.valueOf(option.getSortOrder()));

                VoteVO.Option op = new VoteVO.Option();
                op.setContent(option.getContent());
                op.setSortOrder(option.getSortOrder());
                op.setNumber(count);

                r.add(op);

            }

            vo.setOptions(r);

            //添加范围
            List<CreateVoteDTO.Scope> scopes = voteScopeMapper.listByVoteId(item.getId()).stream()
                    .map(obj->new CreateVoteDTO.Scope(obj.getTargetType(),obj.getTargetId()))
                    .toList();
            vo.setScopes(scopes);

            //添加已选择的
            List<VoteRecordEntity> voteRecord = voteRecordMapper.listByVoteIdAndUserId(item.getId(),currentUser.getUserId());
            if(!CollectionUtil.isEmpty(voteRecord)){
                List<Integer> choices = voteRecord.stream()
                        .map(VoteRecordEntity::getOptionId)
                        .map(Long::intValue)
                        .toList();
                vo.setChoices(choices);
            }

            result.add(vo);
        }

        if(CollectionUtil.isEmpty(result)){
            return Result.ok("暂无投票");
        }

        //管理员直接返回所有投票
        if(Objects.equals(currentUser.getRole(),CurrentUser.Administrator)){
            return Result.ok(result);
        }

        List<Long> housesId = houseService.listUserHousesId(currentUser.getUserId());

        List<Long> communityId = houseService.listUserCommunitiesIdByHousesId(housesId);
        List<Long> buildingId = houseService.listUserBuildingsIdByHousesId(housesId);
        List<Long> grids = houseService.listUserGridsIdByCommunitiesId(communityId);

        //过滤所有的投票
        result = result.stream()
                .filter(item->{
                    //如果投票范围为空，则所有人都可以投票
                    if(CollectionUtil.isEmpty(item.getScopes())){
                        return true;
                    }

                    //如果投票范围不为空，则判断用户是否在投票范围内
                    for (CreateVoteDTO.Scope scope : item.getScopes()) {
                        if(scope.getTargetType()==1&&communityId.contains(scope.getTargetId())){
                            return true;
                        }
                        if(scope.getTargetType()==2&&grids.contains(scope.getTargetId())){
                            return true;
                        }
                        if(scope.getTargetType()==3&&buildingId.contains(scope.getTargetId())){
                            return true;
                        }
                    }

                    return false;
                }).toList();

        return Result.ok(result);

    }

    public Result<?> vote(Long voteId, List<Integer> chooses) {
        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        if(currentUser==null|| !Objects.equals(currentUser.getRole(),CurrentUser.Resident)){
            return Result.fail("用户未登录,居民才能投票");
        }

        VoteEntity vote = voteMapper.getById(voteId);
        if(vote==null){
            return Result.fail("投票不存在");
        }
        if(vote.getStartTime().isAfter(LocalDateTime.now())){
            return Result.fail("投票未开始");
        }
        if(vote.getEndTime().isBefore(LocalDateTime.now())){
            return Result.fail("投票已结束");
        }

        //判断用户是否已经投过票
        List<VoteRecordEntity> voteRecord = voteRecordMapper.listByVoteIdAndUserId(voteId,currentUser.getUserId());
        if(!CollectionUtil.isEmpty(voteRecord)){
            return Result.fail("您已经投过票了");
        }


        if(chooses.size()>vote.getMaxChoices()){
            return Result.fail("最多只能选择"+vote.getMaxChoices()+"个选项");
        }

        //TODO 判断该投票居民是否能投

        List<VoteOptionEntity> options = voteOptionMapper.listByVoteId(voteId);
        for (Integer choose : chooses) {
            if(choose>options.size()){
                return Result.fail("选项不存在");
            }
        }

        //插入投票数据
        for (Integer choose : chooses) {
            VoteRecordEntity record = new VoteRecordEntity();
            record.setVoteId(voteId);
            record.setUserId(currentUser.getUserId());
            //fixme 这里叫optionid不合适了，应该是vote中的第几个选项
            record.setOptionId(Long.valueOf(choose));

            voteRecordMapper.insert(record);
        }

        return Result.ok("投票成功");
    }
}
