<template>
  <div class="map-component">
    <div :id="mapId" class="map-container" :style="{ height: height }"></div>
    
    <!-- 地图控制面板 -->
    <div v-if="showControls" class="map-controls">
      <div class="control-section">
        <h4>图层控制</h4>
        <div class="layer-toggles">
          <label v-for="layer in availableLayers" :key="layer.key" class="layer-toggle">
            <input 
              type="checkbox" 
              :checked="layer.visible" 
              @change="toggleLayer(layer.key)"
            >
            <span>{{ layer.name }}</span>
          </label>
        </div>
      </div>
      
      <div v-if="showDrawingTools" class="control-section">
        <h4>绘制工具</h4>
        <div class="drawing-tools">
          <button 
            v-for="tool in drawingTools" 
            :key="tool.key"
            :class="['tool-btn', { active: activeTool === tool.key }]"
            @click="activateDrawingTool(tool.key)"
          >
            <span class="tool-icon">{{ tool.icon }}</span>
            {{ tool.name }}
          </button>
        </div>
      </div>
      
      <div class="control-section">
        <h4>地图操作</h4>
        <div class="map-actions">
          <button class="action-btn" @click="resetView">
            <span class="btn-icon">🏠</span>
            重置视图
          </button>
          <button class="action-btn" @click="toggleFullscreen">
            <span class="btn-icon">⛶</span>
            全屏
          </button>
        </div>
      </div>

      <div class="control-section">
        <h4>坐标工具</h4>
        <div class="coordinate-tools">
          <button
            :class="['tool-btn', { active: isCoordinateMode }]"
            @click="toggleCoordinateMode"
          >
            <span class="tool-icon">📍</span>
            {{ isCoordinateMode ? '退出坐标模式' : '获取坐标' }}
          </button>

        </div>
      </div>
    </div>

    <!-- 地图信息面板 -->
    <div v-if="showInfo && selectedFeature" class="info-panel">
      <div class="info-header">
        <h4>{{ selectedFeature.title }}</h4>
        <button class="close-btn" @click="closeInfo">×</button>
      </div>
      <div class="info-content">
        <div v-for="(value, key) in selectedFeature.properties" :key="key" class="info-item">
          <span class="info-label">{{ formatLabel(key) }}:</span>
          <span class="info-value">{{ value }}</span>
        </div>
      </div>
    </div>

    <!-- 坐标显示面板 -->
    <div v-if="lastClickedCoordinate" class="coordinate-display-panel">
      <div class="coordinate-header">
        <h4>📍 点击坐标</h4>
        <button class="close-btn" @click="clearCoordinate">×</button>
      </div>
      <div class="coordinate-body">
        <div class="coordinate-info">
          <div class="coordinate-row">
            <span class="label">经度:</span>
            <span class="value">{{ lastClickedCoordinate.lng.toFixed(6) }}</span>
          </div>
          <div class="coordinate-row">
            <span class="label">纬度:</span>
            <span class="value">{{ lastClickedCoordinate.lat.toFixed(6) }}</span>
          </div>
        </div>



        <div class="coordinate-actions">
          <button class="action-button secondary" @click="clearCoordinate">
            🗑️ 清除
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { loadAmapAPI } from '../config/map.js';

// Props
const props = defineProps({
  mapId: {
    type: String,
    default: 'map'
  },
  height: {
    type: String,
    default: '500px'
  },
  center: {
    type: Array,
    default: () => [117.198612, 31.774164] // 地图中心点坐标 (高德地图使用经度在前，纬度在后)
  },
  zoom: {
    type: Number,
    default: 13
  },
  showControls: {
    type: Boolean,
    default: true
  },
  showDrawingTools: {
    type: Boolean,
    default: false
  },
  showInfo: {
    type: Boolean,
    default: true
  },
  layers: {
    type: Array,
    default: () => []
  },
  markers: {
    type: Array,
    default: () => []
  },
  polygons: {
    type: Array,
    default: () => []
  }
});

// Emits
const emit = defineEmits([
  'map-ready',
  'marker-click',
  'polygon-click',
  'draw-created',
  'layer-toggle',
  'coordinate-clicked'
]);

// 响应式数据
const map = ref(null);
const selectedFeature = ref(null);
const activeTool = ref(null);

// 坐标功能相关数据
const isCoordinateMode = ref(false);
const lastClickedCoordinate = ref(null);
const coordinateMarker = ref(null);

// AMap API引用
let AMapAPI = null;

// 可用图层
const availableLayers = ref([
  { key: 'satellite', name: '卫星图', visible: false },
  { key: 'terrain', name: '地形图', visible: false },
  { key: 'placenames', name: '地名标注', visible: true },
  { key: 'grid', name: '网格边界', visible: true },
  { key: 'buildings', name: '建筑物', visible: true },
  { key: 'events', name: '事件标记', visible: true }
]);

// 绘制工具
const drawingTools = [
  { key: 'marker', name: '标记', icon: '📍' },
  { key: 'polygon', name: '多边形', icon: '⬟' },
  { key: 'polyline', name: '线条', icon: '📏' },
  { key: 'circle', name: '圆形', icon: '⭕' }
];

// 地图图层存储
const mapLayers = ref({});

// 初始化地图
const initMap = async () => {
  await nextTick();

  try {
    console.log('开始加载高德地图API...');

    // 动态加载高德地图API
    AMapAPI = await loadAmapAPI();

    // 将AMap暴露到全局作用域，确保组件内可以访问
    window.AMap = AMapAPI;

    console.log('高德地图API加载成功，开始创建地图实例...');
    console.log('地图容器ID:', props.mapId);
    console.log('地图中心点:', props.center);
    console.log('地图缩放级别:', props.zoom);
    console.log('AMap对象:', typeof AMapAPI);

    // 创建高德地图实例
    map.value = new AMapAPI.Map(props.mapId, {
      center: props.center,
      zoom: props.zoom,
      mapStyle: 'amap://styles/normal',
      viewMode: '2D',
      resizeEnable: true,
      rotateEnable: true,
      pitchEnable: true,
      zoomEnable: true,
      dragEnable: true,
      // 显示所有基础地图元素，包括地名标注
      features: ['bg', 'road', 'building', 'point'],
      // 关闭室内地图
      showIndoorMap: false,
      // 设置中文显示
      lang: 'zh_cn'
    });

    console.log('地图实例创建成功:', map.value);

    // 等待地图完全加载
    map.value.on('complete', () => {
      console.log('✅ 地图加载完成');
      console.log('📍 地图中心点:', map.value.getCenter());
      console.log('🔍 地图缩放级别:', map.value.getZoom());

      // 初始化地名显示功能
      initializePlaceNameLayers();

      // 初始化图层状态
      initializeLayerStates();

      // 加载初始数据
      console.log('🗺️ 开始加载标记点，数量:', props.markers?.length || 0);
      loadMarkers();
      loadPolygons();

      // 初始化坐标点击事件
      initCoordinateClickHandler();

      // 触发地图就绪事件
      emit('map-ready', map.value);
    });

  } catch (error) {
    console.error('高德地图初始化失败:', error);
    console.error('错误详情:', error.message);
    console.error('请检查API密钥和网络连接');

    // 显示错误信息给用户
    const mapContainer = document.getElementById(props.mapId);
    if (mapContainer) {
      mapContainer.innerHTML = `
        <div style="display: flex; align-items: center; justify-content: center; height: 100%; background: #f5f5f5; color: #666; font-size: 14px;">
          <div style="text-align: center;">
            <div style="margin-bottom: 10px;">🗺️</div>
            <div>地图加载失败</div>
            <div style="font-size: 12px; margin-top: 5px;">请检查网络连接和API配置</div>
          </div>
        </div>
      `;
    }
  }
};

// 存储标记和多边形的引用
const markersLayer = ref([]);
const polygonsLayer = ref([]);

// 初始化地名显示图层
const initializePlaceNameLayers = () => {
  if (!map.value || !window.AMap) return;

  console.log('初始化地名显示图层...');

  try {
    // 设置地图特性，显示所有基础元素包括地名
    map.value.setFeatures(['bg', 'road', 'building', 'point']);

    // 设置地图样式确保地名可见
    map.value.setMapStyle('amap://styles/normal');

    // 确保地名在所有缩放级别都可见
    const currentZoom = map.value.getZoom();
    console.log('当前缩放级别:', currentZoom);

    // 如果缩放级别太低，自动调整到合适的级别以显示地名
    if (currentZoom < 10) {
      console.log('缩放级别过低，调整到合适级别以显示地名');
      map.value.setZoom(12);
    }

    // 监听地图缩放和移动事件
    map.value.on('zoomend', () => {
      console.log('缩放级别变化:', map.value.getZoom());
      updatePlaceNames();
    });

    map.value.on('moveend', () => {
      console.log('地图移动完成');
    });

    // 强制刷新地图以确保地名显示
    setTimeout(() => {
      if (map.value) {
        // 重新设置features以确保地名显示
        map.value.setFeatures(['bg', 'road', 'building', 'point']);
        console.log('地图已刷新以显示地名');
      }
    }, 1000);

    console.log('地名显示图层初始化完成');

  } catch (error) {
    console.error('地名图层初始化失败:', error);
  }
};



// 更新地名显示
const updatePlaceNames = () => {
  if (!map.value) return;

  const zoom = map.value.getZoom();
  console.log('更新地名显示，当前缩放级别:', zoom);

  try {
    // 检查地名图层是否启用
    const placeNamesLayer = availableLayers.value.find(l => l.key === 'placenames');
    const showPlaceNames = placeNamesLayer ? placeNamesLayer.visible : true;

    // 根据缩放级别和地名图层状态调整显示内容
    if (zoom >= 15) {
      // 高缩放级别：显示背景、道路、建筑物
      if (showPlaceNames) {
        console.log('设置高缩放级别特性：bg, road, building, point');
        map.value.setFeatures(['bg', 'road', 'building', 'point']);
      } else {
        console.log('设置高缩放级别特性（无地名）：bg, road, building');
        map.value.setFeatures(['bg', 'road', 'building']);
      }
    } else if (zoom >= 12) {
      // 中等缩放级别：显示背景、道路
      if (showPlaceNames) {
        console.log('设置中等缩放级别特性：bg, road, point');
        map.value.setFeatures(['bg', 'road', 'point']);
      } else {
        console.log('设置中等缩放级别特性（无地名）：bg, road');
        map.value.setFeatures(['bg', 'road']);
      }
    } else if (zoom >= 8) {
      // 低缩放级别：显示背景、主要道路
      if (showPlaceNames) {
        console.log('设置低缩放级别特性：bg, road, point');
        map.value.setFeatures(['bg', 'road', 'point']);
      } else {
        console.log('设置低缩放级别特性（无地名）：bg, road');
        map.value.setFeatures(['bg', 'road']);
      }
    } else {
      // 很低缩放级别：只显示背景
      if (showPlaceNames) {
        console.log('设置很低缩放级别特性：bg, point');
        map.value.setFeatures(['bg', 'point']);
      } else {
        console.log('设置很低缩放级别特性（无地名）：bg');
        map.value.setFeatures(['bg']);
      }
    }

  } catch (error) {
    console.error('更新地名显示失败:', error);
  }
};

// 初始化图层状态
const initializeLayerStates = () => {
  if (!map.value) return;

  console.log('初始化图层状态...');

  // 首先设置基础地图特性
  const buildingsLayer = availableLayers.value.find(l => l.key === 'buildings');

  // 根据建筑物图层状态设置基础特性
  if (buildingsLayer && buildingsLayer.visible) {
    map.value.setFeatures(['bg', 'road', 'building']);
    console.log('初始化：建筑物图层已启用');
  } else {
    map.value.setFeatures(['bg', 'road']);
    console.log('初始化：建筑物图层已禁用');
  }

  // 根据可用图层的初始状态设置其他图层
  availableLayers.value.forEach(layer => {
    switch (layer.key) {
      case 'satellite':
        if (layer.visible) {
          toggleSatelliteLayer(true);
        }
        break;
      // 其他图层的初始化可以在这里添加
    }
  });

  // 强制刷新地图以确保所有设置生效
  setTimeout(() => {
    if (map.value) {
      map.value.setZoom(map.value.getZoom());
      console.log('地图已刷新，图层状态初始化完成');
    }
  }, 200);
};

// 加载标记
const loadMarkers = () => {
  if (!map.value) return;

  // 清除现有标记
  markersLayer.value.forEach(marker => {
    map.value.remove(marker);
  });
  markersLayer.value = [];

  // 检查事件标记图层是否可见
  const eventsLayer = availableLayers.value.find(l => l.key === 'events');
  if (!eventsLayer || !eventsLayer.visible) {
    return;
  }

  console.log('🗺️ 加载标记点，数量:', props.markers.length)

  props.markers.forEach((markerData, index) => {
    console.log(`📍 处理标记点 ${index + 1}:`, markerData)

    // 获取位置信息，支持多种格式
    let position
    if (markerData.position && Array.isArray(markerData.position)) {
      // 格式: position: [lng, lat]
      position = markerData.position
    } else if (markerData.lng && markerData.lat) {
      // 格式: lng, lat
      position = [markerData.lng, markerData.lat]
    } else if (markerData.longitude && markerData.latitude) {
      // 格式: longitude, latitude
      position = [markerData.longitude, markerData.latitude]
    } else {
      console.warn('❌ 标记点位置信息格式不正确:', markerData)
      return
    }

    console.log('📍 使用位置:', position)

    try {
      // 创建高德地图标记点
      const marker = new window.AMap.Marker({
        position: position,
        title: markerData.title || '标记点'
      });

      // 如果有自定义图标
      if (markerData.icon && markerData.icon.type === 'custom' && markerData.icon.content) {
        marker.setContent(markerData.icon.content)
      }

      // 创建信息窗口
      const infoWindow = new window.AMap.InfoWindow({
        content: markerData.content || markerData.popup || markerData.title || '标记点信息'
      });

      // 添加点击事件
      marker.on('click', () => {
        infoWindow.open(map.value, marker.getPosition());
        selectedFeature.value = {
          title: markerData.title,
          properties: markerData.properties || {}
        };
        emit('marker-clicked', markerData);
      });

      // 添加到地图
      map.value.add(marker);
      markersLayer.value.push(marker);

      console.log('✅ 标记点添加成功')
    } catch (error) {
      console.error('❌ 创建标记点失败:', error, markerData)
    }
  });
};

// 加载多边形
const loadPolygons = () => {
  if (!map.value) return;

  // 清除现有多边形
  polygonsLayer.value.forEach(polygon => {
    map.value.remove(polygon);
  });
  polygonsLayer.value = [];

  // 检查网格边界图层是否可见
  const gridLayer = availableLayers.value.find(l => l.key === 'grid');
  if (!gridLayer || !gridLayer.visible) {
    return;
  }

  props.polygons.forEach(polygonData => {
    console.log('加载多边形:', polygonData.title, '坐标:', polygonData.coordinates);

    // 高德地图使用[lng, lat]格式，我们的数据已经是[lng, lat]格式，不需要转换
    const path = polygonData.coordinates.map(coord => [coord[0], coord[1]]);

    console.log('转换后的路径:', path);

    // 创建多边形
    const polygon = new AMap.Polygon({
      path: path,
      strokeColor: polygonData.color || '#3388ff',
      fillColor: polygonData.fillColor || '#3388ff',
      fillOpacity: polygonData.fillOpacity || 0.2,
      strokeWeight: polygonData.strokeWeight || 2,
      strokeOpacity: 0.8
    });

    console.log('多边形创建成功:', polygon);

    // 创建信息窗口
    const infoWindow = new AMap.InfoWindow({
      content: polygonData.popup || polygonData.title
    });

    // 添加点击事件
    polygon.on('click', (e) => {
      infoWindow.open(map.value, e.lnglat);
      selectedFeature.value = {
        title: polygonData.title,
        properties: polygonData.properties || {}
      };
      emit('polygon-click', polygonData);
    });

    // 添加到地图
    map.value.add(polygon);
    polygonsLayer.value.push(polygon);

    console.log('多边形已添加到地图，当前多边形总数:', polygonsLayer.value.length);
  });

  console.log('多边形加载完成，总共加载了', polygonsLayer.value.length, '个多边形');
};

// 切换图层
const toggleLayer = (layerKey) => {
  const layer = availableLayers.value.find(l => l.key === layerKey);
  if (layer) {
    layer.visible = !layer.visible;

    // 根据图层类型执行相应的显示/隐藏逻辑
    switch (layerKey) {
      case 'satellite':
        toggleSatelliteLayer(layer.visible);
        break;
      case 'terrain':
        toggleTerrainLayer(layer.visible);
        break;
      case 'placenames':
        togglePlaceNamesLayer(layer.visible);
        break;
      case 'grid':
        toggleGridLayer(layer.visible);
        break;
      case 'buildings':
        toggleBuildingsLayer(layer.visible);
        break;
      case 'events':
        toggleEventsLayer(layer.visible);
        break;
      default:
        console.log(`未知图层类型: ${layerKey}`);
    }

    emit('layer-toggle', layerKey, layer.visible);
  }
};

// 图层切换方法
const toggleSatelliteLayer = (visible) => {
  if (!map.value) return;

  if (visible) {
    // 切换到卫星图层
    if (!mapLayers.value.satellite) {
      mapLayers.value.satellite = new AMap.TileLayer.Satellite();
    }
    map.value.add(mapLayers.value.satellite);
  } else {
    if (mapLayers.value.satellite) {
      map.value.remove(mapLayers.value.satellite);
    }
  }
};

const toggleTerrainLayer = (visible) => {
  if (!map.value) return;

  if (visible) {
    // 高德地图没有专门的地形图，使用路网图代替
    map.value.setMapStyle('amap://styles/whitesmoke');
  } else {
    map.value.setMapStyle('amap://styles/normal');
  }
};

const toggleGridLayer = (visible) => {
  if (!map.value) return;

  // 重新加载多边形以应用可见性变化
  loadPolygons();
};

const togglePlaceNamesLayer = (visible) => {
  if (!map.value) return;

  console.log('切换地名标注图层:', visible);

  try {
    if (visible) {
      // 显示地名标注
      console.log('启用地名标注显示...');

      // 重新设置地图特性以确保地名显示
      const currentZoom = map.value.getZoom();
      if (currentZoom >= 15) {
        map.value.setFeatures(['bg', 'road', 'building']);
      } else {
        map.value.setFeatures(['bg', 'road']);
      }

      // 强制刷新地图
      setTimeout(() => {
        if (map.value) {
          map.value.setZoom(map.value.getZoom());
        }
      }, 100);

      console.log('地名标注图层已启用');
    } else {
      // 隐藏地名标注 - 高德地图中地名是内置的，无法完全隐藏
      console.log('地名标注无法完全隐藏，这是高德地图的内置功能');

      console.log('地名标注图层切换完成');
    }
  } catch (error) {
    console.error('切换地名标注图层失败:', error);
  }
};

const toggleBuildingsLayer = (visible) => {
  if (!map.value) return;

  console.log('切换建筑物图层:', visible);

  try {
    if (visible) {
      // 显示建筑物图层
      console.log('启用建筑物显示...');

      // 设置地图特性包含建筑物
      map.value.setFeatures(['bg', 'road', 'building']);

      // 确保建筑物在合适的缩放级别显示
      const currentZoom = map.value.getZoom();
      if (currentZoom < 15) {
        console.log('当前缩放级别较低，建筑物可能不可见，建议放大地图');
      }

      // 强制重新渲染
      setTimeout(() => {
        if (map.value) {
          map.value.setFeatures(['bg', 'road', 'building']);
          // 触发地图重绘
          map.value.setZoom(map.value.getZoom());
        }
      }, 50);

      console.log('建筑物图层已启用');
    } else {
      // 隐藏建筑物图层
      console.log('禁用建筑物显示...');

      // 设置地图特性不包含建筑物
      map.value.setFeatures(['bg', 'road']);

      // 强制重新渲染
      setTimeout(() => {
        if (map.value) {
          map.value.setFeatures(['bg', 'road']);
          // 触发地图重绘
          map.value.setZoom(map.value.getZoom());
        }
      }, 50);

      console.log('建筑物图层已禁用');
    }
  } catch (error) {
    console.error('切换建筑物图层失败:', error);
  }
};

const toggleEventsLayer = (visible) => {
  if (!map.value) return;

  // 重新加载标记以应用可见性变化
  loadMarkers();
};

// 激活绘制工具
const activateDrawingTool = (toolKey) => {
  if (activeTool.value === toolKey) {
    activeTool.value = null;
    // 取消激活工具
    clearDrawingTool();
  } else {
    activeTool.value = toolKey;
    // 激活对应的绘制工具
    initDrawingTool(toolKey);
  }
};

// 初始化绘制工具
const initDrawingTool = (toolKey) => {
  if (!map.value) return;

  // 清除之前的绘制工具
  clearDrawingTool();

  console.log(`激活绘制工具: ${toolKey}`);

  // 禁用地图拖拽和双击缩放
  map.value.setStatus({
    dragEnable: false,
    doubleClickZoom: false
  });

  // 改变鼠标样式
  const mapContainer = document.getElementById(props.mapId);
  if (mapContainer) {
    mapContainer.style.cursor = 'crosshair';
  }

  switch (toolKey) {
    case 'polygon':
      initPolygonDrawing();
      break;
    case 'marker':
      initMarkerDrawing();
      break;
    case 'polyline':
      initPolylineDrawing();
      break;
    case 'circle':
      initCircleDrawing();
      break;
  }
};

// 清除绘制工具
const clearDrawingTool = () => {
  if (map.value) {
    // 清除绘制工具
    if (map.value.drawingTool) {
      map.value.drawingTool.close();
      map.value.drawingTool = null;
    }

    // 恢复地图拖拽和双击缩放
    map.value.setStatus({
      dragEnable: true,
      doubleClickZoom: true
    });

    // 恢复鼠标样式
    const mapContainer = document.getElementById(props.mapId);
    if (mapContainer) {
      mapContainer.style.cursor = '';
    }
  }
};

// 初始化多边形绘制
const initPolygonDrawing = () => {
  if (!map.value) return;

  const drawingPoints = [];
  let tempPolygon = null;
  let tempMarkers = [];
  let tempPolyline = null;

  console.log('开始多边形绘制模式');

  // 添加地图点击事件
  const clickHandler = (event) => {
    // 阻止事件冒泡
    event.stopPropagation();

    const { lng, lat } = event.lnglat;
    drawingPoints.push([lng, lat]);

    console.log('添加点:', lng, lat, '总点数:', drawingPoints.length);

    // 添加点标记
    const marker = new AMap.Marker({
      position: [lng, lat],
      icon: new AMap.Icon({
        size: new AMap.Size(10, 10),
        image: 'data:image/svg+xml;base64,' + btoa(`
          <svg width="10" height="10" xmlns="http://www.w3.org/2000/svg">
            <circle cx="5" cy="5" r="4" fill="#ff0000" stroke="#ffffff" stroke-width="1"/>
          </svg>
        `)
      })
    });
    map.value.add(marker);
    tempMarkers.push(marker);

    // 如果有2个或更多点，绘制连接线
    if (drawingPoints.length >= 2) {
      // 移除之前的线条
      if (tempPolyline) {
        map.value.remove(tempPolyline);
      }

      const path = drawingPoints.map(point => [point[0], point[1]]);
      tempPolyline = new AMap.Polyline({
        path: path,
        strokeColor: '#ff0000',
        strokeWeight: 2,
        strokeOpacity: 0.8,
        strokeStyle: 'dashed'
      });
      map.value.add(tempPolyline);
    }

    // 如果有3个或更多点，创建临时多边形预览
    if (drawingPoints.length >= 3) {
      // 移除之前的多边形
      if (tempPolygon) {
        map.value.remove(tempPolygon);
      }

      const path = drawingPoints.map(point => [point[0], point[1]]);
      tempPolygon = new AMap.Polygon({
        path: path,
        strokeColor: '#ff0000',
        fillColor: '#ff0000',
        fillOpacity: 0.1,
        strokeWeight: 2,
        strokeOpacity: 0.8,
        strokeStyle: 'dashed'
      });
      map.value.add(tempPolygon);
    }
  };

  // 添加双击事件完成绘制
  const dblClickHandler = (event) => {
    event.stopPropagation();

    if (drawingPoints.length >= 3) {
      console.log('多边形绘制完成，点数:', drawingPoints.length);

      // 触发绘制完成事件
      emit('draw-created', {
        type: 'polygon',
        coordinates: [...drawingPoints], // 复制数组
        area: calculatePolygonArea(drawingPoints)
      });
    } else {
      console.log('需要至少3个点才能完成多边形绘制');
      alert('请至少点击3个点来绘制多边形，然后双击完成绘制');
      return;
    }

    // 清理绘制状态
    cleanupDrawing();
  };

  // 清理绘制状态的函数
  const cleanupDrawing = () => {
    // 清理临时图形
    if (tempPolygon) {
      map.value.remove(tempPolygon);
      tempPolygon = null;
    }
    if (tempPolyline) {
      map.value.remove(tempPolyline);
      tempPolyline = null;
    }
    tempMarkers.forEach(marker => {
      map.value.remove(marker);
    });
    tempMarkers.length = 0;

    // 清理数据
    drawingPoints.length = 0;

    // 移除事件监听
    map.value.off('click', clickHandler);
    map.value.off('dblclick', dblClickHandler);

    // 重置工具状态
    activeTool.value = null;
  };

  // 绑定事件
  map.value.on('click', clickHandler);
  map.value.on('dblclick', dblClickHandler);

  map.value.drawingTool = {
    close: cleanupDrawing
  };
};

// 初始化标记绘制
const initMarkerDrawing = () => {
  if (!map.value) return;

  // 添加地图点击事件
  const clickHandler = (event) => {
    const { lng, lat } = event.lnglat;

    // 触发绘制完成事件
    emit('draw-created', {
      type: 'marker',
      coordinates: [lng, lat]
    });

    // 移除点击事件监听
    map.value.off('click', clickHandler);
    activeTool.value = null;
  };

  map.value.on('click', clickHandler);
  map.value.drawingTool = { close: () => map.value.off('click', clickHandler) };
};

// 初始化线条绘制
const initPolylineDrawing = () => {
  if (!map.value) return;

  // 这里可以实现线条绘制逻辑
  console.log('线条绘制功能待实现');
};

// 初始化圆形绘制
const initCircleDrawing = () => {
  if (!map.value) return;

  // 这里可以实现圆形绘制逻辑
  console.log('圆形绘制功能待实现');
};

// 计算多边形面积的辅助函数
const calculatePolygonArea = (coordinates) => {
  if (coordinates.length < 3) return 0;

  let area = 0;
  const n = coordinates.length;

  for (let i = 0; i < n; i++) {
    const j = (i + 1) % n;
    area += coordinates[i][0] * coordinates[j][1];
    area -= coordinates[j][0] * coordinates[i][1];
  }

  area = Math.abs(area) / 2;

  // 转换为平方米（粗略估算）
  const metersPerDegree = 111000;
  return area * metersPerDegree * metersPerDegree;
};

// 重置视图
const resetView = () => {
  if (map.value) {
    map.value.setZoomAndCenter(props.zoom, props.center);
  }
};

// 切换全屏
const toggleFullscreen = () => {
  const mapContainer = document.getElementById(props.mapId);
  if (mapContainer) {
    if (!document.fullscreenElement) {
      mapContainer.requestFullscreen();
    } else {
      document.exitFullscreen();
    }
  }
};

// 关闭信息面板
const closeInfo = () => {
  selectedFeature.value = null;
};

// 格式化标签
const formatLabel = (key) => {
  const labelMap = {
    'name': '名称',
    'area': '面积',
    'manager': '负责人',
    'status': '状态',
    'type': '类型',
    'description': '描述'
  };
  return labelMap[key] || key;
};

// ========== 坐标功能相关方法 ==========

// 初始化坐标点击事件处理器
const initCoordinateClickHandler = () => {
  console.log('初始化坐标点击事件处理器...');

  if (!map.value) {
    console.error('地图实例不存在，无法初始化坐标点击事件');
    return;
  }

  console.log('地图实例存在，添加点击事件监听器...');

  // 添加地图点击事件监听器
  map.value.on('click', handleCoordinateClick);

  console.log('坐标点击事件监听器已添加');
};

// 处理坐标模式下的地图点击
const handleCoordinateClick = (event) => {
  console.log('地图点击事件触发，坐标模式状态:', isCoordinateMode.value);

  // 只在坐标模式下处理点击事件
  if (!isCoordinateMode.value) {
    console.log('不在坐标模式，忽略点击事件');
    return;
  }

  // 高德地图事件对象没有stopPropagation方法，直接处理坐标
  console.log('地图点击事件对象:', event);

  // 安全地获取坐标
  let lng, lat;
  try {
    if (event.lnglat) {
      lng = event.lnglat.lng || event.lnglat.getLng();
      lat = event.lnglat.lat || event.lnglat.getLat();
    } else {
      console.error('事件对象中没有lnglat属性');
      return;
    }
  } catch (error) {
    console.error('获取坐标失败:', error);
    return;
  }

  console.log('坐标模式点击成功:', { lng, lat });

  // 更新最后点击的坐标
  lastClickedCoordinate.value = {
    lng: lng,
    lat: lat,
    timestamp: new Date().toISOString(),
    clickTime: new Date().toLocaleString('zh-CN')
  };

  // 清除之前的坐标标记
  if (coordinateMarker.value) {
    map.value.remove(coordinateMarker.value);
  }

  // 创建新的坐标标记（使用简单的默认标记）
  try {
    if (!AMapAPI) {
      console.error('AMapAPI未初始化');
      return;
    }

    coordinateMarker.value = new AMapAPI.Marker({
      position: [lng, lat],
      title: `坐标点 (${lng.toFixed(6)}, ${lat.toFixed(6)})`,
      // 使用默认的红色标记图标
      content: '<div style="background: red; width: 20px; height: 20px; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>'
    });

    console.log('坐标标记创建成功');
  } catch (error) {
    console.error('创建坐标标记失败:', error);
    // 降级方案：创建简单标记
    if (AMapAPI) {
      coordinateMarker.value = new AMapAPI.Marker({
        position: [lng, lat],
        title: `坐标点 (${lng.toFixed(6)}, ${lat.toFixed(6)})`
      });
    }
  }

  // 添加到地图
  map.value.add(coordinateMarker.value);

  // 触发坐标点击事件
  emit('coordinate-clicked', lastClickedCoordinate.value);

  console.log('坐标已获取并显示:', lastClickedCoordinate.value);
};

// 切换坐标模式
const toggleCoordinateMode = () => {
  console.log('切换坐标模式，当前状态:', isCoordinateMode.value);

  isCoordinateMode.value = !isCoordinateMode.value;

  console.log('新的坐标模式状态:', isCoordinateMode.value);

  if (!isCoordinateMode.value) {
    // 退出坐标模式时清除坐标数据
    clearCoordinate();
    console.log('已清除坐标数据');
  }

  // 改变地图鼠标样式
  const mapContainer = document.getElementById(props.mapId);
  if (mapContainer) {
    mapContainer.style.cursor = isCoordinateMode.value ? 'crosshair' : '';
    console.log('鼠标样式已更改为:', isCoordinateMode.value ? 'crosshair' : 'default');
  } else {
    console.error('找不到地图容器:', props.mapId);
  }

  // 检查地图实例是否存在
  if (!map.value) {
    console.error('地图实例不存在！');
    return;
  }

  console.log('坐标模式:', isCoordinateMode.value ? '开启' : '关闭');
  console.log('地图实例状态:', map.value ? '正常' : '未初始化');
};

// 清除坐标
const clearCoordinate = () => {
  lastClickedCoordinate.value = null;

  if (coordinateMarker.value && map.value) {
    map.value.remove(coordinateMarker.value);
    coordinateMarker.value = null;
  }
};



// 监听props变化
watch(() => props.markers, loadMarkers, { deep: true });
watch(() => props.polygons, loadPolygons, { deep: true });

// 生命周期
onMounted(() => {
  initMap();
});

onUnmounted(() => {
  if (map.value) {
    // 清理绘制工具
    clearDrawingTool();

    // 清理坐标功能
    clearCoordinate();
    map.value.off('click', handleCoordinateClick);

    // 清理所有标记
    markersLayer.value.forEach(marker => {
      map.value.remove(marker);
    });
    markersLayer.value = [];

    // 清理所有多边形
    polygonsLayer.value.forEach(polygon => {
      map.value.remove(polygon);
    });
    polygonsLayer.value = [];

    // 清理地图图层
    Object.values(mapLayers.value).forEach(layer => {
      if (layer) {
        try {
          map.value.remove(layer);
        } catch (error) {
          console.warn('清理图层时出错:', error);
        }
      }
    });
    mapLayers.value = {};

    // 销毁地图实例
    map.value.destroy();
    map.value = null;
  }
});

// 暴露方法给父组件
defineExpose({
  getMap: () => map.value,
  toggleLayer: (layerKey, visible) => {
    const layer = availableLayers.value.find(l => l.key === layerKey);
    if (layer) {
      layer.visible = visible;
      // 根据图层类型执行相应的显示/隐藏逻辑
      switch (layerKey) {
        case 'satellite':
          toggleSatelliteLayer(visible);
          break;
        case 'terrain':
          toggleTerrainLayer(visible);
          break;
        case 'placenames':
          togglePlaceNamesLayer(visible);
          break;
        case 'grid':
          toggleGridLayer(visible);
          break;
        case 'buildings':
          toggleBuildingsLayer(visible);
          break;
        case 'events':
          toggleEventsLayer(visible);
          break;
      }
    }
  },
  getLayerVisibility: (layerKey) => {
    const layer = availableLayers.value.find(l => l.key === layerKey);
    return layer ? layer.visible : false;
  },
  setLayerVisibility: (layerKey, visible) => {
    const layer = availableLayers.value.find(l => l.key === layerKey);
    if (layer && layer.visible !== visible) {
      toggleLayer(layerKey);
    }
  },
  addMarker: (lng, lat, options = {}) => {
    if (!map.value) return null;
    const marker = new AMap.Marker({
      position: [lng, lat],
      ...options
    });
    map.value.add(marker);
    markersLayer.value.push(marker);
    return marker;
  },
  addPolygon: (coordinates, options = {}) => {
    if (!map.value) return null;
    const path = coordinates.map(coord => [coord[1], coord[0]]);
    const polygon = new AMap.Polygon({
      path: path,
      ...options
    });
    map.value.add(polygon);
    polygonsLayer.value.push(polygon);
    return polygon;
  },
  clearAll: () => {
    if (map.value) {
      // 清除所有标记
      markersLayer.value.forEach(marker => {
        map.value.remove(marker);
      });
      markersLayer.value = [];

      // 清除所有多边形
      polygonsLayer.value.forEach(polygon => {
        map.value.remove(polygon);
      });
      polygonsLayer.value = [];
    }
  },
  resetView: () => {
    if (map.value) {
      map.value.setZoomAndCenter(props.zoom, props.center);
    }
  },
  refreshData: () => {
    // 清除现有数据并重新加载
    if (map.value) {
      // 清除标记
      markersLayer.value.forEach(marker => {
        map.value.remove(marker);
      });
      markersLayer.value = [];

      // 清除多边形
      polygonsLayer.value.forEach(polygon => {
        map.value.remove(polygon);
      });
      polygonsLayer.value = [];

      // 重新加载数据
      loadMarkers();
      loadPolygons();
    }
  },
  // 坐标模式控制方法
  enableCoordinateMode: () => {
    isCoordinateMode.value = true;
    console.log('坐标模式已启用');
  },
  disableCoordinateMode: () => {
    isCoordinateMode.value = false;
    clearCoordinate();
    console.log('坐标模式已禁用');
  },
  toggleCoordinateMode: () => {
    isCoordinateMode.value = !isCoordinateMode.value;
    if (!isCoordinateMode.value) {
      clearCoordinate();
    }
    console.log('坐标模式已切换为:', isCoordinateMode.value ? '启用' : '禁用');
  },
  getLastClickedCoordinate: () => {
    return lastClickedCoordinate.value;
  },
  clearCoordinate: () => {
    clearCoordinate();
  }
});
</script>

<style scoped>
.map-component {
  position: relative;
  width: 100%;
}

.map-container {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.map-controls {
  position: absolute;
  top: 15px;
  right: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.15);
  padding: 15px;
  max-width: 250px;
  max-height: 80%;
  overflow-y: auto;
  z-index: 1000;
}

.control-section {
  margin-bottom: 20px;
}

.control-section:last-child {
  margin-bottom: 0;
}

.control-section h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #e1e8ed;
  padding-bottom: 5px;
}

.layer-toggles {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.layer-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 13px;
  color: #666;
}

.layer-toggle input[type="checkbox"] {
  margin: 0;
}

.drawing-tools {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.tool-btn {
  background: #f8f9fa;
  border: 1px solid #e1e8ed;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
}

.tool-btn:hover {
  background: #e9ecef;
  border-color: #4a90e2;
}

.tool-btn.active {
  background: #4a90e2;
  color: white;
  border-color: #4a90e2;
}

.tool-icon {
  font-size: 14px;
}

.map-actions {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.action-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: background 0.3s ease;
}

.action-btn:hover {
  background: #5a6268;
}

.btn-icon {
  font-size: 14px;
}

.info-panel {
  position: absolute;
  bottom: 15px;
  left: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.15);
  max-width: 300px;
  z-index: 1000;
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #e1e8ed;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.info-header h4 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.close-btn:hover {
  background: #e9ecef;
  color: #333;
}

.info-content {
  padding: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-weight: 600;
  color: #666;
}

.info-value {
  color: #333;
}

/* 坐标工具样式 */
.coordinate-tools {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.tool-btn {
  background: #f8f9fa;
  border: 1px solid #e1e8ed;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
}

.tool-btn:hover {
  background: #e9ecef;
  border-color: #ced4da;
  color: #495057;
}

.tool-btn.active {
  background: #007bff;
  border-color: #007bff;
  color: white;
}

.tool-btn.copy-btn {
  background: #28a745;
  border-color: #28a745;
  color: white;
}

.tool-btn.copy-btn:hover {
  background: #218838;
  border-color: #1e7e34;
}

.tool-icon {
  font-size: 14px;
}

/* 坐标显示面板样式 */
.coordinate-display-panel {
  position: absolute;
  bottom: 15px;
  right: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
  max-width: 350px;
  min-width: 300px;
  z-index: 1001;
  border: 1px solid #e1e8ed;
}

.coordinate-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  border-bottom: 1px solid #e1e8ed;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px 8px 0 0;
}

.coordinate-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.coordinate-body {
  padding: 15px;
}

.coordinate-info {
  margin-bottom: 15px;
}

.coordinate-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
}

.coordinate-row:last-child {
  margin-bottom: 0;
}

.coordinate-row .label {
  font-weight: 500;
  color: #666;
}

.coordinate-row .value {
  color: #333;
  font-family: 'Courier New', monospace;
  background: #f8f9fa;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.json-section {
  margin-bottom: 15px;
}

.json-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.json-title {
  font-size: 13px;
  font-weight: 600;
  color: #495057;
}

.mini-copy-btn {
  background: #17a2b8;
  color: white;
  border: none;
  padding: 2px 6px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 10px;
  transition: background 0.2s ease;
}

.mini-copy-btn:hover {
  background: #138496;
}

.json-content {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 10px;
  font-family: 'Courier New', monospace;
  font-size: 10px;
  overflow-x: auto;
  max-height: 120px;
  overflow-y: auto;
}

.json-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  color: #495057;
  line-height: 1.4;
}

.coordinate-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  flex: 1;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.action-button.primary {
  background: #007bff;
  color: white;
}

.action-button.primary:hover {
  background: #0056b3;
}

.action-button.secondary {
  background: #6c757d;
  color: white;
}

.action-button.secondary:hover {
  background: #545b62;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .map-controls {
    position: static;
    margin-top: 15px;
    max-width: none;
    max-height: none;
  }

  .info-panel {
    position: static;
    margin-top: 15px;
    max-width: none;
  }

  .coordinate-display-panel {
    position: static;
    margin-top: 15px;
    max-width: none;
    min-width: auto;
  }

  .coordinate-actions {
    flex-direction: column;
  }
}
</style>
