package com.hfut.xiaozu.car;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageHelper;
import com.hfut.xiaozu.car.bind.VehicleHouseBindingDTO;
import com.hfut.xiaozu.car.bind.VehicleHouseBindingEntity;
import com.hfut.xiaozu.car.bind.VehicleHouseBindingMapper;
import com.hfut.xiaozu.car.info.VehicleDTO;
import com.hfut.xiaozu.car.info.VehicleInfo;
import com.hfut.xiaozu.car.info.VehicleInfoMapper;
import com.hfut.xiaozu.common.Result;
import com.hfut.xiaozu.house.binding.UserHouseBinding;
import com.hfut.xiaozu.house.binding.UserHouseBindingMapper;
import com.hfut.xiaozu.user.context.CurrentUser;
import com.hfut.xiaozu.user.context.UserContextHolder;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-06-27
 */
@Service
public class CarService {

    @Resource
    private VehicleInfoMapper vehicleInfoMapper;

    @Resource
    private VehicleHouseBindingMapper vehicleHouseBindingMapper;

    @Resource
    private UserHouseBindingMapper userHouseBindingMapper;


    public Result<?> addNewCar(VehicleDTO dto) {
        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        if(currentUser==null||!Objects.equals(currentUser.getRole(),CurrentUser.Resident)){
            return Result.fail("只有居民才能新增车辆");
        }

        VehicleInfo vehicleInfo = new VehicleInfo();
        BeanUtil.copyProperties(dto,vehicleInfo);
        vehicleInfo.setUserId(currentUser.getUserId());

        int insert = vehicleInfoMapper.insert(vehicleInfo);
        if(insert!=1){
            return Result.fail("新增车辆异常");
        }

        return Result.ok("新增车辆成功");

    }

    public Result<?> listMyCar() {

        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        if (currentUser == null || !Objects.equals(currentUser.getRole(), CurrentUser.Resident)) {
            return Result.fail("只有居民才能新增车辆");
        }

        List<VehicleInfo> cars = vehicleInfoMapper.getByUserId(currentUser.getUserId());

        //车辆基本信息
        List<Map<String,Object>> result = cars.stream()
                .map(obj->BeanUtil.beanToMap(obj,new HashMap<String,Object>(),
                        CopyOptions.create().setIgnoreProperties("createTime","updateTime")))
                .toList();

        //查询车辆-房屋绑定记录，判断该车辆状态
        //一辆车可以有多条绑定记录
        result.forEach(
                carinfo->{
                    List<VehicleHouseBindingEntity> bindingRecordList = vehicleHouseBindingMapper.ListByVehicleId((Long) carinfo.get("id"));
                    if(CollectionUtil.isEmpty(bindingRecordList)){
                        //未申请绑定任何一个房屋
                        carinfo.put("status",0);
                    }else {

                        List<Map<String, Object>> smallbindingRecordList = bindingRecordList.stream()
                                .map(obj -> BeanUtil.beanToMap(obj,new HashMap<String,Object>(),
                                        CopyOptions.create().setIgnoreProperties("createTime", "updateTime", "vehicleId", "approvedBy", "approvalRemark")))
                                .toList();

                        carinfo.put("bindingRecordList",smallbindingRecordList);
                    }}
                );

        return Result.ok(result);
    }

    public Result<?> getMyBindRecordByCarId(Long carId) {
        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        if (currentUser == null || !Objects.equals(currentUser.getRole(), CurrentUser.Resident)) {
            return Result.fail("只有居民查询自己的车辆房屋绑定申请车辆");
        }

        VehicleInfo vehicle = vehicleInfoMapper.getById(carId);
        if(vehicle == null || vehicle.getUserId()!=currentUser.getUserId()){
            return Result.fail("车辆不存在 或者 该车辆不属于你");
        }

        List<VehicleHouseBindingEntity> bindingRecord = vehicleHouseBindingMapper.ListByVehicleId(carId);

        if(CollectionUtil.isEmpty(bindingRecord)){
            return Result.fail("该车辆未申请绑定任何房屋");
        }

        List<Map<String,Object>> result = bindingRecord.stream()
                .map(obj->BeanUtil.beanToMap(obj,new HashMap<String,Object>() ,CopyOptions.create()
                        .setIgnoreProperties("createTime","updateTime")))
                .toList();

        return Result.ok(result);
    }

    @Transactional
    public Result<?> bindingHouse(VehicleHouseBindingDTO dto) {
        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        if (currentUser == null || !Objects.equals(currentUser.getRole(), CurrentUser.Resident)) {
            return Result.fail("只有居民才能申请车辆绑定房屋");
        }

        Long vehicleId = dto.getVehicleId();
        VehicleInfo car = vehicleInfoMapper.getById(vehicleId);
        if(car==null||car.getUserId()!=currentUser.getUserId()){
            return Result.fail("车辆不存在或者车辆不属于你");
        }

        List<Long> houseList = dto.getHouseIds();
        for (Long l : houseList) {
            UserHouseBinding houseRecord = userHouseBindingMapper.getByHouseId(l);

            if(houseRecord==null||!Objects.equals(houseRecord.getUserId(),currentUser.getUserId()) ||!Objects.equals(houseRecord.getStatus(),2)){
                return Result.fail("房屋不存在或者房屋不属于你");
            }
        }

        List<VehicleHouseBindingEntity> entities = houseList.stream()
                .map(houseId -> {
                    VehicleHouseBindingEntity entity = new VehicleHouseBindingEntity();

                    BeanUtil.copyProperties(dto, entity, "houseIds");
                    entity.setHouseId(houseId);
                    entity.setStatus(1); // 1-待审核

                    return entity;
                }).toList();

        // 批量插入数据库
        int insertCount = 0;
        for (VehicleHouseBindingEntity entity : entities) {
            insertCount += vehicleHouseBindingMapper.insert(entity);
        }

        if (insertCount != houseList.size()) {
            return Result.fail("车辆绑定房屋申请提交失败");
        }

        return Result.ok("车辆绑定房屋申请已提交，请等待审核");
    }

    public Result<?> listBingingRecordByStatus(Integer status, Integer pageNum, Integer pageSize) {
        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        if (currentUser == null || Objects.equals(currentUser.getRole(), CurrentUser.Resident)) {
            return Result.fail("权限不足");
        }

        PageHelper.startPage(pageNum, pageSize);
        List<VehicleHouseBindingEntity> result = vehicleHouseBindingMapper.listByStatus(status);

        if(CollectionUtil.isEmpty(result)){
            return Result.fail("当前状态无记录");
        }

        return Result.ok(result);
    }

    public Result<?> updateBingingRecord(Long id, Integer status, String remark) {
        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        if (currentUser == null || Objects.equals(currentUser.getRole(), CurrentUser.Resident)) {
            return Result.fail("权限不足");
        }

        VehicleHouseBindingEntity record = vehicleHouseBindingMapper.getById(id);

        if(record==null){
            return Result.fail("此记录不存在");
        }

        record.setStatus(status);
        record.setApprovalRemark(remark);
        record.setApprovedBy(currentUser.getUserId());

        int i = vehicleHouseBindingMapper.updateById(record);
        if(i!=1){
            return Result.fail("更新异常");
        }
        return Result.ok("更新成功");
    }
}
