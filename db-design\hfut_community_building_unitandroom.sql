-- 插入小区信息
INSERT INTO community_info (community_name, address_detail, geo_json)
VALUES (
    '合肥工业大学翡翠湖校区', 
    '安徽省合肥市蜀山区锦绣大道193号',
    '{"latitude": 117.2016, "longitude": 31.7726}'
);

-- 获取小区ID
SET @community_id = LAST_INSERT_ID();

-- 插入12栋楼信息
INSERT INTO building_info (community_id, building_code, building_name, geo_json)
VALUES 
(@community_id, '1', '一号楼', '{"latitude": 117.203154, "longitude": 31.773078}'),
(@community_id, '2', '二号楼', '{"latitude": 117.203133, "longitude": 31.774301}'),
(@community_id, '3', '三号楼', '{"latitude": 117.202435, "longitude": 31.772777}'),
(@community_id, '4', '四号楼', '{"latitude": 117.202468, "longitude": 31.773922}'),
(@community_id, '5', '五号楼', '{"latitude": 117.201711, "longitude": 31.772116}'),
(@community_id, '6', '六号楼', '{"latitude": 117.201647, "longitude": 31.773571}'),
(@community_id, '7', '七号楼', '{"latitude": 117.200896, "longitude": 31.771724}'),
(@community_id, '8', '八号楼', '{"latitude": 117.200874, "longitude": 31.773028}'),
(@community_id, '9', '九号楼', '{"latitude": 117.199968, "longitude": 31.771418}'),
(@community_id, '10', '十号楼', '{"latitude": 117.199115, "longitude": 31.772226}'),
(@community_id, '11', '十一号楼', '{"latitude": 117.19919, "longitude": 31.771195}'),
(@community_id, '12', '十二号楼', '{"latitude": 117.198246, "longitude": 31.771669}');

-- 插入单元信息（每栋楼2个单元）
INSERT INTO unit_info (building_id, unit_code, floor_count) VALUES (1, '1单元1', 10);
INSERT INTO unit_info (building_id, unit_code, floor_count) VALUES (1, '1单元2', 10);
INSERT INTO unit_info (building_id, unit_code, floor_count) VALUES (2, '2单元1', 10);
INSERT INTO unit_info (building_id, unit_code, floor_count) VALUES (2, '2单元2', 10);
INSERT INTO unit_info (building_id, unit_code, floor_count) VALUES (3, '3单元1', 10);
INSERT INTO unit_info (building_id, unit_code, floor_count) VALUES (3, '3单元2', 10);
INSERT INTO unit_info (building_id, unit_code, floor_count) VALUES (4, '4单元1', 10);
INSERT INTO unit_info (building_id, unit_code, floor_count) VALUES (4, '4单元2', 10);
INSERT INTO unit_info (building_id, unit_code, floor_count) VALUES (5, '5单元1', 10);
INSERT INTO unit_info (building_id, unit_code, floor_count) VALUES (5, '5单元2', 10);
INSERT INTO unit_info (building_id, unit_code, floor_count) VALUES (6, '6单元1', 10);
INSERT INTO unit_info (building_id, unit_code, floor_count) VALUES (6, '6单元2', 10);
INSERT INTO unit_info (building_id, unit_code, floor_count) VALUES (7, '7单元1', 10);
INSERT INTO unit_info (building_id, unit_code, floor_count) VALUES (7, '7单元2', 10);
INSERT INTO unit_info (building_id, unit_code, floor_count) VALUES (8, '8单元1', 10);
INSERT INTO unit_info (building_id, unit_code, floor_count) VALUES (8, '8单元2', 10);
INSERT INTO unit_info (building_id, unit_code, floor_count) VALUES (9, '9单元1', 10);
INSERT INTO unit_info (building_id, unit_code, floor_count) VALUES (9, '9单元2', 10);
INSERT INTO unit_info (building_id, unit_code, floor_count) VALUES (10, '10单元1', 10);
INSERT INTO unit_info (building_id, unit_code, floor_count) VALUES (10, '10单元2', 10);
INSERT INTO unit_info (building_id, unit_code, floor_count) VALUES (11, '11单元1', 10);
INSERT INTO unit_info (building_id, unit_code, floor_count) VALUES (11, '11单元2', 10);
INSERT INTO unit_info (building_id, unit_code, floor_count) VALUES (12, '12单元1', 10);
INSERT INTO unit_info (building_id, unit_code, floor_count) VALUES (12, '12单元2', 10);

-- 插入房屋信息（每个单元10层，每层2户）
-- 一号楼单元1
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(1, '1栋1单元0101', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(1, '1栋1单元0102', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(1, '1栋1单元0201', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(1, '1栋1单元0202', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(1, '1栋1单元0301', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(1, '1栋1单元0302', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(1, '1栋1单元0401', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(1, '1栋1单元0402', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(1, '1栋1单元0501', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(1, '1栋1单元0502', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(1, '1栋1单元0601', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(1, '1栋1单元0602', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(1, '1栋1单元0701', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(1, '1栋1单元0702', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(1, '1栋1单元0801', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(1, '1栋1单元0802', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(1, '1栋1单元0901', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(1, '1栋1单元0902', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(1, '1栋1单元1001', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(1, '1栋1单元1002', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2));

-- 一号楼单元2
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(2, '1栋2单元0101', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(2, '1栋2单元0102', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(2, '1栋2单元0201', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(2, '1栋2单元0202', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(2, '1栋2单元0301', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(2, '1栋2单元0302', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(2, '1栋2单元0401', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(2, '1栋2单元0402', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(2, '1栋2单元0501', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(2, '1栋2单元0502', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(2, '1栋2单元0601', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(2, '1栋2单元0602', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(2, '1栋2单元0701', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(2, '1栋2单元0702', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(2, '1栋2单元0801', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(2, '1栋2单元0802', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(2, '1栋2单元0901', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(2, '1栋2单元0902', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(2, '1栋2单元1001', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(2, '1栋2单元1002', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2));

-- 二号楼单元1
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(3, '2栋1单元0101', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(3, '2栋1单元0102', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(3, '2栋1单元0201', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(3, '2栋1单元0202', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(3, '2栋1单元0301', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(3, '2栋1单元0302', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(3, '2栋1单元0401', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(3, '2栋1单元0402', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(3, '2栋1单元0501', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(3, '2栋1单元0502', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(3, '2栋1单元0601', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(3, '2栋1单元0602', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(3, '2栋1单元0701', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(3, '2栋1单元0702', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(3, '2栋1单元0801', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(3, '2栋1单元0802', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(3, '2栋1单元0901', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(3, '2栋1单元0902', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(3, '2栋1单元1001', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(3, '2栋1单元1002', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2));

-- 二号楼单元2
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(4, '2栋2单元0101', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(4, '2栋2单元0102', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(4, '2栋2单元0201', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(4, '2栋2单元0202', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(4, '2栋2单元0301', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(4, '2栋2单元0302', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(4, '2栋2单元0401', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(4, '2栋2单元0402', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(4, '2栋2单元0501', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(4, '2栋2单元0502', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(4, '2栋2单元0601', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(4, '2栋2单元0602', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(4, '2栋2单元0701', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(4, '2栋2单元0702', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(4, '2栋2单元0801', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(4, '2栋2单元0802', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(4, '2栋2单元0901', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(4, '2栋2单元0902', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(4, '2栋2单元1001', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(4, '2栋2单元1002', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2));

-- 三号楼单元1
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(5, '3栋1单元0101', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(5, '3栋1单元0102', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(5, '3栋1单元0201', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(5, '3栋1单元0202', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(5, '3栋1单元0301', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(5, '3栋1单元0302', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(5, '3栋1单元0401', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(5, '3栋1单元0402', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(5, '3栋1单元0501', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(5, '3栋1单元0502', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(5, '3栋1单元0601', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(5, '3栋1单元0602', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(5, '3栋1单元0701', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(5, '3栋1单元0702', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(5, '3栋1单元0801', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(5, '3栋1单元0802', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(5, '3栋1单元0901', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(5, '3栋1单元0902', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(5, '3栋1单元1001', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(5, '3栋1单元1002', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2));

-- 三号楼单元2
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(6, '3栋2单元0101', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(6, '3栋2单元0102', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(6, '3栋2单元0201', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(6, '3栋2单元0202', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(6, '3栋2单元0301', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(6, '3栋2单元0302', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(6, '3栋2单元0401', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(6, '3栋2单元0402', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(6, '3栋2单元0501', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(6, '3栋2单元0502', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(6, '3栋2单元0601', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(6, '3栋2单元0602', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(6, '3栋2单元0701', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(6, '3栋2单元0702', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(6, '3栋2单元0801', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(6, '3栋2单元0802', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(6, '3栋2单元0901', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(6, '3栋2单元0902', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(6, '3栋2单元1001', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(6, '3栋2单元1002', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2));

-- 四号楼单元1
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(7, '4栋1单元0101', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(7, '4栋1单元0102', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(7, '4栋1单元0201', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(7, '4栋1单元0202', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(7, '4栋1单元0301', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(7, '4栋1单元0302', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(7, '4栋1单元0401', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(7, '4栋1单元0402', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(7, '4栋1单元0501', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(7, '4栋1单元0502', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(7, '4栋1单元0601', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(7, '4栋1单元0602', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(7, '4栋1单元0701', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(7, '4栋1单元0702', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(7, '4栋1单元0801', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(7, '4栋1单元0802', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(7, '4栋1单元0901', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(7, '4栋1单元0902', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(7, '4栋1单元1001', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(7, '4栋1单元1002', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2));

-- 四号楼单元2
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(8, '4栋2单元0101', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(8, '4栋2单元0102', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(8, '4栋2单元0201', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(8, '4栋2单元0202', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(8, '4栋2单元0301', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(8, '4栋2单元0302', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(8, '4栋2单元0401', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(8, '4栋2单元0402', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(8, '4栋2单元0501', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(8, '4栋2单元0502', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(8, '4栋2单元0601', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(8, '4栋2单元0602', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(8, '4栋2单元0701', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(8, '4栋2单元0702', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(8, '4栋2单元0801', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(8, '4栋2单元0802', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(8, '4栋2单元0901', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(8, '4栋2单元0902', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(8, '4栋2单元1001', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(8, '4栋2单元1002', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2));

-- 五号楼单元1
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(9, '5栋1单元0101', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(9, '5栋1单元0102', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(9, '5栋1单元0201', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(9, '5栋1单元0202', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(9, '5栋1单元0301', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(9, '5栋1单元0302', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(9, '5栋1单元0401', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(9, '5栋1单元0402', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(9, '5栋1单元0501', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(9, '5栋1单元0502', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(9, '5栋1单元0601', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(9, '5栋1单元0602', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(9, '5栋1单元0701', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(9, '5栋1单元0702', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(9, '5栋1单元0801', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(9, '5栋1单元0802', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(9, '5栋1单元0901', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(9, '5栋1单元0902', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(9, '5栋1单元1001', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(9, '5栋1单元1002', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2));

-- 五号楼单元2
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(10, '5栋2单元0101', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(10, '5栋2单元0102', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(10, '5栋2单元0201', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(10, '5栋2单元0202', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(10, '5栋2单元0301', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(10, '5栋2单元0302', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(10, '5栋2单元0401', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(10, '5栋2单元0402', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(10, '5栋2单元0501', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(10, '5栋2单元0502', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(10, '5栋2单元0601', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(10, '5栋2单元0602', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(10, '5栋2单元0701', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(10, '5栋2单元0702', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(10, '5栋2单元0801', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(10, '5栋2单元0802', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(10, '5栋2单元0901', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(10, '5栋2单元0902', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(10, '5栋2单元1001', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(10, '5栋2单元1002', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2));

-- 六号楼单元1
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(11, '6栋1单元0101', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(11, '6栋1单元0102', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(11, '6栋1单元0201', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(11, '6栋1单元0202', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(11, '6栋1单元0301', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(11, '6栋1单元0302', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(11, '6栋1单元0401', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(11, '6栋1单元0402', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(11, '6栋1单元0501', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(11, '6栋1单元0502', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(11, '6栋1单元0601', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(11, '6栋1单元0602', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(11, '6栋1单元0701', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(11, '6栋1单元0702', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(11, '6栋1单元0801', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(11, '6栋1单元0802', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(11, '6栋1单元0901', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(11, '6栋1单元0902', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(11, '6栋1单元1001', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(11, '6栋1单元1002', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2));

-- 六号楼单元2
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(12, '6栋2单元0101', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(12, '6栋2单元0102', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(12, '6栋2单元0201', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(12, '6栋2单元0202', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(12, '6栋2单元0301', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(12, '6栋2单元0302', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(12, '6栋2单元0401', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(12, '6栋2单元0402', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(12, '6栋2单元0501', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(12, '6栋2单元0502', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(12, '6栋2单元0601', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(12, '6栋2单元0602', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(12, '6栋2单元0701', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(12, '6栋2单元0702', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(12, '6栋2单元0801', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(12, '6栋2单元0802', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(12, '6栋2单元0901', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(12, '6栋2单元0902', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(12, '6栋2单元1001', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(12, '6栋2单元1002', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2));

-- 七号楼单元1
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(13, '7栋1单元0101', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(13, '7栋1单元0102', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(13, '7栋1单元0201', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(13, '7栋1单元0202', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(13, '7栋1单元0301', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(13, '7栋1单元0302', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(13, '7栋1单元0401', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(13, '7栋1单元0402', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(13, '7栋1单元0501', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(13, '7栋1单元0502', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(13, '7栋1单元0601', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(13, '7栋1单元0602', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(13, '7栋1单元0701', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(13, '7栋1单元0702', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(13, '7栋1单元0801', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(13, '7栋1单元0802', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(13, '7栋1单元0901', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(13, '7栋1单元0902', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(13, '7栋1单元1001', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(13, '7栋1单元1002', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2));

-- 七号楼单元2
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(14, '7栋2单元0101', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(14, '7栋2单元0102', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(14, '7栋2单元0201', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(14, '7栋2单元0202', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(14, '7栋2单元0301', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(14, '7栋2单元0302', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(14, '7栋2单元0401', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(14, '7栋2单元0402', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(14, '7栋2单元0501', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(14, '7栋2单元0502', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(14, '7栋2单元0601', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(14, '7栋2单元0602', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(14, '7栋2单元0701', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(14, '7栋2单元0702', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(14, '7栋2单元0801', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(14, '7栋2单元0802', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(14, '7栋2单元0901', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(14, '7栋2单元0902', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(14, '7栋2单元1001', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(14, '7栋2单元1002', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2));

-- 八号楼单元1
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(15, '8栋1单元0101', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(15, '8栋1单元0102', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(15, '8栋1单元0201', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(15, '8栋1单元0202', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(15, '8栋1单元0301', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(15, '8栋1单元0302', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(15, '8栋1单元0401', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(15, '8栋1单元0402', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(15, '8栋1单元0501', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(15, '8栋1单元0502', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(15, '8栋1单元0601', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(15, '8栋1单元0602', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(15, '8栋1单元0701', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(15, '8栋1单元0702', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(15, '8栋1单元0801', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(15, '8栋1单元0802', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(15, '8栋1单元0901', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(15, '8栋1单元0902', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(15, '8栋1单元1001', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(15, '8栋1单元1002', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2));

-- 八号楼单元2
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(16, '8栋2单元0101', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(16, '8栋2单元0102', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(16, '8栋2单元0201', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(16, '8栋2单元0202', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(16, '8栋2单元0301', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(16, '8栋2单元0302', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(16, '8栋2单元0401', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(16, '8栋2单元0402', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(16, '8栋2单元0501', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(16, '8栋2单元0502', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(16, '8栋2单元0601', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(16, '8栋2单元0602', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(16, '8栋2单元0701', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(16, '8栋2单元0702', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(16, '8栋2单元0801', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(16, '8栋2单元0802', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(16, '8栋2单元0901', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(16, '8栋2单元0902', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(16, '8栋2单元1001', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(16, '8栋2单元1002', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2));

-- 九号楼单元1
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(17, '9栋1单元0101', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(17, '9栋1单元0102', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(17, '9栋1单元0201', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(17, '9栋1单元0202', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(17, '9栋1单元0301', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(17, '9栋1单元0302', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(17, '9栋1单元0401', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(17, '9栋1单元0402', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(17, '9栋1单元0501', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(17, '9栋1单元0502', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(17, '9栋1单元0601', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(17, '9栋1单元0602', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(17, '9栋1单元0701', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(17, '9栋1单元0702', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(17, '9栋1单元0801', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(17, '9栋1单元0802', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(17, '9栋1单元0901', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(17, '9栋1单元0902', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(17, '9栋1单元1001', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(17, '9栋1单元1002', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2));

-- 九号楼单元2
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(18, '9栋2单元0101', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(18, '9栋2单元0102', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(18, '9栋2单元0201', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(18, '9栋2单元0202', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(18, '9栋2单元0301', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(18, '9栋2单元0302', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(18, '9栋2单元0401', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(18, '9栋2单元0402', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(18, '9栋2单元0501', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(18, '9栋2单元0502', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(18, '9栋2单元0601', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(18, '9栋2单元0602', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(18, '9栋2单元0701', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(18, '9栋2单元0702', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(18, '9栋2单元0801', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(18, '9栋2单元0802', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(18, '9栋2单元0901', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(18, '9栋2单元0902', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(18, '9栋2单元1001', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(18, '9栋2单元1002', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2));

-- 十号楼单元1
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(19, '10栋1单元0101', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(19, '10栋1单元0102', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(19, '10栋1单元0201', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(19, '10栋1单元0202', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(19, '10栋1单元0301', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(19, '10栋1单元0302', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(19, '10栋1单元0401', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(19, '10栋1单元0402', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(19, '10栋1单元0501', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(19, '10栋1单元0502', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(19, '10栋1单元0601', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(19, '10栋1单元0602', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(19, '10栋1单元0701', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(19, '10栋1单元0702', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(19, '10栋1单元0801', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(19, '10栋1单元0802', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(19, '10栋1单元0901', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(19, '10栋1单元0902', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(19, '10栋1单元1001', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(19, '10栋1单元1002', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2));

-- 十号楼单元2
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(20, '10栋2单元0101', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(20, '10栋2单元0102', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(20, '10栋2单元0201', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(20, '10栋2单元0202', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(20, '10栋2单元0301', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(20, '10栋2单元0302', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(20, '10栋2单元0401', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(20, '10栋2单元0402', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(20, '10栋2单元0501', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(20, '10栋2单元0502', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(20, '10栋2单元0601', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(20, '10栋2单元0602', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(20, '10栋2单元0701', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(20, '10栋2单元0702', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(20, '10栋2单元0801', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(20, '10栋2单元0802', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(20, '10栋2单元0901', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(20, '10栋2单元0902', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(20, '10栋2单元1001', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(20, '10栋2单元1002', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2));

-- 十一号楼单元1
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(21, '11栋1单元0101', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(21, '11栋1单元0102', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(21, '11栋1单元0201', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(21, '11栋1单元0202', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(21, '11栋1单元0301', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(21, '11栋1单元0302', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(21, '11栋1单元0401', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(21, '11栋1单元0402', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(21, '11栋1单元0501', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(21, '11栋1单元0502', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(21, '11栋1单元0601', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(21, '11栋1单元0602', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(21, '11栋1单元0701', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(21, '11栋1单元0702', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(21, '11栋1单元0801', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(21, '11栋1单元0802', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(21, '11栋1单元0901', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(21, '11栋1单元0902', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(21, '11栋1单元1001', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(21, '11栋1单元1002', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2));

-- 十一号楼单元2
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(22, '11栋2单元0101', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(22, '11栋2单元0102', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(22, '11栋2单元0201', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(22, '11栋2单元0202', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(22, '11栋2单元0301', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(22, '11栋2单元0302', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(22, '11栋2单元0401', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(22, '11栋2单元0402', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(22, '11栋2单元0501', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(22, '11栋2单元0502', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(22, '11栋2单元0601', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(22, '11栋2单元0602', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(22, '11栋2单元0701', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(22, '11栋2单元0702', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(22, '11栋2单元0801', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(22, '11栋2单元0802', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(22, '11栋2单元0901', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(22, '11栋2单元0902', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(22, '11栋2单元1001', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(22, '11栋2单元1002', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2));

-- 十二号楼单元1
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(23, '12栋1单元0101', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(23, '12栋1单元0102', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(23, '12栋1单元0201', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(23, '12栋1单元0202', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(23, '12栋1单元0301', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(23, '12栋1单元0302', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(23, '12栋1单元0401', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(23, '12栋1单元0402', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(23, '12栋1单元0501', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(23, '12栋1单元0502', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(23, '12栋1单元0601', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(23, '12栋1单元0602', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(23, '12栋1单元0701', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(23, '12栋1单元0702', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(23, '12栋1单元0801', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(23, '12栋1单元0802', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(23, '12栋1单元0901', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(23, '12栋1单元0902', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(23, '12栋1单元1001', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(23, '12栋1单元1002', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2));

-- 十二号楼单元2
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(24, '12栋2单元0101', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(24, '12栋2单元0102', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(24, '12栋2单元0201', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(24, '12栋2单元0202', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(24, '12栋2单元0301', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(24, '12栋2单元0302', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(24, '12栋2单元0401', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(24, '12栋2单元0402', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(24, '12栋2单元0501', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(24, '12栋2单元0502', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(24, '12栋2单元0601', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(24, '12栋2单元0602', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(24, '12栋2单元0701', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(24, '12栋2单元0702', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(24, '12栋2单元0801', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(24, '12栋2单元0802', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(24, '12栋2单元0901', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(24, '12栋2单元0902', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(24, '12栋2单元1001', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2)),
(24, '12栋2单元1002', ROUND(RAND() * 20 + 30, 2), FLOOR(RAND() * 2));
    