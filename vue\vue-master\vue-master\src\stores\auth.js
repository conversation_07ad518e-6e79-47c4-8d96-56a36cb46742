/**
 * 认证状态管理
 * 使用Pinia管理用户登录状态、token等
 */

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { authApi } from '../services/authApi.js';

export const useAuthStore = defineStore('auth', () => {
  // ==================== 状态定义 ====================
  
  // 用户信息
  const user = ref(null);
  
  // 认证token
  const token = ref('');
  
  // 刷新token
  const refreshToken = ref('');
  
  // 登录状态
  const isLoading = ref(false);
  
  // token过期时间
  const tokenExpiry = ref(null);
  
  // 记住我状态
  const rememberMe = ref(false);

  // ==================== 计算属性 ====================
  
  // 是否已认证
  const isAuthenticated = computed(() => {
    const hasToken = !!token.value;
    const hasUser = !!user.value;
    const tokenNotExpired = !isTokenExpired.value;
    const result = hasToken && hasUser && tokenNotExpired;

    console.log('🔍 isAuthenticated 计算:', {
      hasToken,
      hasUser,
      tokenNotExpired,
      tokenExpiry: tokenExpiry.value,
      currentTime: new Date().toISOString(),
      result
    });

    return result;
  });
  
  // token是否过期
  const isTokenExpired = computed(() => {
    if (!tokenExpiry.value) return false;
    return new Date() >= new Date(tokenExpiry.value);
  });
  
  // 用户角色
  const userRole = computed(() => {
    return user.value?.role || 'guest';
  });
  
  // 用户权限
  const userPermissions = computed(() => {
    return user.value?.permissions || [];
  });

  // ==================== 方法定义 ====================
  
  /**
   * 登录
   * @param {Object} credentials - 登录凭据
   * @param {string} credentials.username - 用户名
   * @param {string} credentials.password - 密码
   * @param {boolean} credentials.rememberMe - 是否记住我
   */
  async function login(credentials) {
    try {
      isLoading.value = true;
      
      console.log('🔐 发起登录请求...');
      console.log('📝 登录参数:', {
        username: credentials.username,
        rememberMe: credentials.rememberMe
      });
      
      // 调用登录API
      const response = await authApi.login({
        username: credentials.username,
        password: credentials.password
      });
      
      if (!response.success) {
        throw new Error(response.message || '登录失败');
      }
      
      const { data } = response;
      
      console.log('✅ 登录API响应成功');
      console.log('👤 用户信息:', data.user);
      console.log('🔑 Token信息:', {
        token: data.token.substring(0, 20) + '...',
        expiresAt: data.expiresAt
      });
      
      // 保存认证信息
      await setAuthData({
        user: data.user,
        token: data.token,
        refreshToken: data.refreshToken,
        expiresAt: data.expiresAt,
        rememberMe: credentials.rememberMe
      });
      
      console.log('💾 认证信息已保存');
      
      // 设置token自动刷新
      scheduleTokenRefresh();
      
      return data;
      
    } catch (error) {
      console.error('❌ 登录失败:', error);
      
      // 清除可能的残留数据
      await clearAuthData();
      
      // 重新抛出错误供组件处理
      throw error;
    } finally {
      isLoading.value = false;
    }
  }
  
  /**
   * 登出
   */
  async function logout() {
    try {
      console.log('🚪 开始登出流程...');
      
      // 如果有token，通知服务器登出
      if (token.value) {
        try {
          await authApi.logout();
          console.log('✅ 服务器登出成功');
        } catch (error) {
          console.warn('⚠️ 服务器登出失败，继续本地登出:', error.message);
        }
      }
      
      // 清除本地认证数据
      await clearAuthData();
      
      console.log('✅ 登出完成');
      
    } catch (error) {
      console.error('❌ 登出过程中发生错误:', error);
      // 即使出错也要清除本地数据
      await clearAuthData();
    }
  }
  
  /**
   * 刷新token
   */
  async function refreshAuthToken() {
    try {
      console.log('🔄 开始刷新token...');
      
      if (!refreshToken.value) {
        throw new Error('没有刷新token');
      }
      
      const response = await authApi.refreshToken(refreshToken.value);
      
      if (!response.success) {
        throw new Error(response.message || 'token刷新失败');
      }
      
      const { data } = response;
      
      // 更新token信息
      token.value = data.token;
      tokenExpiry.value = data.expiresAt;
      
      if (data.refreshToken) {
        refreshToken.value = data.refreshToken;
      }
      
      // 保存到存储
      saveTokenToStorage();
      
      console.log('✅ Token刷新成功');
      
      // 重新设置自动刷新
      scheduleTokenRefresh();
      
      return data;
      
    } catch (error) {
      console.error('❌ Token刷新失败:', error);
      
      // 刷新失败，清除认证信息并跳转到登录页
      await clearAuthData();
      
      // 跳转到登录页
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
      
      throw error;
    }
  }
  
  /**
   * 设置认证数据
   */
  async function setAuthData(authData) {
    user.value = authData.user;
    token.value = authData.token;
    refreshToken.value = authData.refreshToken;
    tokenExpiry.value = authData.expiresAt;
    rememberMe.value = authData.rememberMe;
    
    // 保存到本地存储
    saveToStorage();
  }
  
  /**
   * 清除认证数据
   */
  async function clearAuthData() {
    user.value = null;
    token.value = '';
    refreshToken.value = '';
    tokenExpiry.value = null;
    rememberMe.value = false;
    
    // 清除本地存储
    clearStorage();
    
    // 清除定时器
    if (refreshTimer) {
      clearTimeout(refreshTimer);
      refreshTimer = null;
    }
  }
  
  /**
   * 从本地存储恢复认证状态
   */
  function restoreAuthState() {
    try {
      console.log('🔄 恢复认证状态...');
      
      // 尝试从localStorage恢复
      const persistentData = localStorage.getItem('auth_data');
      if (persistentData) {
        const data = JSON.parse(persistentData);
        console.log('📱 从localStorage恢复数据');
        restoreFromData(data);
        return;
      }
      
      // 尝试从sessionStorage恢复
      const sessionData = sessionStorage.getItem('auth_data');
      if (sessionData) {
        const data = JSON.parse(sessionData);
        console.log('💾 从sessionStorage恢复数据');
        restoreFromData(data);
        return;
      }
      
      console.log('ℹ️ 没有找到认证数据');
      
    } catch (error) {
      console.error('❌ 恢复认证状态失败:', error);
      clearAuthData();
    }
  }
  
  /**
   * 从数据恢复状态
   */
  function restoreFromData(data) {
    user.value = data.user;
    token.value = data.token;
    refreshToken.value = data.refreshToken;
    tokenExpiry.value = data.tokenExpiry;
    rememberMe.value = data.rememberMe;
    
    // 检查token是否过期
    if (isTokenExpired.value) {
      console.log('⚠️ Token已过期，尝试刷新...');
      refreshAuthToken().catch(() => {
        console.log('❌ Token刷新失败，清除认证数据');
        clearAuthData();
      });
    } else {
      console.log('✅ 认证状态恢复成功');
      // 设置自动刷新
      scheduleTokenRefresh();
    }
  }
  
  /**
   * 保存到本地存储
   */
  function saveToStorage() {
    const authData = {
      user: user.value,
      token: token.value,
      refreshToken: refreshToken.value,
      tokenExpiry: tokenExpiry.value,
      rememberMe: rememberMe.value
    };
    
    const dataString = JSON.stringify(authData);
    
    if (rememberMe.value) {
      // 记住我：保存到localStorage
      localStorage.setItem('auth_data', dataString);
      sessionStorage.removeItem('auth_data');
      console.log('💾 认证数据已保存到localStorage');
    } else {
      // 不记住我：保存到sessionStorage
      sessionStorage.setItem('auth_data', dataString);
      localStorage.removeItem('auth_data');
      console.log('💾 认证数据已保存到sessionStorage');
    }
  }
  
  /**
   * 仅保存token到存储（用于token刷新）
   */
  function saveTokenToStorage() {
    const storage = rememberMe.value ? localStorage : sessionStorage;
    const existingData = storage.getItem('auth_data');
    
    if (existingData) {
      const data = JSON.parse(existingData);
      data.token = token.value;
      data.tokenExpiry = tokenExpiry.value;
      data.refreshToken = refreshToken.value;
      storage.setItem('auth_data', JSON.stringify(data));
    }
  }
  
  /**
   * 清除本地存储
   */
  function clearStorage() {
    localStorage.removeItem('auth_data');
    sessionStorage.removeItem('auth_data');
    console.log('🗑️ 本地存储已清除');
  }
  
  // ==================== Token自动刷新 ====================
  
  let refreshTimer = null;
  
  /**
   * 设置token自动刷新
   */
  function scheduleTokenRefresh() {
    // 清除现有定时器
    if (refreshTimer) {
      clearTimeout(refreshTimer);
    }
    
    if (!tokenExpiry.value) return;
    
    const expiryTime = new Date(tokenExpiry.value).getTime();
    const currentTime = Date.now();
    const timeUntilExpiry = expiryTime - currentTime;
    
    // 在过期前5分钟刷新token
    const refreshTime = Math.max(timeUntilExpiry - 5 * 60 * 1000, 60 * 1000);
    
    if (refreshTime > 0) {
      refreshTimer = setTimeout(() => {
        console.log('⏰ 自动刷新token...');
        refreshAuthToken().catch(error => {
          console.error('❌ 自动刷新token失败:', error);
        });
      }, refreshTime);
      
      console.log(`⏰ Token将在 ${Math.round(refreshTime / 1000 / 60)} 分钟后自动刷新`);
    }
  }
  
  // ==================== 权限检查 ====================
  
  /**
   * 检查用户是否有指定权限
   * @param {string} permission - 权限名称
   * @returns {boolean}
   */
  function hasPermission(permission) {
    return userPermissions.value.includes(permission);
  }
  
  /**
   * 检查用户是否有指定角色
   * @param {string} role - 角色名称
   * @returns {boolean}
   */
  function hasRole(role) {
    return userRole.value === role;
  }
  
  // ==================== 返回store接口 ====================
  
  return {
    // 状态
    user,
    token,
    refreshToken,
    isLoading,
    tokenExpiry,
    rememberMe,
    
    // 计算属性
    isAuthenticated,
    isTokenExpired,
    userRole,
    userPermissions,
    
    // 方法
    login,
    logout,
    refreshAuthToken,
    setAuthData,
    clearAuthData,
    restoreAuthState,
    hasPermission,
    hasRole
  };
});
