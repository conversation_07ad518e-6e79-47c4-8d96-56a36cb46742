<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>坐标获取功能测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        #map-container {
            width: 100%;
            height: 500px;
            border: 1px solid #ccc;
            position: relative;
        }
        
        .controls {
            margin-bottom: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            margin-right: 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary.active {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .coordinate-display {
            margin-top: 10px;
            padding: 15px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            display: none;
        }
        
        .coordinate-display.show {
            display: block;
        }
        
        .json-output {
            background: #fff;
            border: 1px solid #ddd;
            padding: 10px;
            margin-top: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>地图坐标获取功能测试</h1>
    
    <div class="controls">
        <button id="toggle-coordinate" class="btn btn-primary">获取坐标</button>
        <button id="copy-json" class="btn btn-success" style="display: none;">复制JSON</button>
        <button id="clear-coordinate" class="btn" style="display: none;">清除</button>
    </div>
    
    <div id="map-container"></div>
    
    <div id="coordinate-display" class="coordinate-display">
        <h3>📍 点击坐标信息</h3>
        <div id="coordinate-info"></div>
        <div class="json-output" id="json-output"></div>
    </div>

    <script>
        // 高德地图配置
        window._AMapSecurityConfig = {
            securityJsCode: '830ff4b97ad96c81625c4d44049c8452',
        };
    </script>
    <script src="https://webapi.amap.com/maps?v=2.0&key=6654ed14b4cb7aae5003dc379fd3f134"></script>
    
    <script>
        let map = null;
        let isCoordinateMode = false;
        let lastClickedCoordinate = null;
        let coordinateMarker = null;
        
        // 初始化地图
        function initMap() {
            console.log('初始化地图...');
            
            map = new AMap.Map('map-container', {
                center: [117.198612, 31.774164], // 设置的中心点坐标
                zoom: 16,
                mapStyle: 'amap://styles/normal',
                viewMode: '2D'
            });
            
            console.log('地图初始化完成');
            
            // 添加地图点击事件
            map.on('click', handleMapClick);
            
            console.log('地图点击事件已绑定');
        }
        
        // 处理地图点击
        function handleMapClick(event) {
            console.log('地图点击事件触发，坐标模式:', isCoordinateMode);
            
            if (!isCoordinateMode) {
                console.log('不在坐标模式，忽略点击');
                return;
            }
            
            const { lng, lat } = event.lnglat;
            console.log('获取到坐标:', lng, lat);
            
            // 更新坐标信息
            lastClickedCoordinate = {
                lng: lng,
                lat: lat,
                timestamp: new Date().toISOString(),
                clickTime: new Date().toLocaleString('zh-CN')
            };
            
            // 清除之前的标记
            if (coordinateMarker) {
                map.remove(coordinateMarker);
            }
            
            // 创建新标记
            coordinateMarker = new AMap.Marker({
                position: [lng, lat],
                title: `坐标点 (${lng.toFixed(6)}, ${lat.toFixed(6)})`,
                content: '<div style="background: red; width: 20px; height: 20px; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>'
            });
            
            map.add(coordinateMarker);
            
            // 显示坐标信息
            displayCoordinate();
        }
        
        // 显示坐标信息
        function displayCoordinate() {
            if (!lastClickedCoordinate) return;
            
            const coord = lastClickedCoordinate;
            
            // 更新坐标信息显示
            document.getElementById('coordinate-info').innerHTML = `
                <p><strong>经度:</strong> ${coord.lng.toFixed(6)}</p>
                <p><strong>纬度:</strong> ${coord.lat.toFixed(6)}</p>
                <p><strong>点击时间:</strong> ${coord.clickTime}</p>
            `;
            
            // 生成JSON
            const jsonData = {
                longitude: parseFloat(coord.lng.toFixed(6)),
                latitude: parseFloat(coord.lat.toFixed(6)),
                timestamp: coord.timestamp,
                clickTime: coord.clickTime,
                location: {
                    type: "Point",
                    coordinates: [
                        parseFloat(coord.lng.toFixed(6)),
                        parseFloat(coord.lat.toFixed(6))
                    ]
                }
            };
            
            document.getElementById('json-output').textContent = JSON.stringify(jsonData, null, 2);
            
            // 显示坐标面板
            document.getElementById('coordinate-display').classList.add('show');
            document.getElementById('copy-json').style.display = 'inline-block';
            document.getElementById('clear-coordinate').style.display = 'inline-block';
        }
        
        // 切换坐标模式
        function toggleCoordinateMode() {
            isCoordinateMode = !isCoordinateMode;
            
            const btn = document.getElementById('toggle-coordinate');
            if (isCoordinateMode) {
                btn.textContent = '退出坐标模式';
                btn.classList.add('active');
                document.getElementById('map-container').style.cursor = 'crosshair';
                console.log('坐标模式已开启');
            } else {
                btn.textContent = '获取坐标';
                btn.classList.remove('active');
                document.getElementById('map-container').style.cursor = '';
                clearCoordinate();
                console.log('坐标模式已关闭');
            }
        }
        
        // 清除坐标
        function clearCoordinate() {
            lastClickedCoordinate = null;
            
            if (coordinateMarker && map) {
                map.remove(coordinateMarker);
                coordinateMarker = null;
            }
            
            document.getElementById('coordinate-display').classList.remove('show');
            document.getElementById('copy-json').style.display = 'none';
            document.getElementById('clear-coordinate').style.display = 'none';
        }
        
        // 复制JSON
        function copyJSON() {
            if (!lastClickedCoordinate) return;
            
            const jsonText = document.getElementById('json-output').textContent;
            
            navigator.clipboard.writeText(jsonText).then(() => {
                alert('JSON已复制到剪贴板！');
            }).catch(err => {
                console.error('复制失败:', err);
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = jsonText;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('JSON已复制到剪贴板！');
            });
        }
        
        // 绑定事件
        document.getElementById('toggle-coordinate').addEventListener('click', toggleCoordinateMode);
        document.getElementById('copy-json').addEventListener('click', copyJSON);
        document.getElementById('clear-coordinate').addEventListener('click', clearCoordinate);
        
        // 页面加载完成后初始化地图
        window.onload = function() {
            console.log('页面加载完成，开始初始化地图');
            initMap();
        };
    </script>
</body>
</html>
