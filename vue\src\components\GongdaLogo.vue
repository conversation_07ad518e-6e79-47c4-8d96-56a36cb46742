<template>
  <div class="gongda-logo" :style="{ width: size + 'px', height: size + 'px' }">
    <img
      :src="logoIcon"
      :alt="alt"
      :width="size"
      :height="size"
      class="logo-image"
    />
  </div>
</template>

<script setup>
import logoIcon from '../assets/icon/001.png';

// 定义props
const props = defineProps({
  size: {
    type: Number,
    default: 40
  },
  alt: {
    type: String,
    default: '软工tech Logo'
  }
});
</script>

<style scoped>
.gongda-logo {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: inherit;
}

.logo-image {
  transition: transform 0.3s ease;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  border-radius: 4px;
  object-fit: contain;
}

.gongda-logo:hover .logo-image {
  transform: scale(1.05);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15));
}
</style>
