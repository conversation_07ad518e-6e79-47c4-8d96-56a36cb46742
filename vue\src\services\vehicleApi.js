/**
 * 车辆管理API服务
 * 处理车辆相关的后端API调用
 * 使用HTTP拦截器自动处理token和错误
 */

import http from '../utils/httpInterceptor.js';

/**
 * 添加新车辆
 * @param {Object} vehicleData - 车辆数据
 * @returns {Promise<Object>} API响应
 */
export const addVehicle = async (vehicleData) => {
  console.log('🔧 API: 添加新车辆', vehicleData);

  try {
    // 使用HTTP拦截器发送请求（自动添加token）
    const response = await http.post('/car/add', vehicleData);

    console.log('✅ API: 车辆添加成功', response);

    return {
      success: true,
      data: response.data,
      message: response.msg || '车辆添加成功'
    };

  } catch (error) {
    console.error('❌ API: 车辆添加失败', error);
    throw error;
  }
};

/**
 * 获取当前用户的车辆列表
 * @returns {Promise<Object>} API响应
 */
export const getMyVehicles = async () => {
  console.log('🔧 API: 获取我的车辆列表');

  try {
    // 使用HTTP拦截器发送请求（自动添加token）
    const response = await http.get('/car/list/me');

    console.log('✅ API: 车辆列表获取成功', response);

    return {
      success: true,
      data: response.data,
      message: response.msg || '车辆列表获取成功'
    };

  } catch (error) {
    console.error('❌ API: 车辆列表获取失败', error);
    throw error;
  }
};

/**
 * 获取车辆绑定记录状态
 * @param {number} carId - 车辆ID
 * @returns {Promise<Object>} API响应
 */
export const getVehicleBindingStatus = async (carId) => {
  console.log('🔧 API: 获取车辆绑定状态', carId);

  try {
    // 使用HTTP拦截器发送请求（自动添加token）
    const response = await http.get(`/car/binding/${carId}`);

    console.log('✅ API: 车辆绑定状态获取成功', response);

    return {
      success: true,
      data: response.data,
      message: response.msg || '车辆绑定状态获取成功'
    };

  } catch (error) {
    console.error('❌ API: 车辆绑定状态获取失败', error);
    throw error;
  }
};

/**
 * 获取当前用户的房屋绑定信息
 * @returns {Promise<Object>} API响应
 */
export const getMyHouseBindings = async () => {
  console.log('🔧 API: 获取我的房屋绑定信息');

  try {
    // 使用HTTP拦截器发送请求（自动添加token）
    const response = await http.get('/grids/houses/binding/me');

    console.log('✅ API: 房屋绑定信息获取成功', response);

    return {
      success: true,
      data: response.data,
      message: response.msg || '房屋绑定信息获取成功'
    };

  } catch (error) {
    console.error('❌ API: 房屋绑定信息获取失败', error);
    throw error;
  }
};

/**
 * 申请车辆绑定房屋
 * @param {Object} bindingData - 绑定数据
 * @returns {Promise<Object>} API响应
 */
export const bindVehicleToHouse = async (bindingData) => {
  console.log('🔧 API: 申请车辆绑定房屋', bindingData);

  try {
    // 使用HTTP拦截器发送请求（自动添加token）
    const response = await http.post('/car/binding', bindingData);

    console.log('✅ API: 车辆绑定申请成功', response);

    return {
      success: true,
      data: response.data,
      message: response.msg || '车辆绑定申请成功'
    };

  } catch (error) {
    console.error('❌ API: 车辆绑定申请失败', error);
    throw error;
  }
};
