package com.hfut.xiaozu;

import cn.hutool.core.collection.CollectionUtil;
import com.hfut.xiaozu.common.Result;
import com.hfut.xiaozu.house.information.CommunitiesVO;
import com.hfut.xiaozu.house.information.CommunityInfo;
import com.hfut.xiaozu.house.information.CommunityInfoMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-25
 */
@SpringBootTest
@Slf4j
public class MapperTest {
    @Resource
    CommunityInfoMapper communityInfoMapper;

    @Test
    public void listAllcommunities() {

        List<CommunityInfo> list = communityInfoMapper.list();

        if(CollectionUtil.isEmpty(list)){
            log.error("null");
        }

        List<CommunitiesVO> result=new ArrayList<>();

        for (CommunityInfo communityInfo : list) {
            log.info(communityInfo.toString());
        }

    }
}
