package com.hfut.xiaozu.common.ocr;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025-06-24
 */
@Configuration
@Getter
public class IDOCRConfig {
    @Value("${ID_OCR_API_KEY:null}")
    private String apiKey;

    @Value("${ID_OCR_SECRET_KEY:null}")
    private String secretKey;

    @Value("${ID_OCR_ACCESS_TOKEN:null}")
    private String accessToken;
}
