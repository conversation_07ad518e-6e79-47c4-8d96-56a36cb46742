package com.hfut.xiaozu.family.info;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【family_member(业主家庭成员信息表)】的数据库操作Mapper
* @createDate 2025-06-28 15:55:06
* @Entity com.hfut.xiaozu.user.family.FamilyMember
*/
@Mapper
public interface FamilyMemberMapper {

    int deleteById(Long id);

    int insert(FamilyMemberEntity record);

    FamilyMemberEntity getById(Long id);

    List<FamilyMemberEntity> listByOwnerId(Long owner_id);

    int update(FamilyMemberEntity record);

}
