<template>
  <div class="resident-dashboard">
    <div class="dashboard-header">
      <h1>居民端</h1>
      <p class="subtitle">社区服务系统 - 便民服务与功能导航</p>
    </div>
    
    <div class="dashboard-content">
      <!-- 居民端功能模块 -->
      <section class="function-section">
        <h2 class="section-title">居民服务功能</h2>
        <div class="grid-container">
          <div class="grid-item" @click="navigateTo('/resident/real-name-auth')">
            <div class="item-icon">🆔</div>
            <h3>实名认证</h3>
            <p>表单填写, OCR识别, 认证状态管理, 真实性校验</p>
          </div>
          
          <div class="grid-item" @click="navigateTo('/resident/house-management')">
            <div class="item-icon">🏠</div>
            <h3>房屋管理</h3>
            <p>房屋绑定, 二维码绑定, 档案信息同步, 绑定审核</p>
          </div>
          
          <div class="grid-item" @click="navigateTo('/resident/vehicle-management')">
            <div class="item-icon">🚗</div>
            <h3>车辆管理</h3>
            <p>车辆车位绑定, 车辆房屋绑定, 车辆信息管理, 车辆验证</p>
          </div>
          
          <div class="grid-item" @click="navigateTo('/resident/family-management')">
            <div class="item-icon">👨‍👩‍👧‍👦</div>
            <h3>家人管理</h3>
            <p>家人信息录入, 家人绑定业主</p>
          </div>
          
          <div class="grid-item" @click="navigateTo('/resident/issue-report')">
            <div class="item-icon">📋</div>
            <h3>问题上报</h3>
            <p>问题反馈, 事件分类, 事件流转与处理跟踪, 进度通知</p>
          </div>
          
          <div class="grid-item" @click="navigateTo('/resident/budget-vote')">
            <div class="item-icon">🗳️</div>
            <h3>预算支出投票</h3>
            <p>投票管理, 投票结果展示, 投票鉴权与投票</p>
          </div>
        </div>
      </section>

      <!-- 公共模块 -->
      <section class="function-section">
        <h2 class="section-title">公共模块</h2>
        <div class="grid-container">
          <div class="grid-item" @click="navigateTo('/common/user-permission')">
            <div class="item-icon">👥</div>
            <h3>用户与权限服务</h3>
            <p>登录鉴权, 账号体系</p>
          </div>
          
          <div class="grid-item" @click="navigateTo('/common/file-storage')">
            <div class="item-icon">💾</div>
            <h3>文件存储服务</h3>
            <p>阿里OSS</p>
          </div>
          
          <div class="grid-item" @click="navigateTo('/common/message-notification')">
            <div class="item-icon">📢</div>
            <h3>消息通知服务</h3>
            <p>系统内通知</p>
          </div>
          
          <div class="grid-item" @click="navigateTo('/common/log-monitor')">
            <div class="item-icon">📈</div>
            <h3>日志与监控</h3>
            <p>操作日志, 错误告警, 系统性能监控</p>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';

const router = useRouter();

const navigateTo = (path) => {
  // 跳转到对应的页面
  router.push(path);
};
</script>

<style scoped>
.resident-dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px 0;
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.dashboard-header h1 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 2.2em;
  font-weight: bold;
}

.subtitle {
  margin: 0;
  color: #666;
  font-size: 1.1em;
}

.dashboard-content {
  padding: 0 20px;
}

.function-section {
  margin-bottom: 50px;
}

.section-title {
  margin: 0 0 25px 0;
  padding-bottom: 10px;
  border-bottom: 3px solid #2196f3;
  color: #333;
  font-size: 1.5em;
  font-weight: bold;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 25px;
}

.grid-item {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  text-align: center;
}

.grid-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  border-color: #2196f3;
}

.item-icon {
  font-size: 2.5em;
  margin-bottom: 15px;
  display: block;
}

.grid-item h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.2em;
  font-weight: bold;
}

.grid-item p {
  margin: 0;
  color: #666;
  font-size: 0.95em;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-container {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .dashboard-header h1 {
    font-size: 1.8em;
  }
  
  .grid-item {
    padding: 20px;
  }
}
</style>
