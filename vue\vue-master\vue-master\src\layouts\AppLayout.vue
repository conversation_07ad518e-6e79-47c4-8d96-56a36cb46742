<template>
  <div id="app-layout">
    <!-- 简化的布局，主要用于登录和注册页面 -->
    <main>
      <router-view />
    </main>

    <!-- 版权栏 -->
    <footer class="copyright-footer">
      <div class="copyright-content">
        <div class="company-info">
          <GongdaLogo :size="32" class="company-logo" />
          <span class="company-name">软工tech</span>
          <span class="copyright-text">版权所有</span>
        </div>
        <div class="legal-notice">
          <span class="warning-text">侵犯必究</span>
          <span class="year">© {{ currentYear }}</span>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import GongdaLogo from '../components/GongdaLogo.vue';

// 这个布局现在主要用于登录和注册页面
// 主要的应用布局已经移到 SidebarLayout.vue

// 计算当前年份
const currentYear = computed(() => new Date().getFullYear());
</script>

<style scoped>
#app-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

/* 版权栏样式 */
.copyright-footer {
  background: var(--sidebar-bg);
  color: var(--main-text);
  padding: 0;
  box-shadow: var(--shadow);
  z-index: 1000;
}

.copyright-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 30px;
}

.company-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.company-logo {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.company-name, .warning-text, .copyright-text, .year {
  color: black;
}

.company-name {
  font-size: 18px;
  font-weight: 700;
  color: #070000 ;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.copyright-text {
  font-size: 14px;
  font-weight: 500;
  opacity: 0.9;
}

.legal-notice {
  display: flex;
  align-items: center;
  gap: 20px;
}

.warning-text {
  font-size: 14px;
  font-weight: 600;
  color: #fff !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.year {
  font-size: 14px;
  font-weight: 500;
  opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .copyright-content {
    flex-direction: column;
    gap: 10px;
    text-align: center;
    padding: 0 20px;
  }

  .company-info,
  .legal-notice {
    gap: 10px;
  }

  .company-name,
  .warning-text {
    font-size: 16px;
  }

  .copyright-text,
  .year {
    font-size: 12px;
  }
}

.copyright-footer, .copyright-content, .company-info, .company-name, .copyright-text, .legal-notice, .warning-text, .year {
  color: #222 !important;
}
</style>