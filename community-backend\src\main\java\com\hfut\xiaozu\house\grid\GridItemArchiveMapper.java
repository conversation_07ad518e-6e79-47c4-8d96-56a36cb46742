package com.hfut.xiaozu.house.grid;

import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【grid_item_archive(全息档案主表)】的数据库操作Mapper
* @createDate 2025-06-27 15:31:22
* @Entity com.hfut.xiaozu.house.grid.GridItemArchive
*/
@Mapper
public interface GridItemArchiveMapper {

    int deleteById(Long id);

    int insert(GridItemArchive record);

    GridItemArchive getById(Long id);

    int updateByIdSelective(GridItemArchive record);

    int updateById(GridItemArchive record);

}
