<template>
  <div class="vehicle-management">
    <div class="page-header">
      <h1>车辆管理</h1>
      <p class="subtitle">管理您的车辆信息和绑定状态</p>

      <!-- 页面切换按钮 -->
      <div class="page-tabs">
        <button
          class="tab-btn"
          :class="{ active: currentPage === 'list' }"
          @click="switchPage('list')"
        >
          <span class="tab-icon">🚗</span>
          我的车辆
        </button>
        <button
          class="tab-btn"
          :class="{ active: currentPage === 'add' }"
          @click="switchPage('add')"
        >
          <span class="tab-icon">➕</span>
          添加车辆
        </button>
      </div>
    </div>

    <div class="management-container">
      <!-- 我的车辆页面 -->
      <div v-if="currentPage === 'list'" class="vehicle-list-page">
        <div class="vehicle-list-section">
          <h2 class="section-title">
            <span class="title-icon">🚗</span>
            我的车辆列表
          </h2>

          <div v-if="loadingVehicles" class="loading-state">
            <div class="loading-spinner"></div>
            <span>加载车辆信息中...</span>
          </div>

          <div v-else-if="vehicles.length === 0" class="empty-state">
            <div class="empty-icon">🚗</div>
            <p>暂无车辆信息</p>
            <button class="btn primary" @click="switchPage('add')">
              添加第一辆车
            </button>
          </div>

          <div v-else class="vehicle-cards">
            <div
              v-for="vehicle in vehicles"
              :key="vehicle.id"
              class="vehicle-card"
            >
              <div class="vehicle-header">
                <h3 class="plate-number">{{ vehicle.plateNumber }}</h3>
                <span class="status-badge" :class="getStatusClass(vehicle.status)">
                  {{ getVehicleStatusText(vehicle.status) }}
                </span>
              </div>
              <div class="vehicle-details">
                <div class="detail-row">
                  <span class="label">品牌:</span>
                  <span class="value">{{ vehicle.brand || '未填写' }}</span>
                </div>
                <div class="detail-row">
                  <span class="label">颜色:</span>
                  <span class="value">{{ vehicle.color || '未填写' }}</span>
                </div>
                <div class="detail-row">
                  <span class="label">是否小区内车辆:</span>
                  <span class="value">{{ vehicle.isInCommunity ? '是' : '否' }}</span>
                </div>
              </div>
              <div class="vehicle-actions">
                <button class="btn secondary" @click="viewBindingStatus(vehicle)">
                  查看绑定状态
                </button>
                <button class="btn primary" @click="openBindingModal(vehicle)">
                  申请绑定
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 添加车辆页面 -->
      <div v-if="currentPage === 'add'" class="add-vehicle-page">
        <div class="add-vehicle-section">
          <h2 class="section-title">
            <span class="title-icon">➕</span>
            添加新车辆
          </h2>

          <form @submit.prevent="submitVehicle" class="vehicle-form">
            <div class="form-group">
              <label for="plateNumber" class="form-label">
                <span class="required">*</span>
                车牌号码
              </label>
              <input
                id="plateNumber"
                v-model="vehicleForm.plateNumber"
                type="text"
                class="form-input"
                placeholder="请输入车牌号码，如：皖A12345"
                required
                maxlength="10"
              />
              <div class="form-hint">请输入完整的车牌号码</div>
            </div>

            <div class="form-group">
              <label for="brand" class="form-label">车辆品牌</label>
              <input
                id="brand"
                v-model="vehicleForm.brand"
                type="text"
                class="form-input"
                placeholder="请输入车辆品牌，如：宝马、奔驰"
                maxlength="50"
              />
            </div>

            <div class="form-group">
              <label for="color" class="form-label">车辆颜色</label>
              <select
                id="color"
                v-model="vehicleForm.color"
                class="form-select"
              >
                <option value="">请选择车辆颜色</option>
                <option value="白色">白色</option>
                <option value="黑色">黑色</option>
                <option value="银色">银色</option>
                <option value="红色">红色</option>
                <option value="蓝色">蓝色</option>
                <option value="灰色">灰色</option>
                <option value="其他">其他</option>
              </select>
            </div>

            <div class="form-group">
              <label class="form-label">
                <span class="required">*</span>
                是否为小区内车辆
              </label>
              <div class="radio-group">
                <label class="radio-option">
                  <input
                    v-model="vehicleForm.isInCommunity"
                    type="radio"
                    :value="1"
                    required
                  />
                  <span class="radio-label">是</span>
                </label>
                <label class="radio-option">
                  <input
                    v-model="vehicleForm.isInCommunity"
                    type="radio"
                    :value="0"
                    required
                  />
                  <span class="radio-label">否</span>
                </label>
              </div>
              <div class="form-hint">小区内车辆指在小区内有固定车位的车辆</div>
            </div>

            <div class="form-actions">
              <button
                type="button"
                class="btn secondary"
                @click="resetForm"
              >
                重置
              </button>
              <button
                type="submit"
                class="btn primary"
                :disabled="submittingVehicle"
              >
                <span v-if="submittingVehicle">提交中...</span>
                <span v-else>添加车辆</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 绑定状态弹窗 -->
    <div v-if="showBindingModal" class="modal-overlay" @click="closeBindingModal">
      <div class="modal-content large-modal" @click.stop>
        <div class="modal-header">
          <h3>车辆绑定状态</h3>
          <button class="close-btn" @click="closeBindingModal">×</button>
        </div>
        <div class="modal-body">
          <div v-if="loadingBinding" class="loading-state">
            <div class="loading-spinner"></div>
            <span>查询绑定状态中...</span>
          </div>
          <div v-else-if="selectedVehicle" class="binding-info">
            <div class="vehicle-info-header">
              <h4>{{ selectedVehicle.plateNumber }}</h4>
              <span class="vehicle-brand">{{ selectedVehicle.brand }} | {{ selectedVehicle.color }}</span>
            </div>

            <div v-if="bindingRecords.length === 0" class="no-binding-records">
              <div class="empty-icon">📋</div>
              <p>该车辆暂无绑定记录</p>
              <button class="btn primary" @click="openBindingModalFromStatus">
                立即申请绑定
              </button>
            </div>

            <div v-else class="binding-records">
              <h5>绑定记录</h5>
              <div class="records-list">
                <div
                  v-for="record in bindingRecords"
                  :key="record.id"
                  class="binding-record"
                >
                  <div class="record-header">
                    <span class="record-id">记录 #{{ record.id }}</span>
                    <span class="record-status" :class="getBindingStatusClass(record.status)">
                      {{ getBindingStatusText(record.status) }}
                    </span>
                  </div>

                  <div class="record-details">
                    <div class="detail-item">
                      <span class="label">房屋ID:</span>
                      <span class="value">{{ record.houseId }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="label">车位号:</span>
                      <span class="value">{{ record.spaceNumber }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="label">车位类型:</span>
                      <span class="value">{{ getSpaceTypeText(record.spaceType) }}</span>
                    </div>
                    <div v-if="record.approvedBy" class="detail-item">
                      <span class="label">审核人:</span>
                      <span class="value">{{ record.approvedBy }}</span>
                    </div>
                    <div v-if="record.approvalRemark" class="detail-item">
                      <span class="label">审核备注:</span>
                      <span class="value">{{ record.approvalRemark }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 车辆绑定申请弹窗 -->
    <div v-if="showBindingApplicationModal" class="modal-overlay" @click="closeBindingApplicationModal">
      <div class="modal-content large-modal" @click.stop>
        <div class="modal-header">
          <h3>车辆绑定申请</h3>
          <button class="close-btn" @click="closeBindingApplicationModal">×</button>
        </div>
        <div class="modal-body">
          <div v-if="loadingHouses" class="loading-state">
            <div class="loading-spinner"></div>
            <span>加载房屋信息中...</span>
          </div>
          <div v-else class="binding-form">
            <div class="vehicle-info-section">
              <h4>选择的车辆</h4>
              <div class="selected-vehicle">
                <span class="vehicle-plate">{{ selectedVehicleForBinding?.plateNumber }}</span>
                <span class="vehicle-details">
                  {{ selectedVehicleForBinding?.brand }} | {{ selectedVehicleForBinding?.color }}
                </span>
              </div>
            </div>

            <form @submit.prevent="submitBinding" class="binding-application-form">
              <div class="form-group">
                <label class="form-label">
                  <span class="required">*</span>
                  选择房屋
                </label>
                <div v-if="myHouses.length === 0" class="no-houses-warning">
                  <p>⚠️ 您还没有绑定任何房屋，请先到房屋管理页面申请房屋绑定。</p>
                  <button type="button" class="btn secondary" @click="goToHouseManagement">
                    前往房屋管理
                  </button>
                </div>
                <div v-else class="house-selection">
                  <div
                    v-for="house in myHouses"
                    :key="house.id"
                    class="house-option"
                    :class="{ selected: bindingForm.houseIds.includes(house.houseId) }"
                    @click="toggleHouseSelection(house.houseId)"
                  >
                    <div class="house-info">
                      <h5>房屋ID: {{ house.houseId }}</h5>
                      <p>
                        关系: {{ getRelationTypeText(house.relationType) }} |
                        状态: {{ getHouseStatusText(house.status) }}
                      </p>
                      <p class="house-time">申请时间: {{ formatTime(house.submitTime) }}</p>
                    </div>
                    <div class="selection-indicator">
                      <span v-if="bindingForm.houseIds.includes(house.houseId)">✓</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="form-group">
                <label for="spaceNumber" class="form-label">
                  <span class="required">*</span>
                  车位号
                </label>
                <input
                  id="spaceNumber"
                  v-model="bindingForm.spaceNumber"
                  type="text"
                  class="form-input"
                  placeholder="请输入车位号，如：A001、B-15"
                  required
                  maxlength="20"
                />
                <div class="form-hint">请输入您的车位编号</div>
              </div>

              <div class="form-group">
                <label class="form-label">
                  <span class="required">*</span>
                  车位类型
                </label>
                <div class="radio-group">
                  <label class="radio-option">
                    <input
                      v-model="bindingForm.spaceType"
                      type="radio"
                      :value="1"
                      required
                    />
                    <span class="radio-label">固定车位</span>
                  </label>
                  <label class="radio-option">
                    <input
                      v-model="bindingForm.spaceType"
                      type="radio"
                      :value="2"
                      required
                    />
                    <span class="radio-label">临时车位</span>
                  </label>
                </div>
                <div class="form-hint">固定车位：长期使用的专属车位；临时车位：短期或共享使用的车位</div>
              </div>

              <div class="form-actions">
                <button
                  type="button"
                  class="btn secondary"
                  @click="closeBindingApplicationModal"
                >
                  取消
                </button>
                <button
                  type="submit"
                  class="btn primary"
                  :disabled="submittingBinding || myHouses.length === 0 || bindingForm.houseIds.length === 0"
                >
                  <span v-if="submittingBinding">提交中...</span>
                  <span v-else>提交申请</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { addVehicle, getMyVehicles, getVehicleBindingStatus, getMyHouseBindings, bindVehicleToHouse } from '../../services/vehicleApi.js';

// 响应式数据
const router = useRouter();
const currentPage = ref('list');
const vehicles = ref([]);
const loadingVehicles = ref(false);
const submittingVehicle = ref(false);
const showBindingModal = ref(false);
const selectedVehicle = ref(null);
const loadingBinding = ref(false);
const bindingRecords = ref([]);

// 绑定申请相关数据
const showBindingApplicationModal = ref(false);
const selectedVehicleForBinding = ref(null);
const myHouses = ref([]);
const loadingHouses = ref(false);
const submittingBinding = ref(false);

// 车辆表单数据
const vehicleForm = reactive({
  plateNumber: '',
  brand: '',
  color: '',
  isInCommunity: null
});

// 绑定申请表单数据
const bindingForm = reactive({
  vehicleId: null,
  houseIds: [],
  spaceNumber: '',
  spaceType: null
});

// 方法
const switchPage = (page) => {
  currentPage.value = page;
  if (page === 'list') {
    loadVehicles();
  }
};

const loadVehicles = async () => {
  loadingVehicles.value = true;
  try {
    const response = await getMyVehicles();
    if (response.success) {
      vehicles.value = response.data || [];
      console.log('✅ 车辆列表加载成功:', vehicles.value);
    }
  } catch (error) {
    console.error('❌ 车辆列表加载失败:', error);
    alert('加载车辆列表失败: ' + error.message);
  } finally {
    loadingVehicles.value = false;
  }
};

const submitVehicle = async () => {
  submittingVehicle.value = true;
  try {
    const response = await addVehicle(vehicleForm);
    if (response.success) {
      alert('车辆添加成功！');
      resetForm();
      // 切换到车辆列表页面并刷新数据
      currentPage.value = 'list';
      await loadVehicles();
    }
  } catch (error) {
    console.error('❌ 车辆添加失败:', error);
    alert('车辆添加失败: ' + error.message);
  } finally {
    submittingVehicle.value = false;
  }
};

const resetForm = () => {
  Object.assign(vehicleForm, {
    plateNumber: '',
    brand: '',
    color: '',
    isInCommunity: null
  });
};

const viewBindingStatus = async (vehicle) => {
  selectedVehicle.value = vehicle;
  showBindingModal.value = true;
  loadingBinding.value = true;
  bindingRecords.value = [];

  try {
    console.log('🔧 查询车辆绑定状态，车辆ID:', vehicle.id);
    const response = await getVehicleBindingStatus(vehicle.id);
    if (response.success) {
      console.log('✅ 车辆绑定状态查询成功:', response.data);
      bindingRecords.value = response.data || [];
    } else {
      console.log('❌ 车辆绑定状态查询失败:', response);
      bindingRecords.value = [];
    }
  } catch (error) {
    console.error('❌ 获取绑定状态失败:', error);
    bindingRecords.value = [];
    alert('查询绑定状态失败: ' + error.message);
  } finally {
    loadingBinding.value = false;
  }
};

const closeBindingModal = () => {
  showBindingModal.value = false;
  selectedVehicle.value = null;
  bindingRecords.value = [];
};

// 绑定申请相关方法
const openBindingModal = async (vehicle) => {
  selectedVehicleForBinding.value = vehicle;
  showBindingApplicationModal.value = true;

  // 重置表单
  Object.assign(bindingForm, {
    vehicleId: vehicle.id,
    houseIds: [],
    spaceNumber: '',
    spaceType: null
  });

  // 加载房屋信息
  await loadMyHouses();
};

const closeBindingApplicationModal = () => {
  showBindingApplicationModal.value = false;
  selectedVehicleForBinding.value = null;
  Object.assign(bindingForm, {
    vehicleId: null,
    houseIds: [],
    spaceNumber: '',
    spaceType: null
  });
};

const loadMyHouses = async () => {
  loadingHouses.value = true;
  try {
    console.log('🔧 开始加载房屋绑定信息...');
    const response = await getMyHouseBindings();
    console.log('🔧 API响应:', response);

    if (response.success) {
      console.log('🔧 原始房屋数据:', response.data);
      // 过滤出已通过审核的房屋绑定
      myHouses.value = (response.data || []).filter(house => house.status === 2);
      console.log('✅ 过滤后的房屋绑定信息:', myHouses.value);
      console.log('🔧 房屋数量:', myHouses.value.length);
    } else {
      console.log('❌ API调用失败:', response);
      myHouses.value = [];
    }
  } catch (error) {
    console.error('❌ 房屋绑定信息加载失败:', error);
    myHouses.value = [];
    alert('加载房屋信息失败: ' + error.message);
  } finally {
    loadingHouses.value = false;
  }
};

const toggleHouseSelection = (houseId) => {
  const index = bindingForm.houseIds.indexOf(houseId);
  if (index > -1) {
    bindingForm.houseIds.splice(index, 1);
  } else {
    bindingForm.houseIds.push(houseId);
  }
  console.log('🔧 当前选中的房屋IDs:', bindingForm.houseIds);
};

const submitBinding = async () => {
  if (bindingForm.houseIds.length === 0) {
    alert('请选择至少一个房屋');
    return;
  }

  submittingBinding.value = true;
  try {
    const response = await bindVehicleToHouse(bindingForm);
    if (response.success) {
      alert('车辆绑定申请提交成功！请等待审核。');
      closeBindingApplicationModal();
      // 刷新车辆列表
      await loadVehicles();
    }
  } catch (error) {
    console.error('❌ 车辆绑定申请失败:', error);
    alert('车辆绑定申请失败: ' + error.message);
  } finally {
    submittingBinding.value = false;
  }
};

const goToHouseManagement = () => {
  router.push('/resident/house-management');
};

// 辅助函数
const getRelationTypeText = (type) => {
  const types = {
    1: '业主',
    2: '租客',
    3: '家属'
  };
  return types[type] || '未知';
};

const getHouseStatusText = (status) => {
  const statuses = {
    1: '待审核',
    2: '已生效',
    3: '已拒绝',
    4: '已解绑'
  };
  return statuses[status] || '未知';
};

const formatTime = (timeStr) => {
  if (!timeStr) return '未知时间';
  try {
    return new Date(timeStr).toLocaleString('zh-CN');
  } catch (error) {
    return timeStr;
  }
};

const getVehicleStatusText = (status) => {
  const statusMap = {
    0: '正常',
    1: '待审核',
    2: '已禁用'
  };
  return statusMap[status] || '未知';
};

const getStatusClass = (status) => {
  const classMap = {
    0: 'success',
    1: 'warning',
    2: 'danger'
  };
  return classMap[status] || 'default';
};

// 绑定状态相关方法
const getBindingStatusText = (status) => {
  const statusMap = {
    1: '待审核',
    2: '已通过',
    3: '已拒绝'
  };
  return statusMap[status] || '未知状态';
};

const getBindingStatusClass = (status) => {
  const classMap = {
    1: 'warning',
    2: 'success',
    3: 'danger'
  };
  return classMap[status] || 'default';
};

const getSpaceTypeText = (type) => {
  const typeMap = {
    1: '固定车位',
    2: '临时车位'
  };
  return typeMap[type] || '未知类型';
};

const openBindingModalFromStatus = () => {
  closeBindingModal();
  openBindingModal(selectedVehicle.value);
};

// 生命周期
onMounted(() => {
  loadVehicles();
});
</script>

<style scoped>
.vehicle-management {
  background: #f5f7fa;
  min-height: 100vh;
  padding: 20px;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 30px;
  text-align: center;
}

.page-header h1 {
  margin: 0 0 10px 0;
  font-size: 2.5rem;
  font-weight: 600;
}

.subtitle {
  margin: 0 0 30px 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.page-tabs {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.tab-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 12px 24px;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  font-weight: 500;
}

.tab-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

.tab-btn.active {
  background: white;
  color: #667eea;
  border-color: white;
}

.tab-icon {
  font-size: 1.2rem;
}

.management-container {
  max-width: 1200px;
  margin: 0 auto;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 25px;
}

.title-icon {
  font-size: 1.8rem;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.empty-state p {
  font-size: 1.1rem;
  margin-bottom: 25px;
}

/* 车辆卡片 */
.vehicle-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.vehicle-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.vehicle-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.vehicle-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f8f9fa;
}

.plate-number {
  font-size: 1.4rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.success {
  background: #d4edda;
  color: #155724;
}

.status-badge.warning {
  background: #fff3cd;
  color: #856404;
}

.status-badge.danger {
  background: #f8d7da;
  color: #721c24;
}

.vehicle-details {
  margin-bottom: 20px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 8px 0;
}

.detail-row .label {
  font-weight: 600;
  color: #666;
}

.detail-row .value {
  color: #2c3e50;
}

.vehicle-actions {
  display: flex;
  gap: 10px;
}

/* 表单样式 */
.vehicle-form {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: 25px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2c3e50;
}

.required {
  color: #e74c3c;
  margin-right: 4px;
}

.form-input,
.form-select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #667eea;
}

.form-hint {
  margin-top: 6px;
  font-size: 0.9rem;
  color: #666;
}

.radio-group {
  display: flex;
  gap: 20px;
  margin-bottom: 10px;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.radio-option input[type="radio"] {
  margin: 0;
}

.radio-label {
  font-weight: 500;
  color: #2c3e50;
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  margin-top: 30px;
}

/* 按钮样式 */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn.primary {
  background: #667eea;
  color: white;
}

.btn.primary:hover:not(:disabled) {
  background: #5a6fd8;
  transform: translateY(-2px);
}

.btn.secondary {
  background: #6c757d;
  color: white;
}

.btn.secondary:hover:not(:disabled) {
  background: #5a6268;
  transform: translateY(-2px);
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-content.large-modal {
  max-width: 700px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #e1e8ed;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #333;
}

.modal-body {
  padding: 25px;
}

.binding-info h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
}

/* 绑定状态弹窗样式 */
.vehicle-info-header {
  margin-bottom: 25px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

.vehicle-info-header h4 {
  margin: 0 0 8px 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #667eea;
}

.vehicle-brand {
  color: #666;
  font-size: 1rem;
}

.no-binding-records {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.no-binding-records .empty-icon {
  font-size: 3rem;
  margin-bottom: 15px;
}

.no-binding-records p {
  margin: 0 0 20px 0;
  font-size: 1.1rem;
}

.binding-records h5 {
  margin: 0 0 20px 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
  border-bottom: 2px solid #e1e8ed;
  padding-bottom: 10px;
}

.records-list {
  max-height: 400px;
  overflow-y: auto;
}

.binding-record {
  background: #f8f9fa;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.binding-record:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #dee2e6;
}

.record-id {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1rem;
}

.record-status {
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
}

.record-status.success {
  background: #d4edda;
  color: #155724;
}

.record-status.warning {
  background: #fff3cd;
  color: #856404;
}

.record-status.danger {
  background: #f8d7da;
  color: #721c24;
}

.record-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
}

.detail-item .label {
  font-weight: 600;
  color: #666;
  margin-right: 10px;
}

.detail-item .value {
  color: #2c3e50;
  text-align: right;
  flex: 1;
}

/* 绑定申请样式 */
.vehicle-info-section {
  margin-bottom: 25px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.vehicle-info-section h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.selected-vehicle {
  display: flex;
  align-items: center;
  gap: 15px;
}

.vehicle-plate {
  font-size: 1.2rem;
  font-weight: 700;
  color: #667eea;
  padding: 8px 16px;
  background: white;
  border-radius: 6px;
  border: 2px solid #667eea;
}

.vehicle-details {
  color: #666;
  font-size: 0.95rem;
}

.binding-application-form {
  margin-top: 20px;
}

.no-houses-warning {
  text-align: center;
  padding: 30px 20px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  color: #856404;
}

.no-houses-warning p {
  margin: 0 0 20px 0;
  font-size: 1rem;
}

.house-selection {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
}

.house-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #f1f3f4;
  cursor: pointer;
  transition: all 0.3s ease;
}

.house-option:last-child {
  border-bottom: none;
}

.house-option:hover {
  background: #f8f9fa;
}

.house-option.selected {
  background: #e3f2fd;
  border-left: 4px solid #667eea;
}

.house-info h5 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 1rem;
  font-weight: 600;
}

.house-info p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.house-info .house-time {
  margin-top: 5px;
  color: #999;
  font-size: 0.8rem;
}

.selection-indicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: bold;
  transition: all 0.3s ease;
}

.house-option.selected .selection-indicator {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .vehicle-management {
    padding: 15px;
  }

  .page-header {
    padding: 20px;
  }

  .page-header h1 {
    font-size: 2rem;
  }

  .page-tabs {
    flex-direction: column;
    align-items: center;
  }

  .vehicle-cards {
    grid-template-columns: 1fr;
  }

  .vehicle-form {
    padding: 20px;
  }

  .form-actions {
    flex-direction: column;
  }

  .radio-group {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
