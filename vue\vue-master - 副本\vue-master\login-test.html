<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录功能测试 - 智慧社区管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .test-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 100%;
        }

        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .test-title {
            font-size: 28px;
            color: #333;
            margin-bottom: 10px;
        }

        .test-subtitle {
            color: #666;
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn-group {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .btn {
            flex: 1;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e1e5e9;
        }

        .btn-secondary:hover {
            background: #e9ecef;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .message {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .message.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255,255,255,0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .test-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }

        .test-info h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .test-info ul {
            color: #666;
            padding-left: 20px;
        }

        .test-info li {
            margin-bottom: 5px;
        }

        .user-info {
            background: #e8f5e8;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }

        .user-info h4 {
            color: #155724;
            margin-bottom: 10px;
        }

        .user-info p {
            color: #155724;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1 class="test-title">🔐 登录功能测试</h1>
            <p class="test-subtitle">智慧社区管理系统 JWT 登录验证</p>
        </div>

        <div id="messageContainer"></div>

        <form id="loginForm">
            <div class="form-group">
                <label for="userName" class="form-label">用户名/手机号</label>
                <input type="text" id="userName" name="userName" class="form-input"
                       placeholder="请输入用户名或手机号" required>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">密码</label>
                <input type="password" id="password" name="password" class="form-input" 
                       placeholder="请输入密码" required>
            </div>

            <div class="btn-group">
                <button type="button" id="testConnectionBtn" class="btn btn-secondary">
                    测试连接
                </button>
                <button type="button" id="testDatabaseBtn" class="btn btn-secondary">
                    测试数据库
                </button>
            </div>

            <button type="submit" id="loginBtn" class="btn btn-primary">
                立即登录
            </button>

            <div class="btn-group" style="margin-top: 15px;">
                <button type="button" id="getUserInfoBtn" class="btn btn-secondary">
                    获取用户信息
                </button>
                <button type="button" id="logoutBtn" class="btn btn-secondary">
                    退出登录
                </button>
            </div>
        </form>

        <div id="userInfoContainer"></div>

        <div class="test-info">
            <h3>📋 测试说明</h3>
            <ul>
                <li>先点击"测试连接"确保后端服务正常</li>
                <li>再点击"测试数据库"确保数据库连接正常</li>
                <li>使用已注册的用户名/手机号和密码登录</li>
                <li>登录成功后可测试获取用户信息功能</li>
                <li>查看控制台日志了解详细信息</li>
            </ul>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080';
        
        // 显示消息
        function showMessage(message, type = 'info') {
            const container = document.getElementById('messageContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = message;
            container.innerHTML = '';
            container.appendChild(messageDiv);
            
            // 3秒后自动清除消息
            setTimeout(() => {
                if (container.contains(messageDiv)) {
                    container.removeChild(messageDiv);
                }
            }, 3000);
        }

        // 显示用户信息
        function showUserInfo(userInfo) {
            const container = document.getElementById('userInfoContainer');
            if (userInfo) {
                container.innerHTML = `
                    <div class="user-info">
                        <h4>👤 当前登录用户</h4>
                        <p><strong>用户名:</strong> ${userInfo.userName || '未知'}</p>
                        <p><strong>用户类型:</strong> ${getUserTypeText(userInfo.userType)}</p>
                        <p><strong>Token:</strong> ${getToken() ? '已获取' : '未获取'}</p>
                    </div>
                `;
            } else {
                container.innerHTML = '';
            }
        }

        // 获取用户类型文本
        function getUserTypeText(userType) {
            switch(userType) {
                case 1: return '🏠 居民';
                case 2: return '🔧 网格员';
                case 3: return '👨‍💼 社区管理员';
                default: return '未知';
            }
        }

        // 获取token
        function getToken() {
            return localStorage.getItem('auth_token');
        }

        // 获取用户信息
        function getCurrentUser() {
            try {
                const userInfo = localStorage.getItem('user_info');
                return userInfo ? JSON.parse(userInfo) : null;
            } catch (error) {
                return null;
            }
        }

        // 设置按钮加载状态
        function setButtonLoading(button, loading) {
            if (loading) {
                button.disabled = true;
                button.innerHTML = '<span class="loading"></span>' + button.dataset.originalText;
            } else {
                button.disabled = false;
                button.innerHTML = button.dataset.originalText;
            }
        }

        // 通用API请求方法
        async function apiRequest(url, options = {}) {
            try {
                console.log('🚀 发送请求:', url, options);
                
                const defaultHeaders = {
                    'Content-Type': 'application/json',
                };

                // 添加token到请求头
                const token = getToken();
                if (token) {
                    defaultHeaders.Authorization = `Bearer ${token}`;
                }

                const response = await fetch(url, {
                    headers: defaultHeaders,
                    ...options
                });

                console.log('📡 响应状态:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }

                const data = await response.json();
                console.log('📦 响应数据:', data);
                
                return data;
            } catch (error) {
                console.error('❌ 请求失败:', error);
                throw error;
            }
        }

        // 测试连接
        document.getElementById('testConnectionBtn').addEventListener('click', async function() {
            this.dataset.originalText = this.textContent;
            setButtonLoading(this, true);

            try {
                const result = await apiRequest(`${API_BASE_URL}/api/auth/test`);

                if (result.code === 1) {
                    showMessage('✅ API连接测试成功: ' + result.message, 'success');
                } else {
                    showMessage('❌ API连接测试失败: ' + result.message, 'error');
                }
            } catch (error) {
                showMessage('❌ 连接测试失败: ' + error.message, 'error');
            } finally {
                setButtonLoading(this, false);
            }
        });

        // 测试数据库
        document.getElementById('testDatabaseBtn').addEventListener('click', async function() {
            this.dataset.originalText = this.textContent;
            setButtonLoading(this, true);

            try {
                const result = await apiRequest(`${API_BASE_URL}/api/auth/test-db`);

                if (result.code === 1) {
                    showMessage('✅ 数据库连接测试成功: ' + result.message, 'success');
                } else {
                    showMessage('❌ 数据库连接测试失败: ' + result.message, 'error');
                }
            } catch (error) {
                showMessage('❌ 数据库测试失败: ' + error.message, 'error');
            } finally {
                setButtonLoading(this, false);
            }
        });

        // 登录表单提交
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const submitBtn = document.getElementById('loginBtn');
            submitBtn.dataset.originalText = submitBtn.textContent;
            setButtonLoading(submitBtn, true);

            try {
                const formData = new FormData(this);
                const loginData = {
                    userName: formData.get('userName').trim(),
                    password: formData.get('password')
                };

                console.log('🔐 登录数据:', loginData);

                // 前端验证
                if (!loginData.userName) {
                    throw new Error('请输入用户名或手机号');
                }

                if (!loginData.password || loginData.password.length < 6) {
                    throw new Error('密码长度至少6位字符');
                }

                const result = await apiRequest(`${API_BASE_URL}/api/auth/login`, {
                    method: 'POST',
                    body: JSON.stringify(loginData)
                });

                if (result.code === 1) {
                    // 登录成功，保存token和用户信息
                    if (result.data && result.data.token) {
                        localStorage.setItem('auth_token', result.data.token);
                        localStorage.setItem('user_info', JSON.stringify({
                            userName: result.data.userName,
                            userType: result.data.userType
                        }));
                    }

                    showMessage('🎉 登录成功: ' + result.message, 'success');
                    showUserInfo(getCurrentUser());
                } else {
                    showMessage('❌ 登录失败: ' + result.message, 'error');
                }
            } catch (error) {
                showMessage('❌ 登录失败: ' + error.message, 'error');
            } finally {
                setButtonLoading(submitBtn, false);
            }
        });

        // 获取用户信息
        document.getElementById('getUserInfoBtn').addEventListener('click', async function() {
            this.dataset.originalText = this.textContent;
            setButtonLoading(this, true);

            try {
                const result = await apiRequest(`${API_BASE_URL}/api/auth/user`);

                if (result.code === 1) {
                    showMessage('✅ 获取用户信息成功', 'success');
                    console.log('用户信息:', result.data);
                } else {
                    showMessage('❌ 获取用户信息失败: ' + result.message, 'error');
                }
            } catch (error) {
                showMessage('❌ 获取用户信息失败: ' + error.message, 'error');
            } finally {
                setButtonLoading(this, false);
            }
        });

        // 退出登录
        document.getElementById('logoutBtn').addEventListener('click', async function() {
            this.dataset.originalText = this.textContent;
            setButtonLoading(this, true);

            try {
                // 清除本地存储
                localStorage.removeItem('auth_token');
                localStorage.removeItem('user_info');

                showMessage('✅ 退出登录成功', 'success');
                showUserInfo(null);

                // 清空表单
                document.getElementById('loginForm').reset();
            } catch (error) {
                showMessage('❌ 退出登录失败: ' + error.message, 'error');
            } finally {
                setButtonLoading(this, false);
            }
        });

        // 页面加载完成后检查登录状态
        window.addEventListener('load', function() {
            const userInfo = getCurrentUser();
            if (userInfo) {
                showUserInfo(userInfo);
                showMessage('🎉 检测到已登录状态', 'success');
            } else {
                showMessage('🚀 测试页面加载完成，请先测试连接状态', 'info');
            }
        });
    </script>
</body>
</html>
