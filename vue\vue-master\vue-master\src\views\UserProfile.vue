<template>
  <div class="profile-container">
    <h2>个人信息</h2>
    <div v-if="user" class="profile-details">
      <div>
        <strong>用户名:</strong> {{ user.username }}
      </div>
      <div>
        <strong>邮箱:</strong> {{ user.email }}
      </div>
      <div>
        <strong>注册日期:</strong> {{ user.joinDate }}
      </div>
      <!-- 可以根据需求图谱添加更多来自“居民端”或“用户与权限服务”的信息 -->
      <div>
        <strong>角色:</strong> {{ user.role }}
      </div>
       <button @click="logout">退出登录</button>
    </div>
    <div v-else>
      <p>加载中...</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const user = ref(null);

// 模拟获取用户数据
const fetchUserData = () => {
  // 实际项目中，这里会从API获取用户数据或从状态管理中读取
  setTimeout(() => {
    user.value = {
      username: 'testUser',
      email: '<EMAIL>',
      joinDate: '2024-01-01',
      role: '居民'
      // 可以根据需求图谱添加更多字段
    };
  }, 500);
};

const logout = () => {
  // 实际项目中，这里会清除token并跳转到登录页
  alert('已退出登录');
  router.push('/login');
};

onMounted(() => {
  fetchUserData();
});
</script>

<style scoped>
.profile-container {
  max-width: 600px;
  margin: 50px auto;
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.profile-container h2 {
  text-align: center;
  margin-bottom: 20px;
  color: #333;
}

.profile-details div {
  margin-bottom: 10px;
  padding: 8px;
  border-bottom: 1px solid #eee;
}

.profile-details div:last-child {
  border-bottom: none;
}

.profile-details strong {
  color: #555;
}

.profile-container button {
  display: block;
  width: auto;
  margin: 20px auto 0;
  padding: 10px 20px;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.profile-container button:hover {
  background-color: #c82333;
}
</style>