<template>
  <div class="vehicle-info-collection">
    <div class="collection-container">
      <div class="collection-header">
        <div class="header-top">
          <div class="title-section">
            <h1 class="page-title">
              <span class="title-icon">🚗</span>
              车辆信息采集
            </h1>
            <p class="page-description">
              管理和审核社区车辆绑定申请，确保停车位合理分配
            </p>
          </div>
          <div class="header-actions">
            <button
              @click="refreshApplications"
              class="refresh-btn"
              :disabled="isLoading"
            >
              <span class="btn-icon">🔄</span>
              {{ isLoading ? '刷新中...' : '刷新数据' }}
            </button>
          </div>
        </div>
      </div>

      <!-- 状态筛选器 -->
      <div class="filter-section">
        <div class="filter-card">
          <h3 class="filter-title">申请状态筛选</h3>
          <div class="status-filters">
            <button
              v-for="status in statusOptions"
              :key="status.value"
              @click="selectStatus(status.value)"
              class="status-btn"
              :class="{ active: selectedStatus === status.value }"
            >
              <span class="status-icon">{{ status.icon }}</span>
              <span class="status-text">{{ status.label }}</span>
              <span v-if="statusCounts[status.value] !== undefined" class="status-count">
                {{ statusCounts[status.value] }}
              </span>
            </button>
          </div>
        </div>
      </div>

      <!-- 申请列表 -->
      <div class="applications-section">
        <div class="section-header">
          <h3 class="section-title">
            {{ getStatusText(selectedStatus) }}申请列表
            <span v-if="applications.length > 0" class="count-badge">{{ applications.length }}</span>
          </h3>
        </div>

        <!-- 加载状态 -->
        <div v-if="isLoading" class="loading-card">
          <div class="loading-content">
            <div class="loading-spinner"></div>
            <p>正在加载申请数据...</p>
          </div>
        </div>

        <!-- 错误信息 -->
        <div v-else-if="errorMessage" class="error-card">
          <div class="error-content">
            <span class="error-icon">❌</span>
            <span class="error-text">{{ errorMessage }}</span>
            <button @click="refreshApplications" class="retry-btn">重试</button>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else-if="applications.length === 0" class="empty-card">
          <div class="empty-content">
            <div class="empty-icon">📋</div>
            <h4>暂无{{ getStatusText(selectedStatus) }}申请</h4>
            <p>当前没有找到相关的车辆绑定申请记录</p>
          </div>
        </div>

        <!-- 申请列表 -->
        <div v-else class="applications-list">
          <div
            v-for="application in applications"
            :key="application.id"
            class="application-card"
            :class="getApplicationCardClass(application.status)"
          >
            <div class="card-header">
              <div class="application-info">
                <h4 class="application-title">
                  车辆绑定申请 #{{ application.id }}
                </h4>
                <span class="application-status" :class="getStatusClass(application.status)">
                  {{ getStatusText(application.status) }}
                </span>
              </div>
              <div class="card-actions">
                <button
                  @click="viewApplicationDetails(application)"
                  class="action-btn view-btn"
                >
                  查看详情
                </button>
              </div>
            </div>

            <div class="card-content">
              <div class="info-grid">
                <div class="info-item">
                  <label>车辆ID：</label>
                  <span class="info-value">{{ application.vehicleId }}</span>
                </div>
                <div class="info-item">
                  <label>房屋ID：</label>
                  <span class="info-value">{{ application.houseId }}</span>
                </div>
                <div class="info-item">
                  <label>车位号：</label>
                  <span class="info-value">{{ application.spaceNumber }}</span>
                </div>
                <div class="info-item">
                  <label>车位类型：</label>
                  <span class="info-value">{{ getSpaceTypeText(application.spaceType) }}</span>
                </div>
                <div class="info-item">
                  <label>申请时间：</label>
                  <span class="info-value">{{ formatDateTime(application.createTime) }}</span>
                </div>
                <div class="info-item">
                  <label>更新时间：</label>
                  <span class="info-value">{{ formatDateTime(application.updateTime) }}</span>
                </div>
              </div>

              <div v-if="application.approvalRemark" class="remark-section">
                <label>审核备注：</label>
                <p class="remark-text">{{ application.approvalRemark }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详情弹窗 -->
    <div v-if="showDetailModal" class="modal-overlay" @click="closeDetailModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>车辆绑定申请详情</h3>
          <button @click="closeDetailModal" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
          <div v-if="selectedApplication" class="detail-content">
            <div class="detail-section">
              <h4 class="section-title">基本信息</h4>
              <div class="detail-grid">
                <div class="detail-item">
                  <label>申请ID：</label>
                  <span>{{ selectedApplication.id }}</span>
                </div>
                <div class="detail-item">
                  <label>车辆ID：</label>
                  <span>{{ selectedApplication.vehicleId }}</span>
                </div>
                <div class="detail-item">
                  <label>房屋ID：</label>
                  <span>{{ selectedApplication.houseId }}</span>
                </div>
                <div class="detail-item">
                  <label>车位号：</label>
                  <span>{{ selectedApplication.spaceNumber }}</span>
                </div>
                <div class="detail-item">
                  <label>车位类型：</label>
                  <span>{{ getSpaceTypeText(selectedApplication.spaceType) }}</span>
                </div>
                <div class="detail-item">
                  <label>申请状态：</label>
                  <span class="status-badge" :class="getStatusClass(selectedApplication.status)">
                    {{ getStatusText(selectedApplication.status) }}
                  </span>
                </div>
              </div>
            </div>

            <div class="detail-section">
              <h4 class="section-title">时间信息</h4>
              <div class="detail-grid">
                <div class="detail-item">
                  <label>申请时间：</label>
                  <span>{{ formatDateTime(selectedApplication.createTime) }}</span>
                </div>
                <div class="detail-item">
                  <label>更新时间：</label>
                  <span>{{ formatDateTime(selectedApplication.updateTime) }}</span>
                </div>
                <div class="detail-item">
                  <label>审核人员：</label>
                  <span>{{ selectedApplication.approvedBy || '未审核' }}</span>
                </div>
              </div>
            </div>

            <div v-if="selectedApplication.approvalRemark" class="detail-section">
              <h4 class="section-title">审核备注</h4>
              <div class="remark-content">
                <p>{{ selectedApplication.approvalRemark }}</p>
              </div>
            </div>

            <div v-if="selectedApplication.status === 1" class="detail-section">
              <h4 class="section-title">审核操作</h4>
              <div class="approval-actions">
                <button
                  @click="approveApplication"
                  class="action-btn approve-btn"
                  :disabled="isProcessing"
                >
                  <span class="btn-icon">✅</span>
                  {{ isProcessing ? '处理中...' : '审核通过' }}
                </button>
                <button
                  @click="rejectApplication"
                  class="action-btn reject-btn"
                  :disabled="isProcessing"
                >
                  <span class="btn-icon">❌</span>
                  {{ isProcessing ? '处理中...' : '审核拒绝' }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 成功信息显示 -->
    <div v-if="successMessage" class="success-card">
      <div class="success-content">
        <span class="success-icon">✅</span>
        <span class="success-text">{{ successMessage }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 响应式数据
const applications = ref([]);
const selectedStatus = ref(1); // 默认显示待审核
const isLoading = ref(false);
const errorMessage = ref('');
const successMessage = ref('');
const showDetailModal = ref(false);
const selectedApplication = ref(null);
const isProcessing = ref(false);

// 状态选项
const statusOptions = [
  { value: 1, label: '待审核', icon: '⏳' },
  { value: 2, label: '已生效', icon: '✅' },
  { value: 3, label: '已拒绝', icon: '❌' }
];

// 状态统计
const statusCounts = ref({});

// 获取认证令牌
const getToken = () => {
  const token = localStorage.getItem('auth_data') || sessionStorage.getItem('auth_data');
  if (!token) return null;

  try {
    const authData = JSON.parse(token);
    return authData.token;
  } catch (e) {
    console.error('解析token失败:', e);
    return null;
  }
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    1: '待审核',
    2: '已生效',
    3: '已拒绝'
  };
  return statusMap[status] || '未知状态';
};

// 获取状态样式类
const getStatusClass = (status) => {
  const classMap = {
    1: 'status-pending',
    2: 'status-approved',
    3: 'status-rejected'
  };
  return classMap[status] || '';
};

// 获取申请卡片样式类
const getApplicationCardClass = (status) => {
  const classMap = {
    1: 'card-pending',
    2: 'card-approved',
    3: 'card-rejected'
  };
  return classMap[status] || '';
};

// 获取车位类型文本
const getSpaceTypeText = (spaceType) => {
  const typeMap = {
    1: '固定车位',
    2: '临时车位'
  };
  return typeMap[spaceType] || '未知类型';
};

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '-';
  try {
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (e) {
    return dateTimeStr;
  }
};

// 选择状态
const selectStatus = (status) => {
  selectedStatus.value = status;
  loadApplications();
};

// 加载申请数据
const loadApplications = async () => {
  if (isLoading.value) return;

  isLoading.value = true;
  errorMessage.value = '';
  applications.value = [];

  try {
    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    console.log('🔍 查询车辆绑定申请，状态:', selectedStatus.value);

    const response = await fetch(`http://localhost:8080/api/car/binding/list/${selectedStatus.value}?pageNum=1&pageSize=100`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('📋 API响应:', result);

    if (result.code === 200) {
      applications.value = result.data || [];
      console.log('✅ 成功加载申请数据:', applications.value.length, '条记录');
    } else {
      throw new Error(result.msg || '获取申请数据失败');
    }
  } catch (error) {
    console.error('❌ 加载申请数据失败:', error);
    errorMessage.value = error.message || '加载申请数据失败，请稍后重试';
  } finally {
    isLoading.value = false;
  }
};

// 刷新申请数据
const refreshApplications = () => {
  loadApplications();
};

// 查看申请详情
const viewApplicationDetails = (application) => {
  selectedApplication.value = application;
  showDetailModal.value = true;
};

// 关闭详情弹窗
const closeDetailModal = () => {
  showDetailModal.value = false;
  selectedApplication.value = null;
};

// 审核通过申请
const approveApplication = async () => {
  if (!selectedApplication.value || isProcessing.value) return;

  isProcessing.value = true;
  errorMessage.value = '';

  try {
    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    console.log('🔍 审核通过申请 ID:', selectedApplication.value.id);

    const response = await fetch(`http://localhost:8080/api/car/binding/update/${selectedApplication.value.id}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        status: 2, // 已生效
        approvalRemark: '审核通过'
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ 审核通过响应:', result);

    if (result.code === 200) {
      successMessage.value = '申请审核通过成功！';
      closeDetailModal();
      loadApplications(); // 重新加载数据

      // 3秒后清除成功消息
      setTimeout(() => {
        successMessage.value = '';
      }, 3000);
    } else {
      throw new Error(result.msg || '审核操作失败');
    }
  } catch (error) {
    console.error('❌ 审核通过失败:', error);
    errorMessage.value = error.message || '审核操作失败，请稍后重试';
  } finally {
    isProcessing.value = false;
  }
};

// 审核拒绝申请
const rejectApplication = async () => {
  if (!selectedApplication.value || isProcessing.value) return;

  const rejectReason = prompt('请输入拒绝原因：');
  if (!rejectReason) return;

  isProcessing.value = true;
  errorMessage.value = '';

  try {
    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    console.log('🔍 审核拒绝申请 ID:', selectedApplication.value.id);

    const response = await fetch(`http://localhost:8080/api/car/binding/update/${selectedApplication.value.id}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        status: 3, // 已拒绝
        approvalRemark: rejectReason
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ 审核拒绝响应:', result);

    if (result.code === 200) {
      successMessage.value = '申请审核拒绝成功！';
      closeDetailModal();
      loadApplications(); // 重新加载数据

      // 3秒后清除成功消息
      setTimeout(() => {
        successMessage.value = '';
      }, 3000);
    } else {
      throw new Error(result.msg || '审核操作失败');
    }
  } catch (error) {
    console.error('❌ 审核拒绝失败:', error);
    errorMessage.value = error.message || '审核操作失败，请稍后重试';
  } finally {
    isProcessing.value = false;
  }
};

// 页面加载时初始化
onMounted(async () => {
  console.log('🔍 车辆信息采集页面已加载');
  await loadApplications();
});
</script>

<style scoped>
.vehicle-info-collection {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.collection-container {
  max-width: 1200px;
  margin: 0 auto;
}

.collection-header {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
  overflow: hidden;
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.title-section {
  flex: 1;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 36px;
}

.page-description {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.refresh-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.refresh-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-icon {
  font-size: 16px;
}

/* 筛选器样式 */
.filter-section {
  margin-bottom: 30px;
}

.filter-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.filter-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 20px 0;
}

.status-filters {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.status-btn {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.status-btn:hover {
  border-color: #667eea;
  background: #f0f2ff;
}

.status-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: white;
}

.status-icon {
  font-size: 16px;
}

.status-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.status-btn.active .status-count {
  background: rgba(255, 255, 255, 0.3);
}

/* 申请列表样式 */
.applications-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  background: #f8f9fa;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.count-badge {
  background: #667eea;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

/* 加载和错误状态 */
.loading-card,
.error-card,
.empty-card {
  padding: 60px 40px;
  text-align: center;
}

.loading-content,
.error-content,
.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon,
.empty-icon {
  font-size: 48px;
  opacity: 0.6;
}

.error-text {
  color: #e74c3c;
  font-size: 16px;
}

.retry-btn {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s ease;
}

.retry-btn:hover {
  background: #c0392b;
}

.empty-content h4 {
  color: #6c757d;
  font-size: 18px;
  margin: 0;
}

.empty-content p {
  color: #adb5bd;
  font-size: 14px;
  margin: 0;
}

/* 申请卡片样式 */
.applications-list {
  padding: 24px;
}

.application-card {
  border: 2px solid #e9ecef;
  border-radius: 12px;
  margin-bottom: 20px;
  transition: all 0.3s ease;
  background: white;
}

.application-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.application-card.card-pending {
  border-left: 4px solid #ffc107;
}

.application-card.card-approved {
  border-left: 4px solid #28a745;
}

.application-card.card-rejected {
  border-left: 4px solid #dc3545;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #f8f9fa;
}

.application-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.application-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.application-status {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.application-status.status-pending {
  background: #fff3cd;
  color: #856404;
}

.application-status.status-approved {
  background: #d4edda;
  color: #155724;
}

.application-status.status-rejected {
  background: #f8d7da;
  color: #721c24;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.view-btn {
  background: #667eea;
  color: white;
}

.view-btn:hover {
  background: #5a6fd8;
  transform: translateY(-1px);
}

.approve-btn {
  background: #28a745;
  color: white;
}

.approve-btn:hover:not(:disabled) {
  background: #218838;
}

.reject-btn {
  background: #dc3545;
  color: white;
}

.reject-btn:hover:not(:disabled) {
  background: #c82333;
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.card-content {
  padding: 24px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item label {
  font-weight: 500;
  color: #6c757d;
  min-width: 80px;
}

.info-value {
  color: #2c3e50;
  font-weight: 500;
}

.remark-section {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.remark-section label {
  font-weight: 600;
  color: #2c3e50;
  display: block;
  margin-bottom: 8px;
}

.remark-text {
  color: #495057;
  margin: 0;
  line-height: 1.5;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
}

.modal-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6c757d;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.modal-body {
  padding: 24px;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-section .section-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e9ecef;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item label {
  font-weight: 500;
  color: #6c757d;
  font-size: 14px;
}

.detail-item span {
  color: #2c3e50;
  font-weight: 500;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  display: inline-block;
}

.remark-content {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.remark-content p {
  margin: 0;
  color: #495057;
  line-height: 1.5;
}

.approval-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

/* 成功消息样式 */
.success-card {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 8px;
  padding: 16px 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  z-index: 1001;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.success-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.success-icon {
  font-size: 20px;
}

.success-text {
  color: #155724;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-top {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .status-filters {
    flex-direction: column;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .card-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .modal-content {
    margin: 10px;
    max-height: calc(100vh - 20px);
  }

  .detail-grid {
    grid-template-columns: 1fr;
  }

  .approval-actions {
    flex-direction: column;
  }
}
</style>
