<template>
  <div class="simple-map-test">
    <h1>简单地图测试</h1>
    
    <div class="test-info">
      <h3>API配置信息</h3>
      <p><strong>API Key:</strong> {{ apiKey }}</p>
      <p><strong>安全密钥:</strong> {{ securityKey }}</p>
      <p><strong>API状态:</strong> {{ apiStatus }}</p>
    </div>

    <div class="test-buttons">
      <button @click="testAPILoad" class="test-btn">测试API加载</button>
      <button @click="createSimpleMap" class="test-btn">创建简单地图</button>
      <button @click="clearLogs" class="test-btn">清除日志</button>
    </div>

    <div class="map-container">
      <div id="simple-map" style="width: 100%; height: 400px; background: #f0f0f0; border: 1px solid #ccc;">
        <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">
          等待地图加载...
        </div>
      </div>
    </div>

    <div class="logs">
      <h3>测试日志</h3>
      <div class="log-content">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-level" :class="log.level">{{ log.level }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 响应式数据
const apiKey = ref('6654ed14b4cb7aae5003dc379fd3f134');
const securityKey = ref('830ff4b97ad96c81625c4d44049c8452');
const apiStatus = ref('未测试');
const logs = ref([]);

// 添加日志
const addLog = (level, message) => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    level: level,
    message: message
  });
  
  if (logs.value.length > 50) {
    logs.value.pop();
  }
};

// 清除日志
const clearLogs = () => {
  logs.value = [];
  addLog('INFO', '日志已清除');
};

// 测试API加载
const testAPILoad = async () => {
  addLog('INFO', '开始测试高德地图API加载...');
  
  try {
    // 检查是否已经加载
    if (typeof AMap !== 'undefined') {
      addLog('SUCCESS', 'AMap已存在，版本: ' + (AMap.version || '未知'));
      apiStatus.value = '已加载';
      return;
    }

    // 设置安全密钥
    window._AMapSecurityConfig = {
      securityJsCode: securityKey.value,
    };
    addLog('INFO', '安全密钥已设置');

    // 动态加载脚本
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = `https://webapi.amap.com/maps?v=2.0&key=${apiKey.value}`;
    
    addLog('INFO', '开始加载脚本: ' + script.src);

    script.onload = () => {
      if (typeof AMap !== 'undefined') {
        addLog('SUCCESS', 'API加载成功！AMap版本: ' + (AMap.version || '未知'));
        apiStatus.value = '加载成功';
      } else {
        addLog('ERROR', 'API脚本加载完成但AMap对象未定义');
        apiStatus.value = '加载失败';
      }
    };

    script.onerror = (error) => {
      addLog('ERROR', 'API脚本加载失败: ' + error.message);
      apiStatus.value = '加载失败';
    };

    document.head.appendChild(script);
    addLog('INFO', 'API脚本已添加到页面');

  } catch (error) {
    addLog('ERROR', '测试过程中发生错误: ' + error.message);
    apiStatus.value = '测试失败';
  }
};

// 创建简单地图
const createSimpleMap = () => {
  addLog('INFO', '开始创建简单地图...');
  
  if (typeof AMap === 'undefined') {
    addLog('ERROR', 'AMap未定义，请先加载API');
    return;
  }

  try {
    const map = new AMap.Map('simple-map', {
      center: [116.4074, 39.9042], // 北京坐标
      zoom: 13,
      mapStyle: 'amap://styles/normal'
    });

    addLog('SUCCESS', '地图创建成功！');
    
    // 添加地图事件监听
    map.on('complete', () => {
      addLog('SUCCESS', '地图加载完成');
    });

    map.on('click', (e) => {
      addLog('INFO', `地图点击: [${e.lnglat.lng.toFixed(6)}, ${e.lnglat.lat.toFixed(6)}]`);
    });

    // 添加一个测试标记
    const marker = new AMap.Marker({
      position: [116.4074, 39.9042],
      title: '测试标记'
    });
    
    map.add(marker);
    addLog('INFO', '测试标记已添加');

  } catch (error) {
    addLog('ERROR', '创建地图失败: ' + error.message);
  }
};

// 页面加载时自动测试
onMounted(() => {
  addLog('INFO', '页面加载完成，开始自动测试...');
  setTimeout(() => {
    testAPILoad();
  }, 1000);
});
</script>

<style scoped>
.simple-map-test {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.test-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.test-info h3 {
  margin-top: 0;
  color: #333;
}

.test-info p {
  margin: 5px 0;
  font-family: monospace;
}

.test-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.test-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
}

.test-btn:hover {
  background: #0056b3;
}

.map-container {
  margin-bottom: 20px;
}

.logs {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
}

.logs h3 {
  margin-top: 0;
  color: #333;
}

.log-content {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
  background: white;
  font-family: monospace;
  font-size: 12px;
}

.log-item {
  display: flex;
  gap: 10px;
  padding: 2px 0;
  border-bottom: 1px solid #f0f0f0;
}

.log-time {
  color: #666;
  min-width: 80px;
}

.log-level {
  font-weight: bold;
  min-width: 60px;
}

.log-level.INFO {
  color: #007bff;
}

.log-level.SUCCESS {
  color: #28a745;
}

.log-level.ERROR {
  color: #dc3545;
}

.log-message {
  color: #333;
  flex: 1;
}
</style>
