package com.hfut.xiaozu.common.config;

import com.hfut.xiaozu.security.JwtAuthInterceptor;
import com.hfut.xiaozu.security.JwtConfigProperties;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @date 2025-06-23
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Resource
    private JwtAuthInterceptor JwtAuthInterceptor;

    @Resource
    private JwtConfigProperties jwtConfig;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(JwtAuthInterceptor)
                .addPathPatterns("/**")    // 需要保护的API路径
                .excludePathPatterns(jwtConfig.getExcludedPaths());
    }
}


