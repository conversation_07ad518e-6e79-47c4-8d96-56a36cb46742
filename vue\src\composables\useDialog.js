/**
 * 对话框组合式API
 * 提供在组合式API中使用对话框的便捷方法
 */

import { inject } from 'vue'

export function useDialog() {
  const dialog = inject('dialog')
  
  if (!dialog) {
    console.warn('Dialog service not found. Make sure the dialog plugin is installed.')
    // 返回一个空的对话框服务，避免错误
    return {
      showInfo: (message, title) => console.log('Info:', title, message),
      showSuccess: (message, title) => console.log('Success:', title, message),
      showWarning: (message, title) => console.log('Warning:', title, message),
      showError: (message, title) => console.log('Error:', title, message),
      showConfirm: (message, title) => Promise.resolve(false),
      showPrompt: (message, title) => Promise.resolve(null),
      showTextareaPrompt: (message, title) => Promise.resolve(null),
      showLoading: (message, title) => Promise.resolve(),
      closeDialog: () => {},
      updateLoading: () => {},
      alert: (message, title) => console.log('Alert:', title, message),
      confirm: (message, title) => Promise.resolve(false),
      prompt: (message, defaultValue, title) => Promise.resolve(null)
    }
  }
  
  return dialog
}

export default useDialog
