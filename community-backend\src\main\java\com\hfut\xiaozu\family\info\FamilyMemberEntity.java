package com.hfut.xiaozu.family.info;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 业主家庭成员信息表
 * @TableName family_member
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FamilyMemberEntity {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 业主ID（关联user_account）
     */
    private Long ownerId;

    /**
     * 家人姓名
     */
    private String memberName;

    /**
     * 与业主关系:1-配偶 2-子女 3-父母 4-其他亲属
     */
    private Integer relationship;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 是否主要家庭成员 0-否 1-是
     */
    private Integer isPrimary;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
