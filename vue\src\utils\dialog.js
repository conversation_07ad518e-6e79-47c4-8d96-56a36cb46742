/**
 * 全局对话框服务
 * 提供统一的弹窗接口，替换浏览器原生的 alert、confirm 等
 */

import { createApp, ref, h } from 'vue'
import CustomDialog from '../components/CustomDialog.vue'

// 全局对话框状态
const dialogState = ref({
  visible: false,
  type: 'info',
  title: '提示',
  message: '',
  confirmText: '确定',
  cancelText: '取消',
  showCancelButton: false,
  showCloseButton: true,
  closeOnClickOutside: false,
  inputType: null,
  inputPlaceholder: '请输入...',
  loading: false,
  loadingText: '处理中...'
})

let dialogApp = null
let dialogContainer = null
let currentResolve = null
let currentReject = null

// 初始化对话框容器
const initDialog = () => {
  if (dialogContainer) return

  // 创建容器元素
  dialogContainer = document.createElement('div')
  dialogContainer.id = 'custom-dialog-container'
  document.body.appendChild(dialogContainer)

  // 创建Vue应用实例
  dialogApp = createApp({
    setup() {
      const handleConfirm = (result) => {
        dialogState.value.visible = false
        if (currentResolve) {
          currentResolve(result)
          currentResolve = null
        }
      }

      const handleCancel = (result) => {
        dialogState.value.visible = false
        if (currentReject) {
          currentReject(result)
          currentReject = null
        }
      }

      const handleClose = () => {
        dialogState.value.visible = false
        if (currentReject) {
          currentReject({ confirmed: false })
          currentReject = null
        }
      }

      return () => h(CustomDialog, {
        'modelValue': dialogState.value.visible,
        'onUpdate:modelValue': (value) => { dialogState.value.visible = value },
        'type': dialogState.value.type,
        'title': dialogState.value.title,
        'message': dialogState.value.message,
        'confirmText': dialogState.value.confirmText,
        'cancelText': dialogState.value.cancelText,
        'showCancelButton': dialogState.value.showCancelButton,
        'showCloseButton': dialogState.value.showCloseButton,
        'closeOnClickOutside': dialogState.value.closeOnClickOutside,
        'inputType': dialogState.value.inputType,
        'inputPlaceholder': dialogState.value.inputPlaceholder,
        'loading': dialogState.value.loading,
        'loadingText': dialogState.value.loadingText,
        'onConfirm': handleConfirm,
        'onCancel': handleCancel,
        'onClose': handleClose
      })
    }
  })

  dialogApp.mount(dialogContainer)
}

// 显示对话框的通用方法
const showDialog = (options) => {
  return new Promise((resolve, reject) => {
    initDialog()

    // 更新对话框状态
    Object.assign(dialogState.value, {
      visible: true,
      type: 'info',
      title: '提示',
      message: '',
      confirmText: '确定',
      cancelText: '取消',
      showCancelButton: false,
      showCloseButton: true,
      closeOnClickOutside: false,
      inputType: null,
      inputPlaceholder: '请输入...',
      loading: false,
      loadingText: '处理中...',
      ...options
    })

    currentResolve = resolve
    currentReject = reject
  })
}

// 信息提示
export const showInfo = (message, title = '提示') => {
  return showDialog({
    type: 'info',
    title,
    message,
    showCancelButton: false
  })
}

// 成功提示
export const showSuccess = (message, title = '成功') => {
  return showDialog({
    type: 'success',
    title,
    message,
    showCancelButton: false
  })
}

// 警告提示
export const showWarning = (message, title = '警告') => {
  return showDialog({
    type: 'warning',
    title,
    message,
    showCancelButton: false
  })
}

// 错误提示
export const showError = (message, title = '错误') => {
  return showDialog({
    type: 'error',
    title,
    message,
    showCancelButton: false
  })
}

// 确认对话框
export const showConfirm = (message, title = '确认', options = {}) => {
  return showDialog({
    type: 'confirm',
    title,
    message,
    showCancelButton: true,
    confirmText: '确定',
    cancelText: '取消',
    ...options
  })
}

// 输入对话框
export const showPrompt = (message, title = '输入', options = {}) => {
  return showDialog({
    type: 'info',
    title,
    message,
    showCancelButton: true,
    inputType: 'text',
    inputPlaceholder: '请输入...',
    confirmText: '确定',
    cancelText: '取消',
    ...options
  })
}

// 文本域输入对话框
export const showTextareaPrompt = (message, title = '输入', options = {}) => {
  return showDialog({
    type: 'info',
    title,
    message,
    showCancelButton: true,
    inputType: 'textarea',
    inputPlaceholder: '请输入...',
    confirmText: '确定',
    cancelText: '取消',
    ...options
  })
}

// 加载对话框
export const showLoading = (message = '处理中...', title = '请稍候') => {
  return showDialog({
    type: 'info',
    title,
    message,
    showCancelButton: false,
    showCloseButton: false,
    loading: true,
    loadingText: message
  })
}

// 关闭对话框
export const closeDialog = () => {
  if (dialogState.value.visible) {
    dialogState.value.visible = false
    if (currentReject) {
      currentReject({ confirmed: false })
      currentReject = null
    }
  }
}

// 更新对话框加载状态
export const updateLoading = (loading, loadingText = '处理中...') => {
  dialogState.value.loading = loading
  dialogState.value.loadingText = loadingText
}

// 替换原生alert的便捷方法
export const alert = (message, title = '提示') => {
  return showInfo(message, title)
}

// 替换原生confirm的便捷方法
export const confirm = (message, title = '确认') => {
  return showConfirm(message, title).then(
    (result) => result.confirmed,
    () => false
  )
}

// 替换原生prompt的便捷方法
export const prompt = (message, defaultValue = '', title = '输入') => {
  return showPrompt(message, title, {
    inputPlaceholder: defaultValue || '请输入...'
  }).then(
    (result) => result.confirmed ? result.inputValue : null,
    () => null
  )
}

// 默认导出
export default {
  showInfo,
  showSuccess,
  showWarning,
  showError,
  showConfirm,
  showPrompt,
  showTextareaPrompt,
  showLoading,
  closeDialog,
  updateLoading,
  alert,
  confirm,
  prompt
}
