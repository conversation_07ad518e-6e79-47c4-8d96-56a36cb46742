# 投票功能测试指南

## 🎯 测试目标
验证居民端投票提交功能是否正常工作，确保API调用格式正确。

## 🔧 API规范确认

### 投票提交API
- **URL**: `POST /api/vote/{voteId}`
- **Headers**: `Authorization: Bearer {token}`
- **Request Body**:
```json
{
  "voteId": 1,
  "optionIds": [1, 2]
}
```

## 🧪 测试步骤

### 1. 准备测试环境

1. **启动后端服务**
   ```bash
   cd community-backend
   mvn spring-boot:run
   ```

2. **启动前端服务**
   ```bash
   cd vue
   npm run dev
   ```

3. **确认登录状态**
   - 访问系统并登录
   - 确保获得有效的JWT token

### 2. 使用测试工具

#### 方法一：专用测试页面
访问：`http://localhost:3000/vote-submit-test`

**测试步骤：**
1. 输入投票ID
2. 输入选项ID（多个用逗号分隔）
3. 点击"从存储加载"获取当前用户token
4. 点击"测试投票提交"

#### 方法二：居民端实际测试
访问：`http://localhost:3000/resident/budget-vote`

**测试步骤：**
1. 查看投票列表
2. 点击"立即投票"
3. 选择投票选项
4. 点击"确认投票"
5. 观察结果

### 3. 调试模式测试

在居民端投票页面：
1. 如果出现错误，点击"显示调试信息"
2. 查看Token状态、API地址等信息
3. 点击"测试API连接"验证连通性

## 🔍 验证要点

### 1. 请求格式验证
确认发送的请求包含：
- 正确的URL格式：`/api/vote/{voteId}`
- 正确的请求体格式：`{ "voteId": number, "optionIds": [number] }`
- 正确的认证头：`Authorization: Bearer {token}`

### 2. 选项ID处理
- 单选投票：`optionIds` 包含一个元素
- 多选投票：`optionIds` 包含多个元素
- 选项ID应该是数字类型

### 3. 错误处理
测试各种错误情况：
- 无效的投票ID
- 无效的选项ID
- 过期的token
- 重复投票
- 投票已结束

## 📊 测试用例

### 用例1：正常单选投票
```json
{
  "voteId": 1,
  "optionIds": [1]
}
```

### 用例2：正常多选投票
```json
{
  "voteId": 1,
  "optionIds": [1, 2]
}
```

### 用例3：无效选项ID
```json
{
  "voteId": 1,
  "optionIds": [999]
}
```

### 用例4：空选项数组
```json
{
  "voteId": 1,
  "optionIds": []
}
```

## 🐛 常见问题排查

### 问题1：投票提交失败
**可能原因：**
- 后端API未实现
- 请求格式不正确
- Token无效或过期

**排查步骤：**
1. 检查浏览器Network标签页的请求详情
2. 确认请求URL和请求体格式
3. 验证token有效性

### 问题2：选项ID不正确
**可能原因：**
- 前端获取的选项数据结构不匹配
- 选项ID字段名称不一致

**排查步骤：**
1. 在调试模式下查看选项数据结构
2. 确认选项是否有`id`字段
3. 检查`sortOrder`作为备用ID

### 问题3：重复投票错误
**可能原因：**
- 后端检测到用户已投票
- 投票状态未正确更新

**排查步骤：**
1. 确认后端投票记录逻辑
2. 检查用户投票历史
3. 验证投票状态更新机制

## 📝 测试记录模板

### 测试环境
- 前端版本：
- 后端版本：
- 浏览器：
- 测试时间：

### 测试结果
| 测试用例 | 预期结果 | 实际结果 | 状态 | 备注 |
|---------|---------|---------|------|------|
| 单选投票 | 提交成功 |         |      |      |
| 多选投票 | 提交成功 |         |      |      |
| 无效选项 | 错误提示 |         |      |      |
| 重复投票 | 错误提示 |         |      |      |

### 发现的问题
1. 
2. 
3. 

### 解决方案
1. 
2. 
3. 

## 🚀 性能测试

### 响应时间测试
- 投票提交响应时间应 < 2秒
- 投票列表加载时间应 < 3秒

### 并发测试
- 多用户同时投票
- 验证数据一致性

## ✅ 验收标准

投票功能通过测试的标准：
- [ ] 可以成功提交单选投票
- [ ] 可以成功提交多选投票
- [ ] 错误情况有适当的提示
- [ ] 投票结果实时更新
- [ ] 用户体验流畅
- [ ] 无控制台错误
- [ ] API调用格式正确

## 📞 技术支持

如果测试过程中遇到问题：
1. 查看浏览器开发者工具的详细错误信息
2. 使用提供的测试工具进行调试
3. 记录详细的错误日志和复现步骤
4. 提供测试环境信息

测试完成后，请提供详细的测试报告和发现的问题。
