<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hfut.xiaozu.vote.info.VoteOptionMapper">

    <resultMap id="BaseResultMap" type="com.hfut.xiaozu.vote.info.VoteOptionEntity">
            <id property="id" column="id" />
            <result property="voteId" column="vote_id" />
            <result property="content" column="content" />
            <result property="sortOrder" column="sort_order" />
    </resultMap>

    <sql id="Base_Column_List">
        id,vote_id,content,sort_order
    </sql>

    <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from vote_option
        where  id = #{id}
    </select>

    <select id="listByVoteId" resultType="com.hfut.xiaozu.vote.info.VoteOptionEntity">
        select
        <include refid="Base_Column_List" />
        from vote_option
        where  vote_id = #{voteId}
    </select>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from vote_option
        where  id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.hfut.xiaozu.vote.info.VoteOptionEntity" useGeneratedKeys="true">
        insert into vote_option
        ( vote_id,content,sort_order)
        values (#{voteId},#{content},#{sortOrder})
    </insert>

    <update id="updateById" parameterType="com.hfut.xiaozu.vote.info.VoteOptionEntity">
        update vote_option
        <set>
                <if test="voteId != null">
                    vote_id = #{voteId},
                </if>
                <if test="content != null">
                    content = #{content},
                </if>
                <if test="sortOrder != null">
                    sort_order = #{sortOrder},
                </if>
        </set>
        where   id = #{id}
    </update>

</mapper>
