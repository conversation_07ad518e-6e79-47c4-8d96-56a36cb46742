<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dialog服务测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #2196F3;
            margin-top: 0;
        }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976D2;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            background: #f8f9fa;
            border-left: 4px solid #2196F3;
        }
        .error {
            border-left-color: #f44336;
            background: #ffebee;
        }
        .success {
            border-left-color: #4caf50;
            background: #e8f5e8;
        }
        .code {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Dialog服务测试</h1>
        
        <div class="test-section">
            <h3>1. 问题分析</h3>
            <p>根据控制台输出，退出登录功能被调用了，但可能是dialog服务的问题。</p>
            <div class="code">
                控制台警告：Component provided template option but runtime compilation is not supported
            </div>
            <p>这个警告表明Vue的运行时编译有问题，可能影响了dialog组件的正常工作。</p>
        </div>

        <div class="test-section">
            <h3>2. 解决方案</h3>
            <p>我们需要修改SidebarLayout.vue中的退出登录实现，使用更简单可靠的方式：</p>
            <div class="code">
// 方案1：使用浏览器原生confirm作为备选
const logout = async () => {
  try {
    // 尝试使用自定义dialog
    const confirmed = await dialog.confirm('确定要退出登录吗？', '退出确认');
    if (confirmed) {
      await authStore.logout();
      userStore.logout();
      router.push('/login');
    }
  } catch (error) {
    // 如果自定义dialog失败，使用原生confirm
    if (confirm('确定要退出登录吗？')) {
      await authStore.logout();
      userStore.logout();
      router.push('/login');
    }
  }
};
            </div>
        </div>

        <div class="test-section">
            <h3>3. 测试原生confirm</h3>
            <p>测试浏览器原生的confirm功能是否正常：</p>
            <button onclick="testNativeConfirm()">🔍 测试原生confirm</button>
            <div id="nativeResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>4. 模拟退出登录流程</h3>
            <p>模拟完整的退出登录流程：</p>
            <button onclick="simulateLogout()">🚪 模拟退出登录</button>
            <div id="logoutResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>5. 建议的修复代码</h3>
            <p>以下是建议的SidebarLayout.vue修复代码：</p>
            <div class="code">
// 退出登录 - 增强版本，包含错误处理
const logout = async () => {
  let confirmed = false;
  
  try {
    // 尝试使用自定义dialog
    confirmed = await dialog.confirm('确定要退出登录吗？', '退出确认');
  } catch (error) {
    console.warn('自定义dialog失败，使用原生confirm:', error);
    // 备选方案：使用原生confirm
    confirmed = confirm('确定要退出登录吗？');
  }
  
  if (confirmed) {
    try {
      // 执行退出登录
      await authStore.logout();
      userStore.logout();
      
      // 显示成功消息
      try {
        await dialog.showSuccess('退出登录成功！');
      } catch (error) {
        console.log('退出登录成功！');
      }
      
      // 跳转到登录页
      router.push('/login');
    } catch (error) {
      console.error('退出登录失败:', error);
      try {
        await dialog.showError('退出登录失败，请重试');
      } catch (e) {
        alert('退出登录失败，请重试');
      }
    }
  }
};
            </div>
        </div>
    </div>

    <script>
        function testNativeConfirm() {
            const confirmed = confirm('这是原生confirm测试，点击确定继续');
            const result = confirmed ? '✅ 用户点击了确定' : '❌ 用户点击了取消';
            showResult('nativeResult', result, confirmed ? 'success' : 'error');
        }

        function simulateLogout() {
            const confirmed = confirm('确定要退出登录吗？');
            if (confirmed) {
                // 模拟清理localStorage
                localStorage.removeItem('userToken');
                localStorage.removeItem('userType');
                localStorage.removeItem('userData');
                localStorage.removeItem('authToken');
                
                showResult('logoutResult', '✅ 模拟退出登录成功！已清理localStorage数据', 'success');
                
                // 模拟跳转
                setTimeout(() => {
                    showResult('logoutResult', '✅ 退出登录完成！正常情况下会跳转到登录页面', 'success');
                }, 1000);
            } else {
                showResult('logoutResult', '❌ 用户取消了退出登录', 'error');
            }
        }

        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }
    </script>
</body>
</html>
