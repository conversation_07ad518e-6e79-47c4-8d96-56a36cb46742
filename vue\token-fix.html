<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Token问题修复工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button.success {
            background: #28a745;
        }
        .button.danger {
            background: #dc3545;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .result.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .input-group {
            margin-bottom: 15px;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Token问题修复工具</h1>
        
        <!-- 当前状态检查 -->
        <div class="section">
            <h3>1. 当前Token状态检查</h3>
            <button class="button" onclick="checkCurrentTokenStatus()">检查当前状态</button>
            <button class="button" onclick="findAllTokens()">查找所有Token</button>
            <div id="statusResult" class="result" style="display: none;"></div>
        </div>

        <!-- 手动设置Token -->
        <div class="section">
            <h3>2. 手动设置Token</h3>
            <div class="input-group">
                <label for="manualToken">输入有效的JWT Token:</label>
                <input type="text" id="manualToken" placeholder="粘贴您的JWT Token">
            </div>
            <button class="button success" onclick="setManualToken()">设置Token</button>
            <button class="button" onclick="testManualToken()">测试Token</button>
            <div id="manualResult" class="result" style="display: none;"></div>
        </div>

        <!-- 从其他页面复制Token -->
        <div class="section">
            <h3>3. 从登录页面获取Token</h3>
            <p>如果您在其他标签页已经登录，可以尝试从那里复制Token：</p>
            <button class="button" onclick="copyTokenFromLogin()">尝试获取Token</button>
            <div id="copyResult" class="result" style="display: none;"></div>
        </div>

        <!-- 重新登录 -->
        <div class="section">
            <h3>4. 重新登录</h3>
            <p>如果以上方法都无效，建议重新登录：</p>
            <button class="button danger" onclick="clearAllAndRedirect()">清除数据并重新登录</button>
            <div id="loginResult" class="result" style="display: none;"></div>
        </div>

        <!-- 测试投票API -->
        <div class="section">
            <h3>5. 测试投票API</h3>
            <button class="button" onclick="testVoteApi()">测试投票列表API</button>
            <button class="button" onclick="testVoteSubmit()">测试投票提交API</button>
            <div id="testResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = content;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }

        // 1. 检查当前Token状态
        function checkCurrentTokenStatus() {
            let result = '=== Token状态检查 ===\n\n';
            
            // 检查localStorage
            result += 'localStorage检查:\n';
            const authData = localStorage.getItem('auth_data');
            if (authData) {
                try {
                    const parsed = JSON.parse(authData);
                    result += `  auth_data: 存在 (token: ${parsed.token ? '有' : '无'})\n`;
                    if (parsed.token) {
                        result += `  token长度: ${parsed.token.length}\n`;
                        result += `  token前缀: ${parsed.token.substring(0, 30)}...\n`;
                    }
                } catch (e) {
                    result += `  auth_data: 解析失败 - ${e.message}\n`;
                }
            } else {
                result += '  auth_data: 不存在\n';
            }
            
            // 检查其他可能的token存储
            const tokenKeys = ['auth_token', 'userToken', 'token', 'jwt_token', 'access_token'];
            tokenKeys.forEach(key => {
                const value = localStorage.getItem(key);
                result += `  ${key}: ${value ? '存在' : '不存在'}\n`;
            });
            
            // 检查sessionStorage
            result += '\nsessionStorage检查:\n';
            const sessionAuthData = sessionStorage.getItem('auth_data');
            if (sessionAuthData) {
                try {
                    const parsed = JSON.parse(sessionAuthData);
                    result += `  auth_data: 存在 (token: ${parsed.token ? '有' : '无'})\n`;
                } catch (e) {
                    result += `  auth_data: 解析失败 - ${e.message}\n`;
                }
            } else {
                result += '  auth_data: 不存在\n';
            }
            
            showResult('statusResult', result, 'info');
        }

        // 查找所有可能的Token
        function findAllTokens() {
            let result = '=== 查找所有Token ===\n\n';
            
            result += 'localStorage中的所有项目:\n';
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                const value = localStorage.getItem(key);
                const preview = value.length > 50 ? value.substring(0, 50) + '...' : value;
                result += `  ${key}: ${preview}\n`;
            }
            
            result += '\nsessionStorage中的所有项目:\n';
            for (let i = 0; i < sessionStorage.length; i++) {
                const key = sessionStorage.key(i);
                const value = sessionStorage.getItem(key);
                const preview = value.length > 50 ? value.substring(0, 50) + '...' : value;
                result += `  ${key}: ${preview}\n`;
            }
            
            showResult('statusResult', result, 'info');
        }

        // 2. 手动设置Token
        function setManualToken() {
            const token = document.getElementById('manualToken').value.trim();
            if (!token) {
                showResult('manualResult', '请输入Token', 'error');
                return;
            }

            try {
                // 设置到auth_data格式
                const authData = {
                    token: token,
                    tokenExpiry: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24小时后过期
                    user: { id: 1, username: 'manual_user' } // 临时用户信息
                };
                
                localStorage.setItem('auth_data', JSON.stringify(authData));
                
                // 也设置到兼容格式
                localStorage.setItem('auth_token', token);
                
                showResult('manualResult', `Token已设置成功!\n长度: ${token.length}\n前缀: ${token.substring(0, 30)}...`, 'success');
            } catch (error) {
                showResult('manualResult', `设置Token失败: ${error.message}`, 'error');
            }
        }

        // 测试手动设置的Token
        async function testManualToken() {
            const token = document.getElementById('manualToken').value.trim();
            if (!token) {
                showResult('manualResult', '请先输入Token', 'error');
                return;
            }

            try {
                const response = await fetch('http://localhost:8080/api/vote/list', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    showResult('manualResult', `Token测试成功!\n状态: ${response.status}\n数据: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult('manualResult', `Token测试失败!\n状态: ${response.status}\n错误: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult('manualResult', `Token测试异常: ${error.message}`, 'error');
            }
        }

        // 3. 从登录页面复制Token
        function copyTokenFromLogin() {
            // 尝试从其他窗口获取token
            showResult('copyResult', '请手动执行以下步骤:\n1. 打开登录页面\n2. 登录成功后，在控制台运行: localStorage.getItem("auth_data")\n3. 复制返回的token值\n4. 粘贴到上面的输入框中', 'info');
        }

        // 4. 清除所有数据并重新登录
        function clearAllAndRedirect() {
            if (confirm('确定要清除所有数据并重新登录吗？')) {
                // 清除所有可能的认证数据
                localStorage.clear();
                sessionStorage.clear();
                
                showResult('loginResult', '所有数据已清除，即将跳转到登录页面...', 'success');
                
                setTimeout(() => {
                    window.location.href = '/login';
                }, 2000);
            }
        }

        // 5. 测试投票API
        async function testVoteApi() {
            const authData = localStorage.getItem('auth_data');
            let token = null;
            
            if (authData) {
                try {
                    const parsed = JSON.parse(authData);
                    token = parsed.token;
                } catch (e) {
                    showResult('testResult', '无法解析auth_data', 'error');
                    return;
                }
            }
            
            if (!token) {
                token = localStorage.getItem('auth_token');
            }
            
            if (!token) {
                showResult('testResult', '没有找到Token，请先设置Token', 'error');
                return;
            }

            try {
                const response = await fetch('http://localhost:8080/api/vote/list', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();
                
                let result = `=== 投票API测试结果 ===\n`;
                result += `状态码: ${response.status}\n`;
                result += `状态文本: ${response.statusText}\n`;
                result += `响应数据: ${JSON.stringify(data, null, 2)}`;
                
                showResult('testResult', result, response.ok ? 'success' : 'error');
            } catch (error) {
                showResult('testResult', `API测试异常: ${error.message}`, 'error');
            }
        }

        // 测试投票提交
        async function testVoteSubmit() {
            const authData = localStorage.getItem('auth_data');
            let token = null;
            
            if (authData) {
                try {
                    const parsed = JSON.parse(authData);
                    token = parsed.token;
                } catch (e) {
                    showResult('testResult', '无法解析auth_data', 'error');
                    return;
                }
            }
            
            if (!token) {
                showResult('testResult', '没有找到Token，请先设置Token', 'error');
                return;
            }

            try {
                const response = await fetch('http://localhost:8080/api/vote/1', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        voteId: 1,
                        optionIds: [1]
                    })
                });

                const data = await response.json();
                
                let result = `=== 投票提交测试结果 ===\n`;
                result += `状态码: ${response.status}\n`;
                result += `状态文本: ${response.statusText}\n`;
                result += `响应数据: ${JSON.stringify(data, null, 2)}`;
                
                showResult('testResult', result, response.ok ? 'success' : 'error');
            } catch (error) {
                showResult('testResult', `投票提交测试异常: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动检查
        window.onload = function() {
            checkCurrentTokenStatus();
        };
    </script>
</body>
</html>
