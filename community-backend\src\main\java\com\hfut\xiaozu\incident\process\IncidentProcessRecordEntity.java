package com.hfut.xiaozu.incident.process;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 事件处理记录表
 * @TableName incident_process_record
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IncidentProcessRecordEntity {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 事件ID
     */
    private Long incidentId;

    /**
     * 操作: 1-上报 2-分派 3-开始处理 4-完成 5-关闭
     */
    private Integer status;

    /**
     * 操作人ID
     */
    private Long userId;

    /**
     * 操作人角色: 1-物业人员 2-网格员
     */
    private Integer userRole;

    /**
     * 处理备注
     */
    private String remark;

    /**
     * 操作时间
     */
    private LocalDateTime createTime;
}
