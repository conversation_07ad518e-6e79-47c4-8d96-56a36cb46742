<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实名认证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .result.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .token-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-size: 12px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🆔 实名认证测试</h1>
        <p>测试实名认证API接口</p>

        <div class="form-group">
            <label for="realName">真实姓名</label>
            <input type="text" id="realName" placeholder="请输入真实姓名" value="张三">
        </div>

        <div class="form-group">
            <label for="idCard">身份证号</label>
            <input type="text" id="idCard" placeholder="请输入身份证号" value="110101199001011234">
        </div>

        <div>
            <button onclick="checkToken()">🔑 检查Token</button>
            <button onclick="submitVerification()" id="submitBtn">📝 提交认证</button>
            <button onclick="clearResult()">🗑️ 清除结果</button>
        </div>

        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080/api';

        // 获取认证token（模拟identityApi.js的逻辑）
        function getAuthToken() {
            try {
                const authData = localStorage.getItem('auth_data') || sessionStorage.getItem('auth_data');
                if (authData) {
                    const parsed = JSON.parse(authData);
                    if (parsed.token) {
                        return parsed.token;
                    }
                }
            } catch (error) {
                console.warn('解析auth_data失败:', error);
            }
            
            return localStorage.getItem('auth_token') || 
                   localStorage.getItem('userToken') || 
                   null;
        }

        // 生成会话ID
        function generateSessionId() {
            return 'session_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
        }

        // 显示结果
        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }

        // 清除结果
        function clearResult() {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'none';
        }

        // 检查Token状态
        function checkToken() {
            const token = getAuthToken();
            
            if (token) {
                showResult(`✅ Token可用\n\nToken预览: ${token.substring(0, 50)}...\n\n存储位置检查:\n- auth_data: ${!!(localStorage.getItem('auth_data') || sessionStorage.getItem('auth_data'))}\n- auth_token: ${!!localStorage.getItem('auth_token')}\n- userToken: ${!!localStorage.getItem('userToken')}`, 'success');
            } else {
                showResult('❌ 未找到有效Token\n\n请先登录获取Token', 'error');
            }
        }

        // 提交实名认证
        async function submitVerification() {
            const submitBtn = document.getElementById('submitBtn');
            const realName = document.getElementById('realName').value.trim();
            const idCard = document.getElementById('idCard').value.trim();

            if (!realName || !idCard) {
                showResult('❌ 请填写完整信息', 'error');
                return;
            }

            const token = getAuthToken();
            if (!token) {
                showResult('❌ 未找到认证Token，请先登录', 'error');
                return;
            }

            submitBtn.disabled = true;
            submitBtn.textContent = '提交中...';

            try {
                const sessionId = generateSessionId();
                const requestData = {
                    submittedIdCard: idCard,
                    submittedName: realName,
                    sessionId: sessionId
                };

                console.log('🚀 发送实名认证请求:', requestData);

                const response = await fetch(`${API_BASE_URL}/auth/identity-verification`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(requestData)
                });

                console.log('📡 响应状态:', response.status, response.statusText);

                const result = await response.text();
                console.log('📦 响应内容:', result);

                let parsedResult;
                try {
                    parsedResult = JSON.parse(result);
                } catch (e) {
                    parsedResult = { raw: result };
                }

                if (response.ok) {
                    showResult(`✅ 提交成功！\n\nHTTP状态: ${response.status}\n\n响应内容:\n${JSON.stringify(parsedResult, null, 2)}`, 'success');
                } else {
                    showResult(`❌ 提交失败\n\nHTTP状态: ${response.status} ${response.statusText}\n\n响应内容:\n${JSON.stringify(parsedResult, null, 2)}`, 'error');
                }

            } catch (error) {
                console.error('❌ 请求失败:', error);
                showResult(`❌ 网络错误\n\n错误信息: ${error.message}\n\n可能原因:\n1. 后端服务未启动\n2. 网络连接问题\n3. CORS跨域问题`, 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '📝 提交认证';
            }
        }

        // 页面加载时检查token状态
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔍 页面加载，检查token状态...');
            checkToken();
        });
    </script>
</body>
</html>
