<template>
  <div class="issue-report">
    <div class="page-header">
      <h1 class="page-title">
        <span class="title-icon">📢</span>
        问题上报
      </h1>
      <p class="page-description">发现社区问题？请在此上报，我们会及时处理</p>
    </div>

    <div class="report-container">
      <!-- 问题信息表单 -->
      <div class="form-section">
        <div class="section-header">
          <h2>问题详情</h2>
          <p>请详细描述您发现的问题</p>
        </div>

        <form @submit.prevent="submitReport" class="report-form">
          <!-- 问题标题 -->
          <div class="form-group">
            <label class="form-label">
              <span class="label-icon">📝</span>
              问题标题 <span class="required">*</span>
            </label>
            <input
              v-model="reportForm.title"
              type="text"
              class="form-input"
              placeholder="请简要描述问题"
              maxlength="100"
              required
            />
            <div class="char-count">{{ reportForm.title.length }}/100</div>
          </div>

          <!-- 问题类型 -->
          <div class="form-group">
            <label class="form-label">
              <span class="label-icon">🏷️</span>
              问题类型 <span class="required">*</span>
            </label>
            <select v-model="reportForm.categoryType" class="form-select" required>
              <option value="">请选择问题类型</option>
              <option
                v-for="category in issueCategories"
                :key="category.value"
                :value="category.value"
              >
                {{ category.icon }} {{ category.label }}
              </option>
            </select>
          </div>

          <!-- 优先级 -->
          <div class="form-group">
            <label class="form-label">
              <span class="label-icon">⚡</span>
              优先级 <span class="required">*</span>
            </label>
            <select v-model="reportForm.priority" class="form-select" required>
              <option value="">请选择优先级</option>
              <option
                v-for="priority in priorityLevels"
                :key="priority.value"
                :value="priority.value"
              >
                {{ priority.icon }} {{ priority.label }}
              </option>
            </select>
          </div>

          <!-- 问题描述 -->
          <div class="form-group">
            <label class="form-label">
              <span class="label-icon">📄</span>
              问题描述 <span class="required">*</span>
            </label>
            <textarea
              v-model="reportForm.description"
              class="form-textarea"
              placeholder="请详细描述问题的具体情况、发生时间等"
              rows="4"
              maxlength="500"
              required
            ></textarea>
            <div class="char-count">{{ reportForm.description.length }}/500</div>
          </div>

          <!-- 位置描述 -->
          <div class="form-group">
            <label class="form-label">
              <span class="label-icon">📍</span>
              位置描述
            </label>
            <input
              v-model="reportForm.locationDescription"
              type="text"
              class="form-input"
              placeholder="如：翰林雅居10栋、小区门口等"
              maxlength="100"
            />
          </div>

          <!-- 提交按钮 -->
          <div class="form-actions">
            <button
              type="submit"
              class="submit-btn"
              :disabled="!canSubmit || isSubmitting"
            >
              <span v-if="isSubmitting" class="loading-spinner"></span>
              <span class="btn-text">{{ isSubmitting ? '提交中...' : '提交问题上报' }}</span>
            </button>
            <button type="button" class="reset-btn" @click="resetForm">
              重置表单
            </button>
          </div>
        </form>
      </div>

      <!-- 地图选点区域 -->
      <div class="map-section">
        <div class="section-header">
          <h2>问题位置</h2>
          <p>请在地图上点击标记问题发生的具体位置</p>
        </div>

        <div class="map-controls">
          <div class="location-info">
            <div v-if="selectedLocation" class="location-display">
              <span class="location-icon">📍</span>
              <span class="location-text">
                已选择位置：{{ selectedLocation.lng.toFixed(6) }}, {{ selectedLocation.lat.toFixed(6) }}
              </span>
              <button class="clear-location-btn" @click="clearLocation">
                <span>✕</span>
              </button>
            </div>
            <div v-else class="location-placeholder">
              <span class="placeholder-icon">📍</span>
              <span class="placeholder-text">请在地图上点击选择问题位置</span>
            </div>
          </div>
        </div>

        <div class="map-container">
          <MapComponent
            ref="mapComponent"
            map-id="issue-report-map"
            :height="'400px'"
            :center="mapCenter"
            :zoom="16"
            :markers="locationMarkers"
            :show-controls="true"
            :show-drawing-tools="false"
            @map-ready="onMapReady"
            @coordinate-clicked="onLocationSelected"
          />
        </div>
      </div>
    </div>

    <!-- 成功提示 -->
    <div v-if="showSuccessMessage" class="success-overlay">
      <div class="success-modal">
        <div class="success-icon">✅</div>
        <h3>问题上报成功！</h3>
        <p>您的问题已成功提交，我们会尽快处理。</p>
        <div class="success-actions">
          <button @click="closeSuccessMessage" class="success-btn">确定</button>
          <button @click="submitAnother" class="success-btn secondary">继续上报</button>
        </div>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="errorMessage" class="error-message">
      <span class="error-icon">❌</span>
      <span class="error-text">{{ errorMessage }}</span>
      <button @click="clearError" class="error-close">✕</button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import MapComponent from '../../components/MapComponent.vue'
import { useUserStore } from '../../stores/userStore'
import { submitIssueReport, getIssueCategories, getPriorityLevels, validateIssueData } from '../../services/issueApi.js'

// 用户状态
const userStore = useUserStore()

// 响应式数据
const mapComponent = ref(null)
const isSubmitting = ref(false)
const showSuccessMessage = ref(false)
const errorMessage = ref('')
const selectedLocation = ref(null)

// 选项数据
const issueCategories = ref(getIssueCategories())
const priorityLevels = ref(getPriorityLevels())

// 表单数据
const reportForm = ref({
  title: '',
  description: '',
  categoryType: '',
  priority: '',
  locationDescription: '',
  communityId: 1 // 默认小区ID，实际应该从用户信息获取
})

// 地图配置
const mapCenter = [117.198612, 31.774164] // 默认地图中心点

// 地图标记
const locationMarkers = computed(() => {
  if (!selectedLocation.value) return []
  
  return [{
    lng: selectedLocation.value.lng,
    lat: selectedLocation.value.lat,
    title: '问题位置',
    popup: `<div><h4>问题位置</h4><p>经度: ${selectedLocation.value.lng.toFixed(6)}</p><p>纬度: ${selectedLocation.value.lat.toFixed(6)}</p></div>`,
    properties: { type: 'issue-location' }
  }]
})

// 表单验证
const canSubmit = computed(() => {
  return reportForm.value.title.trim() &&
         reportForm.value.description.trim() &&
         reportForm.value.categoryType &&
         reportForm.value.priority &&
         selectedLocation.value
})

// 地图就绪事件
const onMapReady = () => {
  console.log('问题上报地图初始化完成')
  // 启用坐标模式，允许点击地图获取坐标
  if (mapComponent.value) {
    mapComponent.value.enableCoordinateMode()
  }
}

// 地图点击选择位置
const onLocationSelected = (event) => {
  console.log('选择位置:', event)

  // 验证坐标数据
  if (!event || typeof event.lng !== 'number' || typeof event.lat !== 'number') {
    console.error('无效的坐标数据:', event)
    errorMessage.value = '获取位置坐标失败，请重新选择'
    return
  }

  selectedLocation.value = {
    lng: event.lng,
    lat: event.lat
  }

  // 清除之前的错误信息
  if (errorMessage.value.includes('位置')) {
    errorMessage.value = ''
  }

  console.log('位置选择成功:', selectedLocation.value)
}

// 清除选择的位置
const clearLocation = () => {
  selectedLocation.value = null
}

// 提交问题上报
const submitReport = async () => {
  if (!canSubmit.value || isSubmitting.value) return

  isSubmitting.value = true
  errorMessage.value = ''

  try {
    // 准备提交数据
    const submitData = {
      title: reportForm.value.title.trim(),
      description: reportForm.value.description.trim(),
      categoryType: parseInt(reportForm.value.categoryType),
      priority: parseInt(reportForm.value.priority),
      locationDescription: reportForm.value.locationDescription.trim(),
      locationGeojson: {
        latitude: selectedLocation.value.lat,
        longitude: selectedLocation.value.lng
      },
      communityId: reportForm.value.communityId
    }

    // 验证数据
    const validation = validateIssueData(submitData)
    if (!validation.isValid) {
      throw new Error(validation.errors.join('; '))
    }

    console.log('🔧 提交问题上报数据:', submitData)

    // 调用API服务
    const result = await submitIssueReport(submitData)

    if (result.success) {
      showSuccessMessage.value = true
      console.log('✅ 问题上报成功')
    } else {
      throw new Error(result.message || '问题上报失败')
    }

  } catch (error) {
    console.error('❌ 问题上报失败:', error)
    errorMessage.value = error.message || '问题上报失败，请稍后重试'
  } finally {
    isSubmitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  reportForm.value = {
    title: '',
    description: '',
    categoryType: '',
    priority: '',
    locationDescription: '',
    communityId: 1
  }
  selectedLocation.value = null
  errorMessage.value = ''
}

// 关闭成功提示
const closeSuccessMessage = () => {
  showSuccessMessage.value = false
  resetForm()
}

// 继续上报
const submitAnother = () => {
  showSuccessMessage.value = false
  resetForm()
}

// 清除错误信息
const clearError = () => {
  errorMessage.value = ''
}

// 组件挂载
onMounted(() => {
  console.log('问题上报页面已挂载')
})
</script>

<style scoped>
.issue-report {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 30px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 10px 0;
}

.title-icon {
  font-size: 32px;
}

.page-description {
  font-size: 16px;
  color: #7f8c8d;
  margin: 0;
}

.report-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  max-width: 1400px;
  margin: 0 auto;
}

.form-section, .map-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.section-header {
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f1f3f4;
}

.section-header h2 {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.section-header p {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
}

.report-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  position: relative;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
}

.label-icon {
  font-size: 16px;
}

.required {
  color: #e74c3c;
}

.form-input, .form-select, .form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus, .form-select:focus, .form-textarea:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.char-count {
  text-align: right;
  font-size: 12px;
  color: #95a5a6;
  margin-top: 5px;
}

.form-actions {
  display: flex;
  gap: 15px;
  margin-top: 20px;
}

.submit-btn {
  flex: 1;
  padding: 15px 20px;
  background: #27ae60;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.submit-btn:hover:not(:disabled) {
  background: #229954;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(39, 174, 96, 0.3);
}

.submit-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
}

.reset-btn {
  padding: 15px 20px;
  background: #95a5a6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.reset-btn:hover {
  background: #7f8c8d;
  transform: translateY(-2px);
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.map-controls {
  margin-bottom: 20px;
}

.location-info {
  background: #f8f9fa;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  padding: 15px;
}

.location-display {
  display: flex;
  align-items: center;
  gap: 10px;
}

.location-icon {
  font-size: 18px;
  color: #27ae60;
}

.location-text {
  flex: 1;
  font-size: 14px;
  color: #2c3e50;
  font-weight: 500;
}

.clear-location-btn {
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: all 0.3s ease;
}

.clear-location-btn:hover {
  background: #c0392b;
  transform: scale(1.1);
}

.location-placeholder {
  display: flex;
  align-items: center;
  gap: 10px;
}

.placeholder-icon {
  font-size: 18px;
  color: #bdc3c7;
}

.placeholder-text {
  font-size: 14px;
  color: #7f8c8d;
  font-style: italic;
}

.map-container {
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  overflow: hidden;
}

.success-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.success-modal {
  background: white;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.success-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.success-modal h3 {
  font-size: 24px;
  font-weight: 600;
  color: #27ae60;
  margin: 0 0 15px 0;
}

.success-modal p {
  font-size: 16px;
  color: #7f8c8d;
  margin: 0 0 30px 0;
  line-height: 1.5;
}

.success-actions {
  display: flex;
  gap: 15px;
}

.success-btn {
  flex: 1;
  padding: 12px 20px;
  background: #27ae60;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.success-btn:hover {
  background: #229954;
  transform: translateY(-2px);
}

.success-btn.secondary {
  background: #3498db;
}

.success-btn.secondary:hover {
  background: #2980b9;
}

.error-message {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #e74c3c;
  color: white;
  padding: 15px 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
  z-index: 1000;
  max-width: 400px;
}

.error-icon {
  font-size: 18px;
}

.error-text {
  flex: 1;
  font-size: 14px;
}

.error-close {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 16px;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .report-container {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .issue-report {
    padding: 15px;
  }

  .page-header {
    padding: 20px 15px;
  }

  .page-title {
    font-size: 24px;
  }

  .form-section, .map-section {
    padding: 20px;
  }

  .form-actions {
    flex-direction: column;
  }

  .success-modal {
    padding: 30px 20px;
  }

  .success-actions {
    flex-direction: column;
  }
}
</style>
