package com.hfut.xiaozu.house.information;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 房屋信息表
 * @TableName house_info
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class HouseInfo {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 单元ID
     */
    private Long unitId;

    /**
     * 门牌号
     */
    private String houseNumber;

    /**
     * 房屋面积(m²)
     */
    private BigDecimal areaSize;

    /**
     * 是否已入住 0-否 1-是
     */
    private Integer isOccupied;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
