// 共享的事件数据
export const eventData = [
  {
    id: 'EVT001',
    type: 'maintenance',
    title: '电梯故障',
    description: '3号楼电梯停运，需要维修',
    location: '芙蓉社区A区3号楼',
    status: 'pending',
    priority: 'high',
    lat: 39.9042,
    lng: 116.4074,
    reportTime: new Date('2024-01-20T09:30:00'),
    assignee: '张三',
    gridId: 1
  },
  {
    id: 'EVT002',
    type: 'security',
    title: '可疑人员',
    description: '发现可疑人员在小区内徘徊',
    location: '芙蓉社区B区门口',
    status: 'processing',
    priority: 'high',
    lat: 39.9044,
    lng: 116.4085,
    reportTime: new Date('2024-01-20T14:15:00'),
    assignee: '李四',
    gridId: 2
  },
  {
    id: 'EVT003',
    type: 'environment',
    title: '垃圾清理',
    description: '垃圾桶已满，需要及时清理',
    location: '芙蓉社区C区垃圾站',
    status: 'completed',
    priority: 'medium',
    lat: 39.9057,
    lng: 116.4067,
    reportTime: new Date('2024-01-19T16:45:00'),
    assignee: '王五',
    gridId: 3
  },
  {
    id: 'EVT004',
    type: 'maintenance',
    title: '路灯损坏',
    description: '小区主干道路灯不亮',
    location: '芙蓉社区D区主干道',
    status: 'pending',
    priority: 'medium',
    lat: 39.9045,
    lng: 116.4095,
    reportTime: new Date('2024-01-20T19:20:00'),
    assignee: '赵六',
    gridId: 4
  },
  {
    id: 'EVT005',
    type: 'emergency',
    title: '水管爆裂',
    description: '地下水管爆裂，影响供水',
    location: '芙蓉社区E区地下室',
    status: 'processing',
    priority: 'urgent',
    lat: 39.9035,
    lng: 116.4060,
    reportTime: new Date('2024-01-20T11:10:00'),
    assignee: '孙七',
    gridId: 5
  },
  {
    id: 'EVT006',
    type: 'security',
    title: '车辆违停',
    description: '消防通道被车辆占用',
    location: '芙蓉社区A区消防通道',
    status: 'pending',
    priority: 'medium',
    lat: 39.9040,
    lng: 116.4072,
    reportTime: new Date('2024-01-20T08:45:00'),
    assignee: '张三',
    gridId: 1
  },
  {
    id: 'EVT007',
    type: 'environment',
    title: '绿化维护',
    description: '花坛需要修剪和浇水',
    location: '芙蓉社区B区中心花园',
    status: 'completed',
    priority: 'low',
    lat: 39.9046,
    lng: 116.4088,
    reportTime: new Date('2024-01-19T10:30:00'),
    assignee: '李四',
    gridId: 2
  },
  {
    id: 'EVT008',
    type: 'maintenance',
    title: '门禁故障',
    description: '单元门禁系统无法正常使用',
    location: '芙蓉社区C区5号楼',
    status: 'processing',
    priority: 'high',
    lat: 39.9055,
    lng: 116.4070,
    reportTime: new Date('2024-01-20T13:20:00'),
    assignee: '王五',
    gridId: 3
  }
];

// 事件类型配置
export const eventTypes = [
  { key: 'maintenance', name: '设施维护', color: '#007bff', icon: '🔧' },
  { key: 'security', name: '安全事件', color: '#dc3545', icon: '🛡️' },
  { key: 'environment', name: '环境卫生', color: '#28a745', icon: '🌱' },
  { key: 'emergency', name: '紧急事件', color: '#fd7e14', icon: '🚨' }
];

// 事件状态配置
export const eventStatuses = [
  { key: 'pending', name: '待处理', color: '#ffc107' },
  { key: 'processing', name: '处理中', color: '#17a2b8' },
  { key: 'completed', name: '已完成', color: '#28a745' }
];

// 优先级配置
export const eventPriorities = [
  { key: 'low', name: '低', color: '#6c757d' },
  { key: 'medium', name: '中', color: '#ffc107' },
  { key: 'high', name: '高', color: '#fd7e14' },
  { key: 'urgent', name: '紧急', color: '#dc3545' }
];

// 将事件数据转换为地图标记格式
export const getEventMarkers = (typeFilter = [], statusFilter = [], timeFilter = 'all') => {
  let filteredEvents = eventData;

  // 类型过滤
  if (typeFilter.length > 0) {
    filteredEvents = filteredEvents.filter(event => typeFilter.includes(event.type));
  }

  // 状态过滤
  if (statusFilter.length > 0) {
    filteredEvents = filteredEvents.filter(event => statusFilter.includes(event.status));
  }

  // 时间过滤
  if (timeFilter !== 'all') {
    const now = new Date();
    const filterDate = new Date();
    
    switch (timeFilter) {
      case 'today':
        filterDate.setHours(0, 0, 0, 0);
        break;
      case 'week':
        filterDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        filterDate.setMonth(now.getMonth() - 1);
        break;
    }
    
    filteredEvents = filteredEvents.filter(event => event.reportTime >= filterDate);
  }

  return filteredEvents.map(event => {
    const eventType = eventTypes.find(type => type.key === event.type);
    const eventStatus = eventStatuses.find(status => status.key === event.status);
    const eventPriority = eventPriorities.find(priority => priority.key === event.priority);

    return {
      lat: event.lat,
      lng: event.lng,
      title: `${event.id} - ${eventType?.name || event.type}`,
      popup: `
        <div style="min-width: 200px;">
          <h4 style="margin: 0 0 8px 0; color: ${eventType?.color || '#333'};">
            ${eventType?.icon || '📋'} ${event.title}
          </h4>
          <p style="margin: 4px 0; font-size: 13px;"><strong>位置：</strong>${event.location}</p>
          <p style="margin: 4px 0; font-size: 13px;"><strong>描述：</strong>${event.description}</p>
          <p style="margin: 4px 0; font-size: 13px;">
            <strong>状态：</strong>
            <span style="color: ${eventStatus?.color || '#333'};">${eventStatus?.name || event.status}</span>
          </p>
          <p style="margin: 4px 0; font-size: 13px;">
            <strong>优先级：</strong>
            <span style="color: ${eventPriority?.color || '#333'};">${eventPriority?.name || event.priority}</span>
          </p>
          <p style="margin: 4px 0; font-size: 13px;"><strong>负责人：</strong>${event.assignee}</p>
          <p style="margin: 4px 0; font-size: 12px; color: #666;">
            ${event.reportTime.toLocaleString('zh-CN')}
          </p>
        </div>
      `,
      properties: {
        eventId: event.id,
        type: event.type,
        status: event.status,
        priority: event.priority,
        gridId: event.gridId
      }
    };
  });
};

// 获取事件统计信息
export const getEventStats = () => {
  const total = eventData.length;
  const pending = eventData.filter(event => event.status === 'pending').length;
  const processing = eventData.filter(event => event.status === 'processing').length;
  const completed = eventData.filter(event => event.status === 'completed').length;
  
  const typeStats = eventTypes.map(type => ({
    ...type,
    count: eventData.filter(event => event.type === type.key).length
  }));

  return {
    total,
    pending,
    processing,
    completed,
    typeStats
  };
};
