/**
 * 事件管理API服务
 * 处理事件相关的后端API调用
 */

import http from '../utils/httpInterceptor.js';

/**
 * 指派网格员处理事件
 * @param {number} incidentId - 事件ID
 * @param {number} handlerId - 网格员ID
 * @returns {Promise<Object>} API响应
 */
export const assignGridWorker = async (incidentId, handlerId) => {
  console.log('🔧 指派网格员处理事件:', { incidentId, handlerId });

  try {
    // 使用HTTP拦截器发送GET请求（自动添加token）
    // 后端使用GET方法和RequestParam接收handlerId参数
    const response = await http.get(`/incident/${incidentId}/assign?handlerId=${handlerId}`);

    console.log('✅ 指派网格员成功:', response);

    return {
      success: true,
      data: response.data,
      message: response.msg || response.message || '指派成功'
    };

  } catch (error) {
    console.error('❌ 指派网格员失败:', error);
    throw error;
  }
};

/**
 * 网格员完成事件
 * @param {number} incidentId - 事件ID
 * @returns {Promise<Object>} API响应
 */
export const completeIncident = async (incidentId) => {
  console.log('✅ 网格员完成事件:', { incidentId });

  try {
    // 使用HTTP拦截器发送PUT请求（自动添加token）
    // 后端使用PUT方法，只需要路径参数incidentId，不需要请求体
    const response = await http.put(`/incident/${incidentId}/complete`, null);

    console.log('✅ 完成事件成功:', response);

    return {
      success: true,
      data: response.data,
      message: response.msg || response.message || '完成成功'
    };

  } catch (error) {
    console.error('❌ 完成事件失败:', error);
    throw error;
  }
};

/**
 * 获取事件列表
 * @param {number} status - 事件状态
 * @returns {Promise<Object>} API响应
 */
export const getEventList = async (status) => {
  console.log('🔧 获取事件列表:', status);

  try {
    const response = await http.get(`/incident/list/${status}`);

    console.log('✅ 获取事件列表成功:', response);

    return {
      success: true,
      data: response.data || [],
      message: response.msg || '获取事件列表成功'
    };

  } catch (error) {
    console.error('❌ 获取事件列表失败:', error);
    throw error;
  }
};

/**
 * 获取事件统计数据
 * @returns {Promise<Object>} API响应
 */
export const getEventCounts = async () => {
  console.log('🔧 获取事件统计数据...');

  try {
    const response = await http.get('/incident/counts');

    console.log('✅ 获取事件统计数据成功:', response);

    return {
      success: true,
      data: response.data || {},
      message: response.msg || '获取事件统计数据成功'
    };

  } catch (error) {
    console.error('❌ 获取事件统计数据失败:', error);
    throw error;
  }
};
