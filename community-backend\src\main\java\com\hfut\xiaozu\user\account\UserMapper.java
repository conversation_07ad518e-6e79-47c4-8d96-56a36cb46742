package com.hfut.xiaozu.user.account;

import org.apache.ibatis.annotations.Mapper;

/**
 *
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@Mapper
public interface UserMapper {

    UserAccountEntity getByPhone(String phone);

    UserAccountEntity getByUsername(String userName);

    Integer save(UserRegisterDTO userRegisterDTO);

    UserAccountEntity getByPhoneAndPasswordAndUserType(String userName, String password, Integer userType);

    UserAccountEntity getByUsernameAndPasswordAndUserType(String userName, String password, Integer userType);

    String getNameById(Long userId);

    UserAccountEntity getById(Long userId);

    int update(UserAccountEntity user);
}
