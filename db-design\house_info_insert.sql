INSERT INTO house_info (id, unit_id, house_number, area_size, is_occupied, create_time, update_time)
SELECT 
    tmp.id,
    CEIL(tmp.id / 36) AS unit_id,
    CONCAT(
        LPAD(CEIL((tmp.id % 36) / 2), 2, '0'),
        '0',
        IF(tmp.id % 2 = 0, 2, 1)
    ) AS house_number,
    ROUND(80 + (RAND() * 40), 2) AS area_size,
    0 AS is_occupied,
    NOW() AS create_time,
    NOW() AS update_time
FROM (
    SELECT 63 AS id UNION ALL SELECT 84 UNION ALL SELECT 105 UNION ALL SELECT 106 UNION ALL SELECT 181 UNION ALL 
    SELECT 267 UNION ALL SELECT 308 UNION ALL SELECT 330 UNION ALL SELECT 376 UNION ALL SELECT 396 UNION ALL 
    SELECT 406 UNION ALL SELECT 494 UNION ALL SELECT 585 UNION ALL SELECT 590 UNION ALL SELECT 628 UNION ALL 
    SELECT 669 UNION ALL SELECT 810 UNION ALL SELECT 834 UNION ALL SELECT 848 UNION ALL SELECT 910 UNION ALL 
    SELECT 919 UNION ALL SELECT 939 UNION ALL SELECT 951 UNION ALL SELECT 1119 UNION ALL SELECT 1199 UNION ALL 
    SELECT 1213 UNION ALL SELECT 1214 UNION ALL SELECT 1220 UNION ALL SELECT 1232 UNION ALL SELECT 1247 UNION ALL 
    SELECT 1346 UNION ALL SELECT 1353 UNION ALL SELECT 1384 UNION ALL SELECT 1400 UNION ALL SELECT 1405 UNION ALL 
    SELECT 1432 UNION ALL SELECT 1482 UNION ALL SELECT 1536 UNION ALL SELECT 1559 UNION ALL SELECT 1582 UNION ALL 
    SELECT 1618 UNION ALL SELECT 1674 UNION ALL SELECT 1676 UNION ALL SELECT 1701 UNION ALL SELECT 1703 UNION ALL 
    SELECT 1752 UNION ALL SELECT 1770 UNION ALL SELECT 1777 UNION ALL SELECT 1794 UNION ALL SELECT 1836 UNION ALL 
    SELECT 1918 UNION ALL SELECT 1992 UNION ALL SELECT 2090 UNION ALL SELECT 2134 UNION ALL SELECT 2140 UNION ALL 
    SELECT 2144 UNION ALL SELECT 2225 UNION ALL SELECT 2252 UNION ALL SELECT 2341 UNION ALL SELECT 2428 UNION ALL 
    SELECT 2448 UNION ALL SELECT 2472 UNION ALL SELECT 2528 UNION ALL SELECT 2552 UNION ALL SELECT 2613 UNION ALL 
    SELECT 2661 UNION ALL SELECT 2742 UNION ALL SELECT 2743 UNION ALL SELECT 2772 UNION ALL SELECT 2829 UNION ALL 
    SELECT 2832 UNION ALL SELECT 2853 UNION ALL SELECT 2880 UNION ALL SELECT 2895 UNION ALL SELECT 2958 UNION ALL 
    SELECT 2986 UNION ALL SELECT 3013 UNION ALL SELECT 3080 UNION ALL SELECT 3099 UNION ALL SELECT 3191 UNION ALL 
    SELECT 3196 UNION ALL SELECT 3296 UNION ALL SELECT 3307 UNION ALL SELECT 3364 UNION ALL SELECT 3385 UNION ALL 
    SELECT 3412 UNION ALL SELECT 3425 UNION ALL SELECT 3450 UNION ALL SELECT 3477 UNION ALL SELECT 3504 UNION ALL 
    SELECT 3511 UNION ALL SELECT 3593 UNION ALL SELECT 3628 UNION ALL SELECT 3655 UNION ALL SELECT 3661 UNION ALL 
    SELECT 3680 UNION ALL SELECT 3695 UNION ALL SELECT 3774 UNION ALL SELECT 3782 UNION ALL SELECT 3795
) AS tmp;