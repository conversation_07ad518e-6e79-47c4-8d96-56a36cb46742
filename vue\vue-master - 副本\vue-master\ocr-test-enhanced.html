<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR功能增强测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .form-group {
            margin: 20px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .request-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .token-display {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            word-break: break-all;
            margin: 10px 0;
        }
        .file-info {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 OCR功能增强测试</h1>
        <p>按照API文档要求测试OCR身份证识别功能</p>
        
        <div class="request-info">
            <h3>📋 API要求</h3>
            <p><strong>请求头示例：</strong></p>
            <div class="token-display">Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...</div>
            <p><strong>请求体示例：</strong></p>
            <div class="token-display">
Content-Type: multipart/form-data
- session_id: (文本) 会话ID
- image: (文件) 身份证图片
            </div>
        </div>

        <div class="grid">
            <div class="test-section">
                <h2>🔑 Token管理</h2>
                <div class="form-group">
                    <label for="tokenInput">手动设置Token (可选):</label>
                    <input type="text" id="tokenInput" placeholder="输入Bearer token或留空使用存储的token">
                    <button onclick="setToken()">设置Token</button>
                    <button onclick="checkCurrentToken()">检查当前Token</button>
                </div>
                <div id="tokenStatus" class="result info" style="display: none;"></div>
            </div>

            <div class="test-section">
                <h2>📁 文件上传</h2>
                <div class="form-group">
                    <label for="sessionIdInput">Session ID:</label>
                    <input type="text" id="sessionIdInput" value="test-session-123">
                </div>
                <div class="form-group">
                    <label for="imageFile">选择身份证图片:</label>
                    <input type="file" id="imageFile" accept="image/*">
                </div>
                <div id="fileInfo" class="file-info" style="display: none;"></div>
            </div>
        </div>

        <div class="test-section">
            <h2>🚀 OCR测试</h2>
            <button onclick="testOCR()" id="testBtn">开始OCR识别</button>
            <button onclick="testOCRWithCustomToken()" id="customTestBtn">使用自定义Token测试</button>
            <button onclick="clearResults()">清除结果</button>
        </div>

        <div class="test-section">
            <h2>📊 测试结果</h2>
            <div id="requestDetails" class="result info" style="display: none;"></div>
            <div id="testResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080/api';
        let customToken = null;

        // 获取当前有效token
        function getCurrentToken() {
            if (customToken) {
                return customToken;
            }

            // 按优先级获取token
            try {
                const authDataLocal = localStorage.getItem('auth_data');
                if (authDataLocal) {
                    const authData = JSON.parse(authDataLocal);
                    if (authData.token) {
                        return authData.token;
                    }
                }
            } catch (e) {
                console.warn('解析localStorage auth_data失败:', e);
            }

            try {
                const authDataSession = sessionStorage.getItem('auth_data');
                if (authDataSession) {
                    const authData = JSON.parse(authDataSession);
                    if (authData.token) {
                        return authData.token;
                    }
                }
            } catch (e) {
                console.warn('解析sessionStorage auth_data失败:', e);
            }

            // 兼容旧存储
            return localStorage.getItem('auth_token') || 
                   localStorage.getItem('userToken') || 
                   null;
        }

        // 设置自定义token
        function setToken() {
            const tokenInput = document.getElementById('tokenInput');
            const token = tokenInput.value.trim();
            
            if (token) {
                customToken = token;
                showTokenStatus(`✅ 已设置自定义Token\n\nToken预览: ${token.substring(0, 50)}...`, 'success');
            } else {
                customToken = null;
                showTokenStatus('ℹ️ 已清除自定义Token，将使用存储的Token', 'info');
            }
        }

        // 检查当前token状态
        function checkCurrentToken() {
            const token = getCurrentToken();
            
            if (token) {
                const tokenSource = customToken ? '自定义Token' : '存储Token';
                showTokenStatus(`✅ 找到有效Token (${tokenSource})\n\nToken预览: ${token.substring(0, 50)}...\n\n完整Token:\n${token}`, 'success');
            } else {
                showTokenStatus('❌ 未找到有效Token\n\n请先登录或手动设置Token', 'error');
            }
        }

        // 显示token状态
        function showTokenStatus(message, type) {
            const statusDiv = document.getElementById('tokenStatus');
            statusDiv.textContent = message;
            statusDiv.className = `result ${type}`;
            statusDiv.style.display = 'block';
        }

        // 监听文件选择
        document.getElementById('imageFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const fileInfoDiv = document.getElementById('fileInfo');
            
            if (file) {
                fileInfoDiv.innerHTML = `
                    <strong>文件信息:</strong><br>
                    名称: ${file.name}<br>
                    大小: ${(file.size / 1024 / 1024).toFixed(2)} MB<br>
                    类型: ${file.type}<br>
                    最后修改: ${new Date(file.lastModified).toLocaleString()}
                `;
                fileInfoDiv.style.display = 'block';
            } else {
                fileInfoDiv.style.display = 'none';
            }
        });

        // 显示请求详情
        function showRequestDetails(token, sessionId, file) {
            const detailsDiv = document.getElementById('requestDetails');
            detailsDiv.textContent = `📋 请求详情:

URL: ${API_BASE_URL}/auth/ocr
Method: POST
Content-Type: multipart/form-data

Headers:
Authorization: Bearer ${token.substring(0, 50)}...

Form Data:
session_id: ${sessionId}
image: ${file.name} (${(file.size / 1024).toFixed(2)} KB)

发送时间: ${new Date().toLocaleString()}`;
            detailsDiv.style.display = 'block';
        }

        // 显示测试结果
        function showTestResult(result, type) {
            const resultDiv = document.getElementById('testResult');
            resultDiv.textContent = result;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }

        // OCR测试主函数
        async function performOCR(useCustomToken = false) {
            const sessionId = document.getElementById('sessionIdInput').value.trim();
            const fileInput = document.getElementById('imageFile');
            const file = fileInput.files[0];
            const testBtn = document.getElementById('testBtn');
            const customTestBtn = document.getElementById('customTestBtn');

            // 验证输入
            if (!sessionId) {
                showTestResult('❌ 请输入Session ID', 'error');
                return;
            }

            if (!file) {
                showTestResult('❌ 请选择身份证图片', 'error');
                return;
            }

            const token = getCurrentToken();
            if (!token) {
                showTestResult('❌ 未找到有效Token，请先登录或手动设置Token', 'error');
                return;
            }

            // 禁用按钮
            testBtn.disabled = true;
            customTestBtn.disabled = true;
            testBtn.textContent = '🔄 OCR识别中...';

            try {
                // 显示请求详情
                showRequestDetails(token, sessionId, file);

                console.log('🔍 开始OCR测试:', {
                    sessionId,
                    fileName: file.name,
                    fileSize: file.size,
                    fileType: file.type,
                    tokenPreview: token.substring(0, 20) + '...'
                });

                // 创建FormData
                const formData = new FormData();
                formData.append('session_id', sessionId);
                formData.append('image', file);

                // 发送请求
                const response = await fetch(`${API_BASE_URL}/auth/ocr`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });

                console.log('📡 响应状态:', response.status, response.statusText);

                // 处理响应
                if (!response.ok) {
                    let errorMessage = `请求失败 (${response.status})`;
                    try {
                        const errorResult = await response.json();
                        errorMessage = errorResult.msg || errorResult.message || errorMessage;
                        console.error('❌ 错误详情:', errorResult);
                        
                        showTestResult(`❌ OCR识别失败\n\n状态码: ${response.status}\n错误信息: ${errorMessage}\n\n完整错误响应:\n${JSON.stringify(errorResult, null, 2)}`, 'error');
                    } catch (e) {
                        showTestResult(`❌ OCR识别失败\n\n状态码: ${response.status}\n错误信息: ${errorMessage}\n\n无法解析错误响应`, 'error');
                    }
                    return;
                }

                const result = await response.json();
                console.log('📦 OCR识别结果:', result);

                // 显示成功结果
                let resultText = `✅ OCR识别成功!\n\n`;
                
                if (result.data) {
                    if (result.data.name || result.data.ocrName) {
                        resultText += `姓名: ${result.data.name || result.data.ocrName}\n`;
                    }
                    if (result.data.idcard || result.data.ocrIdNumber) {
                        resultText += `身份证号: ${result.data.idcard || result.data.ocrIdNumber}\n`;
                    }
                }

                resultText += `\n完整响应:\n${JSON.stringify(result, null, 2)}`;
                
                showTestResult(resultText, 'success');

            } catch (error) {
                console.error('❌ OCR请求异常:', error);
                showTestResult(`❌ OCR请求异常\n\n错误信息: ${error.message}\n\n请检查:\n1. 网络连接是否正常\n2. 后端服务是否启动\n3. Token是否有效`, 'error');
            } finally {
                // 恢复按钮状态
                testBtn.disabled = false;
                customTestBtn.disabled = false;
                testBtn.textContent = '开始OCR识别';
            }
        }

        // 普通OCR测试
        function testOCR() {
            performOCR(false);
        }

        // 使用自定义Token的OCR测试
        function testOCRWithCustomToken() {
            if (!customToken) {
                showTestResult('❌ 请先设置自定义Token', 'error');
                return;
            }
            performOCR(true);
        }

        // 清除结果
        function clearResults() {
            document.getElementById('requestDetails').style.display = 'none';
            document.getElementById('testResult').style.display = 'none';
            document.getElementById('tokenStatus').style.display = 'none';
        }

        // 页面加载时检查token状态
        window.addEventListener('load', function() {
            checkCurrentToken();
        });
    </script>
</body>
</html>
