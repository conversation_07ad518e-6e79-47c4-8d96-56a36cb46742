package com.hfut.xiaozu.house.information;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【community_info(小区信息表)】的数据库操作Mapper
* @createDate 2025-06-25 21:44:49
*/
@Mapper
public interface CommunityInfoMapper {

    int insert(CommunityInfo record);

    CommunityInfo selectById(Long id);

    List<CommunityInfo> list();

    int update(CommunityInfo record);

    List<Long> getIdByName(String communityName);

    Long getIdByHouseId(Long houseId);
}
