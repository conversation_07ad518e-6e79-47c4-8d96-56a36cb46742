<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hfut.xiaozu.incident.process.IncidentProcessRecordMapper">

    <resultMap id="BaseResultMap" type="com.hfut.xiaozu.incident.process.IncidentProcessRecordEntity">
            <id property="id" column="id" />
            <result property="incidentId" column="incident_id" />
            <result property="status" column="status" />
            <result property="userId" column="user_id" />
            <result property="userRole" column="user_role" />
            <result property="remark" column="remark" />
            <result property="createTime" column="create_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,incident_id,status,user_id,user_role,remark,
        create_time
    </sql>

    <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from incident_process_record
        where  id = #{id}
    </select>


    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.hfut.xiaozu.incident.process.IncidentProcessRecordEntity" useGeneratedKeys="true">
        insert into incident_process_record
        ( incident_id,status,user_id,user_role,remark)
        values (#{incidentId},#{status},#{userId},#{userRole},#{remark})
    </insert>


    <update id="updateById" parameterType="com.hfut.xiaozu.incident.process.IncidentProcessRecordEntity">
        update incident_process_record
        <set>
                <if test="incidentId != null">
                    incident_id = #{incidentId},
                </if>
                <if test="status != null">
                    status = #{status},
                </if>
                <if test="userId != null">
                    user_id = #{userId},
                </if>
                <if test="userRole != null">
                    user_role = #{userRole},
                </if>
                <if test="remark != null">
                    remark = #{remark},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime},
                </if>
        </set>
        where   id = #{id}
    </update>

    <select id="listByIncidentId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from incident_process_record
        where incident_id = #{incidentId}
        order by create_time asc
    </select>

</mapper>
