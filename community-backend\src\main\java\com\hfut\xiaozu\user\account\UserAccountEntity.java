package com.hfut.xiaozu.user.account;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-06-23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserAccountEntity {

    private Long id;

    private String userName;

    private String phone;

    private String password;

    private String realName;

    private String idCard;

    private String avatarUrl;

    /**
     * 是否实名认证（0-否 1-是）
     */
    private Integer isVerified;

    /**
     * 是否删除（0-否 1-是）
     */
    private Integer isDeleted;

    /**
     * 用户类型（1-居民 2-网格员 3-社区管理员）
     */
    private Integer userType;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}
