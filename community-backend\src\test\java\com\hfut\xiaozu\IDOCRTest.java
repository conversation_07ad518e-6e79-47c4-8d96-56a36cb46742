package com.hfut.xiaozu;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hfut.xiaozu.common.ocr.IDOCRConfig;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.net.URLEncoder;

/**
 * <AUTHOR>
 * @date 2025-06-24
 */
@SpringBootTest
@Slf4j
public class IDOCRTest {

    @Resource
    IDOCRConfig idocrConfig;

    @Test
    public void getkey(){
//        System.out.println("test");
        log.error(idocrConfig.getApiKey());
        log.error(idocrConfig.getSecretKey());
        log.error(idocrConfig.getAccessToken());
    }

    //获取OCR识别的access_token，申请一次有效期30天
    @Test
    public void getAccessToken(){
        String url = "https://aip.baidubce.com/oauth/2.0/token?client_id="+ idocrConfig.getApiKey()
                +"&client_secret="+ idocrConfig.getSecretKey()
                +"&grant_type=client_credentials";

        HttpResponse response = HttpRequest.post(url)
                .header("Content-Type", "application/json")
                .header("Accept", "application/json")
                .body("")
                .execute();

        System.out.println(response.body());
    }

    @Test
    public void  idcardLocalPic() {
        try {
            ClassPathResource resource = new ClassPathResource("/pic/ocrTest01Front.jpg");

            String imagePath = resource.getAbsolutePath();

            System.out.println(imagePath);

            byte[] fileBytes = ResourceUtil.readBytes(imagePath);

            String base64Data = Base64.encode(fileBytes);

            // 注意这里仅为了简化编码每一次请求都去获取access_token，线上环境access_token有过期时间， 客户端可自行缓存，过期后重新获取。
            String accessToken = idocrConfig.getAccessToken();
            String fullUrl = "https://aip.baidubce.com/rest/2.0/ocr/v1/idcard?access_token=" + accessToken;

            String bodyParams = "id_card_side=front&image=" + URLEncoder.encode(base64Data, "UTF-8");

            // 4. 发送POST请求
            HttpResponse response = HttpRequest.post(fullUrl)
                    .contentType("application/x-www-form-urlencoded")
                    .body(bodyParams)
                    .timeout(5000)
                    .execute();

            if (response.isOk()) {
                System.out.println(response.body());
            } else {
                throw new RuntimeException("OCR请求失败：状态码=" + response.getStatus());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void IdCardInfoExtractor(){
        String jsonStr ="{\"words_result\":{\"姓名\":{\"words\":\"天童爱丽丝\",\"location\":{\"top\":59,\"left\":101,\"width\":113,\"height\":25}},\"民族\":{\"words\":\"\",\"location\":{\"top\":0,\"left\":0,\"width\":0,\"height\":0}},\"住址\":{\"words\":\"沃托斯市千禧年科技学院研究学习区游戏开发部\",\"location\":{\"top\":190,\"left\":99,\"width\":209,\"height\":53}},\"公民身份号码\":{\"words\":\"110381202103250420\",\"location\":{\"top\":301,\"left\":173,\"width\":330,\"height\":22}},\"出生\":{\"words\":\"20210325\",\"location\":{\"top\":148,\"left\":102,\"width\":204,\"height\":20}},\"性别\":{\"words\":\"女\",\"location\":{\"top\":105,\"left\":99,\"width\":18,\"height\":22}}},\"words_result_num\":6,\"idcard_number_type\":1,\"image_status\":\"normal\",\"log_id\":1937529571478615041}\n";

        JSONObject json = JSONUtil.parseObj(jsonStr);
        JSONObject wordsResult = json.getJSONObject("words_result");

        String name = wordsResult.getJSONObject("姓名").getStr("words");

        String idNumber = wordsResult.getJSONObject("公民身份号码").getStr("words");

        System.out.println("姓名: " + name);
        System.out.println("身份证号: " + idNumber);
    }

    @Test
    public void  idcardFromUrl() {
        try {
            String url="https://community233.oss-cn-nanjing.aliyuncs.com/images/20250624225425877.jpg";
            String accessToken = idocrConfig.getAccessToken();
            String fullUrl = "https://aip.baidubce.com/rest/2.0/ocr/v1/idcard?access_token=" + accessToken;

            String bodyParams = "id_card_side=front&url="+url;

            // 4. 发送POST请求
            HttpResponse response = HttpRequest.post(fullUrl)
                    .contentType("application/x-www-form-urlencoded")
                    .body(bodyParams)
                    .timeout(5000)
                    .execute();

            if (response.isOk()) {
                System.out.println(response.body());
            } else {
                throw new RuntimeException("OCR请求失败：状态码=" + response.getStatus());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
