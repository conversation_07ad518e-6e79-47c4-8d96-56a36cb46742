<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>投票问题诊断工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .section h3 {
            margin-top: 0;
            color: #555;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button.success {
            background: #28a745;
        }
        .button.danger {
            background: #dc3545;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .result.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .status-ok {
            color: #28a745;
            font-weight: bold;
        }
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
        .status-warning {
            color: #ffc107;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 投票问题诊断工具</h1>
        
        <!-- 基础检查 -->
        <div class="section">
            <h3>1. 基础环境检查</h3>
            <button class="button" onclick="checkBasicEnvironment()">检查基础环境</button>
            <div id="basicResult" class="result" style="display: none;"></div>
        </div>

        <!-- Token检查 -->
        <div class="section">
            <h3>2. 认证状态检查</h3>
            <button class="button" onclick="checkAuthStatus()">检查Token状态</button>
            <button class="button success" onclick="showTokenInfo()">显示Token信息</button>
            <div id="authResult" class="result" style="display: none;"></div>
        </div>

        <!-- API连接检查 -->
        <div class="section">
            <h3>3. API连接检查</h3>
            <button class="button" onclick="checkApiConnection()">测试API连接</button>
            <button class="button" onclick="testVoteListApi()">测试投票列表API</button>
            <div id="apiResult" class="result" style="display: none;"></div>
        </div>

        <!-- 投票数据检查 -->
        <div class="section">
            <h3>4. 投票数据检查</h3>
            <button class="button" onclick="checkVoteData()">检查投票数据结构</button>
            <button class="button" onclick="validateVoteOptions()">验证投票选项</button>
            <div id="voteDataResult" class="result" style="display: none;"></div>
        </div>

        <!-- 浏览器控制台检查 -->
        <div class="section">
            <h3>5. 浏览器控制台检查</h3>
            <button class="button" onclick="checkConsoleErrors()">检查控制台错误</button>
            <button class="button danger" onclick="clearConsole()">清除控制台</button>
            <div id="consoleResult" class="result" style="display: none;"></div>
        </div>

        <!-- 实时状态监控 -->
        <div class="section">
            <h3>6. 实时状态监控</h3>
            <div id="statusMonitor">
                <div class="status-item">
                    <span>页面加载状态:</span>
                    <span id="pageStatus" class="status-ok">正常</span>
                </div>
                <div class="status-item">
                    <span>Token状态:</span>
                    <span id="tokenStatus" class="status-warning">未检查</span>
                </div>
                <div class="status-item">
                    <span>API连接状态:</span>
                    <span id="apiStatus" class="status-warning">未检查</span>
                </div>
                <div class="status-item">
                    <span>投票数据状态:</span>
                    <span id="voteDataStatus" class="status-warning">未检查</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 显示结果的辅助函数
        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = content;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }

        // 更新状态的辅助函数
        function updateStatus(statusId, text, type = 'ok') {
            const element = document.getElementById(statusId);
            element.textContent = text;
            element.className = `status-${type}`;
        }

        // 1. 检查基础环境
        function checkBasicEnvironment() {
            const info = {
                userAgent: navigator.userAgent,
                url: window.location.href,
                localStorage: typeof(Storage) !== "undefined",
                fetch: typeof(fetch) !== "undefined",
                currentTime: new Date().toLocaleString()
            };
            
            showResult('basicResult', JSON.stringify(info, null, 2), 'info');
        }

        // 2. 检查认证状态
        function checkAuthStatus() {
            const token = localStorage.getItem('auth_token');
            let result = '';
            let type = 'error';
            
            if (token) {
                result = `Token存在: ${token.substring(0, 50)}...\n长度: ${token.length}`;
                type = 'success';
                updateStatus('tokenStatus', '有效', 'ok');
            } else {
                result = 'Token不存在，用户未登录';
                updateStatus('tokenStatus', '无效', 'error');
            }
            
            showResult('authResult', result, type);
        }

        // 显示Token详细信息
        function showTokenInfo() {
            const token = localStorage.getItem('auth_token');
            if (!token) {
                showResult('authResult', '没有找到Token', 'error');
                return;
            }

            try {
                // 尝试解析JWT token
                const parts = token.split('.');
                if (parts.length === 3) {
                    const payload = JSON.parse(atob(parts[1]));
                    const header = JSON.parse(atob(parts[0]));
                    
                    const info = {
                        header: header,
                        payload: payload,
                        tokenLength: token.length,
                        isExpired: payload.exp ? (Date.now() / 1000) > payload.exp : '无法确定'
                    };
                    
                    showResult('authResult', JSON.stringify(info, null, 2), 'info');
                } else {
                    showResult('authResult', `Token格式异常，不是标准JWT格式\nToken: ${token}`, 'error');
                }
            } catch (error) {
                showResult('authResult', `Token解析失败: ${error.message}\nToken: ${token}`, 'error');
            }
        }

        // 3. 检查API连接
        async function checkApiConnection() {
            const baseUrl = 'http://localhost:8080/api';
            
            try {
                const response = await fetch(`${baseUrl}/vote/list`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = {
                    status: response.status,
                    statusText: response.statusText,
                    ok: response.ok,
                    url: response.url
                };
                
                if (response.ok || response.status === 401) {
                    showResult('apiResult', `API连接正常\n${JSON.stringify(result, null, 2)}`, 'success');
                    updateStatus('apiStatus', '正常', 'ok');
                } else {
                    showResult('apiResult', `API连接异常\n${JSON.stringify(result, null, 2)}`, 'error');
                    updateStatus('apiStatus', '异常', 'error');
                }
                
            } catch (error) {
                showResult('apiResult', `API连接失败: ${error.message}`, 'error');
                updateStatus('apiStatus', '失败', 'error');
            }
        }

        // 测试投票列表API
        async function testVoteListApi() {
            const token = localStorage.getItem('auth_token');
            if (!token) {
                showResult('apiResult', '没有Token，无法测试认证API', 'error');
                return;
            }

            try {
                const response = await fetch('http://localhost:8080/api/vote/list', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();
                const result = {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                };

                if (response.ok) {
                    showResult('apiResult', `投票列表API测试成功\n${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    showResult('apiResult', `投票列表API测试失败\n${JSON.stringify(result, null, 2)}`, 'error');
                }

            } catch (error) {
                showResult('apiResult', `投票列表API测试异常: ${error.message}`, 'error');
            }
        }

        // 4. 检查投票数据
        async function checkVoteData() {
            const token = localStorage.getItem('auth_token');
            if (!token) {
                showResult('voteDataResult', '没有Token，无法获取投票数据', 'error');
                return;
            }

            try {
                const response = await fetch('http://localhost:8080/api/vote/list', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                const votes = result.data || [];

                let analysis = `投票数据分析:\n`;
                analysis += `总投票数: ${votes.length}\n\n`;

                votes.forEach((vote, index) => {
                    analysis += `投票 ${index + 1}:\n`;
                    analysis += `  ID: ${vote.id || '缺失'}\n`;
                    analysis += `  标题: ${vote.title || '缺失'}\n`;
                    analysis += `  最大选择数: ${vote.maxChoices || '缺失'}\n`;
                    analysis += `  选项数量: ${vote.options?.length || 0}\n`;
                    
                    if (vote.options && vote.options.length > 0) {
                        analysis += `  选项详情:\n`;
                        vote.options.forEach((option, optIndex) => {
                            analysis += `    选项 ${optIndex + 1}: ID=${option.id || option.sortOrder || '缺失'}, 内容="${option.content || '缺失'}"\n`;
                        });
                    }
                    analysis += `\n`;
                });

                showResult('voteDataResult', analysis, 'info');
                updateStatus('voteDataStatus', votes.length > 0 ? '正常' : '无数据', votes.length > 0 ? 'ok' : 'warning');

            } catch (error) {
                showResult('voteDataResult', `获取投票数据失败: ${error.message}`, 'error');
                updateStatus('voteDataStatus', '失败', 'error');
            }
        }

        // 验证投票选项
        function validateVoteOptions() {
            // 这个函数需要在实际的投票页面中运行
            showResult('voteDataResult', '请在投票页面的控制台中运行此检查', 'info');
        }

        // 5. 检查控制台错误
        function checkConsoleErrors() {
            showResult('consoleResult', '请打开浏览器开发者工具的Console标签页查看错误信息', 'info');
        }

        // 清除控制台
        function clearConsole() {
            console.clear();
            showResult('consoleResult', '控制台已清除', 'success');
        }

        // 页面加载时自动检查
        window.onload = function() {
            updateStatus('pageStatus', '已加载', 'ok');
            checkAuthStatus();
        };
    </script>
</body>
</html>
