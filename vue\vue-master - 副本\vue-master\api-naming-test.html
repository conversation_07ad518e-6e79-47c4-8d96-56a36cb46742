<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API命名规范测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
            font-size: 2.5rem;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .test-section h3 {
            margin: 0 0 15px 0;
            color: #495057;
        }

        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .test-button:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }

        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }

        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }

        .form-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        .comparison-item {
            padding: 15px;
            border-radius: 8px;
        }

        .old-format {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
        }

        .new-format {
            background: #d4edda;
            border: 1px solid #c3e6cb;
        }

        .comparison-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 API命名规范测试</h1>
            <p>测试前端API请求是否正确使用驼峰命名法</p>
        </div>

        <!-- 注册测试 -->
        <div class="test-section">
            <h3>📝 注册API测试</h3>
            <div class="form-group">
                <label class="form-label">用户名</label>
                <input type="text" id="regUserName" class="form-input" placeholder="测试用户名" value="testUser123">
            </div>
            <div class="form-group">
                <label class="form-label">手机号</label>
                <input type="text" id="regPhone" class="form-input" placeholder="手机号" value="13800138000">
            </div>
            <div class="form-group">
                <label class="form-label">密码</label>
                <input type="password" id="regPassword" class="form-input" placeholder="密码" value="123456">
            </div>
            <div class="form-group">
                <label class="form-label">用户类型</label>
                <select id="regUserType" class="form-input">
                    <option value="1">居民</option>
                    <option value="2">网格员</option>
                    <option value="3">社区管理员</option>
                </select>
            </div>
            <button class="test-button" onclick="testRegisterAPI()">测试注册API (驼峰命名)</button>
            <button class="test-button" onclick="testRegisterAPISnakeCase()">测试注册API (下划线命名)</button>
            <div id="register-result" class="result" style="display: none;"></div>
        </div>

        <!-- 登录测试 -->
        <div class="test-section">
            <h3>🔐 登录API测试</h3>
            <div class="form-group">
                <label class="form-label">用户名</label>
                <input type="text" id="loginUserName" class="form-input" placeholder="用户名" value="testUser123">
            </div>
            <div class="form-group">
                <label class="form-label">密码</label>
                <input type="password" id="loginPassword" class="form-input" placeholder="密码" value="123456">
            </div>
            <button class="test-button" onclick="testLoginAPI()">测试登录API (驼峰命名)</button>
            <button class="test-button" onclick="testLoginAPISnakeCase()">测试登录API (下划线命名)</button>
            <div id="login-result" class="result" style="display: none;"></div>
        </div>

        <!-- 命名对比 -->
        <div class="test-section">
            <h3>📊 命名规范对比</h3>
            <div class="comparison">
                <div class="comparison-item old-format">
                    <div class="comparison-title">❌ 旧格式 (下划线命名)</div>
                    <pre>{
  "user_name": "testUser",
  "user_type": 1,
  "phone": "13800138000"
}</pre>
                </div>
                <div class="comparison-item new-format">
                    <div class="comparison-title">✅ 新格式 (驼峰命名)</div>
                    <pre>{
  "userName": "testUser",
  "userType": 1,
  "phone": "13800138000"
}</pre>
                </div>
            </div>
        </div>

        <!-- 测试结果总结 -->
        <div class="test-section">
            <h3>📈 测试结果总结</h3>
            <div id="summary-result" class="result info">
                点击上方按钮开始测试...
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080';
        let testResults = [];

        // 显示结果
        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${type}`;
            element.textContent = content;
        }

        // 添加测试结果
        function addTestResult(testName, success, details) {
            testResults.push({
                name: testName,
                success: success,
                details: details,
                timestamp: new Date().toLocaleString()
            });
            updateSummary();
        }

        // 更新总结
        function updateSummary() {
            const summary = document.getElementById('summary-result');
            const total = testResults.length;
            const passed = testResults.filter(r => r.success).length;
            const failed = total - passed;

            let summaryText = `测试总结:\n`;
            summaryText += `总计: ${total} 个测试\n`;
            summaryText += `成功: ${passed} 个\n`;
            summaryText += `失败: ${failed} 个\n\n`;

            testResults.forEach(result => {
                summaryText += `${result.success ? '✅' : '❌'} ${result.name}\n`;
                summaryText += `   时间: ${result.timestamp}\n`;
                if (result.details) {
                    summaryText += `   详情: ${result.details}\n`;
                }
                summaryText += '\n';
            });

            summary.textContent = summaryText;
        }

        // 测试注册API (驼峰命名)
        async function testRegisterAPI() {
            try {
                const userData = {
                    userName: document.getElementById('regUserName').value,
                    phone: document.getElementById('regPhone').value,
                    password: document.getElementById('regPassword').value,
                    userType: parseInt(document.getElementById('regUserType').value)
                };

                console.log('🚀 发送注册请求 (驼峰命名):', userData);

                const response = await fetch(`${API_BASE_URL}/api/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(userData)
                });

                const result = await response.json();
                
                const resultText = `请求数据 (驼峰命名):\n${JSON.stringify(userData, null, 2)}\n\n响应结果:\n${JSON.stringify(result, null, 2)}`;
                
                if (response.ok && result.code === 200) {
                    showResult('register-result', resultText, 'success');
                    addTestResult('注册API (驼峰命名)', true, '请求成功');
                } else {
                    showResult('register-result', resultText, 'error');
                    addTestResult('注册API (驼峰命名)', false, result.msg || '请求失败');
                }
            } catch (error) {
                const errorText = `注册API测试失败 (驼峰命名):\n${error.message}`;
                showResult('register-result', errorText, 'error');
                addTestResult('注册API (驼峰命名)', false, error.message);
            }
        }

        // 测试注册API (下划线命名)
        async function testRegisterAPISnakeCase() {
            try {
                const userData = {
                    user_name: document.getElementById('regUserName').value,
                    phone: document.getElementById('regPhone').value,
                    password: document.getElementById('regPassword').value,
                    user_type: parseInt(document.getElementById('regUserType').value)
                };

                console.log('🚀 发送注册请求 (下划线命名):', userData);

                const response = await fetch(`${API_BASE_URL}/api/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(userData)
                });

                const result = await response.json();
                
                const resultText = `请求数据 (下划线命名):\n${JSON.stringify(userData, null, 2)}\n\n响应结果:\n${JSON.stringify(result, null, 2)}`;
                
                if (response.ok && result.code === 200) {
                    showResult('register-result', resultText, 'success');
                    addTestResult('注册API (下划线命名)', true, '请求成功');
                } else {
                    showResult('register-result', resultText, 'error');
                    addTestResult('注册API (下划线命名)', false, result.msg || '请求失败');
                }
            } catch (error) {
                const errorText = `注册API测试失败 (下划线命名):\n${error.message}`;
                showResult('register-result', errorText, 'error');
                addTestResult('注册API (下划线命名)', false, error.message);
            }
        }

        // 测试登录API (驼峰命名)
        async function testLoginAPI() {
            try {
                const loginData = {
                    userName: document.getElementById('loginUserName').value,
                    password: document.getElementById('loginPassword').value
                };

                console.log('🔐 发送登录请求 (驼峰命名):', loginData);

                const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(loginData)
                });

                const result = await response.json();
                
                const resultText = `请求数据 (驼峰命名):\n${JSON.stringify(loginData, null, 2)}\n\n响应结果:\n${JSON.stringify(result, null, 2)}`;
                
                if (response.ok && result.code === 200) {
                    showResult('login-result', resultText, 'success');
                    addTestResult('登录API (驼峰命名)', true, '请求成功');
                } else {
                    showResult('login-result', resultText, 'error');
                    addTestResult('登录API (驼峰命名)', false, result.msg || '请求失败');
                }
            } catch (error) {
                const errorText = `登录API测试失败 (驼峰命名):\n${error.message}`;
                showResult('login-result', errorText, 'error');
                addTestResult('登录API (驼峰命名)', false, error.message);
            }
        }

        // 测试登录API (下划线命名)
        async function testLoginAPISnakeCase() {
            try {
                const loginData = {
                    user_name: document.getElementById('loginUserName').value,
                    password: document.getElementById('loginPassword').value
                };

                console.log('🔐 发送登录请求 (下划线命名):', loginData);

                const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(loginData)
                });

                const result = await response.json();
                
                const resultText = `请求数据 (下划线命名):\n${JSON.stringify(loginData, null, 2)}\n\n响应结果:\n${JSON.stringify(result, null, 2)}`;
                
                if (response.ok && result.code === 200) {
                    showResult('login-result', resultText, 'success');
                    addTestResult('登录API (下划线命名)', true, '请求成功');
                } else {
                    showResult('login-result', resultText, 'error');
                    addTestResult('登录API (下划线命名)', false, result.msg || '请求失败');
                }
            } catch (error) {
                const errorText = `登录API测试失败 (下划线命名):\n${error.message}`;
                showResult('login-result', errorText, 'error');
                addTestResult('登录API (下划线命名)', false, error.message);
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('API命名规范测试工具已加载');
            
            // 生成随机测试数据
            const timestamp = Date.now().toString().slice(-6);
            document.getElementById('regUserName').value = `testUser${timestamp}`;
            document.getElementById('regPhone').value = `138${timestamp.padStart(8, '0')}`;
            document.getElementById('loginUserName').value = `testUser${timestamp}`;
        });
    </script>
</body>
</html>
