# OCR测试功能使用指南

## 功能概述

在前端居民端的实名认证功能页面中，新增了一个测试OCR功能，允许用户上传身份证照片并测试OCR识别效果。

## 功能位置

- 页面路径：`/resident/real-name-auth`
- 功能位置：身份证照片上传区域下方

## 使用步骤

1. **访问实名认证页面**
   - 登录系统后，导航到居民端首页
   - 点击"实名认证"功能模块

2. **上传身份证照片**
   - 在"身份证照片"区域点击上传
   - 选择身份证正面照片（支持JPG、PNG格式，不超过5MB）
   - 照片上传成功后会显示预览

3. **测试OCR功能**
   - 照片上传成功后，会在上传区域下方显示"🔍 测试OCR识别"按钮
   - 点击按钮开始OCR识别测试
   - 识别过程中按钮会显示"🔄 OCR识别中..."

4. **查看识别结果**
   - OCR识别完成后，会在页面下方显示"OCR识别结果"区域
   - 结果以JSON格式显示，包含完整的后端返回数据
   - 如果识别成功，会自动填充姓名和身份证号字段

## 技术实现

### 前端实现

1. **新增响应式数据**
   ```javascript
   const uploadedFile = ref(null); // 存储原始文件对象
   const isOCRTesting = ref(false); // OCR测试状态
   const ocrTestResult = ref(null); // OCR测试结果
   ```

2. **新增API方法**
   ```javascript
   // 在 identityApi.js 中新增
   ocrIdentityCardFile: async (file, sessionId) => {
     const formData = new FormData();
     formData.append('image', file);
     formData.append('session_id', sessionId);
     
     return await apiRequest(`${API_BASE_URL}/auth/ocr`, {
       method: 'POST',
       headers: {
         'Authorization': `Bearer ${getAuthToken()}`
       },
       body: formData
     });
   }
   ```

3. **OCR测试函数**
   - 验证文件是否已上传
   - 生成测试会话ID
   - 调用后端OCR接口
   - 显示完整返回结果
   - 自动填充识别到的信息

### 后端接口

- **接口路径**: `/api/auth/ocr`
- **请求方式**: POST
- **参数格式**: multipart/form-data
- **请求参数**:
  - `session_id`: 认证会话ID
  - `image`: 身份证图片文件

## 返回结果示例

成功识别时的返回格式：
```json
{
  "code": 200,
  "msg": "识别成功",
  "data": {
    "ocrIdNumber": "识别到的身份证号",
    "ocrName": "识别到的姓名"
  }
}
```

失败时的返回格式：
```json
{
  "code": 400,
  "msg": "识别失败原因",
  "data": null
}
```

## 注意事项

1. **文件要求**
   - 支持JPG、PNG格式
   - 文件大小不超过5MB
   - 建议使用清晰的身份证正面照片

2. **网络要求**
   - 需要稳定的网络连接
   - OCR识别可能需要几秒钟时间

3. **权限要求**
   - 需要登录系统
   - 仅居民用户可访问

4. **测试目的**
   - 此功能主要用于测试OCR识别效果
   - 可以验证后端OCR服务是否正常工作
   - 帮助用户了解OCR识别的准确性

## 故障排除

1. **按钮不显示**
   - 确保已上传身份证照片
   - 检查文件是否符合格式要求

2. **识别失败**
   - 检查网络连接
   - 确保照片清晰度足够
   - 查看控制台错误信息

3. **结果不准确**
   - 尝试使用更清晰的照片
   - 确保身份证信息完整可见
   - 避免反光或阴影

## 开发说明

此功能完全独立于正式的实名认证流程，仅用于测试目的。用户可以：
- 多次测试不同的照片
- 查看详细的识别结果
- 验证OCR服务的可用性

测试结果会实时显示在页面上，方便开发者和用户了解OCR识别的效果。
