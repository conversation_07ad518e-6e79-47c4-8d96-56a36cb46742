package com.hfut.xiaozu.car;

import cn.hutool.core.util.IdcardUtil;
import com.hfut.xiaozu.car.bind.UpdateVehicleHouseBindingVO;
import com.hfut.xiaozu.car.bind.VehicleHouseBindingDTO;
import com.hfut.xiaozu.car.info.VehicleDTO;
import com.hfut.xiaozu.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-06-27
 */
@Tag(name =" 车辆管理")
@RestController
@RequestMapping("/api/car")
public class CarController {

    @Resource
    private CarService carService;

    @Operation(summary = "居民新增车辆")
    @PostMapping("/add")
    public Result<?> addNewCar(@RequestBody VehicleDTO dto){
        return carService.addNewCar(dto);
    }

    @Operation(summary = "居民查询自身车辆")
    @GetMapping("/list/me")
    public Result<?> listMyCar(){
        return carService.listMyCar();
    }

    @Operation(summary = "居民查询名下一辆车辆绑定记录状态")
    @GetMapping("/binding/{carId}")
    public Result<?> getBindRecord(@PathVariable Long carId){
        return carService.getMyBindRecordByCarId(carId);
    }

    @Operation(summary = "居民申请将名下车辆绑定房屋(支持一次性绑定多个房屋)")
    @PostMapping("/binding")
    public Result<?> bindingHouse(@RequestBody VehicleHouseBindingDTO dto, BindingResult bindingResult){

        List<String> errors = new ArrayList<>();

        if (bindingResult.hasErrors()) {
            errors.addAll(bindingResult.getAllErrors()
                    .stream()
                    .map(DefaultMessageSourceResolvable::getDefaultMessage)
                    .collect(Collectors.toList()));
        }

        if (!errors.isEmpty()) {
            return Result.fail(errors.toString());
        }

        return carService.bindingHouse(dto);
    }

    @Operation(summary = "物业查询特定状态的车辆-房屋绑定记录")
    @GetMapping("/binding/list/{status}")
    public Result<?> listBingingRecord(@PathVariable Integer status,
                                        @RequestParam(defaultValue = "1") Integer pageNum,
                                       @RequestParam(defaultValue = "10") Integer pageSize
    ){

        if (status == null || (status < 1 && status > 4)) {
            return Result.fail("状态值无效");
        }

        return carService.listBingingRecordByStatus(status,pageNum,pageSize);
    }

    @Operation(summary = "物业更新单个车辆-房屋绑定记录")
    @PutMapping("/binding/update/{id}")
    public Result<?> listBingingRecord(@PathVariable Long id, @RequestBody UpdateVehicleHouseBindingVO vo){
        if(vo.getStatus()==null||!(vo.getStatus()>=2&&vo.getStatus()<=4)){
            return Result.fail("状态值异常");
        }
        return carService.updateBingingRecord(id,vo.getStatus(),vo.getApprovalRemark());
    }
}
