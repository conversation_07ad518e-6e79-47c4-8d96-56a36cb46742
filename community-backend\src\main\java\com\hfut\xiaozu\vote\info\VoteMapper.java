package com.hfut.xiaozu.vote.info;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【vote(投票主表)】的数据库操作Mapper
* @createDate 2025-07-01 10:19:42
* @Entity com.hfut.xiaozu.vote.Vote
*/
@Mapper
public interface VoteMapper {

    int deleteById(Long id);

    int insert(VoteEntity record);

    VoteEntity getById(Long id);

    int updateById(VoteEntity record);

    List<VoteEntity> listAll();
}
