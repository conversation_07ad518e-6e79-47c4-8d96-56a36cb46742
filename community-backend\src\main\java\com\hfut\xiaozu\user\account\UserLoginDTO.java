package com.hfut.xiaozu.user.account;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-06-23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserLoginDTO {

    @NotBlank(message = "用户名不能为空")
    @Size(min=2 ,max=50,message = "用户名长度需在2-50个字符之间")
    private String userName;

    @NotBlank(message = "密码不能为空")
    @Size(min=6 ,max=200,message = "密码长度需在6-200个字符之间")
    private String password;

    // 用户类型（1-居民 2-网格员 3-社区管理员）
    private Integer userType;
}
