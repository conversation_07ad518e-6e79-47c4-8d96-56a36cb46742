package com.hfut.xiaozu.incident;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hfut.xiaozu.common.Result;
import com.hfut.xiaozu.house.grid.CreateGridDTO;
import com.hfut.xiaozu.user.account.UserAccountEntity;
import org.locationtech.jts.geom.*;
import com.hfut.xiaozu.house.binding.UserHouseBinding;
import com.hfut.xiaozu.house.binding.UserHouseBindingMapper;
import com.hfut.xiaozu.house.grid.CommunityGridEntity;
import com.hfut.xiaozu.house.grid.CommunityGridMapper;
import com.hfut.xiaozu.house.information.HouseInfoMapper;
import com.hfut.xiaozu.incident.process.IncidentProcessRecordEntity;
import com.hfut.xiaozu.incident.process.IncidentProcessRecordMapper;
import com.hfut.xiaozu.incident.record.IncidentDetailVO;
import com.hfut.xiaozu.incident.record.IncidentRecordEntity;
import com.hfut.xiaozu.incident.record.IncidentRecordMapper;
import com.hfut.xiaozu.incident.record.ReportIncidentDTO;
import com.hfut.xiaozu.user.account.UserMapper;
import com.hfut.xiaozu.user.context.CurrentUser;
import com.hfut.xiaozu.user.context.UserContextHolder;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.stereotype.Service;

import java.lang.annotation.Retention;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025-06-29
 */
@Service
public class incidentService {

    @Resource
    private IncidentRecordMapper recordMapper;

    @Resource
    private IncidentProcessRecordMapper processRecordMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private UserHouseBindingMapper userHouseBindingMapper;

    @Resource
    private CommunityGridMapper communityGridMapper;

    @Resource
    private HouseInfoMapper houseInfoMapper;

    public Result<?> reportIncident(@Valid ReportIncidentDTO dto) {
        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        if(currentUser == null || Objects.equals(currentUser.getRole(),CurrentUser.Administrator)){
            return Result.fail("只有居民或者网格员才能上报事件");
        }

        if(dto.getCommunityId()==null){
            if(Objects.equals(currentUser.getRole(),CurrentUser.GridWorker)){

                CommunityGridEntity grid=communityGridMapper.getByResponsibleId(currentUser.getUserId());
                if(grid==null){
                    return Result.fail("未选择社区，且上报人为网格员但未分配网格");
                }
                dto.setCommunityId(grid.getCommunityId());
            }else{
                List<UserHouseBinding> houses = userHouseBindingMapper.getByUserId(currentUser.getUserId());
                Set<Long> houseIds = new HashSet<>();

                for (UserHouseBinding house : houses) {
                    if(Objects.equals(house.getStatus(),2)){
                        houseIds.add(house.getHouseId());
                    }
                }
                if(houseIds.isEmpty()){
                    return Result.fail("未选择社区，且上报人为居民，未成功绑定任何房屋");
                }

                // 根据房屋ID查询对应的社区ID
                Set<Long> communityIds = new HashSet<>();
                for (Long houseId : houseIds) {
                    Long communityId = houseInfoMapper.getCommunityIdByHouseId(houseId);
                    if (communityId != null) {
                        communityIds.add(communityId);
                    }
                }

                if (communityIds.isEmpty()) {
                    return Result.fail("无法确定社区信息，请联系管理员");
                }

                if (communityIds.size() > 1) {
                    return Result.fail("您绑定的房屋属于多个社区，请明确指定社区");
                }

                // 设置唯一的社区ID
                dto.setCommunityId(communityIds.iterator().next());
            }

        }

        IncidentRecordEntity record = new IncidentRecordEntity();

        record.setTitle(dto.getTitle());
        record.setDescription(dto.getDescription());
        record.setCategoryType(dto.getCategoryType());
        record.setPriority(dto.getPriority());
        record.setLocationDescription(dto.getLocationDescription());
        record.setLocationGeojson(JSONUtil.toJsonStr(dto.getLocationGeojson()));
        record.setCommunityId(dto.getCommunityId());

        record.setReporterId(currentUser.getUserId());
        record.setStatus(1); // 1-待分派

        int result = recordMapper.insert(record);

        IncidentProcessRecordEntity processRecord = new IncidentProcessRecordEntity();
        processRecord.setIncidentId(record.getId());
        processRecord.setStatus(1);
        processRecord.setUserId(currentUser.getUserId());
        processRecord.setUserRole(currentUser.getRole());
        processRecord.setRemark("事件上报");

        processRecordMapper.insert(processRecord);
        if (result > 0) {
            return Result.ok("事件上报成功");
        }
        return Result.fail("事件上报失败");
    }

    /**
     * 按状态查询事件列表
     * 管理员：查看所有事件
     * 居民：只能查看自己上报的事件
     * 网格员：只能查看自己上报的和被分配的事件
     */
    public Result<?> listIncidentByStatus(Integer status) {
        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        if (currentUser == null) {
            return Result.fail("用户未登录");
        }

        List<IncidentRecordEntity> incidents;

        // 根据用户角色查询不同的事件
        if (Objects.equals(currentUser.getRole(), CurrentUser.Administrator)) {
            // 管理员可以查看所有事件
            incidents = recordMapper.listByStatus(status);
        } else if (Objects.equals(currentUser.getRole(), CurrentUser.Resident)) {
            // 居民只能查看自己上报的事件
            incidents = recordMapper.listByStatusAndReporter(status, currentUser.getUserId());
        } else if (Objects.equals(currentUser.getRole(), CurrentUser.GridWorker)) {
            // 网格员可以查看自己上报的和被分配的事件
            incidents = recordMapper.listByStatusForGridWorker(status, currentUser.getUserId());
        } else {
            return Result.fail("无效的用户角色");
        }

        if (CollectionUtil.isEmpty(incidents)) {
            return Result.ok("暂无相关事件记录");
        }

        return Result.ok(incidents);
    }

    /**
     * 查询所有事件列表（不限制状态）
     * 管理员：查看所有事件
     * 居民：只能查看自己上报的事件
     * 网格员：只能查看自己上报的和被分配的事件
     */
    public Result<?> listAllIncidents() {
        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        if (currentUser == null) {
            return Result.fail("用户未登录");
        }

        List<IncidentRecordEntity> incidents;

        // 根据用户角色查询不同的事件
        if (Objects.equals(currentUser.getRole(), CurrentUser.Administrator)) {
            // 管理员可以查看所有事件
            incidents = recordMapper.listAll();
        } else if (Objects.equals(currentUser.getRole(), CurrentUser.Resident)) {
            // 居民只能查看自己上报的事件
            incidents = recordMapper.listByReporter(currentUser.getUserId());
        } else if (Objects.equals(currentUser.getRole(), CurrentUser.GridWorker)) {
            // 网格员可以查看自己上报的和被分配的事件
            incidents = recordMapper.listForGridWorker(currentUser.getUserId());
        } else {
            return Result.fail("无效的用户角色");
        }

        if (CollectionUtil.isEmpty(incidents)) {
            return Result.ok("暂无相关事件记录");
        }

        return Result.ok(incidents);
    }

    /**
     * 根据事件ID查询事件详情
     * 管理员：可以查看任何事件详情
     * 居民：只能查看自己上报的事件详情
     * 网格员：只能查看自己上报的和被分配给自己处理的事件详情
     */
    public Result<?> getIncidentById(Long incidentId) {
        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        if (currentUser == null) {
            return Result.fail("用户未登录");
        }

        // 先查询事件是否存在
        IncidentRecordEntity incident = recordMapper.getById(incidentId);
        if (incident == null) {
            return Result.fail("事件不存在");
        }

        // 根据用户角色进行权限验证
        boolean hasPermission = false;

        if (Objects.equals(currentUser.getRole(), CurrentUser.Administrator)) {
            // 管理员可以查看任何事件
            hasPermission = true;
        } else if (Objects.equals(currentUser.getRole(), CurrentUser.Resident)) {
            // 居民只能查看自己上报的事件
            hasPermission = Objects.equals(incident.getReporterId(), currentUser.getUserId());
        } else if (Objects.equals(currentUser.getRole(), CurrentUser.GridWorker)) {
            // 网格员可以查看自己上报的和被分配给自己处理的事件
            hasPermission = Objects.equals(incident.getReporterId(), currentUser.getUserId())
                         || Objects.equals(incident.getHandlerId(), currentUser.getUserId());
        }

        if (!hasPermission) {
            return Result.fail("没有权限查看此事件详情");
        }

        return Result.ok(incident);
    }

    /**
     * 根据事件ID查询事件详细信息（包含处理记录）
     * 管理员：可以查看任何事件详情
     * 居民：只能查看自己上报的事件详情
     * 网格员：只能查看自己上报的和被分配给自己处理的事件详情
     */
    public Result<?> getIncidentDetailById(Long incidentId) {
        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        if (currentUser == null) {
            return Result.fail("用户未登录");
        }

        // 先查询事件是否存在
        IncidentRecordEntity incident = recordMapper.getById(incidentId);
        if (incident == null) {
            return Result.fail("事件不存在");
        }

        // 根据用户角色进行权限验证
        boolean hasPermission = false;

        if (Objects.equals(currentUser.getRole(), CurrentUser.Administrator)) {
            // 管理员可以查看任何事件
            hasPermission = true;
        } else if (Objects.equals(currentUser.getRole(), CurrentUser.Resident)) {
            // 居民只能查看自己上报的事件
            hasPermission = Objects.equals(incident.getReporterId(), currentUser.getUserId());
        } else if (Objects.equals(currentUser.getRole(), CurrentUser.GridWorker)) {
            // 网格员可以查看自己上报的和被分配给自己处理的事件
            hasPermission = Objects.equals(incident.getReporterId(), currentUser.getUserId())
                         || Objects.equals(incident.getHandlerId(), currentUser.getUserId());
        }

        if (!hasPermission) {
            return Result.fail("没有权限查看此事件详情");
        }

        // 转换为DetailVO对象，添加描述信息
        IncidentDetailVO incidentDetailVO = new IncidentDetailVO();
        BeanUtil.copyProperties(incident, incidentDetailVO);

        // 查询处理记录
        List<IncidentProcessRecordEntity> processRecords = processRecordMapper.listByIncidentId(incidentId);
        incidentDetailVO.setProcessRecords(processRecords);

        return Result.ok(incidentDetailVO);
    }

    /**
     * 根据事件的地理信息判断所属网格，推荐网格员
     * @param incidentId 事件记录id
     * @return 推荐的网格员ID，如果地理信息不合法或不在任何网格内则返回null
     */
    public Result<?> recommendedGridWorkerByIncidentId(Long incidentId) {
        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        if (currentUser == null || !Objects.equals(currentUser.getRole(), CurrentUser.Administrator)) {
            return Result.fail("用户未登录或者权限不足 ");
        }

        IncidentRecordEntity incident = recordMapper.getById(incidentId);
        if (incident == null) {
            return Result.fail("事件不存在");
        }

        // 检查事件的地理信息是否存在
        Object locationGeojson = incident.getLocationGeojson();
        if (locationGeojson == null) {
            return Result.fail("事件的地理信息不存在,所以无法推荐网格员");
        }

        // 解析地理信息JSON
        JSONObject geoJson = JSONUtil.parseObj(locationGeojson);

        // 经度
        Double lng = geoJson.getDouble("longitude");
        // 纬度
        Double lat = geoJson.getDouble("latitude");


        // 经纬度格式不合法
        if (lng == null || lat == null) {
            return Result.fail("事件的地理信息格式不合法,所以无法推荐网格员");
        }

        // 创建事件位置点
        GeometryFactory factory = new GeometryFactory();
        Point incidentPoint = factory.createPoint(new Coordinate(lng, lat));

        // 获取事件所属小区的所有网格
        List<CommunityGridEntity> grids;
        if (incident.getCommunityId() != null) {
            // 如果事件有明确的小区ID，只查询该小区的网格
            grids = communityGridMapper.listAll().stream()
                    .filter(grid -> Objects.equals(grid.getCommunityId(), incident.getCommunityId()))
                    .toList();
        } else {
            // 如果没有小区ID，查询所有网格
            grids = communityGridMapper.listAll();
        }

        //不存在合适的网格
        if (CollectionUtil.isEmpty(grids)) {
            return Result.fail("根据事件坐标无法推荐网格员，因为事件坐标不在任何网格内");
        }

        // 遍历网格，检查事件位置是否在网格范围内
        for (CommunityGridEntity grid : grids) {
            try {
                // 解析网格边界GeoJSON
                List<CreateGridDTO.point> points = JSONUtil.parseArray(grid.getBoundaryGeojson())
                        .toList(CreateGridDTO.point.class);

                if (points == null || points.size() < 3) {
                    continue;
                }

                // 构建闭合多边形坐标数组
                Coordinate[] polygonCoords = new Coordinate[points.size() + 1];

                for (int i = 0; i < points.size(); i++) {
                    polygonCoords[i]=new Coordinate(
                            points.get(i).getLongitude(),points.get(i).getLatitude());

                }
                polygonCoords[points.size()]=new Coordinate(
                        points.get(0).getLongitude(),points.get(0).getLatitude());


                // 创建多边形
                LinearRing ring = factory.createLinearRing(polygonCoords);
                Polygon polygon = factory.createPolygon(ring, null);

                // 检查事件位置是否在网格内
                if (polygon.contains(incidentPoint)&&grid.getResponsibleId()!=null) {
                    // 返回网格负责人ID
                    return Result.ok(grid.getResponsibleId());
                }

            } catch (Exception e) {
                // 如果解析某个网格失败，继续检查下一个网格
                continue;
            }
        }

        return Result.fail("根据事件坐标无法推荐网格员，因为事件坐标不在任何网格内");
    }

    public Result<?> assignIncidentById(Long incidentId, Long handlerId) {
        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        if (currentUser == null || !Objects.equals(currentUser.getRole(), CurrentUser.Administrator)) {
            return Result.fail("用户未登录或者权限不足 ");
        }

        IncidentRecordEntity incident = recordMapper.getById(incidentId);
        if (incident == null) {
            return Result.fail("事件不存在");
        }
        if(incident.getHandlerId()!=null){
            return Result.fail("事件已被分配");
        }

        UserAccountEntity user = userMapper.getById(handlerId);
        if(user==null ){
            return Result.fail("网格员不存在");
        }
        if(!Objects.equals(user.getUserType(),CurrentUser.GridWorker)){
            return Result.fail("分配人不是网格员");
        }

        //记录事件处理记录
        IncidentProcessRecordEntity record = new IncidentProcessRecordEntity();
        record.setIncidentId(incidentId);
        record.setStatus(2);
        record.setUserId(currentUser.getUserId());
        record.setUserRole(currentUser.getRole());
        record.setRemark("事件分派");

        processRecordMapper.insert(record);


        incident.setStatus(2);
         incident.setHandlerId(handlerId);
         incident.setAssignTime(LocalDateTime.now());
         recordMapper.updateById(incident);

         return Result.ok("分派成功");
    }

    public Result<?> completeIncidentById(Long incidentId) {
        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        if (currentUser == null || !Objects.equals(currentUser.getRole(), CurrentUser.GridWorker)) {
            return Result.fail("只有网格员才能完成任务");
        }

        IncidentRecordEntity incident = recordMapper.getById(incidentId);
        if (incident == null) {
            return Result.fail("事件不存在");
        }
        if(!Objects.equals(incident.getHandlerId(),currentUser.getUserId())){
            return Result.fail("你不是此事件的处理人");
        }
        if(!Objects.equals(incident.getStatus(),2)){
            return Result.fail("事件状态不是处理中，无法完成");
        }

        //记录事件处理记录
        IncidentProcessRecordEntity record = new IncidentProcessRecordEntity();
        record.setIncidentId(incidentId);
        record.setStatus(3);
        record.setUserId(currentUser.getUserId());
        record.setUserRole(currentUser.getRole());
        record.setRemark("事件完成");
        processRecordMapper.insert(record);

        incident.setStatus(3);
        incident.setCompleteTime(LocalDateTime.now());
        recordMapper.updateById(incident);

        return Result.ok("完成成功");
    }
}
