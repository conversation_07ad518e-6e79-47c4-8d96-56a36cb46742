package com.hfut.xiaozu.common.docs;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2025-06-22
 */
@Tag(name = "测试 Knife4j", description = "测试相关接口")
@RestController
@RequestMapping("/knife4j")
public class Knife4jController {

    @Operation(summary = "测试")
    @GetMapping("/test")
    public String test(){
        return "这是一个测试knife4j正常运行的接口";
    }
}
