2025-07-02 08:58:02.167 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 53 ] - Starting XiaozuApplication using Java 17.0.2 with PID 18920 (D:\gitlab\xiangmu\community-backend\target\classes started by joy0531 in D:\gitlab\xiangmu\community-backend)
2025-07-02 08:58:02.177 [ restartedMain ] - [ DEBUG ] [ com.hfut.xiaozu.XiaozuApplication : 54 ] - Running with Spring Boot v3.4.7, Spring v6.2.8
2025-07-02 08:58:02.178 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 652 ] - No active profile set, falling back to 1 default profile: "default"
2025-07-02 08:58:02.212 [ restartedMain ] - [ INFO  ] [ o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor : 252 ] - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-02 08:58:02.213 [ restartedMain ] - [ INFO  ] [ o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor : 252 ] - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-02 08:58:02.757 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 247 ] - Searching for mappers annotated with @Mapper
2025-07-02 08:58:02.757 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 251 ] - Using auto-configuration base package 'com.hfut.xiaozu'
2025-07-02 08:58:02.889 [ restartedMain ] - [ DEBUG ] [ org.apache.ibatis.logging.LogFactory : 112 ] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-02 08:58:02.904 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.class]
2025-07-02 08:58:02.904 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.class]
2025-07-02 08:58:02.904 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.class]
2025-07-02 08:58:02.904 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.class]
2025-07-02 08:58:02.904 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.class]
2025-07-02 08:58:02.904 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.class]
2025-07-02 08:58:02.904 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.class]
2025-07-02 08:58:02.905 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.class]
2025-07-02 08:58:02.905 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.class]
2025-07-02 08:58:02.905 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.class]
2025-07-02 08:58:02.905 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.class]
2025-07-02 08:58:02.905 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.class]
2025-07-02 08:58:02.905 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.class]
2025-07-02 08:58:02.905 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.class]
2025-07-02 08:58:02.905 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.class]
2025-07-02 08:58:02.905 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\info\VoteMapper.class]
2025-07-02 08:58:02.905 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\info\VoteOptionMapper.class]
2025-07-02 08:58:02.907 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\info\VoteScopeMapper.class]
2025-07-02 08:58:02.907 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\record\VoteRecordMapper.class]
2025-07-02 08:58:02.907 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleHouseBindingMapper' and 'com.hfut.xiaozu.car.bind.VehicleHouseBindingMapper' mapperInterface
2025-07-02 08:58:02.908 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleInfoMapper' and 'com.hfut.xiaozu.car.info.VehicleInfoMapper' mapperInterface
2025-07-02 08:58:02.909 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'familyMemberMapper' and 'com.hfut.xiaozu.family.info.FamilyMemberMapper' mapperInterface
2025-07-02 08:58:02.910 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userHouseBindingMapper' and 'com.hfut.xiaozu.house.binding.UserHouseBindingMapper' mapperInterface
2025-07-02 08:58:02.910 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityGridMapper' and 'com.hfut.xiaozu.house.grid.CommunityGridMapper' mapperInterface
2025-07-02 08:58:02.911 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'gridItemArchiveMapper' and 'com.hfut.xiaozu.house.grid.GridItemArchiveMapper' mapperInterface
2025-07-02 08:58:02.911 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'buildingInfoMapper' and 'com.hfut.xiaozu.house.information.BuildingInfoMapper' mapperInterface
2025-07-02 08:58:02.912 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityInfoMapper' and 'com.hfut.xiaozu.house.information.CommunityInfoMapper' mapperInterface
2025-07-02 08:58:02.912 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'houseInfoMapper' and 'com.hfut.xiaozu.house.information.HouseInfoMapper' mapperInterface
2025-07-02 08:58:02.912 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'unitInfoMapper' and 'com.hfut.xiaozu.house.information.UnitInfoMapper' mapperInterface
2025-07-02 08:58:02.913 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentProcessRecordMapper' and 'com.hfut.xiaozu.incident.process.IncidentProcessRecordMapper' mapperInterface
2025-07-02 08:58:02.913 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentRecordMapper' and 'com.hfut.xiaozu.incident.record.IncidentRecordMapper' mapperInterface
2025-07-02 08:58:02.913 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userMapper' and 'com.hfut.xiaozu.user.account.UserMapper' mapperInterface
2025-07-02 08:58:02.914 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityOcrRecordMapper' and 'com.hfut.xiaozu.user.idv.IdentityOcrRecordMapper' mapperInterface
2025-07-02 08:58:02.914 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityVerifySubmitMapper' and 'com.hfut.xiaozu.user.idv.IdentityVerifySubmitMapper' mapperInterface
2025-07-02 08:58:02.915 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'voteMapper' and 'com.hfut.xiaozu.vote.info.VoteMapper' mapperInterface
2025-07-02 08:58:02.915 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'voteOptionMapper' and 'com.hfut.xiaozu.vote.info.VoteOptionMapper' mapperInterface
2025-07-02 08:58:02.915 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'voteScopeMapper' and 'com.hfut.xiaozu.vote.info.VoteScopeMapper' mapperInterface
2025-07-02 08:58:02.917 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'voteRecordMapper' and 'com.hfut.xiaozu.vote.record.VoteRecordMapper' mapperInterface
2025-07-02 08:58:03.254 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 111 ] - Tomcat initialized with port 8080 (http)
2025-07-02 08:58:03.263 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardService : 168 ] - Starting service [Tomcat]
2025-07-02 08:58:03.264 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardEngine : 168 ] - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-02 08:58:03.307 [ restartedMain ] - [ INFO  ] [ o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] : 168 ] - Initializing Spring embedded WebApplicationContext
2025-07-02 08:58:03.307 [ restartedMain ] - [ INFO  ] [ o.s.b.w.s.c.ServletWebServerApplicationContext : 301 ] - Root WebApplicationContext: initialization completed in 1094 ms
2025-07-02 08:58:03.367 [ restartedMain ] - [ INFO  ] [ c.a.d.s.b.a.DruidDataSourceAutoConfigure : 55 ] - Init DruidDataSource
2025-07-02 08:58:03.714 [ restartedMain ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 1009 ] - {dataSource-1} inited
2025-07-02 08:58:03.842 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.xml]'
2025-07-02 08:58:03.850 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.xml]'
2025-07-02 08:58:03.859 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.xml]'
2025-07-02 08:58:03.867 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.xml]'
2025-07-02 08:58:03.874 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.xml]'
2025-07-02 08:58:03.882 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.xml]'
2025-07-02 08:58:03.893 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.xml]'
2025-07-02 08:58:03.901 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.xml]'
2025-07-02 08:58:03.907 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.xml]'
2025-07-02 08:58:03.914 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.xml]'
2025-07-02 08:58:03.918 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.xml]'
2025-07-02 08:58:03.925 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.xml]'
2025-07-02 08:58:03.930 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.xml]'
2025-07-02 08:58:03.936 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.xml]'
2025-07-02 08:58:03.942 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.xml]'
2025-07-02 08:58:03.948 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\info\VoteMapper.xml]'
2025-07-02 08:58:03.953 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\info\VoteOptionMapper.xml]'
2025-07-02 08:58:03.959 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\info\VoteScopeMapper.xml]'
2025-07-02 08:58:03.964 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\record\VoteRecordMapper.xml]'
2025-07-02 08:58:04.472 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 319 ] - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-02 08:58:04.638 [ restartedMain ] - [ INFO  ] [ o.s.b.d.autoconfigure.OptionalLiveReloadServer : 59 ] - LiveReload server is running on port 35729
2025-07-02 08:58:04.685 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 243 ] - Tomcat started on port 8080 (http) with context path '/'
2025-07-02 08:58:04.693 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 59 ] - Started XiaozuApplication in 2.844 seconds (process running for 4.112)
2025-07-02 08:58:54.025 [ http-nio-8080-exec-1 ] - [ INFO  ] [ o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] : 168 ] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-02 08:58:54.025 [ http-nio-8080-exec-1 ] - [ INFO  ] [ org.springframework.web.servlet.DispatcherServlet : 532 ] - Initializing Servlet 'dispatcherServlet'
2025-07-02 08:58:54.027 [ http-nio-8080-exec-1 ] - [ INFO  ] [ org.springframework.web.servlet.DispatcherServlet : 554 ] - Completed initialization in 1 ms
2025-07-02 08:58:54.496 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-02 08:58:54.501 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-02 08:58:55.184 [ http-nio-8080-exec-5 ] - [ INFO  ] [ org.springdoc.api.AbstractOpenApiResource : 390 ] - Init duration for springdoc-openapi is: 593 ms
2025-07-02 08:59:04.788 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/auth/me
2025-07-02 08:59:04.790 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/auth/identity/me
2025-07-02 08:59:04.790 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/houses/binding/me
2025-07-02 08:59:04.797 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/me
2025-07-02 08:59:04.801 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-02 08:59:04.801 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: PUT /api/auth/identity/me
2025-07-02 08:59:04.811 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-02 08:59:04.812 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-02 08:59:07.447 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-02 08:59:07.463 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/communities
2025-07-02 08:59:07.468 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-07-02 08:59:09.869 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-02 08:59:09.882 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-07-02 08:59:11.439 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-02 08:59:11.900 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/vote/list
2025-07-02 08:59:11.906 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 08:59:15.083 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-02 08:59:18.275 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-02 08:59:22.856 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-02 08:59:24.564 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/me
2025-07-02 08:59:24.566 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: PUT /api/auth/identity/me
2025-07-02 08:59:24.567 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-02 08:59:24.573 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-02 08:59:24.573 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-02 08:59:26.572 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/me
2025-07-02 08:59:26.586 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-02 08:59:26.590 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-02 08:59:26.590 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-02 08:59:43.366 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-02 08:59:46.041 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-02 09:01:19.937 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/family
2025-07-02 09:01:20.042 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-02 09:01:32.321 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-02 09:01:33.073 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-02 09:02:30.096 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/incident
2025-07-02 09:02:44.539 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/list
2025-07-02 09:02:44.544 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-07-02 09:02:44.544 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/list
2025-07-02 09:02:44.544 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/binding/list/1
2025-07-02 09:02:50.426 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/list
2025-07-02 09:02:50.428 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/list
2025-07-02 09:02:50.428 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/binding/list/1
2025-07-02 09:02:50.431 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-07-02 09:02:50.465 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-02 09:02:56.657 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-07-02 09:02:56.666 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-07-02 09:02:56.710 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-07-02 09:02:56.737 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-07-02 09:02:56.750 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-07-02 09:03:02.919 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/list
2025-07-02 09:03:02.923 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-07-02 09:03:02.923 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/list
2025-07-02 09:03:02.923 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/binding/list/1
2025-07-02 09:03:03.008 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-02 09:03:05.285 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-02 09:03:05.948 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-07-02 09:03:05.961 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-07-02 09:03:05.973 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-07-02 09:03:05.984 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-07-02 09:03:05.996 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-07-02 09:03:07.231 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/auth/list/grid-workers
2025-07-02 09:03:07.234 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/list/grid-workers
2025-07-02 09:03:21.083 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/list/grid-workers
2025-07-02 09:03:23.233 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/7/assign
2025-07-02 09:03:23.278 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-07-02 09:03:24.470 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-07-02 09:03:24.479 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-07-02 09:03:25.767 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-07-02 09:03:25.775 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-07-02 09:03:35.261 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/list
2025-07-02 09:03:35.261 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/list
2025-07-02 09:03:35.262 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/binding/list/1
2025-07-02 09:03:35.262 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-07-02 09:03:36.375 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/list
2025-07-02 09:03:36.376 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/list
2025-07-02 09:03:36.378 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/binding/list/1
2025-07-02 09:03:36.378 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-07-02 09:03:36.409 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-02 09:05:17.486 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-07-02 09:05:17.494 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-07-02 09:05:17.505 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-07-02 09:05:17.516 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-07-02 09:05:17.529 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-07-02 09:05:17.875 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-02 09:05:18.348 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-02 09:05:18.767 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-02 09:05:19.132 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-07-02 09:05:19.138 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-07-02 09:05:19.149 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-07-02 09:05:19.162 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-07-02 09:05:19.173 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-07-02 09:05:20.571 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-07-02 09:05:20.577 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-07-02 09:05:26.434 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-07-02 09:05:26.444 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-07-02 09:05:26.928 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-07-02 09:05:26.935 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-07-02 09:05:27.283 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-07-02 09:05:27.290 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-07-02 09:05:28.341 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-07-02 09:05:28.348 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-07-02 09:05:28.757 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-07-02 09:05:28.763 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-07-02 09:05:29.664 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-07-02 09:05:29.671 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-07-02 09:05:41.159 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/list
2025-07-02 09:05:41.163 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/binding/list/1
2025-07-02 09:05:41.163 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/list
2025-07-02 09:05:41.163 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-07-02 09:05:42.344 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-02 09:06:22.885 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/vote
2025-07-02 09:06:22.888 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/vote
2025-07-02 09:06:41.513 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/me
2025-07-02 09:06:41.514 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: PUT /api/auth/identity/me
2025-07-02 09:06:41.517 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-02 09:06:41.519 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-02 09:06:41.520 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-02 09:06:43.987 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-02 09:06:44.891 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-02 09:06:44.903 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-07-02 09:06:47.382 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-02 09:06:48.376 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: PUT /api/auth/identity/me
2025-07-02 09:06:50.390 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-02 09:06:52.195 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 09:07:09.529 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 09:07:48.791 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/vote
2025-07-02 09:08:17.607 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/list
2025-07-02 09:08:17.608 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/list
2025-07-02 09:08:17.610 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/binding/list/1
2025-07-02 09:08:17.610 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-07-02 09:08:19.005 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-07-02 09:08:46.263 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/vote
2025-07-02 09:08:53.362 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 09:09:20.152 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 09:10:11.756 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 09:10:35.538 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-02 09:10:35.538 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-02 09:11:08.388 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-02 09:11:08.388 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-02 09:11:11.733 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-02 09:11:11.733 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-02 09:11:16.966 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 09:12:15.588 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 09:12:25.665 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/vote/8
2025-07-02 09:12:25.670 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/vote/8
2025-07-02 09:12:25.685 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 09:12:28.824 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/vote/8
2025-07-02 09:12:28.836 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 09:12:35.455 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 09:12:52.800 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 09:14:02.033 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/vote
2025-07-02 09:14:39.516 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 09:14:49.076 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 09:14:57.282 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 09:15:05.962 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/vote/6
2025-07-02 09:15:05.967 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/vote/6
2025-07-02 09:15:05.982 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 09:15:10.406 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 09:15:24.730 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-02 09:15:24.730 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-02 09:15:27.198 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-02 09:15:27.198 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-02 09:15:28.630 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-02 09:15:28.630 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-02 09:15:35.710 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/vote/10
2025-07-02 09:15:35.712 [ http-nio-8080-exec-7 ] - [ DEBUG ] [ com.hfut.xiaozu.security.JwtUtils : 60 ] - JWT validation failed: 'exp':[2025-06-25 11:32:25] is before now:[2025-07-02 09:15:35]
2025-07-02 09:15:35.712 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 59 ] - 请求 POST /api/vote/10 token无效
2025-07-02 09:15:38.556 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-02 09:15:38.556 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-02 09:15:45.944 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-02 09:15:45.945 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-02 09:15:46.637 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-02 09:15:46.637 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-02 09:15:46.934 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-02 09:15:46.935 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-02 09:15:51.335 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/vote/10
2025-07-02 09:16:01.295 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 09:16:08.867 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/vote/3
2025-07-02 09:16:08.870 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/vote/3
2025-07-02 09:16:08.882 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 09:16:13.080 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 09:16:48.834 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 09:18:11.200 [ File Watcher ] - [ INFO  ] [ o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener : 211 ] - Restarting due to 119 class path changes (0 additions, 119 deletions, 0 modifications)
2025-07-02 09:18:11.236 [ Thread-5 ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 54 ] - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-02 09:18:11.430 [ tomcat-shutdown ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 76 ] - Graceful shutdown complete
2025-07-02 09:18:11.470 [ Thread-5 ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2174 ] - {dataSource-1} closing ...
2025-07-02 09:18:11.496 [ Thread-5 ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2247 ] - {dataSource-1} closed
2025-07-02 09:18:17.621 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 53 ] - Starting XiaozuApplication using Java 17.0.2 with PID 18920 (D:\gitlab\xiangmu\community-backend\target\classes started by joy0531 in D:\gitlab\xiangmu\community-backend)
2025-07-02 09:18:17.622 [ restartedMain ] - [ DEBUG ] [ com.hfut.xiaozu.XiaozuApplication : 54 ] - Running with Spring Boot v3.4.7, Spring v6.2.8
2025-07-02 09:18:17.622 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 652 ] - No active profile set, falling back to 1 default profile: "default"
2025-07-02 09:18:17.912 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 247 ] - Searching for mappers annotated with @Mapper
2025-07-02 09:18:17.914 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 251 ] - Using auto-configuration base package 'com.hfut.xiaozu'
2025-07-02 09:18:18.026 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.class]
2025-07-02 09:18:18.027 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.class]
2025-07-02 09:18:18.027 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.class]
2025-07-02 09:18:18.027 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.class]
2025-07-02 09:18:18.027 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.class]
2025-07-02 09:18:18.027 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.class]
2025-07-02 09:18:18.027 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.class]
2025-07-02 09:18:18.027 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.class]
2025-07-02 09:18:18.029 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.class]
2025-07-02 09:18:18.029 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.class]
2025-07-02 09:18:18.029 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.class]
2025-07-02 09:18:18.029 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.class]
2025-07-02 09:18:18.029 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.class]
2025-07-02 09:18:18.029 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.class]
2025-07-02 09:18:18.029 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.class]
2025-07-02 09:18:18.029 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\info\VoteMapper.class]
2025-07-02 09:18:18.029 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\info\VoteOptionMapper.class]
2025-07-02 09:18:18.029 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\info\VoteScopeMapper.class]
2025-07-02 09:18:18.030 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\record\VoteRecordMapper.class]
2025-07-02 09:18:18.030 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleHouseBindingMapper' and 'com.hfut.xiaozu.car.bind.VehicleHouseBindingMapper' mapperInterface
2025-07-02 09:18:18.033 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleInfoMapper' and 'com.hfut.xiaozu.car.info.VehicleInfoMapper' mapperInterface
2025-07-02 09:18:18.034 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'familyMemberMapper' and 'com.hfut.xiaozu.family.info.FamilyMemberMapper' mapperInterface
2025-07-02 09:18:18.035 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userHouseBindingMapper' and 'com.hfut.xiaozu.house.binding.UserHouseBindingMapper' mapperInterface
2025-07-02 09:18:18.037 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityGridMapper' and 'com.hfut.xiaozu.house.grid.CommunityGridMapper' mapperInterface
2025-07-02 09:18:18.037 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'gridItemArchiveMapper' and 'com.hfut.xiaozu.house.grid.GridItemArchiveMapper' mapperInterface
2025-07-02 09:18:18.038 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'buildingInfoMapper' and 'com.hfut.xiaozu.house.information.BuildingInfoMapper' mapperInterface
2025-07-02 09:18:18.039 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityInfoMapper' and 'com.hfut.xiaozu.house.information.CommunityInfoMapper' mapperInterface
2025-07-02 09:18:18.039 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'houseInfoMapper' and 'com.hfut.xiaozu.house.information.HouseInfoMapper' mapperInterface
2025-07-02 09:18:18.040 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'unitInfoMapper' and 'com.hfut.xiaozu.house.information.UnitInfoMapper' mapperInterface
2025-07-02 09:18:18.041 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentProcessRecordMapper' and 'com.hfut.xiaozu.incident.process.IncidentProcessRecordMapper' mapperInterface
2025-07-02 09:18:18.042 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentRecordMapper' and 'com.hfut.xiaozu.incident.record.IncidentRecordMapper' mapperInterface
2025-07-02 09:18:18.043 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userMapper' and 'com.hfut.xiaozu.user.account.UserMapper' mapperInterface
2025-07-02 09:18:18.043 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityOcrRecordMapper' and 'com.hfut.xiaozu.user.idv.IdentityOcrRecordMapper' mapperInterface
2025-07-02 09:18:18.045 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityVerifySubmitMapper' and 'com.hfut.xiaozu.user.idv.IdentityVerifySubmitMapper' mapperInterface
2025-07-02 09:18:18.046 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'voteMapper' and 'com.hfut.xiaozu.vote.info.VoteMapper' mapperInterface
2025-07-02 09:18:18.046 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'voteOptionMapper' and 'com.hfut.xiaozu.vote.info.VoteOptionMapper' mapperInterface
2025-07-02 09:18:18.047 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'voteScopeMapper' and 'com.hfut.xiaozu.vote.info.VoteScopeMapper' mapperInterface
2025-07-02 09:18:18.049 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'voteRecordMapper' and 'com.hfut.xiaozu.vote.record.VoteRecordMapper' mapperInterface
2025-07-02 09:18:18.156 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 111 ] - Tomcat initialized with port 8080 (http)
2025-07-02 09:18:18.156 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardService : 168 ] - Starting service [Tomcat]
2025-07-02 09:18:18.156 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardEngine : 168 ] - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-02 09:18:18.184 [ restartedMain ] - [ INFO  ] [ o.a.c.c.ContainerBase.[Tomcat-2].[localhost].[/] : 168 ] - Initializing Spring embedded WebApplicationContext
2025-07-02 09:18:18.185 [ restartedMain ] - [ INFO  ] [ o.s.b.w.s.c.ServletWebServerApplicationContext : 301 ] - Root WebApplicationContext: initialization completed in 558 ms
2025-07-02 09:18:18.214 [ restartedMain ] - [ INFO  ] [ c.a.d.s.b.a.DruidDataSourceAutoConfigure : 55 ] - Init DruidDataSource
2025-07-02 09:18:18.449 [ restartedMain ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 1009 ] - {dataSource-2} inited
2025-07-02 09:18:18.530 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.xml]'
2025-07-02 09:18:18.540 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.xml]'
2025-07-02 09:18:18.550 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.xml]'
2025-07-02 09:18:18.561 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.xml]'
2025-07-02 09:18:18.581 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.xml]'
2025-07-02 09:18:18.592 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.xml]'
2025-07-02 09:18:18.614 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.xml]'
2025-07-02 09:18:18.637 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.xml]'
2025-07-02 09:18:18.648 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.xml]'
2025-07-02 09:18:18.660 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.xml]'
2025-07-02 09:18:18.671 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.xml]'
2025-07-02 09:18:18.700 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.xml]'
2025-07-02 09:18:18.713 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.xml]'
2025-07-02 09:18:18.721 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.xml]'
2025-07-02 09:18:18.727 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.xml]'
2025-07-02 09:18:18.735 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\info\VoteMapper.xml]'
2025-07-02 09:18:18.743 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\info\VoteOptionMapper.xml]'
2025-07-02 09:18:18.751 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\info\VoteScopeMapper.xml]'
2025-07-02 09:18:18.758 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\vote\record\VoteRecordMapper.xml]'
2025-07-02 09:18:19.178 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 319 ] - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-02 09:18:19.303 [ restartedMain ] - [ INFO  ] [ o.s.b.d.autoconfigure.OptionalLiveReloadServer : 59 ] - LiveReload server is running on port 35729
2025-07-02 09:18:19.365 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 243 ] - Tomcat started on port 8080 (http) with context path '/'
2025-07-02 09:18:19.372 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 59 ] - Started XiaozuApplication in 1.815 seconds (process running for 1218.792)
2025-07-02 09:18:19.376 [ restartedMain ] - [ INFO  ] [ o.s.b.d.a.ConditionEvaluationDeltaLoggingListener : 63 ] - Condition evaluation unchanged
2025-07-02 09:18:30.862 [ http-nio-8080-exec-1 ] - [ INFO  ] [ o.a.c.c.ContainerBase.[Tomcat-2].[localhost].[/] : 168 ] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-02 09:18:30.862 [ http-nio-8080-exec-1 ] - [ INFO  ] [ org.springframework.web.servlet.DispatcherServlet : 532 ] - Initializing Servlet 'dispatcherServlet'
2025-07-02 09:18:30.863 [ http-nio-8080-exec-1 ] - [ INFO  ] [ org.springframework.web.servlet.DispatcherServlet : 554 ] - Completed initialization in 1 ms
2025-07-02 09:18:31.020 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/me
2025-07-02 09:18:31.021 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: PUT /api/auth/identity/me
2025-07-02 09:18:31.023 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-02 09:18:31.030 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-02 09:18:31.030 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-02 09:18:41.229 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 09:18:44.894 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/vote/3
2025-07-02 09:18:44.905 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 09:19:10.094 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 09:19:16.191 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/vote/3
2025-07-02 09:19:16.202 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 09:19:48.640 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/me
2025-07-02 09:19:48.640 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: PUT /api/auth/identity/me
2025-07-02 09:19:48.641 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-02 09:19:48.645 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-02 09:19:48.647 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-02 09:19:51.469 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 09:19:57.005 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/vote/5
2025-07-02 09:19:57.008 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/vote/5
2025-07-02 09:19:57.020 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 09:20:01.446 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/vote/8
2025-07-02 09:20:01.459 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 09:21:10.239 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 09:21:30.828 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-02 09:21:30.829 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-02 09:21:31.599 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-07-02 09:21:31.599 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-07-02 09:21:48.914 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/vote/list
2025-07-02 09:22:41.295 [ SpringApplicationShutdownHook ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 54 ] - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-02 09:22:41.384 [ tomcat-shutdown ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 76 ] - Graceful shutdown complete
2025-07-02 09:22:41.387 [ SpringApplicationShutdownHook ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2174 ] - {dataSource-2} closing ...
2025-07-02 09:22:41.388 [ SpringApplicationShutdownHook ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2247 ] - {dataSource-2} closed
