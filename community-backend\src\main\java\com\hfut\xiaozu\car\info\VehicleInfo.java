package com.hfut.xiaozu.car.info;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 车辆信息表
 * @TableName vehicle_info
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VehicleInfo {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 所属用户ID
     */
    private Long userId;

    /**
     * 车牌号码
     */
    private String plateNumber;

    /**
     * 车辆品牌
     */
    private String brand;

    /**
     * 车辆颜色
     */
    private String color;

    /**
     * 是否小区内车辆 0-否 1-是
     */
    private Integer isInCommunity;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
