<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hfut.xiaozu.user.idv.IdentityVerifySubmitMapper">

    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO community.identity_verify_submit(user_id, session_id, submitted_name, submitted_id_card, status)
        VALUES(#{userId},#{sessionId},#{submittedName},#{submittedIdCard},0)
    </insert>

    <update id="updateStatusById">
        UPDATE identity_verify_submit
        <set>
            <if test="status != null">status = #{status},</if>
            <if test="reviewRemark != null">review_remark = #{reviewRemark},</if>
            <if test="reviewerId != null">reviewer_id = #{reviewerId},</if>
        </set>
        WHERE id = #{id}

    </update>

    <select id="listByStatus" resultType="com.hfut.xiaozu.user.idv.IdentityVerifySubmitEntity">
            SELECT * FROM community.identity_verify_submit
            WHERE status =#{status}
    </select>

    <select id="getById" resultType="com.hfut.xiaozu.user.idv.IdentityVerifySubmitEntity">
        SELECT * FROM community.identity_verify_submit
        WHERE id =#{id}
    </select>

    <select id="getByUserId" resultType="com.hfut.xiaozu.user.idv.IdentityVerifySubmitEntity">
        SELECT * FROM community.identity_verify_submit
        WHERE user_id =#{userId}
    </select>

</mapper>

