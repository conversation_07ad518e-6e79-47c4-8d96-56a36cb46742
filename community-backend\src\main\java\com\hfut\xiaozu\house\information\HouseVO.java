package com.hfut.xiaozu.house.information;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 房屋信息表
 * @TableName house_info
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HouseVO {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 门牌号
     */
    private String houseNumber;

    /**
     * 房屋面积(m²)
     */
    private BigDecimal areaSize;

    /**
     * 是否已入住 0-否 1-是
     */
    private Integer isOccupied;

}
