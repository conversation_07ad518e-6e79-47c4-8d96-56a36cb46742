package com.hfut.xiaozu.user.context;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-06-23
 */
public class UserContextHolder {
    private static final ThreadLocal<CurrentUser> context = new ThreadLocal<>();

    public static void setCurrentUser(CurrentUser user) {
        context.set(user);
    }

    public static CurrentUser getCurrentUser() {
        return context.get();
    }

    public static void clear() {
        context.remove();
    }

    // 新增角色判断方法
    public static boolean isCurrentUserRole(Integer expectedRole) {
        CurrentUser currentUser = getCurrentUser();
        return currentUser != null &&
                Objects.equals(currentUser.getRole(),expectedRole);
    }
}
