<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hfut.xiaozu.family.info.FamilyMemberMapper">

    <resultMap id="BaseResultMap" type="com.hfut.xiaozu.family.info.FamilyMemberEntity">
            <id property="id" column="id" />
            <result property="ownerId" column="owner_id" />
            <result property="memberName" column="member_name" />
            <result property="relationship" column="relationship" />
            <result property="idCard" column="id_card" />
            <result property="contactPhone" column="contact_phone" />
            <result property="isPrimary" column="is_primary" />
            <result property="isDeleted" column="is_deleted" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,owner_id,member_name,relationship,id_card,contact_phone,
        is_primary,is_deleted,create_time,update_time
    </sql>

    <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from family_member
        where  id = #{id}
    </select>

    <select id="listByOwnerId" resultType="com.hfut.xiaozu.family.info.FamilyMemberEntity">
        select
        <include refid="Base_Column_List" />
        from community.family_member
        where  owner_id = #{owner_id} AND is_deleted = 0;
    </select>

    <delete id="deleteById" parameterType="java.lang.Long">
        update community.family_member SET is_deleted = 1
        where  id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.hfut.xiaozu.family.info.FamilyMemberEntity" useGeneratedKeys="true">
        insert into community.family_member
        ( owner_id,member_name,relationship,id_card,contact_phone,is_primary)
        values (#{ownerId},#{memberName},#{relationship},#{idCard},#{contactPhone},#{isPrimary})
    </insert>

    <update id="update" parameterType="com.hfut.xiaozu.family.info.FamilyMemberEntity">
        update family_member
        <set>
                <if test="memberName != null">
                    member_name = #{memberName},
                </if>
                <if test="relationship != null">
                    relationship = #{relationship},
                </if>
                <if test="idCard != null">
                    id_card = #{idCard},
                </if>
                <if test="contactPhone != null">
                    contact_phone = #{contactPhone},
                </if>
                <if test="isPrimary != null">
                    is_primary = #{isPrimary},
                </if>
        </set>
        where   id = #{id}
    </update>


</mapper>
