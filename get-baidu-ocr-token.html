<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>百度OCR Token获取工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .current-config {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .copy-btn {
            background-color: #28a745;
            color: white;
            padding: 5px 10px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            margin-left: 10px;
        }
        .copy-btn:hover {
            background-color: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔑 百度OCR Access Token获取工具</h1>
        
        <div class="current-config">
            <h3>📋 当前后端配置</h3>
            <p><strong>API Key:</strong> MT377gXmfsgOwVAAcVBaseYO</p>
            <p><strong>Secret Key:</strong> xodTwg66M7sLNHEcelmzp2kQcG7MpVLS</p>
            <p><strong>当前Access Token:</strong> 24.949b65e4adfab79b2ceedc21a414701.2592000.1753412317.282335-119337225</p>
            <p><strong>状态:</strong> <span style="color: red;">❌ 可能已过期</span></p>
        </div>

        <form id="tokenForm">
            <div class="form-group">
                <label for="apiKey">API Key:</label>
                <input type="text" id="apiKey" value="MT377gXmfsgOwVAAcVBaseYO" required>
            </div>
            
            <div class="form-group">
                <label for="secretKey">Secret Key:</label>
                <input type="text" id="secretKey" value="xodTwg66M7sLNHEcelmzp2kQcG7MpVLS" required>
            </div>
            
            <button type="submit" id="getTokenBtn">🚀 获取新的Access Token</button>
        </form>

        <div id="result"></div>
    </div>

    <script>
        document.getElementById('tokenForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const apiKey = document.getElementById('apiKey').value.trim();
            const secretKey = document.getElementById('secretKey').value.trim();
            const resultDiv = document.getElementById('result');
            const btn = document.getElementById('getTokenBtn');
            
            if (!apiKey || !secretKey) {
                resultDiv.innerHTML = '<div class="result error">❌ 请填写API Key和Secret Key</div>';
                return;
            }
            
            btn.disabled = true;
            btn.textContent = '🔄 获取中...';
            resultDiv.innerHTML = '<div class="result info">🔄 正在获取Access Token...</div>';
            
            try {
                const url = `https://aip.baidubce.com/oauth/2.0/token?client_id=${apiKey}&client_secret=${secretKey}&grant_type=client_credentials`;
                
                console.log('🌐 请求URL:', url);
                
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });
                
                const data = await response.json();
                console.log('📦 响应数据:', data);
                
                if (data.access_token) {
                    const expiresIn = data.expires_in || 2592000; // 默认30天
                    const expiresDate = new Date(Date.now() + expiresIn * 1000);
                    
                    const resultHtml = `
<div class="result success">
✅ 成功获取新的Access Token！

🔑 新的Access Token:
${data.access_token}
<button class="copy-btn" onclick="copyToClipboard('${data.access_token}')">复制</button>

⏰ 有效期: ${expiresIn}秒 (约${Math.round(expiresIn/86400)}天)
📅 过期时间: ${expiresDate.toLocaleString('zh-CN')}

📝 更新配置文件:
请将以下内容更新到 community-backend/src/main/resources/application-dev.yml:

ocr:
  api-key: ${apiKey}
  secret-key: ${secretKey}
  access-token: ${data.access_token}
<button class="copy-btn" onclick="copyToClipboard('ocr:\\n  api-key: ${apiKey}\\n  secret-key: ${secretKey}\\n  access-token: ${data.access_token}')">复制配置</button>

🔄 更新后请重启后端服务！
</div>`;
                    
                    resultDiv.innerHTML = resultHtml;
                } else if (data.error) {
                    resultDiv.innerHTML = `
<div class="result error">
❌ 获取Access Token失败

错误代码: ${data.error}
错误描述: ${data.error_description || '未知错误'}

💡 可能的原因:
1. API Key或Secret Key不正确
2. 网络连接问题
3. 百度API服务异常

🔧 解决建议:
1. 检查API Key和Secret Key是否正确
2. 确认网络连接正常
3. 稍后重试
</div>`;
                } else {
                    resultDiv.innerHTML = `
<div class="result error">
❌ 未知响应格式

响应内容: ${JSON.stringify(data, null, 2)}
</div>`;
                }
                
            } catch (error) {
                console.error('❌ 请求失败:', error);
                resultDiv.innerHTML = `
<div class="result error">
❌ 请求失败: ${error.message}

💡 可能的原因:
1. 网络连接问题
2. CORS跨域限制
3. 百度API服务不可用

🔧 解决建议:
1. 检查网络连接
2. 尝试使用后端测试代码获取token
3. 稍后重试
</div>`;
            } finally {
                btn.disabled = false;
                btn.textContent = '🚀 获取新的Access Token';
            }
        });
        
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                alert('✅ 已复制到剪贴板！');
            }, function(err) {
                console.error('❌ 复制失败:', err);
                alert('❌ 复制失败，请手动复制');
            });
        }
    </script>
</body>
</html>
