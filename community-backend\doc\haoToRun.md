
# 后端如何跑起来

0. 配置文件里的mysql数据库，用户名，密码，数据库名字

## 1. 要先启动数据库:
    整合了druid连接池后，要启动数据库，才能启动程序，否则启动程序后无法使用任何接口

## 2. 首先，要申请[阿里云oss](https://www.aliyun.com/product/oss)，可以用支付宝登录 


![img_1.png](img_1.png)

申请一个accesskey

![img_2.png](img_2.png)

创建一个accesskey
，这个只能弹出一次，因此自己保存好
![img_3.png](img_3.png)

然后将这个两个设置为自己的环境变量
![img_4.png](img_4.png)


然后去申请一个oss的bucket(https://oss.console.aliyun.com/bucket)
![img_5.png](img_5.png)

你就自己写个bucket名称，地域可以选南京什么的，其他全默认


创建bucket，进去后，在项目中的application.yml最下面有个配置项
bucket 和endpoint，前者是你桶的名称，后者是下图蓝色框框的网址
![img_6.png](img_6.png)

然后要给桶配置公共读权限


![img_7.png](img_7.png)
![img_8.png](img_8.png)
你的页面显示和上面两个图一样就行


## 3.申请百度身份证OCR
要申请百度ocr的api和key，怎么申请去问乔泽凯（我账号有问题好像。。）
然后会获得api和密钥，下面第二个和第三个填进去，然后第一个先填null
![img_9.png](img_9.png)

然后在后端的 IDOCRTest.java 测试类中，跑    public void getAccessToken()方法。
会获得一串json，里面的access_token，复制到刚才让你填null的环境变量里。

