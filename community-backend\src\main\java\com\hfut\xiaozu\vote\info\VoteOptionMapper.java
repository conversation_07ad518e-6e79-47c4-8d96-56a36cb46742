package com.hfut.xiaozu.vote.info;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【vote_option(投票选项表)】的数据库操作Mapper
* @createDate 2025-07-01 10:19:42
* @Entity com.hfut.xiaozu.vote.VoteOption
*/
@Mapper
public interface VoteOptionMapper {

    int deleteById(Long id);

    int insert(VoteOptionEntity record);

    VoteOptionEntity getById(Long id);

    int updateById(VoteOptionEntity record);

    List<VoteOptionEntity> listByVoteId(Long voteId);
}
