package com.hfut.xiaozu.user.idv;

import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【identity_ocr_record(身份证OCR识别记录表（每次OCR调用记录）)】的数据库操作Mapper
* @createDate 2025-06-24 23:21:40
* @Entity generator.domain.IdentityOcrRecord
*/
@Mapper
public interface IdentityOcrRecordMapper {
    int insert(IdentityOcrRecordEntity record);

    void updateOcrRecord(IdentityOcrRecordEntity record);
}
