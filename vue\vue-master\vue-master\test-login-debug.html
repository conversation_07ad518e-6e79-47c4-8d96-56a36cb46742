<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .result.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 登录调试测试</h1>
        <p>测试登录API并检查返回的数据格式</p>

        <form id="loginForm">
            <div class="form-group">
                <label for="userName">用户名/手机号</label>
                <input type="text" id="userName" value="test123" required>
            </div>

            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" value="123456" required>
            </div>

            <div class="form-group">
                <label for="userType">用户类型</label>
                <select id="userType" required>
                    <option value="1">1-居民</option>
                    <option value="2">2-网格员</option>
                    <option value="3">3-社区管理员</option>
                </select>
            </div>

            <button type="submit" id="loginBtn">🚀 测试登录</button>
            <button type="button" onclick="checkStorage()">📱 检查存储</button>
            <button type="button" onclick="clearStorage()">🗑️ 清除存储</button>
        </form>

        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080/api';

        // 显示结果
        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }

        // 检查存储状态
        function checkStorage() {
            const storage = {
                localStorage: {
                    auth_data: localStorage.getItem('auth_data'),
                    auth_token: localStorage.getItem('auth_token'),
                    userToken: localStorage.getItem('userToken'),
                    user_info: localStorage.getItem('user_info')
                },
                sessionStorage: {
                    auth_data: sessionStorage.getItem('auth_data'),
                    auth_token: sessionStorage.getItem('auth_token'),
                    userToken: sessionStorage.getItem('userToken'),
                    user_info: sessionStorage.getItem('user_info')
                }
            };

            showResult(`📱 当前存储状态:\n\n${JSON.stringify(storage, null, 2)}`, 'info');
        }

        // 清除存储
        function clearStorage() {
            const keys = ['auth_data', 'auth_token', 'userToken', 'user_info'];
            keys.forEach(key => {
                localStorage.removeItem(key);
                sessionStorage.removeItem(key);
            });
            showResult('🗑️ 存储已清除', 'info');
        }

        // 登录测试
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const loginBtn = document.getElementById('loginBtn');
            const userName = document.getElementById('userName').value;
            const password = document.getElementById('password');
            const userType = parseInt(document.getElementById('userType').value);

            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';

            try {
                const requestData = {
                    userName: userName,
                    password: password.value,
                    userType: userType
                };

                console.log('🚀 发送登录请求:', requestData);
                showResult('🚀 正在发送登录请求...', 'info');

                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                console.log('📡 响应状态:', response.status, response.statusText);

                const result = await response.json();
                console.log('📦 响应数据:', result);

                let message = `📦 登录响应详情:\n\n`;
                message += `HTTP状态: ${response.status} ${response.statusText}\n\n`;
                message += `响应数据:\n${JSON.stringify(result, null, 2)}\n\n`;

                if (response.ok && result.code === 200) {
                    // 登录成功，手动保存token（模拟authApi.js的逻辑）
                    if (result.data && result.data.token) {
                        localStorage.setItem('auth_token', result.data.token);

                        // 由于后端只返回token，从请求数据中获取用户信息
                        localStorage.setItem('user_info', JSON.stringify({
                            userName: requestData.userName,
                            userType: requestData.userType
                        }));

                        message += `✅ Token已保存:\n`;
                        message += `- auth_token: ${result.data.token.substring(0, 30)}...\n`;
                        message += `- user_info: ${JSON.stringify({userName: requestData.userName, userType: requestData.userType})}\n`;
                        message += `\n💡 注意: 后端只返回token，用户信息来自请求数据\n`;
                    } else {
                        message += `⚠️ 警告: 响应中没有token数据\n`;
                    }
                    showResult(message, 'success');
                } else {
                    message += `❌ 登录失败: ${result.msg || result.message || '未知错误'}`;
                    showResult(message, 'error');
                }

            } catch (error) {
                console.error('❌ 登录失败:', error);
                showResult(`❌ 网络错误:\n\n${error.message}\n\n可能原因:\n1. 后端服务未启动\n2. 网络连接问题\n3. CORS跨域问题`, 'error');
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = '🚀 测试登录';
            }
        });

        // 页面加载时检查存储状态
        document.addEventListener('DOMContentLoaded', function() {
            checkStorage();
        });
    </script>
</body>
</html>
