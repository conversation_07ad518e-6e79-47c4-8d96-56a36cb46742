<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hfut.xiaozu.vote.info.VoteMapper">

    <resultMap id="BaseResultMap" type="com.hfut.xiaozu.vote.info.VoteEntity">
            <id property="id" column="id" />
            <result property="title" column="title" />
            <result property="description" column="description" />
            <result property="startTime" column="start_time" />
            <result property="endTime" column="end_time" />
            <result property="maxChoices" column="max_choices" />
            <result property="isAnonymous" column="is_anonymous" />
            <result property="creatorId" column="creator_id" />
            <result property="createTime" column="create_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,title,description,start_time,end_time,max_choices,
        is_anonymous,creator_id,create_time
    </sql>

    <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from vote
        where  id = #{id}
    </select>

    <select id="listAll" resultType="com.hfut.xiaozu.vote.info.VoteEntity">
        select
        <include refid="Base_Column_List" />
        from vote
    </select>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from vote
        where  id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.hfut.xiaozu.vote.info.VoteEntity" useGeneratedKeys="true">
        insert into vote
        ( title,description,start_time,end_time,max_choices,
        is_anonymous,creator_id)
        values (#{title},#{description},#{startTime},#{endTime},#{maxChoices},
        #{isAnonymous},#{creatorId})
    </insert>

    <update id="updateById" parameterType="com.hfut.xiaozu.vote.info.VoteEntity">
        update vote
        <set>
                <if test="title != null">
                    title = #{title},
                </if>
                <if test="description != null">
                    description = #{description},
                </if>
                <if test="startTime != null">
                    start_time = #{startTime},
                </if>
                <if test="endTime != null">
                    end_time = #{endTime},
                </if>
                <if test="maxChoices != null">
                    max_choices = #{maxChoices},
                </if>
                <if test="isAnonymous != null">
                    is_anonymous = #{isAnonymous},
                </if>
                <if test="creatorId != null">
                    creator_id = #{creatorId},
                </if>
        </set>
        where   id = #{id}
    </update>


</mapper>
