# 高德地图API配置指南

## 1. 获取高德地图API密钥

### 步骤1：注册高德开发者账号
1. 访问 [高德开放平台](https://lbs.amap.com/)
2. 点击右上角"控制台"，注册并登录账号
3. 完成开发者认证

### 步骤2：创建应用
1. 登录后进入控制台
2. 点击"创建新应用"
3. 填写应用信息：
   - 应用名称：芙蓉社区管理系统
   - 应用类型：Web端(JS API)

### 步骤3：添加Key
1. 在应用管理页面，点击"添加Key"
2. 填写Key信息：
   - Key名称：芙蓉社区地图
   - 服务平台：Web端(JS API)
   - 白名单：添加你的域名（开发环境可以使用 `*` 或 `localhost:*`）

### 步骤4：获取安全密钥
1. 在应用详情页面，找到"安全密钥"
2. 复制安全密钥（用于提高API安全性）

## 2. 配置项目

### 方法1：使用环境变量（推荐）
1. 在项目根目录的 `.env` 文件中设置：
```
VITE_AMAP_API_KEY=你的API密钥
VITE_AMAP_SECURITY_KEY=你的安全密钥
```

2. 重启开发服务器

### 方法2：直接修改配置文件
1. 打开 `src/config/map.js`
2. 将对应的密钥替换为你的实际密钥

## 3. 验证配置

启动项目后，如果地图正常显示，说明配置成功。

如果遇到问题，请检查：
1. API密钥和安全密钥是否正确
2. 域名是否在白名单中
3. 网络连接是否正常
4. 浏览器控制台是否有错误信息

## 4. 注意事项

- 高德地图使用GCJ02坐标系（国测局坐标系）
- 免费版有调用次数限制，请根据需要选择合适的套餐
- 生产环境请务必设置正确的域名白名单，避免API密钥泄露
- 建议配置安全密钥以提高API安全性

## 5. 坐标系说明

高德地图使用GCJ02坐标系，坐标格式为 `[经度, 纬度]`：
- 经度在前，纬度在后
- 与GPS坐标系(WGS84)有偏移，但与百度地图(BD09)不同
- 如需转换其他坐标系，请使用高德提供的坐标转换API

## 6. 当前配置

项目已配置的API密钥：
- API Key: 6654ed14b4cb7aae5003dc379fd3f134
- 安全密钥: 830ff4b97ad96c81625c4d44049c8452

这些密钥已经在代码中配置好，可以直接使用。
