<template>
  <div class="layer-control">
    <div class="control-header">
      <h4>图层控制</h4>
      <button class="toggle-btn" @click="collapsed = !collapsed">
        {{ collapsed ? '▼' : '▲' }}
      </button>
    </div>
    
    <div v-if="!collapsed" class="control-content">
      <div class="layer-toggles">
        <label v-for="layer in layers" :key="layer.key" class="layer-toggle">
          <input 
            type="checkbox" 
            :checked="layer.visible" 
            @change="toggleLayer(layer.key)"
          >
          <span class="layer-name">{{ layer.name }}</span>
          <span v-if="layer.description" class="layer-description">{{ layer.description }}</span>
        </label>
      </div>
      
      <div v-if="showMapModes" class="map-modes">
        <h5>地图模式</h5>
        <div class="mode-buttons">
          <button 
            v-for="mode in mapModes" 
            :key="mode.key"
            :class="['mode-btn', { active: currentMode === mode.key }]"
            @click="switchMapMode(mode.key)"
          >
            <span class="mode-icon">{{ mode.icon }}</span>
            <span class="mode-name">{{ mode.name }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';

// Props
const props = defineProps({
  layers: {
    type: Array,
    default: () => [
      { key: 'grid', name: '网格边界', visible: true, description: '显示/隐藏网格边界' },
      { key: 'buildings', name: '建筑物', visible: true, description: '显示/隐藏建筑物轮廓' },
      { key: 'events', name: '事件标记', visible: true, description: '显示/隐藏事件标记点' },
      { key: 'satellite', name: '卫星图', visible: false, description: '切换卫星图层' },
      { key: 'terrain', name: '地形图', visible: false, description: '切换地形图层' }
    ]
  },
  showMapModes: {
    type: Boolean,
    default: true
  },
  currentMode: {
    type: String,
    default: 'normal'
  }
});

// Emits
const emit = defineEmits(['layer-toggle', 'mode-change']);

// 响应式数据
const collapsed = ref(false);

// 地图模式
const mapModes = [
  { key: 'normal', name: '标准', icon: '🗺️' },
  { key: 'satellite', name: '卫星', icon: '🛰️' },
  { key: 'terrain', name: '地形', icon: '🏔️' }
];

// 方法
const toggleLayer = (layerKey) => {
  const layer = props.layers.find(l => l.key === layerKey);
  if (layer) {
    layer.visible = !layer.visible;
    emit('layer-toggle', layerKey, layer.visible);
  }
};

const switchMapMode = (mode) => {
  emit('mode-change', mode);
};
</script>

<style scoped>
.layer-control {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border: 1px solid #e1e8ed;
  min-width: 200px;
  font-size: 14px;
}

.control-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e1e8ed;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.control-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.toggle-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 12px;
  color: #666;
  padding: 2px 4px;
  border-radius: 3px;
  transition: background 0.3s ease;
}

.toggle-btn:hover {
  background: #e9ecef;
}

.control-content {
  padding: 16px;
}

.layer-toggles {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.layer-toggle {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background 0.3s ease;
}

.layer-toggle:hover {
  background: #f8f9fa;
}

.layer-toggle input[type="checkbox"] {
  margin: 0;
  cursor: pointer;
}

.layer-toggle .layer-name {
  font-weight: 500;
  color: #333;
  flex: 1;
}

.layer-toggle .layer-description {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
  display: block;
  width: 100%;
}

.map-modes {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #e1e8ed;
}

.map-modes h5 {
  margin: 0 0 12px 0;
  font-size: 13px;
  font-weight: 600;
  color: #333;
}

.mode-buttons {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.mode-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 1px solid #e1e8ed;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 13px;
}

.mode-btn:hover {
  background: #f8f9fa;
  border-color: #4a90e2;
}

.mode-btn.active {
  background: #4a90e2;
  color: white;
  border-color: #4a90e2;
}

.mode-icon {
  font-size: 14px;
}

.mode-name {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layer-control {
    min-width: 180px;
    font-size: 13px;
  }
  
  .control-header {
    padding: 10px 12px;
  }
  
  .control-content {
    padding: 12px;
  }
  
  .layer-toggle {
    padding: 6px;
  }
}
</style>
