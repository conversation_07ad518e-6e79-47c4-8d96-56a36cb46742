<template>
  <div class="real-name-auth">
    <div class="auth-container">
      <div class="auth-header">
        <div class="header-top">
          <div class="title-section">
            <h1 class="page-title">
              <span class="title-icon">🆔</span>
              实名认证
            </h1>
            <p class="page-description">
              请填写您的真实身份信息进行认证，认证通过后可享受更多社区服务
            </p>
          </div>
          <div class="header-actions">
            <button
              @click="queryAuthStatus"
              class="query-status-btn"
              :disabled="isQuerying"
            >
              <span class="btn-icon">🔍</span>
              {{ isQuerying ? '查询中...' : '查询审批结果' }}
            </button>
          </div>
        </div>
      </div>

      <!-- 认证状态显示 -->
      <div v-if="verificationStatus" class="status-card">
        <div class="status-content">
          <div class="status-icon" :class="getStatusClass()">
            {{ getStatusIcon() }}
          </div>
          <div class="status-info">
            <h3>{{ getStatusTitle() }}</h3>
            <p>{{ getStatusDescription() }}</p>
          </div>
        </div>
      </div>

      <!-- 详细审批结果显示 -->
      <div v-if="authResult" class="auth-result-card">
        <div class="result-header">
          <h3 class="result-title">
            <span class="result-icon">📋</span>
            实名认证审批结果
          </h3>
          <span class="result-status" :class="getResultStatusClass(authResult.status)">
            {{ getResultStatusText(authResult.status) }}
          </span>
        </div>

        <div class="result-content">
          <div class="result-section">
            <h4 class="section-title">提交信息</h4>
            <div class="info-grid">
              <div class="info-item">
                <label>提交姓名：</label>
                <span class="info-value">{{ authResult.submittedName }}</span>
              </div>
              <div class="info-item">
                <label>身份证号：</label>
                <span class="info-value">{{ formatIdCard(authResult.submittedIdCard) }}</span>
              </div>
            </div>
          </div>

          <div class="result-section">
            <h4 class="section-title">审核信息</h4>
            <div class="info-grid">
              <div class="info-item">
                <label>审核状态：</label>
                <span class="info-value status" :class="getResultStatusClass(authResult.status)">
                  {{ getResultStatusText(authResult.status) }}
                </span>
              </div>
              <div class="info-item">
                <label>审核备注：</label>
                <span class="info-value">{{ authResult.reviewRemark || '无' }}</span>
              </div>
              <div class="info-item">
                <label>提交时间：</label>
                <span class="info-value">{{ formatDateTime(authResult.createTime) }}</span>
              </div>
              <div class="info-item">
                <label>审核时间：</label>
                <span class="info-value">{{ formatDateTime(authResult.updateTime) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 认证表单 -->
      <div v-if="!verificationStatus || verificationStatus === 'rejected'" class="auth-form-card">
        <form @submit.prevent="submitVerification" class="auth-form">
          <div class="form-section">
            <h3 class="section-title">身份信息</h3>
            
            <div class="form-group">
              <label for="realName" class="form-label">
                <span class="label-text">真实姓名</span>
                <span class="required">*</span>
              </label>
              <input
                id="realName"
                v-model="formData.submittedName"
                type="text"
                class="form-input"
                placeholder="请输入您的真实姓名"
                required
                :disabled="isSubmitting"
              />
            </div>

            <div class="form-group">
              <label for="idCard" class="form-label">
                <span class="label-text">身份证号码</span>
                <span class="required">*</span>
              </label>
              <input
                id="idCard"
                v-model="formData.submittedIdCard"
                type="text"
                class="form-input"
                placeholder="请输入18位身份证号码"
                maxlength="18"
                required
                :disabled="isSubmitting"
                @input="validateIdCard"
              />
              <div v-if="idCardError" class="error-message">
                {{ idCardError }}
              </div>
            </div>
          </div>

          <!-- 身份证照片上传区域 -->
          <div class="form-section">
            <h3 class="section-title">身份证照片</h3>
            <p class="section-description">
              请上传身份证正面照片，确保照片清晰、完整
            </p>

            <div class="upload-area">
              <div class="upload-card" @click="triggerFileUpload">
                <input
                  ref="fileInput"
                  type="file"
                  accept="image/*"
                  @change="handleFileUpload"
                  style="display: none"
                  :disabled="isSubmitting"
                />
                <div v-if="!uploadedImage" class="upload-placeholder">
                  <div class="upload-icon">📷</div>
                  <p class="upload-text">点击上传身份证正面照片</p>
                  <p class="upload-hint">支持 JPG、PNG 格式，文件大小不超过 5MB</p>
                </div>
                <div v-else class="uploaded-image">
                  <img :src="uploadedImage" alt="身份证照片" />
                  <div class="image-overlay">
                    <button type="button" @click.stop="removeImage" class="remove-btn">
                      ✕
                    </button>
                  </div>
                </div>
              </div>

              <!-- OCR测试按钮 -->
              <div v-if="uploadedImage && uploadedFile" class="ocr-test-section">
                <button
                  type="button"
                  @click="testOCR"
                  class="ocr-test-btn"
                  :disabled="isOCRTesting"
                >
                  <span v-if="isOCRTesting">🔄 OCR识别中...</span>
                  <span v-else>🔍 测试OCR识别</span>
                </button>
              </div>
            </div>
          </div>

          <!-- OCR测试结果显示区域 -->
          <div v-if="ocrTestResult" class="form-section">
            <h3 class="section-title">OCR识别结果</h3>
            <div class="ocr-result-card">
              <div v-if="ocrTestResult.success && ocrTestResult.extractedData" class="ocr-success-result">
                <div class="ocr-field">
                  <span class="field-label">姓名:</span>
                  <span class="field-value">{{ ocrTestResult.extractedData.name || '未识别' }}</span>
                </div>
                <div class="ocr-field">
                  <span class="field-label">身份证号:</span>
                  <span class="field-value">{{ ocrTestResult.extractedData.idCard || '未识别' }}</span>
                </div>
              </div>
              <div v-else-if="ocrTestResult.error" class="ocr-error-result">
                <span class="error-text">{{ ocrTestResult.message || 'OCR识别失败' }}</span>
              </div>
              <div v-else class="ocr-raw-result">
                <pre class="ocr-result-json">{{ JSON.stringify(ocrTestResult, null, 2) }}</pre>
              </div>
            </div>
          </div>

          <!-- OCR确认对话框 -->
          <div v-if="showOcrConfirmDialog" class="ocr-confirm-overlay" @click="closeOcrConfirmDialog">
            <div class="ocr-confirm-dialog" @click.stop>
              <div class="dialog-header">
                <h3>🔍 OCR识别结果确认</h3>
                <button class="close-btn" @click="closeOcrConfirmDialog">×</button>
              </div>
              <div class="dialog-content">
                <p>OCR已成功识别出以下信息，是否自动填写到表单中？</p>
                <div class="ocr-confirm-fields">
                  <div class="confirm-field">
                    <span class="field-label">姓名:</span>
                    <span class="field-value">{{ ocrConfirmData.name }}</span>
                  </div>
                  <div class="confirm-field">
                    <span class="field-label">身份证号:</span>
                    <span class="field-value">{{ ocrConfirmData.idCard }}</span>
                  </div>
                </div>
              </div>
              <div class="dialog-actions">
                <button class="btn-secondary" @click="closeOcrConfirmDialog">取消</button>
                <button class="btn-primary" @click="confirmOcrFill">确认填写</button>
              </div>
            </div>
          </div>

          <!-- 提交按钮 -->
          <div class="form-actions">
            <button
              type="submit"
              class="submit-btn"
              :disabled="!canSubmit || isSubmitting"
              :class="{ loading: isSubmitting }"
            >
              <span v-if="isSubmitting" class="loading-spinner">⏳</span>
              <span v-else class="btn-icon">✓</span>
              {{ isSubmitting ? '提交中...' : '提交认证申请' }}
            </button>
          </div>
        </form>
      </div>

      <!-- 错误信息显示 -->
      <div v-if="errorMessage" class="error-card">
        <div class="error-content">
          <span class="error-icon">⚠️</span>
          <span class="error-text">{{ errorMessage }}</span>
        </div>
      </div>

      <!-- 成功信息显示 -->
      <div v-if="successMessage" class="success-card">
        <div class="success-content">
          <span class="success-icon">✅</span>
          <span class="success-text">{{ successMessage }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { identityApi, identityUtils } from '../../services/identityApi.js';

const router = useRouter();

// 响应式数据
const formData = ref({
  submittedName: '',
  submittedIdCard: '',
  sessionId: ''
});

const uploadedImage = ref(null);
const uploadedFile = ref(null); // 存储原始文件对象
const fileInput = ref(null);
const isSubmitting = ref(false);
const errorMessage = ref('');
const successMessage = ref('');
const idCardError = ref('');
const verificationStatus = ref(null); // null, 'pending', 'approved', 'rejected'
const isOCRTesting = ref(false); // OCR测试状态
const ocrTestResult = ref(null); // OCR测试结果
const showOcrConfirmDialog = ref(false); // 显示OCR确认对话框
const ocrConfirmData = ref({ name: '', idCard: '' }); // OCR确认数据
const authResult = ref(null); // 认证审批结果
const isQuerying = ref(false); // 查询状态

// 计算属性
const canSubmit = computed(() => {
  return formData.value.submittedName.trim() && 
         formData.value.submittedIdCard.trim() && 
         !idCardError.value &&
         uploadedImage.value;
});

// 身份证号码验证
const validateIdCard = () => {
  const idCard = formData.value.submittedIdCard;
  if (!idCard) {
    idCardError.value = '';
    return;
  }

  const validation = identityUtils.validateIdCard(idCard);
  idCardError.value = validation.valid ? '' : validation.message;
};

// 触发文件上传
const triggerFileUpload = () => {
  if (!isSubmitting.value) {
    fileInput.value?.click();
  }
};

// 处理文件上传
const handleFileUpload = (event) => {
  const file = event.target.files[0];
  if (!file) return;

  // 使用工具函数验证文件
  const validation = identityUtils.validateImageFile(file);
  if (!validation.valid) {
    errorMessage.value = validation.message;
    return;
  }

  // 保存原始文件对象
  uploadedFile.value = file;

  // 创建预览
  const reader = new FileReader();
  reader.onload = (e) => {
    uploadedImage.value = e.target.result;
    errorMessage.value = '';
  };
  reader.readAsDataURL(file);
};

// 移除图片
const removeImage = () => {
  uploadedImage.value = null;
  uploadedFile.value = null;
  ocrTestResult.value = null; // 清除OCR测试结果
  showOcrConfirmDialog.value = false; // 关闭确认对话框
  ocrConfirmData.value = { name: '', idCard: '' }; // 清除确认数据
  if (fileInput.value) {
    fileInput.value.value = '';
  }
};

// 关闭OCR确认对话框
const closeOcrConfirmDialog = () => {
  showOcrConfirmDialog.value = false;
  ocrConfirmData.value = { name: '', idCard: '' };
};

// 确认填写OCR识别的数据
const confirmOcrFill = () => {
  if (ocrConfirmData.value.name) {
    formData.value.submittedName = ocrConfirmData.value.name;
  }
  if (ocrConfirmData.value.idCard) {
    formData.value.submittedIdCard = ocrConfirmData.value.idCard;
  }

  closeOcrConfirmDialog();
  successMessage.value = '已自动填写OCR识别的信息到表单中。';

  // 清除错误信息
  errorMessage.value = '';
  idCardError.value = '';
};

// 获取认证状态相关方法
const getStatusClass = () => {
  switch (verificationStatus.value) {
    case 'pending': return 'status-pending';
    case 'approved': return 'status-approved';
    case 'rejected': return 'status-rejected';
    default: return '';
  }
};

const getStatusIcon = () => {
  switch (verificationStatus.value) {
    case 'pending': return '⏳';
    case 'approved': return '✅';
    case 'rejected': return '❌';
    default: return '';
  }
};

const getStatusTitle = () => {
  switch (verificationStatus.value) {
    case 'pending': return '认证审核中';
    case 'approved': return '认证已通过';
    case 'rejected': return '认证被拒绝';
    default: return '';
  }
};

const getStatusDescription = () => {
  switch (verificationStatus.value) {
    case 'pending': return '您的实名认证申请正在审核中，请耐心等待...';
    case 'approved': return '恭喜！您的实名认证已通过，现在可以享受完整的社区服务。';
    case 'rejected': return '很抱歉，您的认证申请被拒绝，请重新提交正确的身份信息。';
    default: return '';
  }
};

// 提交认证申请
const submitVerification = async () => {
  if (!canSubmit.value || isSubmitting.value) return;

  isSubmitting.value = true;
  errorMessage.value = '';
  successMessage.value = '';

  try {
    // 生成会话ID
    formData.value.sessionId = identityUtils.generateSessionId();

    console.log('🔍 提交实名认证申请:', {
      submittedName: formData.value.submittedName,
      submittedIdCard: formData.value.submittedIdCard,
      sessionId: formData.value.sessionId,
      hasImage: !!uploadedImage.value
    });

    // 调用API服务
    const result = await identityApi.submitVerification({
      submittedIdCard: formData.value.submittedIdCard,
      submittedName: formData.value.submittedName,
      sessionId: formData.value.sessionId
    });

    console.log('📦 认证申请结果:', result);

    if (result.success) {
      successMessage.value = result.message || '认证申请提交成功！';
      verificationStatus.value = 'pending';

      // 清空表单
      formData.value.submittedName = '';
      formData.value.submittedIdCard = '';
      removeImage();
    } else {
      errorMessage.value = result.message || '提交失败，请重试';
    }
  } catch (error) {
    console.error('❌ 提交认证申请失败:', error);
    errorMessage.value = '网络错误，请检查网络连接后重试';
  } finally {
    isSubmitting.value = false;
  }
};

// OCR测试功能
const testOCR = async () => {
  if (!uploadedFile.value) {
    errorMessage.value = '请先上传身份证照片';
    return;
  }

  isOCRTesting.value = true;
  ocrTestResult.value = null;
  errorMessage.value = '';

  try {
    // 生成测试用的会话ID
    const testSessionId = identityUtils.generateSessionId();

    console.log('🔍 开始OCR测试:', {
      fileName: uploadedFile.value.name,
      fileSize: uploadedFile.value.size,
      sessionId: testSessionId
    });

    // 调用OCR API
    const result = await identityApi.ocrIdentityCardFile(uploadedFile.value, testSessionId);

    console.log('📦 OCR测试结果:', result);

    // 处理OCR结果
    if (result.success && result.data) {
      // 提取姓名和身份证号，兼容多种字段名
      const extractedData = {
        name: result.data.name || result.data.ocrName || '',
        idCard: result.data.idcard || result.data.idCard || result.data.ocrIdNumber || ''
      };

      // 保存结果用于显示
      ocrTestResult.value = {
        success: true,
        extractedData: extractedData
      };

      // 如果识别到了姓名和身份证号，显示确认对话框
      if (extractedData.name && extractedData.idCard) {
        ocrConfirmData.value = extractedData;
        showOcrConfirmDialog.value = true;
        successMessage.value = 'OCR识别成功！请确认是否自动填写表单。';
      } else {
        successMessage.value = 'OCR识别完成，但部分信息未能识别。';
      }
    } else {
      ocrTestResult.value = {
        success: false,
        message: result.message || 'OCR识别失败'
      };
      errorMessage.value = result.message || 'OCR识别失败';
    }
  } catch (error) {
    console.error('❌ OCR测试失败:', error);
    errorMessage.value = '网络错误，OCR测试失败';
    ocrTestResult.value = {
      error: true,
      message: error.message,
      details: error
    };
  } finally {
    isOCRTesting.value = false;
  }
};

// 查询认证状态
const checkVerificationStatus = async () => {
  try {
    console.log('🔍 开始查询认证状态...');

    // 检查token状态
    const token = localStorage.getItem('auth_data') || sessionStorage.getItem('auth_data') ||
                  localStorage.getItem('auth_token') || localStorage.getItem('userToken');
    console.log('🔑 当前token状态:', {
      hasAuthData: !!(localStorage.getItem('auth_data') || sessionStorage.getItem('auth_data')),
      hasAuthToken: !!localStorage.getItem('auth_token'),
      hasUserToken: !!localStorage.getItem('userToken'),
      tokenPreview: token ? token.substring(0, 20) + '...' : 'null'
    });

    const result = await identityApi.getVerificationInfo();
    console.log('🔍 查询认证状态结果:', result);

    if (result.success && result.data) {
      // 根据返回的数据判断认证状态
      // 这里需要根据实际的API返回格式调整
      if (result.data.realName && result.data.idCard) {
        verificationStatus.value = 'approved';
      }
    }
  } catch (error) {
    console.error('❌ 查询认证状态失败:', error);
    // 不显示错误给用户，因为这只是查询状态，失败了也不影响提交认证
  }
};

// 查询当前用户实名认证审批结果
const queryAuthStatus = async () => {
  if (isQuerying.value) return;

  isQuerying.value = true;
  authResult.value = null;

  try {
    console.log('🔍 查询实名认证审批结果...');

    // 获取token
    const token = localStorage.getItem('auth_data') || sessionStorage.getItem('auth_data');
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    const authData = JSON.parse(token);
    if (!authData.token) {
      throw new Error('认证令牌无效，请重新登录');
    }

    const response = await fetch('http://localhost:8080/api/auth/identity/me', {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${authData.token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('📋 审批结果响应:', result);

    if (result.code === 200) {
      authResult.value = result.data;
      console.log('✅ 成功获取审批结果:', authResult.value);
    } else {
      throw new Error(result.msg || '获取审批结果失败');
    }
  } catch (err) {
    console.error('❌ 查询审批结果失败:', err);
    alert('查询失败: ' + err.message);
  } finally {
    isQuerying.value = false;
  }
};

// 获取审批结果状态文本
const getResultStatusText = (status) => {
  const statusMap = {
    0: '待审核',
    1: '审核失败',
    2: '审核通过'
  };
  return statusMap[status] || '未知状态';
};

// 获取审批结果状态样式类
const getResultStatusClass = (status) => {
  const classMap = {
    0: 'pending',
    1: 'rejected',
    2: 'approved'
  };
  return classMap[status] || '';
};

// 格式化身份证号（隐藏中间部分）
const formatIdCard = (idCard) => {
  if (!idCard || idCard.length < 8) return idCard;
  return idCard.substring(0, 4) + '****' + idCard.substring(idCard.length - 4);
};

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '-';
  try {
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (e) {
    return dateTimeStr;
  }
};

// 页面加载时初始化
onMounted(async () => {
  console.log('🔍 实名认证页面已加载');
  await checkVerificationStatus();
});
</script>

<style scoped>
.real-name-auth {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.auth-container {
  max-width: 800px;
  margin: 0 auto;
}

.auth-header {
  margin-bottom: 30px;
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.title-section {
  flex: 1;
  text-align: center;
}

.header-actions {
  flex-shrink: 0;
}

.query-status-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #FFF7E2, #FFB07C);
  color: #5B5347;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(255, 176, 124, 0.3);
}

.query-status-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #FFB07C, #FF9A55);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 176, 124, 0.4);
}

.query-status-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-icon {
  font-size: 16px;
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 28px;
  font-weight: 700;
  color: var(--text-dark);
  margin: 0 0 16px 0;
}

.title-icon {
  font-size: 32px;
}

.page-description {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.6;
}

/* 状态卡片样式 */
.status-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.status-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-icon {
  font-size: 48px;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
}

.status-approved {
  background: #d4edda;
  color: #155724;
}

.status-rejected {
  background: #f8d7da;
  color: #721c24;
}

.status-info h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
}

.status-info p {
  margin: 0;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* 审批结果卡片样式 */
.auth-result-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-left: 4px solid var(--primary-btn);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
}

.result-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-dark);
}

.result-icon {
  font-size: 20px;
}

.result-status {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.result-status.pending {
  background: #fff3cd;
  color: #856404;
}

.result-status.approved {
  background: #d4edda;
  color: #155724;
}

.result-status.rejected {
  background: #f8d7da;
  color: #721c24;
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.result-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.result-section .section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-dark);
  margin: 0 0 12px 0;
  padding-bottom: 6px;
  border-bottom: 2px solid var(--primary-blue-lighter);
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item label {
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.info-value {
  font-size: 14px;
  color: var(--text-dark);
  font-weight: 500;
}

.info-value.status.pending {
  color: #856404;
}

.info-value.status.approved {
  color: #155724;
}

.info-value.status.rejected {
  color: #721c24;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-top {
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }

  .title-section {
    text-align: center;
  }

  .query-status-btn {
    padding: 10px 16px;
    font-size: 13px;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .auth-result-card {
    padding: 16px;
  }

  .result-section {
    padding: 12px;
  }
}

/* 表单卡片样式 */
.auth-form-card {
  background: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.form-section {
  margin-bottom: 32px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-dark);
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid var(--primary-blue-lighter);
}

.section-description {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0 0 16px 0;
  line-height: 1.5;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-dark);
  margin-bottom: 8px;
}

.required {
  color: #e74c3c;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--medium-gray);
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.form-input:disabled {
  background-color: #f8f9fa;
  cursor: not-allowed;
}

.error-message {
  color: #e74c3c;
  font-size: 12px;
  margin-top: 4px;
}

/* 上传区域样式 */
.upload-area {
  margin-top: 16px;
}

.upload-card {
  border: 2px dashed var(--medium-gray);
  border-radius: 12px;
  padding: 32px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-card:hover {
  border-color: var(--primary-blue);
  background-color: rgba(33, 150, 243, 0.02);
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.upload-icon {
  font-size: 48px;
  opacity: 0.6;
}

.upload-text {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-dark);
  margin: 0;
}

.upload-hint {
  font-size: 12px;
  color: var(--text-secondary);
  margin: 0;
}

.uploaded-image {
  position: relative;
  width: 100%;
  height: 100%;
}

.uploaded-image img {
  max-width: 100%;
  max-height: 300px;
  border-radius: 8px;
  object-fit: contain;
}

.image-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
}

.remove-btn {
  background: rgba(231, 76, 60, 0.9);
  color: white;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
}

.remove-btn:hover {
  background: #e74c3c;
}

/* 表单操作区域 */
.form-actions {
  margin-top: 32px;
  text-align: center;
}

.submit-btn {
  background: linear-gradient(135deg, var(--primary-btn), var(--primary-btn-hover));
  color: #5B5347;
  border: none;
  border-radius: 8px;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  min-width: 200px;
  justify-content: center;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(33, 150, 243, 0.3);
}

.submit-btn:disabled {
  background: var(--medium-gray);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 消息卡片样式 */
.error-card, .success-card {
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.error-card {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
}

.success-card {
  background: #d4edda;
  border: 1px solid #c3e6cb;
}

.error-content, .success-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.error-text {
  color: #721c24;
}

.success-text {
  color: #155724;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .real-name-auth {
    padding: 16px;
  }
  
  .auth-form-card {
    padding: 24px 16px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .upload-card {
    padding: 24px 16px;
    min-height: 150px;
  }
  
  .submit-btn {
    width: 100%;
  }
}

/* OCR测试相关样式 */
.ocr-test-section {
  margin-top: 16px;
  text-align: center;
}

.ocr-test-btn {
  background: linear-gradient(135deg, #FFF7E2, #FFB07C);
  color: #5B5347;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(255, 176, 124, 0.3);
}

.ocr-test-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 176, 124, 0.4);
}

.ocr-test-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.ocr-result-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
}

.ocr-success-result {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.ocr-field {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #e8f5e8;
  border-radius: 6px;
  border-left: 4px solid #28a745;
}

.field-label {
  font-weight: 600;
  color: #495057;
  min-width: 80px;
}

.field-value {
  color: #28a745;
  font-weight: 500;
  font-family: monospace;
}

.ocr-error-result {
  padding: 12px;
  background: #f8d7da;
  border-radius: 6px;
  border-left: 4px solid #dc3545;
}

.ocr-error-result .error-text {
  color: #721c24;
  font-weight: 500;
}

.ocr-raw-result {
  max-height: 200px;
  overflow-y: auto;
}

.ocr-result-json {
  background: #2d3748;
  color: #e2e8f0;
  padding: 16px;
  border-radius: 6px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-all;
  margin: 0;
}

/* OCR确认对话框样式 */
.ocr-confirm-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.ocr-confirm-dialog {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e9ecef;
}

.dialog-header h3 {
  margin: 0;
  color: #495057;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #6c757d;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #f8f9fa;
  color: #495057;
}

.dialog-content {
  padding: 20px 24px;
}

.dialog-content p {
  margin: 0 0 16px;
  color: #495057;
  line-height: 1.5;
}

.ocr-confirm-fields {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
}

.confirm-field {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #007bff;
}

.confirm-field .field-label {
  font-weight: 600;
  color: #495057;
  min-width: 80px;
}

.confirm-field .field-value {
  color: #007bff;
  font-weight: 500;
  font-family: monospace;
  font-size: 14px;
}

.dialog-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 16px 24px 20px;
  border-top: 1px solid #e9ecef;
}

.btn-secondary {
  padding: 8px 20px;
  border: 1px solid #6c757d;
  background: white;
  color: #6c757d;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.btn-secondary:hover {
  background: #6c757d;
  color: white;
}

.btn-primary {
  padding: 8px 20px;
  border: 1px solid #007bff;
  background: #007bff;
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.btn-primary:hover {
  background: #0056b3;
  border-color: #0056b3;
}
</style>
