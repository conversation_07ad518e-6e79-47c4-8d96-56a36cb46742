/* 清新蓝白配色方案 - 蓝色30% 白灰60% 点缀色10% */
:root {
  /* 主色调 - 蓝色系 (30%) */
  --primary-blue: #2196F3;
  --primary-blue-dark: #1976D2;
  --primary-blue-light: #64B5F6;
  --primary-blue-lighter: #E3F2FD;

  /* 辅助蓝色 */
  --secondary-blue: #03A9F4;
  --secondary-blue-dark: #0288D1;
  --secondary-blue-light: #81D4FA;

  /* 中性色 - 白灰系 (60%) */
  --white: #FFFFFF;
  --light-gray: #F8F9FA;
  --gray: #E9ECEF;
  --medium-gray: #DEE2E6;
  --dark-gray: #6C757D;
  --text-dark: #2C3E50;
  --text-light: #495057;

  /* 点缀色 - 清新绿黄 (10%) */
  --accent-green: #4CAF50;
  --accent-green-light: #81C784;
  --accent-green-lighter: #E8F5E8;
  --accent-yellow: #FFC107;
  --accent-yellow-light: #FFD54F;
  --accent-yellow-lighter: #FFF8E1;

  /* 状态色 */
  --success: var(--accent-green);
  --warning: var(--accent-yellow);
  --error: #F44336;
  --info: var(--primary-blue);
}

body {
  background: linear-gradient(
    135deg,
    var(--white) 0%,
    var(--light-gray) 40%,
    var(--primary-blue-lighter) 70%,
    var(--white) 100%
  );
  min-height: 100vh;
}