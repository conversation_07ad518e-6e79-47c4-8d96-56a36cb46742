package com.hfut.xiaozu.vote.info;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 投票主表
 * @TableName vote
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class VoteEntity {
    /**
     * 投票ID
     */
    private Long id;

    /**
     * 投票标题
     */
    private String title;

    /**
     * 投票描述
     */
    private String description;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 最多选择数
     */
    private Integer maxChoices;

    /**
     * 是否匿名（0否1是）
     */
    private Integer isAnonymous;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
