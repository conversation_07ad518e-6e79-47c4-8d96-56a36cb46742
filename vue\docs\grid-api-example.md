# 网格管理API数据格式示例

## 创建网格接口

### 请求URL
```
POST /api/grids
```

### 请求Headers
```
Content-Type: application/json
Authorization: Bearer {token}
```

### 请求Body示例

```json
{
  // ==================== 基本信息 ====================
  "name": "新网格_2024/6/22 21:15:30",           // 网格名称，必填，建议包含时间戳确保唯一性
  "manager": "待分配",                           // 负责人姓名，可选，默认为"待分配"
  "status": "active",                           // 网格状态：active(活跃) | inactive(非活跃)
  "description": "通过地图绘制创建的新网格",        // 网格描述信息，可选

  // ==================== 几何信息 ====================
  "geometry": {
    "type": "Polygon",                          // 几何类型，固定为"Polygon"，符合GeoJSON标准
    "coordinates": [                            // 坐标数组，三维数组结构
      [                                         // 外环坐标（第一个数组是外边界）
        [116.4070, 39.9040],                   // 第1个点：[经度, 纬度]
        [116.4080, 39.9040],                   // 第2个点：[经度, 纬度]
        [116.4080, 39.9050],                   // 第3个点：[经度, 纬度]
        [116.4070, 39.9050],                   // 第4个点：[经度, 纬度]
        [116.4070, 39.9040]                    // 闭合点：必须与第1个点相同
      ]
      // 注意：如果有内环（洞），可以添加更多数组
    ]
  },

  // ==================== 计算属性 ====================
  "properties": {
    "area": 123456,                             // 面积，单位：平方米，系统自动计算
    "perimeter": 1400,                          // 周长，单位：米，系统自动计算
    "centerPoint": {                            // 几何中心点
      "longitude": 116.4075,                    // 中心点经度
      "latitude": 39.9045                      // 中心点纬度
    },
    "boundingBox": {                            // 边界框信息
      "minLng": 116.4070,                       // 最小经度（西边界）
      "maxLng": 116.4080,                       // 最大经度（东边界）
      "minLat": 39.9040,                        // 最小纬度（南边界）
      "maxLat": 39.9050,                        // 最大纬度（北边界）
      "width": 0.001,                           // 宽度（经度跨度）
      "height": 0.001                           // 高度（纬度跨度）
    }
  },

  // ==================== 元数据 ====================
  "metadata": {
    "createdBy": "system",                      // 创建者ID，实际应为当前用户ID
    "createdAt": "2024-06-22T13:15:30.123Z",   // 创建时间，ISO 8601格式
    "source": "manual_drawing",                 // 数据来源：manual_drawing | import | api
    "version": "1.0",                           // 数据版本号
    "coordinateSystem": "WGS84"                 // 坐标系统，默认WGS84 (EPSG:4326)
  },

  // ==================== 业务数据 ====================
  "businessData": {
    "households": 0,                            // 户数，初始为0，后续可更新
    "population": 0,                            // 人口数，初始为0，后续可更新
    "buildings": 0,                             // 建筑数量，初始为0，后续可更新
    "events": 0,                                // 事件数量，初始为0，后续可更新
    "patrols": 0,                               // 巡查次数，初始为0，后续可更新
    "complaints": 0,                            // 投诉数量，初始为0，后续可更新
    "satisfaction": 0                           // 满意度评分，初始为0，后续可更新
  }
}
```

### 响应示例

#### 成功响应
```json
{
  // ==================== 响应状态 ====================
  "success": true,                              // 操作是否成功，布尔值
  "message": "网格创建成功",                      // 响应消息，用于用户提示

  // ==================== 返回数据 ====================
  "data": {
    "id": "grid_1719067530123",                 // 后端生成的唯一网格ID
    "name": "新网格_2024/6/22 21:15:30",        // 网格名称（与请求一致）
    "status": "active",                         // 网格状态（与请求一致）
    "createdAt": "2024-06-22T13:15:30.123Z",    // 实际创建时间（后端时间）
    "updatedAt": "2024-06-22T13:15:30.123Z",    // 最后更新时间（后端时间）

    // 几何信息（与请求一致，后端验证后返回）
    "geometry": {
      "type": "Polygon",                        // 几何类型
      "coordinates": [                          // 坐标数组
        [
          [116.4070, 39.9040],                 // 验证后的坐标点
          [116.4080, 39.9040],
          [116.4080, 39.9050],
          [116.4070, 39.9050],
          [116.4070, 39.9040]                  // 确保多边形闭合
        ]
      ]
    },

    // 计算属性（后端重新计算确保准确性）
    "properties": {
      "area": 123456,                          // 后端精确计算的面积
      "perimeter": 1400,                       // 后端精确计算的周长
      "centerPoint": {                         // 后端计算的中心点
        "longitude": 116.4075,
        "latitude": 39.9045
      }
    },

    // 元数据（后端处理后的信息）
    "metadata": {
      "createdBy": "system",                   // 实际创建者ID
      "source": "manual_drawing",              // 数据来源
      "version": "1.0"                         // 数据版本
    }
  }
}
```

#### 错误响应
```json
{
  // ==================== 响应状态 ====================
  "success": false,                             // 操作失败，固定为false
  "message": "网格创建失败",                      // 用户友好的错误消息

  // ==================== 错误详情 ====================
  "error": {
    "code": "VALIDATION_ERROR",                 // 错误代码，便于程序处理
    "details": "网格名称不能为空",                // 具体错误描述
    "field": "name",                            // 出错的字段名（可选）
    "timestamp": "2024-06-22T13:15:30.123Z"    // 错误发生时间（可选）
  }
}
```

#### 常见错误代码
```json
{
  // 数据验证错误
  "VALIDATION_ERROR": "数据验证失败",
  "REQUIRED_FIELD_MISSING": "必填字段缺失",
  "INVALID_COORDINATES": "坐标格式无效",
  "INVALID_POLYGON": "多边形无效（如自相交）",

  // 业务逻辑错误
  "DUPLICATE_NAME": "网格名称已存在",
  "AREA_TOO_SMALL": "网格面积过小",
  "AREA_TOO_LARGE": "网格面积过大",
  "OVERLAPPING_GRID": "与现有网格重叠",

  // 系统错误
  "DATABASE_ERROR": "数据库操作失败",
  "INTERNAL_ERROR": "服务器内部错误",
  "PERMISSION_DENIED": "权限不足",
  "RATE_LIMIT_EXCEEDED": "请求频率超限"
}
```

## 数据字段说明

### 基本信息
- `name`: 网格名称（必填）
- `manager`: 负责人（可选）
- `status`: 状态（active/inactive）
- `description`: 描述信息

### 几何信息 (geometry)
- `type`: 几何类型，固定为"Polygon"
- `coordinates`: 坐标数组，遵循GeoJSON标准
  - 格式：`[[[lng1, lat1], [lng2, lat2], ...]]`
  - 第一个点和最后一个点必须相同（闭合多边形）
  - 坐标系统：WGS84 (EPSG:4326)

### 属性信息 (properties)
- `area`: 面积（平方米）
- `perimeter`: 周长（米）
- `centerPoint`: 中心点坐标
  - `longitude`: 经度
  - `latitude`: 纬度
- `boundingBox`: 边界框
  - `minLng/maxLng`: 经度范围
  - `minLat/maxLat`: 纬度范围
  - `width/height`: 宽度和高度

### 元数据 (metadata)
- `createdBy`: 创建者ID
- `createdAt`: 创建时间（ISO 8601格式）
- `source`: 数据来源
  - `manual_drawing`: 手动绘制
  - `import`: 数据导入
  - `api`: API创建
- `version`: 数据版本
- `coordinateSystem`: 坐标系统

### 业务数据 (businessData)
- `households`: 户数
- `population`: 人口数
- `buildings`: 建筑数量
- `events`: 事件数量
- `patrols`: 巡查次数
- `complaints`: 投诉数量
- `satisfaction`: 满意度评分

## 其他接口

### 更新网格
```
PUT /api/grids/{id}
```

### 删除网格
```
DELETE /api/grids/{id}
```

### 获取网格列表
```
GET /api/grids?page=1&limit=10&status=active
```

### 获取网格详情
```
GET /api/grids/{id}
```

### 批量操作
```
POST /api/grids/batch
```

### 导出数据
```
GET /api/grids/export?format=json&status=active
```

### 导入数据
```
POST /api/grids/import
Content-Type: multipart/form-data
```

## 注意事项

### 坐标系统和格式
1. **坐标格式**: 使用[经度, 纬度]格式，符合GeoJSON RFC 7946标准
2. **坐标系统**: 默认使用WGS84 (EPSG:4326)，如需其他坐标系请在metadata中说明
3. **坐标精度**: 建议保留6位小数（约1米精度），避免过高精度造成存储浪费
4. **坐标范围**: 经度范围-180到180，纬度范围-90到90

### 多边形规则
1. **多边形闭合**: 第一个点和最后一个点必须完全相同
2. **最少点数**: 至少需要4个点（3个顶点+1个闭合点）
3. **绘制方向**: 建议按顺时针方向绘制外边界
4. **自相交**: 不允许多边形自相交，后端应进行验证
5. **内部洞**: 如有内部洞，按逆时针方向绘制

### 数据验证
1. **前端验证**:
   - 坐标格式检查
   - 多边形闭合检查
   - 必填字段验证
   - 数据类型验证
2. **后端验证**:
   - 几何有效性检查
   - 业务规则验证
   - 权限验证
   - 数据完整性检查

### 性能优化
1. **面积计算**: 后端使用专业地理计算库（如PostGIS、Turf.js）
2. **空间索引**: 建议在数据库中建立空间索引
3. **数据压缩**: 大量坐标点时考虑使用压缩算法
4. **缓存策略**: 对计算结果进行适当缓存

### 错误处理
1. **错误代码**: 提供标准化的错误代码便于程序处理
2. **错误信息**: 提供用户友好的错误描述
3. **字段验证**: 指明具体出错的字段
4. **日志记录**: 记录详细的错误日志用于调试

### 安全考虑
1. **权限控制**: 根据用户角色控制操作权限
2. **数据脱敏**: 敏感信息进行适当脱敏
3. **输入过滤**: 防止SQL注入和XSS攻击
4. **访问限制**: 实施API访问频率限制

### 数据管理
1. **版本控制**: 重要修改保留历史版本
2. **数据备份**: 定期备份重要数据
3. **审计日志**: 记录所有数据变更操作
4. **数据同步**: 多系统间保持数据一致性

## 开发环境配置

在开发环境中，如果后端API不可用，系统会自动返回模拟数据，确保前端功能正常测试。

模拟数据的特征：
- ID格式：`grid_${timestamp}`
- 响应延迟：1秒
- 状态：始终返回成功
- 数据：包含完整的字段结构
