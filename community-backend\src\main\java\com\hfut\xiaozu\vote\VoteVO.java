package com.hfut.xiaozu.vote;

import com.hfut.xiaozu.vote.info.CreateVoteDTO;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VoteVO {

    /**
     * 投票id
     */
    private Long id;

    /**
     * 投票标题
     */
    private String title;

    /**
     * 投票描述
     */
    private String description;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 最多选择数
     */
    private Integer maxChoices;

    /**
     * 是否匿名（0否1是）
     */
    private Integer isAnonymous;

    /**
     * 创建人ID
     */
    private Long creatorId;

    private List<Option> options;

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class Option {
        /**
         * 排序序号
         */
        @NotNull(message = "排序序号不能为空")
        @Min(value = 1, message = "排序序号必须大于等于1")
        private Integer sortOrder;

        /**
         * 选项内容
         */
        @NotBlank(message = "选项内容不能为空")
        @Size(max = 200, message = "选项内容长度不能超过200个字符")
        private String content;

        private Integer number = 0;
    }


    private List<CreateVoteDTO.Scope> scopes;

    private List<Integer> choices;
}
