package com.hfut.xiaozu.user;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hfut.xiaozu.common.constant.HttpStatusConstant;
import com.hfut.xiaozu.user.idv.*;
import com.hfut.xiaozu.user.account.UserMapper;
import com.hfut.xiaozu.user.context.CurrentUser;
import com.hfut.xiaozu.common.Result;
import com.hfut.xiaozu.user.account.UserLoginDTO;
import com.hfut.xiaozu.user.account.UserRegisterDTO;
import com.hfut.xiaozu.user.account.UserAccountEntity;
import com.hfut.xiaozu.user.account.LoginResponseVO;
import com.hfut.xiaozu.security.JwtUtils;
import com.hfut.xiaozu.user.context.UserContextHolder;
import com.hfut.xiaozu.common.oss.AliOssUtil;
import com.hfut.xiaozu.common.ocr.IdCardOCR;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025-06-23
 */
@Service
@Slf4j
public class UserService {

    @Resource
    UserMapper userMapper;

    @Resource
    IdentityVerifySubmitMapper identityVerifySubmitMapper;

    @Resource
    IdentityOcrRecordMapper ocrRecordMapper;

    @Resource
    JwtUtils jwtUtils;

    @Resource
    AliOssUtil aliOssUtil;

    @Resource
    IdCardOCR idCardOCR;

    public Result register(UserRegisterDTO userRegisterDTO) {

        UserAccountEntity userAccountEntity1 = userMapper.getByPhone(userRegisterDTO.getPhone());
        UserAccountEntity userAccountEntity2 = userMapper.getByUsername(userRegisterDTO.getUserName());

        List<String> errors=new ArrayList<>();

        if(userAccountEntity1 != null){
            errors.add("该手机号已经被使用过了");
        }
        if(userAccountEntity2 != null){
            errors.add("该用户名已经被使用过了");
        }

        if(!errors.isEmpty()){
            return Result.fail(errors.toString());
        }

        Integer saveSatus = userMapper.save(userRegisterDTO);
        return Result.ok("注册成功");
    }

    public Result<?> login(UserLoginDTO userLoginDTO) {

        String userName = userLoginDTO.getUserName();
        String password = userLoginDTO.getPassword();
        Integer userType = userLoginDTO.getUserType();

        boolean isPhone = PhoneUtil.isPhone(userName);

        UserAccountEntity user=null;

        if(isPhone){
            user = userMapper.getByPhoneAndPasswordAndUserType(userName,password,userType);
        }else {
            user = userMapper.getByUsernameAndPasswordAndUserType(userName,password,userType);
        }

        if(user==null){
            return Result.fail("账号密码错误");
        }

        String token = jwtUtils.generateToken(new CurrentUser(user.getId(), userType));

        return Result.ok(new LoginResponseVO(token),"登录成功");
    }

    public Result<?> verifyIdentity(@Valid IdentityVerificationDTO identityDTO) {
        CurrentUser currentUser = UserContextHolder.getCurrentUser();

        if(currentUser==null|| !(Objects.equals(currentUser.getRole(),CurrentUser.Resident)) ){
            return Result.fail("只有居民才能进行实名认证");
        }

        List<IdentityVerifySubmitEntity> result = identityVerifySubmitMapper.getByUserId(currentUser.getUserId());

        if(!CollectionUtil.isEmpty(result)){
            return Result.fail("您已提交过实名认证申请");
        }

        IdentityVerifySubmitEntity identityVerifySubmitEntity = new IdentityVerifySubmitEntity();

        identityVerifySubmitEntity.setUserId(currentUser.getUserId());
        identityVerifySubmitEntity.setSessionId(identityDTO.getSessionId());
        identityVerifySubmitEntity.setSubmittedName(identityDTO.getSubmittedName());
        identityVerifySubmitEntity.setSubmittedIdCard(identityDTO.getSubmittedIdCard());
        identityVerifySubmitEntity.setStatus(0);

        identityVerifySubmitMapper.insert(identityVerifySubmitEntity);
        return Result.ok("申请认证成功");
    }

    @Transactional
    public Result<?> processOCR(String sessionId, MultipartFile imageFile) {

        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        if(currentUser==null||currentUser.getUserId()==null){
            return Result.fail("无法使用OCR，请先登录");
        }

        String originalFilename = imageFile.getOriginalFilename();
        String extension = originalFilename.substring(originalFilename.lastIndexOf("."));

        // 生成带时间戳的文件名
        String objectName = StrUtil.format("{}{}",
                DateUtil.format(new Date(), "HHmmssSSS"),
                StrUtil.isNotBlank(extension) ? "." + extension : "");

        try {
            //上传到阿里云oss
            String url = aliOssUtil.upload(imageFile.getBytes(), objectName);
            IdentityOcrRecordEntity record = IdentityOcrRecordEntity.builder()
                    .idCardFrontUrl(url)
                    .userId(currentUser.getUserId())
                    .sessionId(sessionId.toString())
                    .ocrStatus(0)
                    .build();

            if(url==null){
                log.error("图片上传到阿里云oss失败");
                return Result.fail("未知原因");
            }

            ocrRecordMapper.insert(record);

            //转交给百度处理
            String resultJson = idCardOCR.idcardFromUrl(url);

            if(resultJson==null){
                log.error("OCR识别失败，结果为空");
                record.setOcrStatus(2);
                ocrRecordMapper.updateOcrRecord(record);
                return Result.fail("OCR识别失败，无法获取结果");
            }

            //识别成功
            String[] result = idCardOCR.IdCardInfoExtractor(resultJson);
            if(StrUtil.isBlank(result[1])||StrUtil.isBlank(result[0])) {
                log.error("OCR识别失败，{}",result);
                record.setOcrStatus(2);
                ocrRecordMapper.updateOcrRecord(record);
                return Result.fail("OCR识别失败，无法获取结果");
            }

            record.setOcrData(resultJson);
            record.setOcrStatus(1);
            record.setOcrName(result[0]);
            record.setOcrIdNumber(result[1]);

            ocrRecordMapper.updateOcrRecord(record);

            return Result.ok(new OCRResponseVO(result[1],result[0]));
        } catch (IOException e) {
            log.error(e.getMessage());
            return Result.fail("未知原因");
        }
    }

    public Result<?> listIDVRecord(Integer status, Integer pageNum, Integer pageSize) {

        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        if(currentUser==null||currentUser.getRole() == CurrentUser.Resident){
            return Result.fail("没有查询权限");
        }

        PageHelper.startPage(pageNum, pageSize);
        List<IdentityVerifySubmitEntity> list=identityVerifySubmitMapper.listByStatus(status);

        if(CollectionUtil.isEmpty(list)){
            return Result.fail("当前不存在此状态的实名认证申请记录");
        }

        List<IdentityVerifySubmitVListVO> result = new ArrayList<>();
        list.forEach(
                item->{
                    IdentityVerifySubmitVListVO vo = new IdentityVerifySubmitVListVO();

                    BeanUtil.copyProperties(item,vo);
                    vo.setUserName(userMapper.getNameById(item.getUserId()));
                    result.add(vo);
                }
        );
        return Result.ok(result);
    }

    public Result<?> getIDVById(Long id) {
        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        if(currentUser==null||currentUser.getRole() == CurrentUser.Resident){
            return Result.fail("没有查询权限");
        }

        IdentityVerifySubmitEntity result=identityVerifySubmitMapper.getById(id);

        if(result==null){
            return Result.fail("id错误,没有此条记录");
        }

        IdentityVerifySubmitSingleVO vo = new IdentityVerifySubmitSingleVO();

        BeanUtil.copyProperties(result,vo);

        vo.setUserName(userMapper.getNameById(result.getId()));

        return Result.ok(vo);
    }

    @Transactional
    public Result<?> updateIDVById(IdentityVerifySubmitSingleVO dto) {

        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        if(currentUser==null||currentUser.getRole() == CurrentUser.Resident){
            return Result.fail("没有修改权限");
        }

        IdentityVerifySubmitEntity updateResult = identityVerifySubmitMapper.getById(dto.getId());

        updateResult.setStatus(dto.getStatus());
        updateResult.setReviewerId(currentUser.getUserId());
        updateResult.setReviewRemark(dto.getReviewRemark());

        // 实名认证审核通过，需要修改用户实名认证信息
        if(Objects.equals(dto.getStatus(),2)){
            UserAccountEntity user = userMapper.getById(updateResult.getUserId());

            if(user==null){
                return Result.fail("更新失败，用户不存在");
            }

            user.setIdCard(updateResult.getSubmittedIdCard());
            user.setRealName(updateResult.getSubmittedName());
            user.setIsVerified(1);

            int update = userMapper.update(user);
            if(update!=1){
                return Result.fail("更新异常，用户不存在");
            }
        }

        int result = identityVerifySubmitMapper.updateStatusById(updateResult);

        if(result==0){
            return Result.fail("更新失败");
        }
        return Result.ok("更新成功");
    }

    public Result<?> getIDVRecordMyself() {

        CurrentUser currentUser = UserContextHolder.getCurrentUser();

        if(currentUser==null|| !(Objects.equals(currentUser.getRole(),CurrentUser.Resident)) ){
            return Result.fail("只有居民才能查询自身实名认证状态");
        }

        List<IdentityVerifySubmitEntity> result=identityVerifySubmitMapper.getByUserId(currentUser.getUserId());

        if(CollectionUtil.isEmpty(result)){
            return Result.fail("您没有进行实名认证申请");
        }

        if(result.size()>1){
            return Result.fail("您有大于1条实名认证申请记录，错误");
        }

        return Result.ok(result.get(0));
    }

    public Result<?> getMyselfInformation() {
        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        if(currentUser==null){
            return Result.fail("未登录");
        }

        UserAccountEntity me = userMapper.getById(currentUser.getUserId());

        return Result.ok(me);
    }

    public Result<?> listAllGridWorkers() {
        CurrentUser currentUser = UserContextHolder.getCurrentUser();
        if(currentUser==null||!Objects.equals(currentUser.getRole(),CurrentUser.Administrator)){
            return Result.fail("没有查询权限");
        }

        List<UserAccountEntity> gridWorksList = userMapper.listByUserType(CurrentUser.GridWorker);

        if(CollectionUtil.isEmpty(gridWorksList)){
            return Result.fail("不存在网格员");
        }

        List<Map<String,Object>> result = gridWorksList.stream()
                .map(obj->BeanUtil.beanToMap(obj,"id","userName"))
                .toList();

        return Result.ok(result);
    }
}
