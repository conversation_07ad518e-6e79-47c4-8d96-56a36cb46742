------------------------------
-- 用户认证体系
------------------------------
CREATE TABLE user_account (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  user_name VARCHAR(50) NOT NULL COMMENT '用户名（唯一标识）',
  phone VARCHAR(20) NOT NULL COMMENT '手机号码',
  password VARCHAR(200) NOT NULL COMMENT '密码',
  -- password_hash VARCHAR(200) NOT NULL COMMENT '密码哈希值',
  real_name VARCHAR(50) COMMENT '真实姓名',
  id_card VARCHAR(18) COMMENT '身份证号码',
  avatar_url VARCHAR(255) COMMENT '头像URL',
  is_verified TINYINT UNSIGNED DEFAULT 0 COMMENT '是否实名认证 0-否 1-是',
  is_deleted TINYINT UNSIGNED DEFAULT 0 COMMENT '是否删除 0-否 1-是',
  user_type TINYINT UNSIGNED DEFAULT 1 COMMENT '用户类型 1-居民 2-网格员 3-社区管理员' ,
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
  
) COMMENT '用户账户信息表(只有默认业主和网格员，社区管理员才能注册)';

-- 家庭成员信息表（附属业主账户）
CREATE TABLE family_member (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  owner_id BIGINT UNSIGNED NOT NULL COMMENT '业主ID（关联user_account）',
  
  -- 家人基本信息
  member_name VARCHAR(50) NOT NULL COMMENT '家人姓名',
  relationship TINYINT UNSIGNED NOT NULL COMMENT '与业主关系:1-配偶 2-子女 3-父母 4-其他亲属',
  id_card VARCHAR(18) COMMENT '身份证号',
  
  -- 联系方式（可选）
  contact_phone VARCHAR(20) COMMENT '联系电话',
  
  -- 状态管理
  is_primary TINYINT UNSIGNED DEFAULT 0 COMMENT '是否主要家庭成员 0-否 1-是',
  is_deleted TINYINT UNSIGNED DEFAULT 0 COMMENT '是否删除 0-否 1-是',
  
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
  

) COMMENT '业主家庭成员信息表';

-- 实名认证提交表 (仅记录最终提交)
CREATE TABLE identity_verify_submit (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '认证提交ID',
  user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
  session_id VARCHAR(64) NOT NULL COMMENT '认证会话ID（与OCR记录关联）',
  
  -- 用户最终提交的信息
  submitted_name VARCHAR(50) NOT NULL COMMENT '提交的姓名',
  submitted_id_card VARCHAR(18) NOT NULL COMMENT '提交的身份证号',

  status     TINYINT UNSIGNED                NOT NULL DEFAULT 0 COMMENT '当前状态: 0-待审核,1-审核失败,2-审核通过',
  review_remark   VARCHAR(500)     COMMENT '审核结果详情（如失败原因）',
  reviewer_id   BIGINT UNSIGNED    COMMENT '审核人ID（关联用户表）',
  
  -- 公安验证结果
  -- verified_name VARCHAR(50) COMMENT '公安验证的姓名',
  -- verified_id_card VARCHAR(18) COMMENT '公安验证的身份证号',
  -- verify_status TINYINT UNSIGNED DEFAULT 0 COMMENT '验证状态: 0-未验证 1-成功 2-失败',
  -- verify_time DATETIME COMMENT '验证完成时间',
  -- fail_reason VARCHAR(200) COMMENT '验证失败原因',
  
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'

) COMMENT '实名认证提交表（最终提交记录）';

-- OCR识别记录表 (记录每次OCR操作)
CREATE TABLE identity_ocr_record (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT 'OCR记录ID',
  user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
  session_id VARCHAR(64) NOT NULL COMMENT '认证会话ID（每次打开表单生成一个会话ID）',

  id_card_front_url VARCHAR(255) COMMENT '身份证正面照URL',

  ocr_data JSON  COMMENT 'OCR完整识别结果(JSON格式)',
  ocr_name VARCHAR(50) COMMENT 'OCR识别的姓名',
  ocr_id_number VARCHAR(18) COMMENT 'OCR识别的身份证号',

  ocr_status TINYINT UNSIGNED DEFAULT 0 COMMENT 'OCR处理状态: 0-处理中 1-成功 2-失败',
  error_message VARCHAR(200) COMMENT 'OCR失败原因',

  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'

) COMMENT '身份证OCR识别记录表（每次OCR调用记录）';

------------------------------
-- 社区与房屋体系
------------------------------

-- 小区信息表
CREATE TABLE community_info (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  community_name VARCHAR(100) NOT NULL COMMENT '小区名称',
  address_detail VARCHAR(200) NOT NULL COMMENT '详细地址',
  geo_json JSON COMMENT '中心点GeoJSON数据',

  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
  
) COMMENT '小区信息表';

-- 楼栋信息表 
CREATE TABLE building_info (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  community_id BIGINT UNSIGNED NOT NULL COMMENT '小区ID',
  building_code VARCHAR(20) NOT NULL COMMENT '楼栋编号',
  building_name VARCHAR(50) COMMENT '楼栋名称',
  geo_json JSON COMMENT '坐标GeoJSON数据',

  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '楼栋信息表';

-- 单元信息表 (新增)
CREATE TABLE unit_info (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  building_id BIGINT UNSIGNED NOT NULL COMMENT '楼栋ID',
  unit_code VARCHAR(10) NOT NULL COMMENT '单元编号',
  floor_count SMALLINT UNSIGNED COMMENT '楼层数',

  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'

) COMMENT '单元信息表';

-- 房屋信息表 (优化)
CREATE TABLE house_info (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  unit_id BIGINT UNSIGNED NOT NULL COMMENT '单元ID',

  house_number VARCHAR(20) NOT NULL COMMENT '门牌号',
  area_size DECIMAL(8,2) UNSIGNED COMMENT '房屋面积(m²)',
  is_occupied TINYINT UNSIGNED DEFAULT 0 COMMENT '是否已入住 0-否 1-是',

  -- qr_code_url VARCHAR(255) COMMENT '房屋专属二维码URL',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'

) COMMENT '房屋信息表';

-- 用户房屋绑定表
CREATE TABLE user_house_binding (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
  house_id BIGINT UNSIGNED NOT NULL COMMENT '房屋ID',
  
  -- 绑定关系信息
  relation_type TINYINT UNSIGNED NOT NULL COMMENT '关系类型: 1-业主 2-租客' ,
  
  -- 绑定状态管理
  status TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '绑定状态: 1-待审核 2-已生效 3-已拒绝 4-已解绑',
  
  -- 提交信息
  submit_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
  
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  updated_by BIGINT UNSIGNED COMMENT '最后更新人ID',
  remark VARCHAR(255) COMMENT '审核备注(拒绝原因等)'
  
) COMMENT '用户房屋绑定关系表（核心关系状态）';

--  物业审核登记表
CREATE TABLE property_audit_log (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  
  -- 关联信息
  binding_id BIGINT UNSIGNED NOT NULL COMMENT '绑定的关系记录ID',
  
  -- 操作信息
  operation_type TINYINT UNSIGNED NOT NULL COMMENT '操作类型: 1-提交绑定 2-审核通过 3-审核拒绝 4-变更关系 5-解绑',
  operator_id BIGINT UNSIGNED NOT NULL COMMENT '操作人ID',
  operation_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  operation_remark VARCHAR(255) COMMENT '操作备注(审核意见/解绑原因等)',
  
  -- 操作前后状态（记录变更轨迹）
  prev_status TINYINT UNSIGNED COMMENT '操作前状态',
  new_status TINYINT UNSIGNED COMMENT '操作后状态',
  
  -- 附加信息
  attachment VARCHAR(255) COMMENT '操作附件URL(如审核凭证)'

) COMMENT '物业审核操作登记表（记录所有审核操作）';


------------------------------
-- 车辆管理体系
------------------------------
CREATE TABLE vehicle_info (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  user_id BIGINT UNSIGNED NOT NULL COMMENT '所属用户ID',
  plate_number VARCHAR(30) NOT NULL COMMENT '车牌号码',
  brand VARCHAR(50) COMMENT '车辆品牌',
  color VARCHAR(20) COMMENT '车辆颜色',
  is_in_community TINYINT UNSIGNED DEFAULT 1 COMMENT '是否小区内车辆 0-否 1-是',

  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'

) COMMENT '车辆信息表';


-- 车辆房屋绑定表
CREATE TABLE vehicle_house_binding (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  vehicle_id BIGINT UNSIGNED NOT NULL COMMENT '车辆ID',
  house_id BIGINT UNSIGNED NOT NULL COMMENT '房屋ID',
  
  -- 绑定状态
  status TINYINT UNSIGNED DEFAULT 1 COMMENT '绑定状态: 1-待审核 2-已生效 3-已拒绝 4-已解绑',
  
  -- 车位信息
  space_number VARCHAR(20) NOT NULL COMMENT '车位编号',
  space_type TINYINT UNSIGNED DEFAULT 1 COMMENT '车位类型: 1-固定车位 2-临时车位',
  
  -- 审核信息w
  approved_by BIGINT UNSIGNED COMMENT '审核人ID',
  approval_remark VARCHAR(255) COMMENT '审核备注',
  
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '车辆-房屋绑定关系表（支持多房屋绑定）';

-- 车辆审核记录表 (新增)
CREATE TABLE vehicle_audit_log (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  binding_id BIGINT UNSIGNED NOT NULL COMMENT '绑定关系ID',
  
  -- 操作信息
  operation_type TINYINT UNSIGNED NOT NULL COMMENT '操作类型: 1-提交绑定 2-审核通过 3-审核拒绝 4-变更绑定 5-解绑',
  operator_id BIGINT UNSIGNED NOT NULL COMMENT '操作人ID',
  operation_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  operation_remark VARCHAR(255) COMMENT '操作备注',
  
  -- 操作前后状态
  prev_status TINYINT UNSIGNED COMMENT '操作前状态',
  new_status TINYINT UNSIGNED COMMENT '操作后状态'
) COMMENT '车辆绑定审核操作记录表';
------------------------------
-- 事件管理体系
------------------------------
-- 事件主表 (核心信息)
CREATE TABLE incident_record (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  reporter_id BIGINT UNSIGNED NOT NULL COMMENT '上报人ID',
  title VARCHAR(100) NOT NULL COMMENT '事件标题',
  description TEXT NOT NULL COMMENT '事件描述',
  category_type TINYINT UNSIGNED NOT NULL DEFAULT 4 COMMENT '分类类型: 1-环境卫生 2-设施损坏 3-安全问题 4-其他',
  priority TINYINT UNSIGNED DEFAULT 2 COMMENT '优先级: 1-紧急 2-高 3-中 4-低',
  
  -- 位置信息
  location_description VARCHAR(255) COMMENT '位置描述',
  location_geojson JSON COMMENT '位置GeoJSON',
  community_id BIGINT UNSIGNED COMMENT '所属小区ID',
  
  -- 状态管理
  status TINYINT UNSIGNED DEFAULT 1 COMMENT '状态: 1-待分派 2-处理中 3-已完成 4-已关闭',
  handler_id BIGINT UNSIGNED COMMENT '当前处理人ID',
  
  -- 时间管理
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '上报时间',
  assign_time DATETIME COMMENT '分派时间',
  complete_time DATETIME COMMENT '完成时间',

  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '事件记录主表';

-- 事件处理记录表 (简化)
CREATE TABLE incident_process_record (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  incident_id BIGINT UNSIGNED NOT NULL COMMENT '事件ID',
  status TINYINT UNSIGNED NOT NULL COMMENT '操作: 1-上报 2-分派 3-开始处理 4-完成 5-关闭',
  
  -- 操作人信息
  user_id BIGINT UNSIGNED COMMENT '操作人ID',
  user_role TINYINT UNSIGNED COMMENT '操作人角色: 1-物业人员 2-网格员',
  
  -- 处理信息
  remark TEXT COMMENT '处理备注',
  
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间'

) COMMENT '事件处理记录表';

------------------------------
-- 网格管理体系
------------------------------
-- 小区网格表 (保留GIS边界)
CREATE TABLE community_grid (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '网格ID',
  community_id BIGINT UNSIGNED NOT NULL COMMENT '所属小区ID',
  grid_name VARCHAR(50) NOT NULL COMMENT '网格名称',
  boundary_geojson JSON NOT NULL COMMENT '网格边界GeoJSON',
  responsible_id BIGINT UNSIGNED COMMENT '负责人ID(网格员)',

  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
  
) COMMENT '小区网格划分表';


-- 全息档案主表 (通用设计)
CREATE TABLE grid_item_archive (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '档案ID',
  archive_type TINYINT UNSIGNED NOT NULL COMMENT '档案类型: 1-人员 2-房屋 3-车辆 4-单位 5-建筑物 6-党组织',
  related_id BIGINT UNSIGNED COMMENT '关联ID',
  grid_id BIGINT UNSIGNED COMMENT '所属网格ID',
  
  -- 基础信息 (所有档案共享)
  name VARCHAR(100) NOT NULL COMMENT '名称',
  description TEXT COMMENT '描述信息',
  is_special TINYINT UNSIGNED DEFAULT 0 COMMENT '是否重点关注 0-否 1-是',
  
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
  
) COMMENT '全息档案主表';

-- 特殊人群关怀记录表
CREATE TABLE special_care_log (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
  archive_id BIGINT UNSIGNED NOT NULL COMMENT '档案ID',
  care_type TINYINT UNSIGNED NOT NULL COMMENT '关怀类型: 1-短信关怀 2-上门探访 3-电话慰问',
  content VARCHAR(200) COMMENT '关怀内容',
  care_time DATETIME COMMENT '关怀时间',
  care_by BIGINT UNSIGNED COMMENT '操作人ID'
  
) COMMENT '特殊人群关怀记录表';

-- 档案变更日志表
CREATE TABLE archive_change_log (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
  archive_id BIGINT UNSIGNED NOT NULL COMMENT '档案ID',
  operation TINYINT UNSIGNED NOT NULL COMMENT '操作类型: 1-新增 2-修改 3-删除',
  operator_id BIGINT UNSIGNED NOT NULL COMMENT '操作人ID',
  
  remark VARCHAR(200) COMMENT '变更描述',
  
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间'
  
) COMMENT '档案变更日志表';

------------------------------
-- 任务巡查体系
------------------------------
-- 巡查任务表
CREATE TABLE patrol_task (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '任务ID',
  title VARCHAR(100) NOT NULL COMMENT '任务名称',
  grid_id BIGINT UNSIGNED NOT NULL COMMENT '所属网格ID',
  description TEXT COMMENT '任务描述',
  
  -- 任务安排
  start_time DATETIME COMMENT '建议开始时间',
  end_time DATETIME COMMENT '建议结束时间',
  
  -- 状态管理
  status TINYINT UNSIGNED DEFAULT 1 COMMENT '状态: 1-待分配 2-进行中 3-已完成 4-已取消',
  assignee_id BIGINT UNSIGNED COMMENT '执行人ID(网格员)',
  assigned_time DATETIME COMMENT '分配时间',
  
  creator_id BIGINT UNSIGNED NOT NULL COMMENT '创建人ID',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '巡查任务表';

-- 任务执行记录表
CREATE TABLE task_execution (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '执行记录ID',
  task_id BIGINT UNSIGNED NOT NULL COMMENT '任务ID',
  executor_id BIGINT UNSIGNED NOT NULL COMMENT '执行人ID',
  
  -- 执行状态
  start_time DATETIME COMMENT '实际开始时间',
  end_time DATETIME COMMENT '实际完成时间',
  completion_status TINYINT UNSIGNED DEFAULT 1 COMMENT '完成情况: 1-未完成 2-已完成 3-部分完成',
  notes TEXT COMMENT '执行备注',
  
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '记录时间'
  
) COMMENT '任务执行记录表';