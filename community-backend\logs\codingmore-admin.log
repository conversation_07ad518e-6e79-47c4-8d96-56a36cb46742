2025-06-30 15:19:24.039 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 53 ] - Starting XiaozuApplication using Java 17.0.2 with PID 31356 (D:\gitlab\xiangmu\community-backend\target\classes started by joy0531 in D:\gitlab\xiangmu\community-backend)
2025-06-30 15:19:24.049 [ restartedMain ] - [ DEBUG ] [ com.hfut.xiaozu.XiaozuApplication : 54 ] - Running with Spring Boot v3.4.7, Spring v6.2.8
2025-06-30 15:19:24.050 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 652 ] - No active profile set, falling back to 1 default profile: "default"
2025-06-30 15:19:24.090 [ restartedMain ] - [ INFO  ] [ o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor : 252 ] - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-30 15:19:24.092 [ restartedMain ] - [ INFO  ] [ o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor : 252 ] - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-30 15:19:24.669 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 247 ] - Searching for mappers annotated with @Mapper
2025-06-30 15:19:24.669 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 251 ] - Using auto-configuration base package 'com.hfut.xiaozu'
2025-06-30 15:19:24.830 [ restartedMain ] - [ DEBUG ] [ org.apache.ibatis.logging.LogFactory : 112 ] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-06-30 15:19:24.843 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.class]
2025-06-30 15:19:24.843 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.class]
2025-06-30 15:19:24.844 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.class]
2025-06-30 15:19:24.844 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.class]
2025-06-30 15:19:24.844 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.class]
2025-06-30 15:19:24.844 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.class]
2025-06-30 15:19:24.844 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.class]
2025-06-30 15:19:24.844 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.class]
2025-06-30 15:19:24.844 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.class]
2025-06-30 15:19:24.844 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.class]
2025-06-30 15:19:24.844 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.class]
2025-06-30 15:19:24.844 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.class]
2025-06-30 15:19:24.844 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.class]
2025-06-30 15:19:24.844 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.class]
2025-06-30 15:19:24.844 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.class]
2025-06-30 15:19:24.846 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleHouseBindingMapper' and 'com.hfut.xiaozu.car.bind.VehicleHouseBindingMapper' mapperInterface
2025-06-30 15:19:24.848 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleInfoMapper' and 'com.hfut.xiaozu.car.info.VehicleInfoMapper' mapperInterface
2025-06-30 15:19:24.849 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'familyMemberMapper' and 'com.hfut.xiaozu.family.info.FamilyMemberMapper' mapperInterface
2025-06-30 15:19:24.849 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userHouseBindingMapper' and 'com.hfut.xiaozu.house.binding.UserHouseBindingMapper' mapperInterface
2025-06-30 15:19:24.850 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityGridMapper' and 'com.hfut.xiaozu.house.grid.CommunityGridMapper' mapperInterface
2025-06-30 15:19:24.850 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'gridItemArchiveMapper' and 'com.hfut.xiaozu.house.grid.GridItemArchiveMapper' mapperInterface
2025-06-30 15:19:24.851 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'buildingInfoMapper' and 'com.hfut.xiaozu.house.information.BuildingInfoMapper' mapperInterface
2025-06-30 15:19:24.851 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityInfoMapper' and 'com.hfut.xiaozu.house.information.CommunityInfoMapper' mapperInterface
2025-06-30 15:19:24.852 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'houseInfoMapper' and 'com.hfut.xiaozu.house.information.HouseInfoMapper' mapperInterface
2025-06-30 15:19:24.852 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'unitInfoMapper' and 'com.hfut.xiaozu.house.information.UnitInfoMapper' mapperInterface
2025-06-30 15:19:24.853 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentProcessRecordMapper' and 'com.hfut.xiaozu.incident.process.IncidentProcessRecordMapper' mapperInterface
2025-06-30 15:19:24.853 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentRecordMapper' and 'com.hfut.xiaozu.incident.record.IncidentRecordMapper' mapperInterface
2025-06-30 15:19:24.854 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userMapper' and 'com.hfut.xiaozu.user.account.UserMapper' mapperInterface
2025-06-30 15:19:24.854 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityOcrRecordMapper' and 'com.hfut.xiaozu.user.idv.IdentityOcrRecordMapper' mapperInterface
2025-06-30 15:19:24.854 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityVerifySubmitMapper' and 'com.hfut.xiaozu.user.idv.IdentityVerifySubmitMapper' mapperInterface
2025-06-30 15:19:25.273 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 111 ] - Tomcat initialized with port 8080 (http)
2025-06-30 15:19:25.285 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardService : 168 ] - Starting service [Tomcat]
2025-06-30 15:19:25.285 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardEngine : 168 ] - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-30 15:19:25.333 [ restartedMain ] - [ INFO  ] [ o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] : 168 ] - Initializing Spring embedded WebApplicationContext
2025-06-30 15:19:25.334 [ restartedMain ] - [ INFO  ] [ o.s.b.w.s.c.ServletWebServerApplicationContext : 301 ] - Root WebApplicationContext: initialization completed in 1242 ms
2025-06-30 15:19:25.408 [ restartedMain ] - [ INFO  ] [ c.a.d.s.b.a.DruidDataSourceAutoConfigure : 55 ] - Init DruidDataSource
2025-06-30 15:19:25.776 [ restartedMain ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 1009 ] - {dataSource-1} inited
2025-06-30 15:19:25.911 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.xml]'
2025-06-30 15:19:25.919 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.xml]'
2025-06-30 15:19:25.928 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.xml]'
2025-06-30 15:19:25.936 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.xml]'
2025-06-30 15:19:25.942 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.xml]'
2025-06-30 15:19:25.951 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.xml]'
2025-06-30 15:19:25.961 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.xml]'
2025-06-30 15:19:25.967 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.xml]'
2025-06-30 15:19:25.972 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.xml]'
2025-06-30 15:19:25.978 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.xml]'
2025-06-30 15:19:25.984 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.xml]'
2025-06-30 15:19:25.989 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.xml]'
2025-06-30 15:19:25.994 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.xml]'
2025-06-30 15:19:26.000 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.xml]'
2025-06-30 15:19:26.004 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.xml]'
2025-06-30 15:19:37.591 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 319 ] - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-06-30 15:19:37.785 [ restartedMain ] - [ INFO  ] [ o.s.b.d.autoconfigure.OptionalLiveReloadServer : 59 ] - LiveReload server is running on port 35729
2025-06-30 15:19:37.832 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 243 ] - Tomcat started on port 8080 (http) with context path '/'
2025-06-30 15:19:37.840 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 59 ] - Started XiaozuApplication in 14.123 seconds (process running for 15.413)
2025-06-30 15:19:38.446 [ http-nio-8080-exec-1 ] - [ INFO  ] [ o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] : 168 ] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-30 15:19:38.447 [ http-nio-8080-exec-1 ] - [ INFO  ] [ org.springframework.web.servlet.DispatcherServlet : 532 ] - Initializing Servlet 'dispatcherServlet'
2025-06-30 15:19:38.448 [ http-nio-8080-exec-1 ] - [ INFO  ] [ org.springframework.web.servlet.DispatcherServlet : 554 ] - Completed initialization in 1 ms
2025-06-30 15:19:38.916 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:19:38.921 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:19:39.359 [ http-nio-8080-exec-5 ] - [ INFO  ] [ org.springdoc.api.AbstractOpenApiResource : 390 ] - Init duration for springdoc-openapi is: 357 ms
2025-06-30 15:19:55.404 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/auth/list
2025-06-30 15:19:55.408 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/list
2025-06-30 15:19:55.887 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/houses/binding/list
2025-06-30 15:19:55.894 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/list
2025-06-30 15:19:56.491 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/car/binding/list/1
2025-06-30 15:19:56.496 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/binding/list/1
2025-06-30 15:19:58.218 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/list
2025-06-30 15:19:58.618 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/list
2025-06-30 15:20:26.160 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/car/list/me
2025-06-30 15:20:26.170 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-06-30 15:20:26.453 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/family/list/me
2025-06-30 15:20:26.459 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-06-30 15:21:18.317 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/family
2025-06-30 15:21:18.321 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/family
2025-06-30 15:21:19.666 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-06-30 15:21:26.865 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: PUT /api/family
2025-06-30 15:21:28.075 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-06-30 15:21:37.118 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-06-30 15:21:39.711 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/houses/binding/me
2025-06-30 15:21:39.717 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-06-30 15:21:39.731 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/communities
2025-06-30 15:21:39.735 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-06-30 15:21:40.103 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-06-30 15:21:46.511 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:21:46.512 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:21:48.705 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:21:48.705 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:21:49.196 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:21:49.197 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:21:56.713 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:21:56.714 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:22:35.237 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:22:35.238 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:22:35.766 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:22:35.767 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:22:36.389 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:22:36.390 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:22:36.816 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:22:36.816 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:22:37.462 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:22:37.467 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:22:37.881 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:22:37.882 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:22:38.259 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:22:38.259 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:22:41.134 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:22:41.134 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:22:42.574 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:22:42.575 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:22:42.964 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:22:42.965 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:22:47.424 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:22:47.424 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:22:52.603 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:22:52.604 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:22:53.228 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:22:53.228 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:22:53.939 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:22:53.939 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:23:31.135 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-06-30 15:23:31.147 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-06-30 15:23:31.578 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-06-30 15:23:32.502 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-06-30 15:25:08.264 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-06-30 15:25:08.276 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-06-30 15:25:12.166 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-06-30 15:25:13.264 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-06-30 15:25:14.302 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-06-30 15:26:33.771 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:26:33.772 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:26:42.822 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:26:42.822 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:26:59.208 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:26:59.209 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:27:08.335 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:27:08.336 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:27:37.430 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:27:37.430 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:27:38.131 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:27:38.131 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:27:39.181 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:27:39.181 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:28:37.019 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/incident
2025-06-30 15:31:36.280 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/incident
2025-06-30 15:31:36.302 [ http-nio-8080-exec-10 ] - [ ERROR ] [ druid.sql.Statement : 148 ] - {conn-10005, pstmt-20102} execute error. insert into incident_record (reporter_id, title, description, category_type, priority
	, location_description, location_geojson, community_id, status, handler_id
	, assign_time, complete_time)
values (59, '消防设施损坏', '消防栓玻璃破裂', 3, 2
	, '11号楼南楼三楼楼梯口。', null, 0, 1, null
	, null, null)
com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Cannot create a JSON value from a string with CHARACTER SET 'binary'.
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446)
	at com.alibaba.druid.filter.FilterAdapter.preparedStatement_execute(FilterAdapter.java:1063)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.wall.WallFilter.preparedStatement_execute(WallFilter.java:639)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at jdk.internal.reflect.GeneratedMethodAccessor115.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at jdk.proxy4/jdk.proxy4.$Proxy132.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy130.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	at jdk.proxy2/jdk.proxy2.$Proxy86.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:62)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:141)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at jdk.proxy3/jdk.proxy3.$Proxy99.insert(Unknown Source)
	at com.hfut.xiaozu.incident.incidentService.reportIncident(incidentService.java:116)
	at com.hfut.xiaozu.incident.incidentController.reportIncident(incidentController.java:48)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-30 15:31:36.414 [ http-nio-8080-exec-10 ] - [ ERROR ] [ o.a.c.c.C.[.[localhost].[/].[dispatcherServlet] : 170 ] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed: org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Cannot create a JSON value from a string with CHARACTER SET 'binary'.
### The error may exist in file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.xml]
### The error may involve com.hfut.xiaozu.incident.record.IncidentRecordMapper.insert-Inline
### The error occurred while setting parameters
### SQL: insert into incident_record         ( reporter_id,title,description,category_type,priority,         location_description,location_geojson,community_id,status,handler_id,         assign_time,complete_time)         values (?,?,?,?,?,         ?,?,?,?,?,         ?,?)
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Cannot create a JSON value from a string with CHARACTER SET 'binary'.
; Data truncation: Cannot create a JSON value from a string with CHARACTER SET 'binary'.] with root cause
com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Cannot create a JSON value from a string with CHARACTER SET 'binary'.
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446)
	at com.alibaba.druid.filter.FilterAdapter.preparedStatement_execute(FilterAdapter.java:1063)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.wall.WallFilter.preparedStatement_execute(WallFilter.java:639)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at jdk.internal.reflect.GeneratedMethodAccessor115.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at jdk.proxy4/jdk.proxy4.$Proxy132.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy130.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	at jdk.proxy2/jdk.proxy2.$Proxy86.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:62)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:141)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at jdk.proxy3/jdk.proxy3.$Proxy99.insert(Unknown Source)
	at com.hfut.xiaozu.incident.incidentService.reportIncident(incidentService.java:116)
	at com.hfut.xiaozu.incident.incidentController.reportIncident(incidentController.java:48)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-30 15:31:36.414 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /error
2025-06-30 15:31:47.088 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/incident
2025-06-30 15:31:47.090 [ http-nio-8080-exec-4 ] - [ ERROR ] [ druid.sql.Statement : 148 ] - {conn-10005, pstmt-20103} execute error. insert into incident_record (reporter_id, title, description, category_type, priority
	, location_description, location_geojson, community_id, status, handler_id
	, assign_time, complete_time)
values (59, '消防设施损坏', '消防栓玻璃破裂', 3, 2
	, '11号楼南楼三楼楼梯口。', null, 52, 1, null
	, null, null)
com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Cannot create a JSON value from a string with CHARACTER SET 'binary'.
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446)
	at com.alibaba.druid.filter.FilterAdapter.preparedStatement_execute(FilterAdapter.java:1063)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.wall.WallFilter.preparedStatement_execute(WallFilter.java:639)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at jdk.internal.reflect.GeneratedMethodAccessor115.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at jdk.proxy4/jdk.proxy4.$Proxy132.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy130.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	at jdk.proxy2/jdk.proxy2.$Proxy86.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:62)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:141)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at jdk.proxy3/jdk.proxy3.$Proxy99.insert(Unknown Source)
	at com.hfut.xiaozu.incident.incidentService.reportIncident(incidentService.java:116)
	at com.hfut.xiaozu.incident.incidentController.reportIncident(incidentController.java:48)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-30 15:31:47.093 [ http-nio-8080-exec-4 ] - [ ERROR ] [ o.a.c.c.C.[.[localhost].[/].[dispatcherServlet] : 170 ] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed: org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Cannot create a JSON value from a string with CHARACTER SET 'binary'.
### The error may exist in file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.xml]
### The error may involve com.hfut.xiaozu.incident.record.IncidentRecordMapper.insert-Inline
### The error occurred while setting parameters
### SQL: insert into incident_record         ( reporter_id,title,description,category_type,priority,         location_description,location_geojson,community_id,status,handler_id,         assign_time,complete_time)         values (?,?,?,?,?,         ?,?,?,?,?,         ?,?)
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Cannot create a JSON value from a string with CHARACTER SET 'binary'.
; Data truncation: Cannot create a JSON value from a string with CHARACTER SET 'binary'.] with root cause
com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Cannot create a JSON value from a string with CHARACTER SET 'binary'.
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446)
	at com.alibaba.druid.filter.FilterAdapter.preparedStatement_execute(FilterAdapter.java:1063)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.wall.WallFilter.preparedStatement_execute(WallFilter.java:639)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at jdk.internal.reflect.GeneratedMethodAccessor115.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at jdk.proxy4/jdk.proxy4.$Proxy132.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy130.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	at jdk.proxy2/jdk.proxy2.$Proxy86.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:62)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:141)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at jdk.proxy3/jdk.proxy3.$Proxy99.insert(Unknown Source)
	at com.hfut.xiaozu.incident.incidentService.reportIncident(incidentService.java:116)
	at com.hfut.xiaozu.incident.incidentController.reportIncident(incidentController.java:48)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-30 15:31:47.093 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /error
2025-06-30 15:32:25.216 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:32:25.216 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:32:26.739 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:32:26.739 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:32:27.725 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:32:27.725 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:32:29.870 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:32:29.870 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:32:31.600 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:32:31.600 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:32:33.478 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:32:33.478 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:32:35.187 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:32:35.187 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:32:36.478 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 15:32:36.478 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 15:32:41.844 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/incident
2025-06-30 15:32:41.848 [ http-nio-8080-exec-10 ] - [ ERROR ] [ druid.sql.Statement : 148 ] - {conn-10001, pstmt-20108} execute error. insert into incident_record (reporter_id, title, description, category_type, priority
	, location_description, location_geojson, community_id, status, handler_id
	, assign_time, complete_time)
values (59, '消防设施损坏', '消防栓玻璃破裂', 3, 2
	, '11号楼南楼三楼楼梯口。', null, 52, 1, null
	, null, null)
com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Cannot create a JSON value from a string with CHARACTER SET 'binary'.
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446)
	at com.alibaba.druid.filter.FilterAdapter.preparedStatement_execute(FilterAdapter.java:1063)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.wall.WallFilter.preparedStatement_execute(WallFilter.java:639)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at jdk.internal.reflect.GeneratedMethodAccessor115.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at jdk.proxy4/jdk.proxy4.$Proxy132.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy130.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	at jdk.proxy2/jdk.proxy2.$Proxy86.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:62)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:141)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at jdk.proxy3/jdk.proxy3.$Proxy99.insert(Unknown Source)
	at com.hfut.xiaozu.incident.incidentService.reportIncident(incidentService.java:116)
	at com.hfut.xiaozu.incident.incidentController.reportIncident(incidentController.java:48)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-30 15:32:41.849 [ http-nio-8080-exec-10 ] - [ ERROR ] [ o.a.c.c.C.[.[localhost].[/].[dispatcherServlet] : 170 ] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed: org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Cannot create a JSON value from a string with CHARACTER SET 'binary'.
### The error may exist in file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.xml]
### The error may involve com.hfut.xiaozu.incident.record.IncidentRecordMapper.insert-Inline
### The error occurred while setting parameters
### SQL: insert into incident_record         ( reporter_id,title,description,category_type,priority,         location_description,location_geojson,community_id,status,handler_id,         assign_time,complete_time)         values (?,?,?,?,?,         ?,?,?,?,?,         ?,?)
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Cannot create a JSON value from a string with CHARACTER SET 'binary'.
; Data truncation: Cannot create a JSON value from a string with CHARACTER SET 'binary'.] with root cause
com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Cannot create a JSON value from a string with CHARACTER SET 'binary'.
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446)
	at com.alibaba.druid.filter.FilterAdapter.preparedStatement_execute(FilterAdapter.java:1063)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.wall.WallFilter.preparedStatement_execute(WallFilter.java:639)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at jdk.internal.reflect.GeneratedMethodAccessor115.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at jdk.proxy4/jdk.proxy4.$Proxy132.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy130.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	at jdk.proxy2/jdk.proxy2.$Proxy86.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:62)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:141)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at jdk.proxy3/jdk.proxy3.$Proxy99.insert(Unknown Source)
	at com.hfut.xiaozu.incident.incidentService.reportIncident(incidentService.java:116)
	at com.hfut.xiaozu.incident.incidentController.reportIncident(incidentController.java:48)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-30 15:32:41.850 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /error
2025-06-30 15:39:30.246 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/incident
2025-06-30 15:39:30.251 [ http-nio-8080-exec-4 ] - [ ERROR ] [ druid.sql.Statement : 148 ] - {conn-10001, pstmt-20138} execute error. insert into incident_record (reporter_id, title, description, category_type, priority
	, location_description, location_geojson, community_id, status, handler_id
	, assign_time, complete_time)
values (59, '这栋楼坏了', '真的坏', 0, 0
	, '翰林雅居10栋', null, 1, 1, null
	, null, null)
com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Cannot create a JSON value from a string with CHARACTER SET 'binary'.
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446)
	at com.alibaba.druid.filter.FilterAdapter.preparedStatement_execute(FilterAdapter.java:1063)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.wall.WallFilter.preparedStatement_execute(WallFilter.java:639)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at jdk.internal.reflect.GeneratedMethodAccessor115.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at jdk.proxy4/jdk.proxy4.$Proxy132.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy130.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	at jdk.proxy2/jdk.proxy2.$Proxy86.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:62)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:141)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at jdk.proxy3/jdk.proxy3.$Proxy99.insert(Unknown Source)
	at com.hfut.xiaozu.incident.incidentService.reportIncident(incidentService.java:116)
	at com.hfut.xiaozu.incident.incidentController.reportIncident(incidentController.java:48)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-30 15:39:30.252 [ http-nio-8080-exec-4 ] - [ ERROR ] [ o.a.c.c.C.[.[localhost].[/].[dispatcherServlet] : 170 ] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed: org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Cannot create a JSON value from a string with CHARACTER SET 'binary'.
### The error may exist in file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.xml]
### The error may involve com.hfut.xiaozu.incident.record.IncidentRecordMapper.insert-Inline
### The error occurred while setting parameters
### SQL: insert into incident_record         ( reporter_id,title,description,category_type,priority,         location_description,location_geojson,community_id,status,handler_id,         assign_time,complete_time)         values (?,?,?,?,?,         ?,?,?,?,?,         ?,?)
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Cannot create a JSON value from a string with CHARACTER SET 'binary'.
; Data truncation: Cannot create a JSON value from a string with CHARACTER SET 'binary'.] with root cause
com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Cannot create a JSON value from a string with CHARACTER SET 'binary'.
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446)
	at com.alibaba.druid.filter.FilterAdapter.preparedStatement_execute(FilterAdapter.java:1063)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.wall.WallFilter.preparedStatement_execute(WallFilter.java:639)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at jdk.internal.reflect.GeneratedMethodAccessor115.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)
	at jdk.proxy4/jdk.proxy4.$Proxy132.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy130.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	at jdk.proxy2/jdk.proxy2.$Proxy86.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:62)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:141)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at jdk.proxy3/jdk.proxy3.$Proxy99.insert(Unknown Source)
	at com.hfut.xiaozu.incident.incidentService.reportIncident(incidentService.java:116)
	at com.hfut.xiaozu.incident.incidentController.reportIncident(incidentController.java:48)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1769)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-06-30 15:39:30.252 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /error
2025-06-30 16:19:16.150 [ SpringApplicationShutdownHook ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 54 ] - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-30 16:19:16.210 [ tomcat-shutdown ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 76 ] - Graceful shutdown complete
2025-06-30 16:19:16.218 [ SpringApplicationShutdownHook ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2174 ] - {dataSource-1} closing ...
2025-06-30 16:19:16.226 [ SpringApplicationShutdownHook ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2247 ] - {dataSource-1} closed
2025-06-30 16:22:01.746 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 53 ] - Starting XiaozuApplication using Java 17.0.2 with PID 11832 (D:\gitlab\xiangmu\community-backend\target\classes started by joy0531 in D:\gitlab\xiangmu\community-backend)
2025-06-30 16:22:01.747 [ restartedMain ] - [ DEBUG ] [ com.hfut.xiaozu.XiaozuApplication : 54 ] - Running with Spring Boot v3.4.7, Spring v6.2.8
2025-06-30 16:22:01.748 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 652 ] - No active profile set, falling back to 1 default profile: "default"
2025-06-30 16:22:01.779 [ restartedMain ] - [ INFO  ] [ o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor : 252 ] - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-30 16:22:01.779 [ restartedMain ] - [ INFO  ] [ o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor : 252 ] - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-30 16:22:02.297 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 247 ] - Searching for mappers annotated with @Mapper
2025-06-30 16:22:02.297 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 251 ] - Using auto-configuration base package 'com.hfut.xiaozu'
2025-06-30 16:22:02.432 [ restartedMain ] - [ DEBUG ] [ org.apache.ibatis.logging.LogFactory : 112 ] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-06-30 16:22:02.444 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.class]
2025-06-30 16:22:02.444 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.class]
2025-06-30 16:22:02.444 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.class]
2025-06-30 16:22:02.444 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.class]
2025-06-30 16:22:02.444 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.class]
2025-06-30 16:22:02.444 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.class]
2025-06-30 16:22:02.444 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.class]
2025-06-30 16:22:02.444 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.class]
2025-06-30 16:22:02.444 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.class]
2025-06-30 16:22:02.446 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.class]
2025-06-30 16:22:02.446 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.class]
2025-06-30 16:22:02.446 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.class]
2025-06-30 16:22:02.446 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.class]
2025-06-30 16:22:02.446 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.class]
2025-06-30 16:22:02.446 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.class]
2025-06-30 16:22:02.447 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleHouseBindingMapper' and 'com.hfut.xiaozu.car.bind.VehicleHouseBindingMapper' mapperInterface
2025-06-30 16:22:02.447 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleInfoMapper' and 'com.hfut.xiaozu.car.info.VehicleInfoMapper' mapperInterface
2025-06-30 16:22:02.448 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'familyMemberMapper' and 'com.hfut.xiaozu.family.info.FamilyMemberMapper' mapperInterface
2025-06-30 16:22:02.448 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userHouseBindingMapper' and 'com.hfut.xiaozu.house.binding.UserHouseBindingMapper' mapperInterface
2025-06-30 16:22:02.449 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityGridMapper' and 'com.hfut.xiaozu.house.grid.CommunityGridMapper' mapperInterface
2025-06-30 16:22:02.449 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'gridItemArchiveMapper' and 'com.hfut.xiaozu.house.grid.GridItemArchiveMapper' mapperInterface
2025-06-30 16:22:02.449 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'buildingInfoMapper' and 'com.hfut.xiaozu.house.information.BuildingInfoMapper' mapperInterface
2025-06-30 16:22:02.450 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityInfoMapper' and 'com.hfut.xiaozu.house.information.CommunityInfoMapper' mapperInterface
2025-06-30 16:22:02.450 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'houseInfoMapper' and 'com.hfut.xiaozu.house.information.HouseInfoMapper' mapperInterface
2025-06-30 16:22:02.450 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'unitInfoMapper' and 'com.hfut.xiaozu.house.information.UnitInfoMapper' mapperInterface
2025-06-30 16:22:02.451 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentProcessRecordMapper' and 'com.hfut.xiaozu.incident.process.IncidentProcessRecordMapper' mapperInterface
2025-06-30 16:22:02.451 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentRecordMapper' and 'com.hfut.xiaozu.incident.record.IncidentRecordMapper' mapperInterface
2025-06-30 16:22:02.451 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userMapper' and 'com.hfut.xiaozu.user.account.UserMapper' mapperInterface
2025-06-30 16:22:02.452 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityOcrRecordMapper' and 'com.hfut.xiaozu.user.idv.IdentityOcrRecordMapper' mapperInterface
2025-06-30 16:22:02.452 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityVerifySubmitMapper' and 'com.hfut.xiaozu.user.idv.IdentityVerifySubmitMapper' mapperInterface
2025-06-30 16:22:02.820 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 111 ] - Tomcat initialized with port 8080 (http)
2025-06-30 16:22:02.831 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardService : 168 ] - Starting service [Tomcat]
2025-06-30 16:22:02.831 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardEngine : 168 ] - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-30 16:22:02.878 [ restartedMain ] - [ INFO  ] [ o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] : 168 ] - Initializing Spring embedded WebApplicationContext
2025-06-30 16:22:02.879 [ restartedMain ] - [ INFO  ] [ o.s.b.w.s.c.ServletWebServerApplicationContext : 301 ] - Root WebApplicationContext: initialization completed in 1100 ms
2025-06-30 16:22:02.946 [ restartedMain ] - [ INFO  ] [ c.a.d.s.b.a.DruidDataSourceAutoConfigure : 55 ] - Init DruidDataSource
2025-06-30 16:22:03.307 [ restartedMain ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 1009 ] - {dataSource-1} inited
2025-06-30 16:22:03.459 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.xml]'
2025-06-30 16:22:03.470 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.xml]'
2025-06-30 16:22:03.480 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.xml]'
2025-06-30 16:22:03.490 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.xml]'
2025-06-30 16:22:03.496 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.xml]'
2025-06-30 16:22:03.501 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.xml]'
2025-06-30 16:22:03.513 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.xml]'
2025-06-30 16:22:03.519 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.xml]'
2025-06-30 16:22:03.526 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.xml]'
2025-06-30 16:22:03.531 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.xml]'
2025-06-30 16:22:03.537 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.xml]'
2025-06-30 16:22:03.543 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.xml]'
2025-06-30 16:22:03.549 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.xml]'
2025-06-30 16:22:03.554 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.xml]'
2025-06-30 16:22:03.557 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.xml]'
2025-06-30 16:22:15.126 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 319 ] - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-06-30 16:22:15.295 [ restartedMain ] - [ INFO  ] [ o.s.b.d.autoconfigure.OptionalLiveReloadServer : 59 ] - LiveReload server is running on port 35729
2025-06-30 16:22:15.346 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 243 ] - Tomcat started on port 8080 (http) with context path '/'
2025-06-30 16:22:15.354 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 59 ] - Started XiaozuApplication in 13.893 seconds (process running for 14.772)
2025-06-30 16:22:26.961 [ http-nio-8080-exec-1 ] - [ INFO  ] [ o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] : 168 ] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-30 16:22:26.961 [ http-nio-8080-exec-1 ] - [ INFO  ] [ org.springframework.web.servlet.DispatcherServlet : 532 ] - Initializing Servlet 'dispatcherServlet'
2025-06-30 16:22:26.962 [ http-nio-8080-exec-1 ] - [ INFO  ] [ org.springframework.web.servlet.DispatcherServlet : 554 ] - Completed initialization in 1 ms
2025-06-30 16:22:27.385 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 16:22:27.389 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 16:22:27.834 [ http-nio-8080-exec-5 ] - [ INFO  ] [ org.springdoc.api.AbstractOpenApiResource : 390 ] - Init duration for springdoc-openapi is: 336 ms
2025-06-30 16:22:33.769 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 16:22:33.769 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 16:22:34.346 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 16:22:34.346 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 16:22:41.751 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 16:22:41.751 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 16:22:42.084 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 16:22:42.084 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 16:22:46.987 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/incident
2025-06-30 16:24:15.425 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 16:24:15.426 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 16:24:21.059 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-06-30 16:24:54.718 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 16:24:54.719 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 16:25:01.075 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/buildings
2025-06-30 16:25:05.771 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 16:25:05.771 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 16:25:06.287 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 16:25:06.287 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 16:25:06.967 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 16:25:06.967 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 16:26:20.681 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 16:26:20.681 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 16:39:25.928 [ File Watcher ] - [ INFO  ] [ o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener : 211 ] - Restarting due to 99 class path changes (0 additions, 99 deletions, 0 modifications)
2025-06-30 16:39:25.954 [ Thread-5 ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 54 ] - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-30 16:39:26.127 [ tomcat-shutdown ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 76 ] - Graceful shutdown complete
2025-06-30 16:39:26.141 [ Thread-5 ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2174 ] - {dataSource-1} closing ...
2025-06-30 16:39:26.161 [ Thread-5 ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2247 ] - {dataSource-1} closed
2025-06-30 16:39:31.548 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 53 ] - Starting XiaozuApplication using Java 17.0.2 with PID 11832 (D:\gitlab\xiangmu\community-backend\target\classes started by joy0531 in D:\gitlab\xiangmu\community-backend)
2025-06-30 16:39:31.548 [ restartedMain ] - [ DEBUG ] [ com.hfut.xiaozu.XiaozuApplication : 54 ] - Running with Spring Boot v3.4.7, Spring v6.2.8
2025-06-30 16:39:31.549 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 652 ] - No active profile set, falling back to 1 default profile: "default"
2025-06-30 16:39:31.892 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 247 ] - Searching for mappers annotated with @Mapper
2025-06-30 16:39:31.894 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 251 ] - Using auto-configuration base package 'com.hfut.xiaozu'
2025-06-30 16:39:32.007 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.class]
2025-06-30 16:39:32.008 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.class]
2025-06-30 16:39:32.008 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.class]
2025-06-30 16:39:32.008 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.class]
2025-06-30 16:39:32.009 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.class]
2025-06-30 16:39:32.009 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.class]
2025-06-30 16:39:32.009 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.class]
2025-06-30 16:39:32.009 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.class]
2025-06-30 16:39:32.009 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.class]
2025-06-30 16:39:32.009 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.class]
2025-06-30 16:39:32.009 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.class]
2025-06-30 16:39:32.009 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.class]
2025-06-30 16:39:32.009 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.class]
2025-06-30 16:39:32.009 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.class]
2025-06-30 16:39:32.009 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.class]
2025-06-30 16:39:32.011 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleHouseBindingMapper' and 'com.hfut.xiaozu.car.bind.VehicleHouseBindingMapper' mapperInterface
2025-06-30 16:39:32.011 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleInfoMapper' and 'com.hfut.xiaozu.car.info.VehicleInfoMapper' mapperInterface
2025-06-30 16:39:32.012 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'familyMemberMapper' and 'com.hfut.xiaozu.family.info.FamilyMemberMapper' mapperInterface
2025-06-30 16:39:32.012 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userHouseBindingMapper' and 'com.hfut.xiaozu.house.binding.UserHouseBindingMapper' mapperInterface
2025-06-30 16:39:32.014 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityGridMapper' and 'com.hfut.xiaozu.house.grid.CommunityGridMapper' mapperInterface
2025-06-30 16:39:32.014 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'gridItemArchiveMapper' and 'com.hfut.xiaozu.house.grid.GridItemArchiveMapper' mapperInterface
2025-06-30 16:39:32.016 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'buildingInfoMapper' and 'com.hfut.xiaozu.house.information.BuildingInfoMapper' mapperInterface
2025-06-30 16:39:32.016 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityInfoMapper' and 'com.hfut.xiaozu.house.information.CommunityInfoMapper' mapperInterface
2025-06-30 16:39:32.017 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'houseInfoMapper' and 'com.hfut.xiaozu.house.information.HouseInfoMapper' mapperInterface
2025-06-30 16:39:32.018 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'unitInfoMapper' and 'com.hfut.xiaozu.house.information.UnitInfoMapper' mapperInterface
2025-06-30 16:39:32.019 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentProcessRecordMapper' and 'com.hfut.xiaozu.incident.process.IncidentProcessRecordMapper' mapperInterface
2025-06-30 16:39:32.020 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentRecordMapper' and 'com.hfut.xiaozu.incident.record.IncidentRecordMapper' mapperInterface
2025-06-30 16:39:32.021 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userMapper' and 'com.hfut.xiaozu.user.account.UserMapper' mapperInterface
2025-06-30 16:39:32.022 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityOcrRecordMapper' and 'com.hfut.xiaozu.user.idv.IdentityOcrRecordMapper' mapperInterface
2025-06-30 16:39:32.023 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityVerifySubmitMapper' and 'com.hfut.xiaozu.user.idv.IdentityVerifySubmitMapper' mapperInterface
2025-06-30 16:39:32.121 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 111 ] - Tomcat initialized with port 8080 (http)
2025-06-30 16:39:32.122 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardService : 168 ] - Starting service [Tomcat]
2025-06-30 16:39:32.122 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardEngine : 168 ] - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-30 16:39:32.150 [ restartedMain ] - [ INFO  ] [ o.a.c.c.ContainerBase.[Tomcat-2].[localhost].[/] : 168 ] - Initializing Spring embedded WebApplicationContext
2025-06-30 16:39:32.150 [ restartedMain ] - [ INFO  ] [ o.s.b.w.s.c.ServletWebServerApplicationContext : 301 ] - Root WebApplicationContext: initialization completed in 598 ms
2025-06-30 16:39:32.174 [ restartedMain ] - [ INFO  ] [ c.a.d.s.b.a.DruidDataSourceAutoConfigure : 55 ] - Init DruidDataSource
2025-06-30 16:39:32.330 [ restartedMain ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 1009 ] - {dataSource-2} inited
2025-06-30 16:39:32.380 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.xml]'
2025-06-30 16:39:32.386 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.xml]'
2025-06-30 16:39:32.394 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.xml]'
2025-06-30 16:39:32.401 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.xml]'
2025-06-30 16:39:32.411 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.xml]'
2025-06-30 16:39:32.421 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.xml]'
2025-06-30 16:39:32.427 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.xml]'
2025-06-30 16:39:32.435 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.xml]'
2025-06-30 16:39:32.446 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.xml]'
2025-06-30 16:39:32.451 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.xml]'
2025-06-30 16:39:32.469 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.xml]'
2025-06-30 16:39:32.479 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.xml]'
2025-06-30 16:39:32.486 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.xml]'
2025-06-30 16:39:32.492 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.xml]'
2025-06-30 16:39:32.499 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.xml]'
2025-06-30 16:39:32.726 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 319 ] - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-06-30 16:39:32.799 [ restartedMain ] - [ INFO  ] [ o.s.b.d.autoconfigure.OptionalLiveReloadServer : 59 ] - LiveReload server is running on port 35729
2025-06-30 16:39:32.846 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 243 ] - Tomcat started on port 8080 (http) with context path '/'
2025-06-30 16:39:32.851 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 59 ] - Started XiaozuApplication in 1.358 seconds (process running for 1052.268)
2025-06-30 16:39:32.853 [ restartedMain ] - [ INFO  ] [ o.s.b.d.a.ConditionEvaluationDeltaLoggingListener : 63 ] - Condition evaluation unchanged
2025-06-30 16:54:54.715 [ SpringApplicationShutdownHook ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 54 ] - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-30 16:54:54.779 [ tomcat-shutdown ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 76 ] - Graceful shutdown complete
2025-06-30 16:54:54.816 [ SpringApplicationShutdownHook ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2174 ] - {dataSource-2} closing ...
2025-06-30 16:54:54.817 [ SpringApplicationShutdownHook ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2247 ] - {dataSource-2} closed
2025-06-30 17:25:30.078 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 53 ] - Starting XiaozuApplication using Java 17.0.2 with PID 14952 (D:\gitlab\xiangmu\community-backend\target\classes started by joy0531 in D:\gitlab\xiangmu\community-backend)
2025-06-30 17:25:30.080 [ restartedMain ] - [ DEBUG ] [ com.hfut.xiaozu.XiaozuApplication : 54 ] - Running with Spring Boot v3.4.7, Spring v6.2.8
2025-06-30 17:25:30.080 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 652 ] - No active profile set, falling back to 1 default profile: "default"
2025-06-30 17:25:30.122 [ restartedMain ] - [ INFO  ] [ o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor : 252 ] - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-30 17:25:30.122 [ restartedMain ] - [ INFO  ] [ o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor : 252 ] - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-30 17:25:30.781 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 247 ] - Searching for mappers annotated with @Mapper
2025-06-30 17:25:30.782 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 251 ] - Using auto-configuration base package 'com.hfut.xiaozu'
2025-06-30 17:25:30.928 [ restartedMain ] - [ DEBUG ] [ org.apache.ibatis.logging.LogFactory : 112 ] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-06-30 17:25:30.939 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.class]
2025-06-30 17:25:30.939 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.class]
2025-06-30 17:25:30.939 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.class]
2025-06-30 17:25:30.939 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.class]
2025-06-30 17:25:30.939 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.class]
2025-06-30 17:25:30.939 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.class]
2025-06-30 17:25:30.939 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.class]
2025-06-30 17:25:30.940 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.class]
2025-06-30 17:25:30.940 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.class]
2025-06-30 17:25:30.940 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.class]
2025-06-30 17:25:30.940 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.class]
2025-06-30 17:25:30.940 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.class]
2025-06-30 17:25:30.940 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.class]
2025-06-30 17:25:30.940 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.class]
2025-06-30 17:25:30.940 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.class]
2025-06-30 17:25:30.941 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleHouseBindingMapper' and 'com.hfut.xiaozu.car.bind.VehicleHouseBindingMapper' mapperInterface
2025-06-30 17:25:30.943 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleInfoMapper' and 'com.hfut.xiaozu.car.info.VehicleInfoMapper' mapperInterface
2025-06-30 17:25:30.944 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'familyMemberMapper' and 'com.hfut.xiaozu.family.info.FamilyMemberMapper' mapperInterface
2025-06-30 17:25:30.944 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userHouseBindingMapper' and 'com.hfut.xiaozu.house.binding.UserHouseBindingMapper' mapperInterface
2025-06-30 17:25:30.944 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityGridMapper' and 'com.hfut.xiaozu.house.grid.CommunityGridMapper' mapperInterface
2025-06-30 17:25:30.946 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'gridItemArchiveMapper' and 'com.hfut.xiaozu.house.grid.GridItemArchiveMapper' mapperInterface
2025-06-30 17:25:30.946 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'buildingInfoMapper' and 'com.hfut.xiaozu.house.information.BuildingInfoMapper' mapperInterface
2025-06-30 17:25:30.947 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityInfoMapper' and 'com.hfut.xiaozu.house.information.CommunityInfoMapper' mapperInterface
2025-06-30 17:25:30.947 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'houseInfoMapper' and 'com.hfut.xiaozu.house.information.HouseInfoMapper' mapperInterface
2025-06-30 17:25:30.947 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'unitInfoMapper' and 'com.hfut.xiaozu.house.information.UnitInfoMapper' mapperInterface
2025-06-30 17:25:30.948 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentProcessRecordMapper' and 'com.hfut.xiaozu.incident.process.IncidentProcessRecordMapper' mapperInterface
2025-06-30 17:25:30.948 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentRecordMapper' and 'com.hfut.xiaozu.incident.record.IncidentRecordMapper' mapperInterface
2025-06-30 17:25:30.948 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userMapper' and 'com.hfut.xiaozu.user.account.UserMapper' mapperInterface
2025-06-30 17:25:30.949 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityOcrRecordMapper' and 'com.hfut.xiaozu.user.idv.IdentityOcrRecordMapper' mapperInterface
2025-06-30 17:25:30.949 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityVerifySubmitMapper' and 'com.hfut.xiaozu.user.idv.IdentityVerifySubmitMapper' mapperInterface
2025-06-30 17:25:31.353 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 111 ] - Tomcat initialized with port 8080 (http)
2025-06-30 17:25:31.366 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardService : 168 ] - Starting service [Tomcat]
2025-06-30 17:25:31.366 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardEngine : 168 ] - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-30 17:25:31.417 [ restartedMain ] - [ INFO  ] [ o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] : 168 ] - Initializing Spring embedded WebApplicationContext
2025-06-30 17:25:31.417 [ restartedMain ] - [ INFO  ] [ o.s.b.w.s.c.ServletWebServerApplicationContext : 301 ] - Root WebApplicationContext: initialization completed in 1295 ms
2025-06-30 17:25:31.491 [ restartedMain ] - [ INFO  ] [ c.a.d.s.b.a.DruidDataSourceAutoConfigure : 55 ] - Init DruidDataSource
2025-06-30 17:25:31.894 [ restartedMain ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 1009 ] - {dataSource-1} inited
2025-06-30 17:25:32.061 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.xml]'
2025-06-30 17:25:32.071 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.xml]'
2025-06-30 17:25:32.081 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.xml]'
2025-06-30 17:25:32.090 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.xml]'
2025-06-30 17:25:32.098 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.xml]'
2025-06-30 17:25:32.106 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.xml]'
2025-06-30 17:25:32.119 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.xml]'
2025-06-30 17:25:32.128 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.xml]'
2025-06-30 17:25:32.135 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.xml]'
2025-06-30 17:25:32.142 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.xml]'
2025-06-30 17:25:32.150 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.xml]'
2025-06-30 17:25:32.157 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.xml]'
2025-06-30 17:25:32.164 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.xml]'
2025-06-30 17:25:32.170 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.xml]'
2025-06-30 17:25:32.176 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.xml]'
2025-06-30 17:25:32.760 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 319 ] - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-06-30 17:25:32.938 [ restartedMain ] - [ INFO  ] [ o.s.b.d.autoconfigure.OptionalLiveReloadServer : 59 ] - LiveReload server is running on port 35729
2025-06-30 17:25:32.986 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 243 ] - Tomcat started on port 8080 (http) with context path '/'
2025-06-30 17:25:32.993 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 59 ] - Started XiaozuApplication in 3.312 seconds (process running for 4.891)
2025-06-30 17:25:49.445 [ http-nio-8080-exec-1 ] - [ INFO  ] [ o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] : 168 ] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-30 17:25:49.446 [ http-nio-8080-exec-1 ] - [ INFO  ] [ org.springframework.web.servlet.DispatcherServlet : 532 ] - Initializing Servlet 'dispatcherServlet'
2025-06-30 17:25:49.447 [ http-nio-8080-exec-1 ] - [ INFO  ] [ org.springframework.web.servlet.DispatcherServlet : 554 ] - Completed initialization in 1 ms
2025-06-30 17:25:49.493 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/incident
2025-06-30 17:25:49.496 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 POST /api/incident 缺少Authorization头部
2025-06-30 17:26:24.475 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/incident
2025-06-30 17:27:50.284 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/auth/test
2025-06-30 17:27:50.293 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/test
2025-06-30 17:27:50.293 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /api/auth/test 缺少Authorization头部
2025-06-30 17:27:51.484 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /
2025-06-30 17:27:51.485 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET / 缺少Authorization头部
2025-06-30 17:27:52.633 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/auth/test
2025-06-30 17:27:52.634 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /api/auth/test 缺少Authorization头部
2025-06-30 17:28:01.684 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:28:01.685 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:28:02.207 [ http-nio-8080-exec-5 ] - [ INFO  ] [ org.springdoc.api.AbstractOpenApiResource : 390 ] - Init duration for springdoc-openapi is: 477 ms
2025-06-30 17:28:03.975 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:28:03.977 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:28:04.566 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:28:04.567 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:28:04.983 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:28:04.984 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:28:11.982 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:28:11.983 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:28:12.483 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:28:12.483 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:28:13.308 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:28:13.309 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:28:16.159 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:28:16.159 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:28:17.555 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:28:17.557 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:28:20.214 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:28:20.215 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:28:22.455 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:28:22.456 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:28:23.220 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:28:23.220 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:28:26.619 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:28:26.619 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:28:27.116 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:28:27.117 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:28:55.334 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:28:55.334 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:28:59.044 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list
2025-06-30 17:29:02.990 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:29:02.991 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:29:10.763 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:29:17.545 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 17:29:20.371 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:29:26.230 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:29:26.230 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:29:35.812 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:29:35.812 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:29:36.534 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:29:36.534 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:29:38.091 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/1/recommendedGridWorker
2025-06-30 17:29:41.613 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/2/recommendedGridWorker
2025-06-30 17:31:10.688 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:31:10.689 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:31:11.087 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:31:11.087 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:31:11.653 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:31:11.654 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:31:12.194 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:31:12.194 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:38:25.917 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:38:25.917 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:38:26.402 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:38:26.402 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:38:27.110 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:38:27.110 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:38:27.592 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:38:27.592 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:38:27.963 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:38:27.964 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:38:31.978 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:38:31.978 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:38:32.402 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:38:32.403 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:38:32.889 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:38:32.889 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:38:33.571 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:38:33.572 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:38:34.080 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:38:34.080 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:38:35.696 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:38:35.696 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:38:39.847 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:38:39.848 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:47:36.072 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-06-30 17:48:20.595 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-06-30 17:49:25.626 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-06-30 17:49:29.386 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-06-30 17:49:30.312 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:49:30.329 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:49:30.374 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 17:49:30.394 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 17:49:30.409 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 17:49:30.566 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:49:30.566 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /api/incident/list/1 缺少Authorization头部
2025-06-30 17:49:30.573 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:49:30.573 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /api/incident/list/1 缺少Authorization头部
2025-06-30 17:49:30.712 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 17:49:30.713 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /api/incident/list/2 缺少Authorization头部
2025-06-30 17:49:36.457 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 17:49:36.463 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 17:49:38.247 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 17:49:38.254 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 17:49:38.691 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 17:49:38.698 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 17:49:39.741 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 17:49:39.747 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 17:49:40.057 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:49:40.065 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:49:41.458 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:49:41.471 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:49:41.489 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 17:49:41.502 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 17:49:41.514 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 17:49:47.027 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:49:47.036 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:49:47.054 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 17:49:47.067 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 17:49:47.097 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 17:50:00.216 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:50:00.216 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /api/incident/list/1 缺少Authorization头部
2025-06-30 17:50:00.223 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:50:00.223 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /api/incident/list/1 缺少Authorization头部
2025-06-30 17:50:00.370 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 17:50:00.370 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /api/incident/list/2 缺少Authorization头部
2025-06-30 17:50:04.077 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:50:04.083 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:50:04.113 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 17:50:04.128 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 17:50:04.136 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 17:50:12.489 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 17:50:12.495 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 17:50:13.148 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 17:50:13.153 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 17:50:13.597 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 17:50:13.603 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 17:50:14.599 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 17:50:14.605 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 17:50:14.902 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:50:14.909 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:51:45.497 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:51:45.568 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:51:45.597 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 17:51:45.609 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 17:51:45.621 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 17:53:46.899 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:53:46.912 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:53:46.938 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 17:53:46.955 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:53:46.961 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 17:53:46.968 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:53:46.978 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 17:53:46.985 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 17:53:46.999 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 17:53:47.014 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 17:54:06.179 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:54:06.188 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:54:06.200 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 17:54:06.203 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:54:06.211 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:54:06.213 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 17:54:06.226 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 17:54:06.226 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 17:54:06.241 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 17:54:06.255 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 17:54:33.338 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:54:33.349 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:54:33.369 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 17:54:33.381 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 17:54:33.395 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 17:55:14.082 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:55:14.091 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:55:14.104 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 17:55:14.119 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:55:14.126 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 17:55:14.128 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:55:14.139 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 17:55:14.140 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 17:55:14.154 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 17:55:14.166 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 17:56:58.768 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:56:58.778 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:56:58.791 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 17:56:58.803 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 17:56:58.820 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 17:57:04.275 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-06-30 17:57:06.372 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-06-30 17:57:07.042 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:57:07.049 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:57:07.061 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 17:57:07.078 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 17:57:07.089 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 17:57:13.583 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-06-30 17:57:13.931 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:57:13.940 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:57:13.951 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 17:57:13.961 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 17:57:13.977 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 17:58:04.714 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:58:04.721 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 17:58:04.732 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 17:58:04.743 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 17:58:04.755 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 17:58:14.079 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:58:14.079 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:58:15.007 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:58:15.007 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:58:15.596 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:58:15.596 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:58:16.413 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:58:16.413 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:58:16.837 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:58:16.837 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:58:24.316 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:58:24.317 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 17:58:28.813 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 17:58:28.814 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 18:00:29.659 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 18:00:29.668 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 18:00:29.684 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 18:00:29.692 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 18:00:29.700 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 18:00:29.703 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 18:00:29.714 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 18:00:29.717 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 18:00:29.727 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 18:00:29.738 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 18:01:53.018 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 18:01:53.027 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 18:01:53.045 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 18:01:53.045 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 18:01:53.059 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 18:01:53.063 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 18:01:53.077 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 18:01:53.079 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 18:01:53.093 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 18:01:53.107 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 18:01:55.817 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 18:01:55.822 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 18:01:56.373 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 18:01:56.378 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 18:01:56.794 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 18:01:56.800 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 18:01:59.319 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 18:01:59.326 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 18:02:09.417 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 18:02:09.428 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 18:02:09.503 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 18:02:09.531 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 18:02:09.553 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 18:02:25.969 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 18:02:25.975 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 18:02:29.812 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 18:02:29.819 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 18:02:30.580 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 18:02:30.585 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 18:02:31.113 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 18:02:31.118 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 18:02:31.647 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 18:02:31.652 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 18:02:34.897 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 18:02:34.903 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 18:04:20.374 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 18:04:20.381 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 18:04:20.921 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 18:04:20.926 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 18:04:21.403 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 18:04:21.409 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 18:04:21.728 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 18:04:21.734 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 18:04:27.605 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/houses/binding/me
2025-06-30 18:04:27.614 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-06-30 18:04:27.633 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/communities
2025-06-30 18:04:27.637 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-06-30 18:04:28.434 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-06-30 18:04:28.956 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-06-30 18:04:31.187 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-06-30 18:04:31.796 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-06-30 18:04:32.674 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-06-30 18:04:32.685 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-06-30 18:04:36.809 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-06-30 18:04:37.232 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-06-30 18:04:37.847 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-06-30 18:04:37.861 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-06-30 18:04:39.214 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-06-30 18:05:11.561 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-06-30 18:05:24.262 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-06-30 18:05:26.921 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-06-30 18:05:28.230 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 18:05:28.237 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 18:05:28.262 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 18:05:28.281 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 18:05:28.290 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 18:05:30.187 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 18:05:30.192 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 18:05:30.202 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 18:05:30.211 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 18:05:30.220 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 18:05:31.395 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-06-30 18:05:35.839 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 18:05:35.845 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 18:05:35.858 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 18:05:35.876 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 18:05:35.886 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 18:05:43.810 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-06-30 18:05:47.203 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-06-30 18:05:47.208 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-06-30 18:06:42.809 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/grids
2025-06-30 18:06:42.894 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.house.HouseService : 298 ] - {"type":"Polygon","coordinates":[[[117.196843,31.775008],[117.199335,31.776116],[117.201244,31.774046],[117.199583,31.773118],[117.197313,31.773201],[117.197214,31.774173]]]}
2025-06-30 18:06:43.045 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-06-30 18:06:44.696 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-06-30 18:06:57.030 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-06-30 18:07:04.759 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 18:07:04.764 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 18:07:04.774 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 18:07:04.784 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 18:07:04.797 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 18:07:05.947 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-06-30 18:07:15.334 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 18:07:15.334 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 18:07:22.265 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/1/recommendedGridWorker
2025-06-30 18:07:49.369 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/1/recommendedGridWorker
2025-06-30 18:07:50.084 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/1/recommendedGridWorker
2025-06-30 18:08:23.381 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 18:08:23.381 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 18:08:24.000 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 18:08:24.001 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 18:08:51.855 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 18:08:51.855 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 18:08:57.975 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 18:08:57.975 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 18:09:41.644 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 18:09:41.651 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/1
2025-06-30 18:09:41.666 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/2
2025-06-30 18:09:41.689 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/3
2025-06-30 18:09:41.698 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/list/4
2025-06-30 18:10:09.362 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/2/recommendedGridWorker
2025-06-30 19:10:43.516 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/incident/2/recommendedGridWorker
2025-06-30 19:13:56.379 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 19:13:56.379 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 19:13:59.689 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /error
2025-06-30 19:13:59.689 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 46 ] - 请求 GET /error 缺少Authorization头部
2025-06-30 19:14:48.545 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-06-30 19:14:49.608 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-06-30 19:14:49.608 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-06-30 19:15:10.016 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: POST /api/grids
2025-06-30 19:15:10.018 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.house.HouseService : 298 ] - {"type":"Polygon","coordinates":[[[117.192234,31.77619],[117.194444,31.776063],[117.193381,31.777274]]]}
2025-06-30 19:15:10.043 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-06-30 19:15:11.274 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-06-30 19:17:48.587 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-06-30 19:17:48.591 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/listAll
2025-06-30 19:18:11.671 [ File Watcher ] - [ INFO  ] [ o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener : 211 ] - Restarting due to 2 class path changes (0 additions, 0 deletions, 2 modifications)
2025-06-30 19:18:11.683 [ Thread-5 ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 54 ] - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-30 19:18:11.755 [ tomcat-shutdown ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 76 ] - Graceful shutdown complete
2025-06-30 19:18:11.770 [ Thread-5 ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2174 ] - {dataSource-1} closing ...
2025-06-30 19:18:11.784 [ Thread-5 ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2247 ] - {dataSource-1} closed
2025-06-30 19:18:11.922 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 53 ] - Starting XiaozuApplication using Java 17.0.2 with PID 14952 (D:\gitlab\xiangmu\community-backend\target\classes started by joy0531 in D:\gitlab\xiangmu\community-backend)
2025-06-30 19:18:11.922 [ restartedMain ] - [ DEBUG ] [ com.hfut.xiaozu.XiaozuApplication : 54 ] - Running with Spring Boot v3.4.7, Spring v6.2.8
2025-06-30 19:18:11.922 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 652 ] - No active profile set, falling back to 1 default profile: "default"
2025-06-30 19:18:12.150 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 247 ] - Searching for mappers annotated with @Mapper
2025-06-30 19:18:12.150 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 251 ] - Using auto-configuration base package 'com.hfut.xiaozu'
2025-06-30 19:18:12.216 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.class]
2025-06-30 19:18:12.216 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.class]
2025-06-30 19:18:12.216 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.class]
2025-06-30 19:18:12.216 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.class]
2025-06-30 19:18:12.216 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.class]
2025-06-30 19:18:12.216 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.class]
2025-06-30 19:18:12.216 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.class]
2025-06-30 19:18:12.216 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.class]
2025-06-30 19:18:12.216 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.class]
2025-06-30 19:18:12.216 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.class]
2025-06-30 19:18:12.216 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.class]
2025-06-30 19:18:12.216 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.class]
2025-06-30 19:18:12.217 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.class]
2025-06-30 19:18:12.217 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.class]
2025-06-30 19:18:12.217 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.class]
2025-06-30 19:18:12.217 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleHouseBindingMapper' and 'com.hfut.xiaozu.car.bind.VehicleHouseBindingMapper' mapperInterface
2025-06-30 19:18:12.217 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleInfoMapper' and 'com.hfut.xiaozu.car.info.VehicleInfoMapper' mapperInterface
2025-06-30 19:18:12.218 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'familyMemberMapper' and 'com.hfut.xiaozu.family.info.FamilyMemberMapper' mapperInterface
2025-06-30 19:18:12.219 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userHouseBindingMapper' and 'com.hfut.xiaozu.house.binding.UserHouseBindingMapper' mapperInterface
2025-06-30 19:18:12.219 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityGridMapper' and 'com.hfut.xiaozu.house.grid.CommunityGridMapper' mapperInterface
2025-06-30 19:18:12.219 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'gridItemArchiveMapper' and 'com.hfut.xiaozu.house.grid.GridItemArchiveMapper' mapperInterface
2025-06-30 19:18:12.220 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'buildingInfoMapper' and 'com.hfut.xiaozu.house.information.BuildingInfoMapper' mapperInterface
2025-06-30 19:18:12.220 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityInfoMapper' and 'com.hfut.xiaozu.house.information.CommunityInfoMapper' mapperInterface
2025-06-30 19:18:12.220 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'houseInfoMapper' and 'com.hfut.xiaozu.house.information.HouseInfoMapper' mapperInterface
2025-06-30 19:18:12.221 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'unitInfoMapper' and 'com.hfut.xiaozu.house.information.UnitInfoMapper' mapperInterface
2025-06-30 19:18:12.221 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentProcessRecordMapper' and 'com.hfut.xiaozu.incident.process.IncidentProcessRecordMapper' mapperInterface
2025-06-30 19:18:12.221 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentRecordMapper' and 'com.hfut.xiaozu.incident.record.IncidentRecordMapper' mapperInterface
2025-06-30 19:18:12.221 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userMapper' and 'com.hfut.xiaozu.user.account.UserMapper' mapperInterface
2025-06-30 19:18:12.223 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityOcrRecordMapper' and 'com.hfut.xiaozu.user.idv.IdentityOcrRecordMapper' mapperInterface
2025-06-30 19:18:12.223 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityVerifySubmitMapper' and 'com.hfut.xiaozu.user.idv.IdentityVerifySubmitMapper' mapperInterface
2025-06-30 19:18:12.309 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 111 ] - Tomcat initialized with port 8080 (http)
2025-06-30 19:18:12.311 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardService : 168 ] - Starting service [Tomcat]
2025-06-30 19:18:12.311 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardEngine : 168 ] - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-30 19:18:12.329 [ restartedMain ] - [ INFO  ] [ o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] : 168 ] - Initializing Spring embedded WebApplicationContext
2025-06-30 19:18:12.329 [ restartedMain ] - [ INFO  ] [ o.s.b.w.s.c.ServletWebServerApplicationContext : 301 ] - Root WebApplicationContext: initialization completed in 404 ms
2025-06-30 19:18:12.346 [ restartedMain ] - [ INFO  ] [ c.a.d.s.b.a.DruidDataSourceAutoConfigure : 55 ] - Init DruidDataSource
2025-06-30 19:18:12.469 [ restartedMain ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 1009 ] - {dataSource-2} inited
2025-06-30 19:18:12.504 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.xml]'
2025-06-30 19:18:12.508 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.xml]'
2025-06-30 19:18:12.514 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.xml]'
2025-06-30 19:18:12.518 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.xml]'
2025-06-30 19:18:12.524 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.xml]'
2025-06-30 19:18:12.530 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.xml]'
2025-06-30 19:18:12.535 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.xml]'
2025-06-30 19:18:12.538 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.xml]'
2025-06-30 19:18:12.543 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.xml]'
2025-06-30 19:18:12.548 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.xml]'
2025-06-30 19:18:12.552 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.xml]'
2025-06-30 19:18:12.557 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.xml]'
2025-06-30 19:18:12.562 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.xml]'
2025-06-30 19:18:12.568 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.xml]'
2025-06-30 19:18:12.571 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.xml]'
2025-06-30 19:18:23.802 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 319 ] - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-06-30 19:18:23.851 [ restartedMain ] - [ INFO  ] [ o.s.b.d.autoconfigure.OptionalLiveReloadServer : 59 ] - LiveReload server is running on port 35729
2025-06-30 19:18:23.876 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 243 ] - Tomcat started on port 8080 (http) with context path '/'
2025-06-30 19:18:23.881 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 59 ] - Started XiaozuApplication in 12.002 seconds (process running for 6775.779)
2025-06-30 19:18:23.883 [ restartedMain ] - [ INFO  ] [ o.s.b.d.a.ConditionEvaluationDeltaLoggingListener : 63 ] - Condition evaluation unchanged
2025-06-30 19:18:25.344 [ File Watcher ] - [ INFO  ] [ o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener : 211 ] - Restarting due to 2 class path changes (0 additions, 0 deletions, 2 modifications)
2025-06-30 19:18:25.344 [ Thread-7 ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 54 ] - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-30 19:18:25.401 [ tomcat-shutdown ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 76 ] - Graceful shutdown complete
2025-06-30 19:18:25.403 [ Thread-7 ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2174 ] - {dataSource-2} closing ...
2025-06-30 19:18:25.404 [ Thread-7 ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2247 ] - {dataSource-2} closed
2025-06-30 19:18:25.479 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 53 ] - Starting XiaozuApplication using Java 17.0.2 with PID 14952 (D:\gitlab\xiangmu\community-backend\target\classes started by joy0531 in D:\gitlab\xiangmu\community-backend)
2025-06-30 19:18:25.479 [ restartedMain ] - [ DEBUG ] [ com.hfut.xiaozu.XiaozuApplication : 54 ] - Running with Spring Boot v3.4.7, Spring v6.2.8
2025-06-30 19:18:25.479 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 652 ] - No active profile set, falling back to 1 default profile: "default"
2025-06-30 19:18:25.646 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 247 ] - Searching for mappers annotated with @Mapper
2025-06-30 19:18:25.646 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 251 ] - Using auto-configuration base package 'com.hfut.xiaozu'
2025-06-30 19:18:25.695 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.class]
2025-06-30 19:18:25.695 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.class]
2025-06-30 19:18:25.695 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.class]
2025-06-30 19:18:25.695 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.class]
2025-06-30 19:18:25.695 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.class]
2025-06-30 19:18:25.695 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.class]
2025-06-30 19:18:25.695 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.class]
2025-06-30 19:18:25.695 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.class]
2025-06-30 19:18:25.695 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.class]
2025-06-30 19:18:25.695 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.class]
2025-06-30 19:18:25.695 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.class]
2025-06-30 19:18:25.695 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.class]
2025-06-30 19:18:25.695 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.class]
2025-06-30 19:18:25.695 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.class]
2025-06-30 19:18:25.695 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.class]
2025-06-30 19:18:25.695 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleHouseBindingMapper' and 'com.hfut.xiaozu.car.bind.VehicleHouseBindingMapper' mapperInterface
2025-06-30 19:18:25.696 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleInfoMapper' and 'com.hfut.xiaozu.car.info.VehicleInfoMapper' mapperInterface
2025-06-30 19:18:25.696 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'familyMemberMapper' and 'com.hfut.xiaozu.family.info.FamilyMemberMapper' mapperInterface
2025-06-30 19:18:25.696 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userHouseBindingMapper' and 'com.hfut.xiaozu.house.binding.UserHouseBindingMapper' mapperInterface
2025-06-30 19:18:25.697 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityGridMapper' and 'com.hfut.xiaozu.house.grid.CommunityGridMapper' mapperInterface
2025-06-30 19:18:25.697 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'gridItemArchiveMapper' and 'com.hfut.xiaozu.house.grid.GridItemArchiveMapper' mapperInterface
2025-06-30 19:18:25.697 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'buildingInfoMapper' and 'com.hfut.xiaozu.house.information.BuildingInfoMapper' mapperInterface
2025-06-30 19:18:25.698 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityInfoMapper' and 'com.hfut.xiaozu.house.information.CommunityInfoMapper' mapperInterface
2025-06-30 19:18:25.698 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'houseInfoMapper' and 'com.hfut.xiaozu.house.information.HouseInfoMapper' mapperInterface
2025-06-30 19:18:25.698 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'unitInfoMapper' and 'com.hfut.xiaozu.house.information.UnitInfoMapper' mapperInterface
2025-06-30 19:18:25.699 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentProcessRecordMapper' and 'com.hfut.xiaozu.incident.process.IncidentProcessRecordMapper' mapperInterface
2025-06-30 19:18:25.699 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentRecordMapper' and 'com.hfut.xiaozu.incident.record.IncidentRecordMapper' mapperInterface
2025-06-30 19:18:25.699 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userMapper' and 'com.hfut.xiaozu.user.account.UserMapper' mapperInterface
2025-06-30 19:18:25.700 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityOcrRecordMapper' and 'com.hfut.xiaozu.user.idv.IdentityOcrRecordMapper' mapperInterface
2025-06-30 19:18:25.700 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityVerifySubmitMapper' and 'com.hfut.xiaozu.user.idv.IdentityVerifySubmitMapper' mapperInterface
2025-06-30 19:18:25.747 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 111 ] - Tomcat initialized with port 8080 (http)
2025-06-30 19:18:25.748 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardService : 168 ] - Starting service [Tomcat]
2025-06-30 19:18:25.748 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardEngine : 168 ] - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-30 19:18:25.763 [ restartedMain ] - [ INFO  ] [ o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] : 168 ] - Initializing Spring embedded WebApplicationContext
2025-06-30 19:18:25.764 [ restartedMain ] - [ INFO  ] [ o.s.b.w.s.c.ServletWebServerApplicationContext : 301 ] - Root WebApplicationContext: initialization completed in 283 ms
2025-06-30 19:18:25.777 [ restartedMain ] - [ INFO  ] [ c.a.d.s.b.a.DruidDataSourceAutoConfigure : 55 ] - Init DruidDataSource
2025-06-30 19:18:25.860 [ restartedMain ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 1009 ] - {dataSource-3} inited
2025-06-30 19:18:25.888 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.xml]'
2025-06-30 19:18:25.893 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.xml]'
2025-06-30 19:18:25.896 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.xml]'
2025-06-30 19:18:25.900 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.xml]'
2025-06-30 19:18:25.903 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.xml]'
2025-06-30 19:18:25.907 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.xml]'
2025-06-30 19:18:25.911 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.xml]'
2025-06-30 19:18:25.915 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.xml]'
2025-06-30 19:18:25.919 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.xml]'
2025-06-30 19:18:25.925 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.xml]'
2025-06-30 19:18:25.928 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.xml]'
2025-06-30 19:18:25.932 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.xml]'
2025-06-30 19:18:25.937 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.xml]'
2025-06-30 19:18:25.940 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.xml]'
2025-06-30 19:18:25.944 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.xml]'
2025-06-30 19:18:26.056 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 319 ] - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-06-30 19:18:26.089 [ restartedMain ] - [ INFO  ] [ o.s.b.d.autoconfigure.OptionalLiveReloadServer : 59 ] - LiveReload server is running on port 35729
2025-06-30 19:18:26.114 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 243 ] - Tomcat started on port 8080 (http) with context path '/'
2025-06-30 19:18:26.118 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 59 ] - Started XiaozuApplication in 0.673 seconds (process running for 6778.016)
2025-06-30 19:18:26.119 [ restartedMain ] - [ INFO  ] [ o.s.b.d.a.ConditionEvaluationDeltaLoggingListener : 63 ] - Condition evaluation unchanged
2025-06-30 19:18:44.293 [ File Watcher ] - [ INFO  ] [ o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener : 211 ] - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-30 19:18:44.294 [ Thread-11 ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 54 ] - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-30 19:18:44.368 [ tomcat-shutdown ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 76 ] - Graceful shutdown complete
2025-06-30 19:18:44.372 [ Thread-11 ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2174 ] - {dataSource-3} closing ...
2025-06-30 19:18:44.372 [ Thread-11 ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2247 ] - {dataSource-3} closed
2025-06-30 19:18:44.455 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 53 ] - Starting XiaozuApplication using Java 17.0.2 with PID 14952 (D:\gitlab\xiangmu\community-backend\target\classes started by joy0531 in D:\gitlab\xiangmu\community-backend)
2025-06-30 19:18:44.456 [ restartedMain ] - [ DEBUG ] [ com.hfut.xiaozu.XiaozuApplication : 54 ] - Running with Spring Boot v3.4.7, Spring v6.2.8
2025-06-30 19:18:44.456 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 652 ] - No active profile set, falling back to 1 default profile: "default"
2025-06-30 19:18:44.627 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 247 ] - Searching for mappers annotated with @Mapper
2025-06-30 19:18:44.627 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 251 ] - Using auto-configuration base package 'com.hfut.xiaozu'
2025-06-30 19:18:44.675 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.class]
2025-06-30 19:18:44.675 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.class]
2025-06-30 19:18:44.675 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.class]
2025-06-30 19:18:44.675 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.class]
2025-06-30 19:18:44.675 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.class]
2025-06-30 19:18:44.675 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.class]
2025-06-30 19:18:44.675 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.class]
2025-06-30 19:18:44.675 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.class]
2025-06-30 19:18:44.676 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.class]
2025-06-30 19:18:44.676 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.class]
2025-06-30 19:18:44.676 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.class]
2025-06-30 19:18:44.676 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.class]
2025-06-30 19:18:44.676 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.class]
2025-06-30 19:18:44.676 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.class]
2025-06-30 19:18:44.676 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.class]
2025-06-30 19:18:44.676 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleHouseBindingMapper' and 'com.hfut.xiaozu.car.bind.VehicleHouseBindingMapper' mapperInterface
2025-06-30 19:18:44.677 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleInfoMapper' and 'com.hfut.xiaozu.car.info.VehicleInfoMapper' mapperInterface
2025-06-30 19:18:44.677 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'familyMemberMapper' and 'com.hfut.xiaozu.family.info.FamilyMemberMapper' mapperInterface
2025-06-30 19:18:44.678 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userHouseBindingMapper' and 'com.hfut.xiaozu.house.binding.UserHouseBindingMapper' mapperInterface
2025-06-30 19:18:44.678 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityGridMapper' and 'com.hfut.xiaozu.house.grid.CommunityGridMapper' mapperInterface
2025-06-30 19:18:44.678 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'gridItemArchiveMapper' and 'com.hfut.xiaozu.house.grid.GridItemArchiveMapper' mapperInterface
2025-06-30 19:18:44.678 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'buildingInfoMapper' and 'com.hfut.xiaozu.house.information.BuildingInfoMapper' mapperInterface
2025-06-30 19:18:44.679 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityInfoMapper' and 'com.hfut.xiaozu.house.information.CommunityInfoMapper' mapperInterface
2025-06-30 19:18:44.679 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'houseInfoMapper' and 'com.hfut.xiaozu.house.information.HouseInfoMapper' mapperInterface
2025-06-30 19:18:44.679 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'unitInfoMapper' and 'com.hfut.xiaozu.house.information.UnitInfoMapper' mapperInterface
2025-06-30 19:18:44.680 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentProcessRecordMapper' and 'com.hfut.xiaozu.incident.process.IncidentProcessRecordMapper' mapperInterface
2025-06-30 19:18:44.680 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentRecordMapper' and 'com.hfut.xiaozu.incident.record.IncidentRecordMapper' mapperInterface
2025-06-30 19:18:44.680 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userMapper' and 'com.hfut.xiaozu.user.account.UserMapper' mapperInterface
2025-06-30 19:18:44.682 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityOcrRecordMapper' and 'com.hfut.xiaozu.user.idv.IdentityOcrRecordMapper' mapperInterface
2025-06-30 19:18:44.682 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityVerifySubmitMapper' and 'com.hfut.xiaozu.user.idv.IdentityVerifySubmitMapper' mapperInterface
2025-06-30 19:18:44.725 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 111 ] - Tomcat initialized with port 8080 (http)
2025-06-30 19:18:44.725 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardService : 168 ] - Starting service [Tomcat]
2025-06-30 19:18:44.725 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardEngine : 168 ] - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-30 19:18:44.741 [ restartedMain ] - [ INFO  ] [ o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] : 168 ] - Initializing Spring embedded WebApplicationContext
2025-06-30 19:18:44.741 [ restartedMain ] - [ INFO  ] [ o.s.b.w.s.c.ServletWebServerApplicationContext : 301 ] - Root WebApplicationContext: initialization completed in 284 ms
2025-06-30 19:18:44.753 [ restartedMain ] - [ INFO  ] [ c.a.d.s.b.a.DruidDataSourceAutoConfigure : 55 ] - Init DruidDataSource
2025-06-30 19:18:44.838 [ restartedMain ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 1009 ] - {dataSource-4} inited
2025-06-30 19:18:44.864 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.xml]'
2025-06-30 19:18:44.872 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.xml]'
2025-06-30 19:18:44.875 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.xml]'
2025-06-30 19:18:44.879 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.xml]'
2025-06-30 19:18:44.882 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.xml]'
2025-06-30 19:18:44.886 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.xml]'
2025-06-30 19:18:44.889 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.xml]'
2025-06-30 19:18:44.892 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.xml]'
2025-06-30 19:18:44.896 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.xml]'
2025-06-30 19:18:44.901 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.xml]'
2025-06-30 19:18:44.905 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.xml]'
2025-06-30 19:18:44.910 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.xml]'
2025-06-30 19:18:44.914 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.xml]'
2025-06-30 19:18:44.919 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.xml]'
2025-06-30 19:18:44.923 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.xml]'
2025-06-30 19:18:56.079 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 319 ] - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-06-30 19:18:56.109 [ restartedMain ] - [ INFO  ] [ o.s.b.d.autoconfigure.OptionalLiveReloadServer : 59 ] - LiveReload server is running on port 35729
2025-06-30 19:18:56.133 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 243 ] - Tomcat started on port 8080 (http) with context path '/'
2025-06-30 19:18:56.137 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 59 ] - Started XiaozuApplication in 11.715 seconds (process running for 6808.035)
2025-06-30 19:18:56.138 [ restartedMain ] - [ INFO  ] [ o.s.b.d.a.ConditionEvaluationDeltaLoggingListener : 63 ] - Condition evaluation unchanged
2025-06-30 19:18:59.691 [ File Watcher ] - [ INFO  ] [ o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener : 211 ] - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-30 19:18:59.693 [ Thread-15 ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 54 ] - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-30 19:18:59.752 [ tomcat-shutdown ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 76 ] - Graceful shutdown complete
2025-06-30 19:18:59.755 [ Thread-15 ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2174 ] - {dataSource-4} closing ...
2025-06-30 19:18:59.755 [ Thread-15 ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2247 ] - {dataSource-4} closed
2025-06-30 19:18:59.824 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 53 ] - Starting XiaozuApplication using Java 17.0.2 with PID 14952 (D:\gitlab\xiangmu\community-backend\target\classes started by joy0531 in D:\gitlab\xiangmu\community-backend)
2025-06-30 19:18:59.824 [ restartedMain ] - [ DEBUG ] [ com.hfut.xiaozu.XiaozuApplication : 54 ] - Running with Spring Boot v3.4.7, Spring v6.2.8
2025-06-30 19:18:59.824 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 652 ] - No active profile set, falling back to 1 default profile: "default"
2025-06-30 19:18:59.988 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 247 ] - Searching for mappers annotated with @Mapper
2025-06-30 19:18:59.988 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 251 ] - Using auto-configuration base package 'com.hfut.xiaozu'
2025-06-30 19:19:00.036 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.class]
2025-06-30 19:19:00.036 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.class]
2025-06-30 19:19:00.037 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.class]
2025-06-30 19:19:00.037 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.class]
2025-06-30 19:19:00.037 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.class]
2025-06-30 19:19:00.037 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.class]
2025-06-30 19:19:00.037 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.class]
2025-06-30 19:19:00.037 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.class]
2025-06-30 19:19:00.037 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.class]
2025-06-30 19:19:00.037 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.class]
2025-06-30 19:19:00.037 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.class]
2025-06-30 19:19:00.037 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.class]
2025-06-30 19:19:00.037 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.class]
2025-06-30 19:19:00.037 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.class]
2025-06-30 19:19:00.037 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.class]
2025-06-30 19:19:00.037 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleHouseBindingMapper' and 'com.hfut.xiaozu.car.bind.VehicleHouseBindingMapper' mapperInterface
2025-06-30 19:19:00.038 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleInfoMapper' and 'com.hfut.xiaozu.car.info.VehicleInfoMapper' mapperInterface
2025-06-30 19:19:00.038 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'familyMemberMapper' and 'com.hfut.xiaozu.family.info.FamilyMemberMapper' mapperInterface
2025-06-30 19:19:00.038 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userHouseBindingMapper' and 'com.hfut.xiaozu.house.binding.UserHouseBindingMapper' mapperInterface
2025-06-30 19:19:00.038 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityGridMapper' and 'com.hfut.xiaozu.house.grid.CommunityGridMapper' mapperInterface
2025-06-30 19:19:00.039 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'gridItemArchiveMapper' and 'com.hfut.xiaozu.house.grid.GridItemArchiveMapper' mapperInterface
2025-06-30 19:19:00.039 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'buildingInfoMapper' and 'com.hfut.xiaozu.house.information.BuildingInfoMapper' mapperInterface
2025-06-30 19:19:00.039 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityInfoMapper' and 'com.hfut.xiaozu.house.information.CommunityInfoMapper' mapperInterface
2025-06-30 19:19:00.040 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'houseInfoMapper' and 'com.hfut.xiaozu.house.information.HouseInfoMapper' mapperInterface
2025-06-30 19:19:00.040 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'unitInfoMapper' and 'com.hfut.xiaozu.house.information.UnitInfoMapper' mapperInterface
2025-06-30 19:19:00.040 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentProcessRecordMapper' and 'com.hfut.xiaozu.incident.process.IncidentProcessRecordMapper' mapperInterface
2025-06-30 19:19:00.041 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentRecordMapper' and 'com.hfut.xiaozu.incident.record.IncidentRecordMapper' mapperInterface
2025-06-30 19:19:00.041 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userMapper' and 'com.hfut.xiaozu.user.account.UserMapper' mapperInterface
2025-06-30 19:19:00.041 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityOcrRecordMapper' and 'com.hfut.xiaozu.user.idv.IdentityOcrRecordMapper' mapperInterface
2025-06-30 19:19:00.042 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityVerifySubmitMapper' and 'com.hfut.xiaozu.user.idv.IdentityVerifySubmitMapper' mapperInterface
2025-06-30 19:19:00.086 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 111 ] - Tomcat initialized with port 8080 (http)
2025-06-30 19:19:00.086 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardService : 168 ] - Starting service [Tomcat]
2025-06-30 19:19:00.086 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardEngine : 168 ] - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-30 19:19:00.103 [ restartedMain ] - [ INFO  ] [ o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] : 168 ] - Initializing Spring embedded WebApplicationContext
2025-06-30 19:19:00.103 [ restartedMain ] - [ INFO  ] [ o.s.b.w.s.c.ServletWebServerApplicationContext : 301 ] - Root WebApplicationContext: initialization completed in 277 ms
2025-06-30 19:19:00.114 [ restartedMain ] - [ INFO  ] [ c.a.d.s.b.a.DruidDataSourceAutoConfigure : 55 ] - Init DruidDataSource
2025-06-30 19:19:00.198 [ restartedMain ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 1009 ] - {dataSource-5} inited
2025-06-30 19:19:00.226 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.xml]'
2025-06-30 19:19:00.230 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.xml]'
2025-06-30 19:19:00.233 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.xml]'
2025-06-30 19:19:00.237 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.xml]'
2025-06-30 19:19:00.240 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.xml]'
2025-06-30 19:19:00.245 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.xml]'
2025-06-30 19:19:00.247 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.xml]'
2025-06-30 19:19:00.250 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.xml]'
2025-06-30 19:19:00.252 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.xml]'
2025-06-30 19:19:00.255 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.xml]'
2025-06-30 19:19:00.258 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.xml]'
2025-06-30 19:19:00.263 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.xml]'
2025-06-30 19:19:00.270 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.xml]'
2025-06-30 19:19:00.275 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.xml]'
2025-06-30 19:19:00.278 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.xml]'
2025-06-30 19:19:00.384 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 319 ] - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-06-30 19:19:00.416 [ restartedMain ] - [ INFO  ] [ o.s.b.d.autoconfigure.OptionalLiveReloadServer : 59 ] - LiveReload server is running on port 35729
2025-06-30 19:19:00.440 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 243 ] - Tomcat started on port 8080 (http) with context path '/'
2025-06-30 19:19:00.444 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 59 ] - Started XiaozuApplication in 0.651 seconds (process running for 6812.343)
2025-06-30 19:19:00.446 [ restartedMain ] - [ INFO  ] [ o.s.b.d.a.ConditionEvaluationDeltaLoggingListener : 63 ] - Condition evaluation unchanged
2025-06-30 19:19:13.341 [ File Watcher ] - [ INFO  ] [ o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener : 211 ] - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-30 19:19:13.342 [ Thread-19 ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 54 ] - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-30 19:19:13.402 [ tomcat-shutdown ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 76 ] - Graceful shutdown complete
2025-06-30 19:19:13.405 [ Thread-19 ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2174 ] - {dataSource-5} closing ...
2025-06-30 19:19:13.405 [ Thread-19 ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2247 ] - {dataSource-5} closed
2025-06-30 19:19:13.470 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 53 ] - Starting XiaozuApplication using Java 17.0.2 with PID 14952 (D:\gitlab\xiangmu\community-backend\target\classes started by joy0531 in D:\gitlab\xiangmu\community-backend)
2025-06-30 19:19:13.470 [ restartedMain ] - [ DEBUG ] [ com.hfut.xiaozu.XiaozuApplication : 54 ] - Running with Spring Boot v3.4.7, Spring v6.2.8
2025-06-30 19:19:13.470 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 652 ] - No active profile set, falling back to 1 default profile: "default"
2025-06-30 19:19:13.638 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 247 ] - Searching for mappers annotated with @Mapper
2025-06-30 19:19:13.639 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 251 ] - Using auto-configuration base package 'com.hfut.xiaozu'
2025-06-30 19:19:13.688 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.class]
2025-06-30 19:19:13.688 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.class]
2025-06-30 19:19:13.688 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.class]
2025-06-30 19:19:13.688 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.class]
2025-06-30 19:19:13.688 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.class]
2025-06-30 19:19:13.688 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.class]
2025-06-30 19:19:13.688 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.class]
2025-06-30 19:19:13.688 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.class]
2025-06-30 19:19:13.688 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.class]
2025-06-30 19:19:13.688 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.class]
2025-06-30 19:19:13.688 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.class]
2025-06-30 19:19:13.688 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.class]
2025-06-30 19:19:13.688 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.class]
2025-06-30 19:19:13.689 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.class]
2025-06-30 19:19:13.689 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.class]
2025-06-30 19:19:13.689 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleHouseBindingMapper' and 'com.hfut.xiaozu.car.bind.VehicleHouseBindingMapper' mapperInterface
2025-06-30 19:19:13.689 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleInfoMapper' and 'com.hfut.xiaozu.car.info.VehicleInfoMapper' mapperInterface
2025-06-30 19:19:13.689 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'familyMemberMapper' and 'com.hfut.xiaozu.family.info.FamilyMemberMapper' mapperInterface
2025-06-30 19:19:13.690 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userHouseBindingMapper' and 'com.hfut.xiaozu.house.binding.UserHouseBindingMapper' mapperInterface
2025-06-30 19:19:13.690 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityGridMapper' and 'com.hfut.xiaozu.house.grid.CommunityGridMapper' mapperInterface
2025-06-30 19:19:13.690 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'gridItemArchiveMapper' and 'com.hfut.xiaozu.house.grid.GridItemArchiveMapper' mapperInterface
2025-06-30 19:19:13.690 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'buildingInfoMapper' and 'com.hfut.xiaozu.house.information.BuildingInfoMapper' mapperInterface
2025-06-30 19:19:13.691 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityInfoMapper' and 'com.hfut.xiaozu.house.information.CommunityInfoMapper' mapperInterface
2025-06-30 19:19:13.691 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'houseInfoMapper' and 'com.hfut.xiaozu.house.information.HouseInfoMapper' mapperInterface
2025-06-30 19:19:13.691 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'unitInfoMapper' and 'com.hfut.xiaozu.house.information.UnitInfoMapper' mapperInterface
2025-06-30 19:19:13.692 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentProcessRecordMapper' and 'com.hfut.xiaozu.incident.process.IncidentProcessRecordMapper' mapperInterface
2025-06-30 19:19:13.692 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentRecordMapper' and 'com.hfut.xiaozu.incident.record.IncidentRecordMapper' mapperInterface
2025-06-30 19:19:13.692 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userMapper' and 'com.hfut.xiaozu.user.account.UserMapper' mapperInterface
2025-06-30 19:19:13.693 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityOcrRecordMapper' and 'com.hfut.xiaozu.user.idv.IdentityOcrRecordMapper' mapperInterface
2025-06-30 19:19:13.693 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityVerifySubmitMapper' and 'com.hfut.xiaozu.user.idv.IdentityVerifySubmitMapper' mapperInterface
2025-06-30 19:19:13.739 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 111 ] - Tomcat initialized with port 8080 (http)
2025-06-30 19:19:13.740 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardService : 168 ] - Starting service [Tomcat]
2025-06-30 19:19:13.740 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardEngine : 168 ] - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-30 19:19:13.757 [ restartedMain ] - [ INFO  ] [ o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] : 168 ] - Initializing Spring embedded WebApplicationContext
2025-06-30 19:19:13.757 [ restartedMain ] - [ INFO  ] [ o.s.b.w.s.c.ServletWebServerApplicationContext : 301 ] - Root WebApplicationContext: initialization completed in 285 ms
2025-06-30 19:19:13.770 [ restartedMain ] - [ INFO  ] [ c.a.d.s.b.a.DruidDataSourceAutoConfigure : 55 ] - Init DruidDataSource
2025-06-30 19:19:13.895 [ restartedMain ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 1009 ] - {dataSource-6} inited
2025-06-30 19:19:13.920 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.xml]'
2025-06-30 19:19:13.924 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.xml]'
2025-06-30 19:19:13.927 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.xml]'
2025-06-30 19:19:13.930 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.xml]'
2025-06-30 19:19:13.933 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.xml]'
2025-06-30 19:19:13.939 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.xml]'
2025-06-30 19:19:13.944 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.xml]'
2025-06-30 19:19:13.947 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.xml]'
2025-06-30 19:19:13.952 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.xml]'
2025-06-30 19:19:13.955 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.xml]'
2025-06-30 19:19:13.959 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.xml]'
2025-06-30 19:19:13.963 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.xml]'
2025-06-30 19:19:13.967 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.xml]'
2025-06-30 19:19:13.972 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.xml]'
2025-06-30 19:19:13.976 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.xml]'
2025-06-30 19:19:14.097 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 319 ] - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-06-30 19:19:14.127 [ restartedMain ] - [ INFO  ] [ o.s.b.d.autoconfigure.OptionalLiveReloadServer : 59 ] - LiveReload server is running on port 35729
2025-06-30 19:19:14.152 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 243 ] - Tomcat started on port 8080 (http) with context path '/'
2025-06-30 19:19:14.155 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 59 ] - Started XiaozuApplication in 0.714 seconds (process running for 6826.054)
2025-06-30 19:19:14.157 [ restartedMain ] - [ INFO  ] [ o.s.b.d.a.ConditionEvaluationDeltaLoggingListener : 63 ] - Condition evaluation unchanged
2025-06-30 19:19:41.681 [ File Watcher ] - [ INFO  ] [ o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener : 211 ] - Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-30 19:19:41.682 [ Thread-23 ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 54 ] - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-30 19:19:41.746 [ tomcat-shutdown ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 76 ] - Graceful shutdown complete
2025-06-30 19:19:41.748 [ Thread-23 ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2174 ] - {dataSource-6} closing ...
2025-06-30 19:19:41.750 [ Thread-23 ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2247 ] - {dataSource-6} closed
2025-06-30 19:19:41.821 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 53 ] - Starting XiaozuApplication using Java 17.0.2 with PID 14952 (D:\gitlab\xiangmu\community-backend\target\classes started by joy0531 in D:\gitlab\xiangmu\community-backend)
2025-06-30 19:19:41.821 [ restartedMain ] - [ DEBUG ] [ com.hfut.xiaozu.XiaozuApplication : 54 ] - Running with Spring Boot v3.4.7, Spring v6.2.8
2025-06-30 19:19:41.821 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 652 ] - No active profile set, falling back to 1 default profile: "default"
2025-06-30 19:19:42.011 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 247 ] - Searching for mappers annotated with @Mapper
2025-06-30 19:19:42.011 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 251 ] - Using auto-configuration base package 'com.hfut.xiaozu'
2025-06-30 19:19:42.062 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.class]
2025-06-30 19:19:42.062 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.class]
2025-06-30 19:19:42.062 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.class]
2025-06-30 19:19:42.062 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.class]
2025-06-30 19:19:42.062 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.class]
2025-06-30 19:19:42.062 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.class]
2025-06-30 19:19:42.062 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.class]
2025-06-30 19:19:42.062 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.class]
2025-06-30 19:19:42.062 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.class]
2025-06-30 19:19:42.062 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.class]
2025-06-30 19:19:42.062 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.class]
2025-06-30 19:19:42.062 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.class]
2025-06-30 19:19:42.062 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.class]
2025-06-30 19:19:42.063 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.class]
2025-06-30 19:19:42.063 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.class]
2025-06-30 19:19:42.063 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleHouseBindingMapper' and 'com.hfut.xiaozu.car.bind.VehicleHouseBindingMapper' mapperInterface
2025-06-30 19:19:42.063 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleInfoMapper' and 'com.hfut.xiaozu.car.info.VehicleInfoMapper' mapperInterface
2025-06-30 19:19:42.064 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'familyMemberMapper' and 'com.hfut.xiaozu.family.info.FamilyMemberMapper' mapperInterface
2025-06-30 19:19:42.064 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userHouseBindingMapper' and 'com.hfut.xiaozu.house.binding.UserHouseBindingMapper' mapperInterface
2025-06-30 19:19:42.064 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityGridMapper' and 'com.hfut.xiaozu.house.grid.CommunityGridMapper' mapperInterface
2025-06-30 19:19:42.065 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'gridItemArchiveMapper' and 'com.hfut.xiaozu.house.grid.GridItemArchiveMapper' mapperInterface
2025-06-30 19:19:42.065 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'buildingInfoMapper' and 'com.hfut.xiaozu.house.information.BuildingInfoMapper' mapperInterface
2025-06-30 19:19:42.065 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityInfoMapper' and 'com.hfut.xiaozu.house.information.CommunityInfoMapper' mapperInterface
2025-06-30 19:19:42.065 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'houseInfoMapper' and 'com.hfut.xiaozu.house.information.HouseInfoMapper' mapperInterface
2025-06-30 19:19:42.067 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'unitInfoMapper' and 'com.hfut.xiaozu.house.information.UnitInfoMapper' mapperInterface
2025-06-30 19:19:42.067 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentProcessRecordMapper' and 'com.hfut.xiaozu.incident.process.IncidentProcessRecordMapper' mapperInterface
2025-06-30 19:19:42.067 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentRecordMapper' and 'com.hfut.xiaozu.incident.record.IncidentRecordMapper' mapperInterface
2025-06-30 19:19:42.068 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userMapper' and 'com.hfut.xiaozu.user.account.UserMapper' mapperInterface
2025-06-30 19:19:42.068 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityOcrRecordMapper' and 'com.hfut.xiaozu.user.idv.IdentityOcrRecordMapper' mapperInterface
2025-06-30 19:19:42.068 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityVerifySubmitMapper' and 'com.hfut.xiaozu.user.idv.IdentityVerifySubmitMapper' mapperInterface
2025-06-30 19:19:42.118 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 111 ] - Tomcat initialized with port 8080 (http)
2025-06-30 19:19:42.119 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardService : 168 ] - Starting service [Tomcat]
2025-06-30 19:19:42.119 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardEngine : 168 ] - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-30 19:19:42.135 [ restartedMain ] - [ INFO  ] [ o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] : 168 ] - Initializing Spring embedded WebApplicationContext
2025-06-30 19:19:42.136 [ restartedMain ] - [ INFO  ] [ o.s.b.w.s.c.ServletWebServerApplicationContext : 301 ] - Root WebApplicationContext: initialization completed in 313 ms
2025-06-30 19:19:42.148 [ restartedMain ] - [ INFO  ] [ c.a.d.s.b.a.DruidDataSourceAutoConfigure : 55 ] - Init DruidDataSource
2025-06-30 19:19:42.244 [ restartedMain ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 1009 ] - {dataSource-7} inited
2025-06-30 19:19:42.268 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.xml]'
2025-06-30 19:19:42.271 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.xml]'
2025-06-30 19:19:42.273 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.xml]'
2025-06-30 19:19:42.278 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.xml]'
2025-06-30 19:19:42.282 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.xml]'
2025-06-30 19:19:42.285 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.xml]'
2025-06-30 19:19:42.288 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.xml]'
2025-06-30 19:19:42.291 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.xml]'
2025-06-30 19:19:42.294 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.xml]'
2025-06-30 19:19:42.297 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.xml]'
2025-06-30 19:19:42.301 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.xml]'
2025-06-30 19:19:42.304 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.xml]'
2025-06-30 19:19:42.307 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.xml]'
2025-06-30 19:19:42.309 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.xml]'
2025-06-30 19:19:42.312 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.xml]'
2025-06-30 19:19:42.414 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 319 ] - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-06-30 19:19:42.442 [ restartedMain ] - [ INFO  ] [ o.s.b.d.autoconfigure.OptionalLiveReloadServer : 59 ] - LiveReload server is running on port 35729
2025-06-30 19:19:42.467 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 243 ] - Tomcat started on port 8080 (http) with context path '/'
2025-06-30 19:19:42.470 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 59 ] - Started XiaozuApplication in 0.681 seconds (process running for 6854.368)
2025-06-30 19:19:42.471 [ restartedMain ] - [ INFO  ] [ o.s.b.d.a.ConditionEvaluationDeltaLoggingListener : 63 ] - Condition evaluation unchanged
