# 注册功能前后端对接文档

## 概述

本文档描述了社区管理系统中用户注册功能的前后端对接实现，包括API接口设计、数据验证、错误处理等。

## API接口规范

### 注册接口

**接口地址**: `POST /api/auth/register`

**请求头**:
```
Content-Type: application/json
```

**请求参数**:
```json
{
  "userName": "string",    // 用户名，2-50个字符
  "phone": "string",       // 手机号，11位数字，1开头
  "password": "string",    // 密码，6-200个字符
  "userType": "integer"    // 用户类型：1-居民，2-网格员，3-管理员
}
```

**响应格式**:
```json
{
  "code": 200,           // 状态码：200-成功，其他-失败
  "msg": "注册成功",      // 响应消息
  "data": null          // 响应数据（注册成功时通常为null）
}
```

## 前端实现

### 1. API服务封装

创建了 `vue/src/services/authApi.js` 文件，封装了用户认证相关的API调用：

```javascript
export const authApi = {
  register: async (userData) => {
    // 数据验证
    // API调用
    // 错误处理
  }
};
```

### 2. 注册页面改进

修改了 `vue/src/views/Register.vue`：

- 将邮箱字段改为手机号字段
- 添加用户类型选择（居民/网格员/管理员）
- 集成真实的API调用
- 完善前端验证逻辑
- 改进错误处理和用户反馈

### 3. 数据验证

前端验证包括：
- 用户类型必选
- 用户名长度验证（2-50字符）
- 手机号格式验证（11位，1开头）
- 密码长度验证（6-200字符）
- 确认密码一致性验证

## 后端实现

### 1. DTO改进

修改了 `UserRegisterDTO.java`：

```java
public class UserRegisterDTO {
    @NotBlank(message = "用户名不能为空")
    @Size(min=2, max=50, message = "用户名长度需在2-50个字符之间")
    private String userName;

    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    @NotBlank(message = "密码不能为空")
    @Size(min=6, max=200, message = "密码长度需在6-200个字符之间")
    private String password;

    @NotNull(message = "用户类型不能为空")
    private Integer userType = 1;
}
```

### 2. 控制器改进

改进了 `UserController.java`：
- 完善参数验证
- 统一错误处理
- 改进API文档注解
- 添加用户类型验证

### 3. 服务层改进

改进了 `UserService.java`：
- 添加事务支持
- 密码加密（BCrypt）
- 重复性检查（用户名、手机号）
- 异常处理

### 4. 数据库映射

修改了 `UserMapper.xml`：
```xml
<insert id="save">
    INSERT INTO community.user_account(user_name, phone, password, user_type, create_time, update_time) 
    VALUES(#{userName}, #{phone}, #{password}, #{userType}, NOW(), NOW());
</insert>
```

### 5. 跨域配置

创建了 `CorsConfig.java` 解决前后端跨域问题：
- 允许所有域名（开发环境）
- 支持所有HTTP方法
- 允许携带认证信息

## 测试方法

### 1. 使用测试页面

打开 `vue/test-register.html` 进行功能测试：

1. 启动后端服务（端口8080）
2. 在浏览器中打开测试页面
3. 填写注册信息进行测试

### 2. 使用Vue应用

1. 启动后端服务：
```bash
cd community-backend
mvn spring-boot:run
```

2. 启动前端服务：
```bash
cd vue
npm run dev
```

3. 访问注册页面进行测试

### 3. API测试工具

使用Postman或curl测试API：

```bash
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "userName": "testuser",
    "phone": "***********",
    "password": "123456",
    "userType": 1
  }'
```

## 数据库准备

确保数据库中存在 `user_account` 表：

```sql
CREATE TABLE user_account (
  id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  user_name VARCHAR(50) NOT NULL,
  phone VARCHAR(20) NOT NULL,
  password VARCHAR(200) NOT NULL,
  user_type TINYINT UNSIGNED DEFAULT 1,
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 错误处理

### 常见错误及解决方案

1. **跨域错误**: 确保CorsConfig配置正确
2. **数据库连接错误**: 检查application.yml中的数据库配置
3. **验证失败**: 检查前后端验证逻辑是否一致
4. **重复注册**: 系统会检查用户名和手机号的唯一性

### 错误响应示例

```json
{
  "code": 500,
  "msg": "该手机号已经被注册; 该用户名已经被注册",
  "data": null
}
```

## 安全考虑

1. **密码加密**: 使用BCrypt进行密码哈希
2. **输入验证**: 前后端双重验证
3. **SQL注入防护**: 使用MyBatis参数化查询
4. **跨域限制**: 生产环境应限制允许的域名

## 后续优化建议

1. 添加短信验证码功能
2. 实现邮箱验证
3. 添加图形验证码
4. 完善日志记录
5. 添加注册频率限制
6. 实现用户激活机制
