package com.hfut.xiaozu.house.information;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 单元信息表
 * @TableName unit_info
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class UnitInfo {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 楼栋ID
     */
    private Long buildingId;

    /**
     * 单元编号
     */
    private String unitCode;

    /**
     * 楼层数
     */
    private Integer floorCount;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
