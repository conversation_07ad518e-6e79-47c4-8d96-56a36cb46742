import { createRouter, createWebHistory } from 'vue-router';
import { useUserStore } from './stores/userStore';
import Login from './views/Login.vue';
import Register from './views/Register.vue';
import Dashboard from './views/Dashboard.vue';
import UserProfile from './views/UserProfile.vue';
import ResidentProfile from './views/ResidentProfile.vue';
import AppLayout from './layouts/AppLayout.vue';
import SidebarLayout from './layouts/SidebarLayout.vue';
import PropertyHome from './views/PropertyHome.vue';
import ResidentHome from './views/ResidentHome.vue';
import LogoTest from './views/LogoTest.vue';

const routes = [
  {
    path: '/',
    redirect: '/login' // 默认重定向到登录页
  },
  // 登录和注册页面使用简单布局
  {
    path: '/login',
    component: AppLayout,
    children: [
      {
        path: '',
        name: 'Login',
        component: Login
      }
    ]
  },
  {
    path: '/register',
    component: AppLayout,
    children: [
      {
        path: '',
        name: 'Register',
        component: Register
      }
    ]
  },
  // 保留原有的dashboard路由作为兼容
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: { requiresAuth: true }
  },
  // Logo测试页面
  {
    path: '/logo-test',
    name: 'LogoTest',
    component: LogoTest
  },
  // 地图测试页面
  {
    path: '/map-test',
    name: 'MapTest',
    component: () => import('./views/DrawingTest.vue')
  },
  // 简单地图测试页面
  {
    path: '/simple-map-test',
    name: 'SimpleMapTest',
    component: () => import('./views/SimpleMapTest.vue')
  },
  // 投票API测试页面
  {
    path: '/vote-api-test',
    name: 'VoteApiTest',
    component: () => import('./views/VoteApiTest.vue')
  },
  // 投票提交测试页面
  {
    path: '/vote-submit-test',
    name: 'VoteSubmitTest',
    component: () => import('./views/VoteSubmitTest.vue')
  },
  // 建筑物图层测试页面
  {
    path: '/building-layer-test',
    name: 'BuildingLayerTest',
    component: () => import('./views/BuildingLayerTest.vue')
  },
  // 物业管理主页路由
  {
    path: '/property-home',
    name: 'PropertyHome',
    component: SidebarLayout,
    meta: { requiresAuth: true, userType: 'property' },
    children: [
      {
        path: '',
        name: 'PropertyHomeDefault',
        component: PropertyHome
      },
      {
        path: 'profile',
        name: 'PropertyProfile',
        component: UserProfile
      }
    ]
  },
  // 居民端主页路由
  {
    path: '/resident-home',
    name: 'ResidentHome',
    component: SidebarLayout,
    meta: { requiresAuth: true, userType: 'resident' },
    children: [
      {
        path: '',
        name: 'ResidentHomeDefault',
        component: ResidentHome
      },
      {
        path: 'profile',
        name: 'ResidentProfile',
        component: ResidentProfile
      }
    ]
  },
  // 物业管理功能路由（占位符）
  {
    path: '/property',
    component: SidebarLayout,
    meta: { requiresAuth: true, userType: 'property' },
    children: [
      {
        path: 'data-overview',
        name: 'DataOverview',
        component: () => import('./views/DataOverview.vue')
      },
      {
        path: 'full-archive',
        name: 'FullArchive',
        component: () => import('./views/placeholders/PlaceholderPage.vue')
      },
      {
        path: 'event-management',
        name: 'EventManagement',
        component: () => import('./views/EventManagement.vue')
      },
      {
        path: 'event-map',
        name: 'EventMap',
        component: () => import('./views/EventMapView.vue')
      },
      {
        path: 'patrol-management',
        name: 'PatrolManagement',
        component: () => import('./views/PatrolRouteManagement.vue')
      },
      {
        path: 'gis-grid',
        name: 'GISGrid',
        component: () => import('./views/GridManagement.vue')
      },
      {
        path: 'gis-management',
        name: 'GISManagement',
        component: () => import('./views/GISManagement.vue')
      },
      {
        path: 'vote-management',
        name: 'VoteManagement',
        component: () => import('./views/property/VoteManagement.vue')
      },
      {
        path: 'info-collection',
        name: 'InfoCollection',
        component: () => import('./views/InfoCollection.vue')
      },
      {
        path: 'house-info-collection',
        name: 'HouseInfoCollection',
        component: () => import('./views/HouseInfoCollection.vue')
      },
      {
        path: 'vehicle-info-collection',
        name: 'VehicleInfoCollection',
        component: () => import('./views/property/VehicleInfoCollection.vue')
      },
      {
        path: 'drawing-test',
        name: 'DrawingTest',
        component: () => import('./views/DrawingTest.vue')
      }
    ]
  },
  // 居民端功能路由（占位符）
  {
    path: '/resident',
    component: SidebarLayout,
    meta: { requiresAuth: true, userType: 'resident' },
    children: [
      {
        path: 'real-name-auth',
        name: 'RealNameAuth',
        component: () => import('./views/resident/RealNameAuth.vue')
      },
      {
        path: 'house-management',
        name: 'HouseManagement',
        component: () => import('./views/resident/HouseManagement.vue')
      },
      {
        path: 'vehicle-management',
        name: 'VehicleManagement',
        component: () => import('./views/resident/VehicleManagement.vue')
      },
      {
        path: 'family-management',
        name: 'FamilyManagement',
        component: () => import('./views/resident/FamilyManagement.vue')
      },
      {
        path: 'issue-report',
        name: 'IssueReport',
        component: () => import('./views/resident/IssueReport.vue')
      },
      {
        path: 'budget-vote',
        name: 'BudgetVote',
        component: () => import('./views/resident/BudgetVote.vue')
      }
    ]
  },
  // 公共模块路由（保留语音演示）
  {
    path: '/common',
    component: SidebarLayout,
    meta: { requiresAuth: true },
    children: [
      // 删除了四个无用模块的路由：user-permission, file-storage, message-notification, log-monitor
      {
        path: 'voice-demo',
        name: 'VoiceDemo',
        component: () => import('./views/VoiceDemo.vue'),
        meta: { title: '语音文字互转演示' }
      }
    ]
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

// 临时禁用路由守卫进行调试
router.beforeEach((to, from, next) => {
  console.log(`🛡️ 路由守卫: ${from.path} → ${to.path}`);
  console.log('� 路由守卫已禁用，直接允许访问');
  next();
});

export default router;