<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hfut.xiaozu.house.grid.CommunityGridMapper">

    <resultMap id="BaseResultMap" type="com.hfut.xiaozu.house.grid.CommunityGridEntity">
            <id property="id" column="id" />
            <result property="communityId" column="community_id" />
            <result property="gridName" column="grid_name" />
            <result property="boundaryGeojson" column="boundary_geojson" />
            <result property="responsibleId" column="responsible_id" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,community_id,grid_name,boundary_geojson,responsible_id,create_time,
        update_time
    </sql>



    <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from community_grid
        where  id = #{id}
    </select>

    <select id="listAll" resultType="com.hfut.xiaozu.house.grid.CommunityGridEntity">
        select
        <include refid="Base_Column_List" />
        from community_grid
    </select>

    <select id="getByResponsibleId" resultType="com.hfut.xiaozu.house.grid.CommunityGridEntity">
        select
        <include refid="Base_Column_List" />
        from community_grid
        where  responsible_id = #{responsibleId}
    </select>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from community.community_grid
        where  id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.hfut.xiaozu.house.grid.CommunityGridEntity" useGeneratedKeys="true">
        insert into community.community_grid
        ( community_id,grid_name,boundary_geojson,responsible_id)
        values (#{communityId},#{gridName},#{boundaryGeojson},#{responsibleId})
    </insert>

    <update id="update">
        update community.community_grid
        <set>
            <if test="communityId != null">community_id = #{communityId},</if>
            <if test="gridName != null">grid_name = #{gridName},</if>
            <if test="boundaryGeojson != null">boundary_geojson = #{boundaryGeojson},</if>
            <if test="responsibleId != null">responsible_id = #{responsibleId},</if>
        </set>
        where id = #{id}
    </update>

</mapper>
