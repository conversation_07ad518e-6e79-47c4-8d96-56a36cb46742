<template>
  <div class="family-management">
    <div class="page-header">
      <h1>家人管理</h1>
      <p class="subtitle">管理您的家庭成员信息</p>

      <!-- 页面切换按钮 -->
      <div class="page-tabs">
        <button
          class="tab-btn"
          :class="{ active: currentPage === 'list' }"
          @click="switchPage('list')"
        >
          <span class="tab-icon">👨‍👩‍👧‍👦</span>
          家人列表
        </button>
        <button
          class="tab-btn"
          :class="{ active: currentPage === 'add' }"
          @click="switchPage('add')"
        >
          <span class="tab-icon">➕</span>
          添加家人
        </button>
      </div>
    </div>

    <div class="management-container">
      <!-- 家人列表页面 -->
      <div v-if="currentPage === 'list'" class="family-list-page">
        <div class="family-list-section">
          <h2 class="section-title">
            <span class="title-icon">👨‍👩‍👧‍👦</span>
            我的家人列表
          </h2>

          <div v-if="loadingFamily" class="loading-state">
            <div class="loading-spinner"></div>
            <span>加载家人信息中...</span>
          </div>

          <div v-else-if="familyMembers.length === 0" class="empty-state">
            <div class="empty-icon">👨‍👩‍👧‍👦</div>
            <p>暂无家人信息</p>
            <button class="btn primary" @click="switchPage('add')">
              添加第一位家人
            </button>
          </div>

          <div v-else class="family-cards">
            <div
              v-for="member in familyMembers"
              :key="member.id"
              class="family-card"
            >
              <div class="member-header">
                <h3 class="member-name">{{ member.memberName }}</h3>
                <span class="primary-badge" v-if="member.isPrimary === 1">
                  主要联系人
                </span>
              </div>
              <div class="member-details">
                <div class="detail-row">
                  <span class="label">关系:</span>
                  <span class="value">{{ getRelationshipText(member.relationship) }}</span>
                </div>
                <div class="detail-row">
                  <span class="label">身份证号:</span>
                  <span class="value">{{ formatIdCard(member.idCard) }}</span>
                </div>
                <div class="detail-row">
                  <span class="label">联系电话:</span>
                  <span class="value">{{ member.contactPhone }}</span>
                </div>
                <div class="detail-row">
                  <span class="label">添加时间:</span>
                  <span class="value">{{ formatTime(member.createTime) }}</span>
                </div>
              </div>
              <div class="member-actions">
                <button class="btn secondary" @click="editFamilyMember(member)">
                  编辑信息
                </button>
                <button class="btn danger" @click="confirmDeleteMember(member)">
                  删除
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 添加家人页面 -->
      <div v-if="currentPage === 'add'" class="add-family-page">
        <div class="add-family-section">
          <h2 class="section-title">
            <span class="title-icon">➕</span>
            添加家人信息
          </h2>

          <div class="family-form-container">
            <div class="form-header">
              <p>您可以一次添加多位家人，请填写每位家人的详细信息。</p>
              <button class="btn secondary" @click="addMemberForm">
                + 添加更多家人
              </button>
            </div>

            <form @submit.prevent="submitFamilyMembers" class="family-form">
              <div
                v-for="(member, index) in familyForms"
                :key="index"
                class="member-form"
              >
                <div class="member-form-header">
                  <h4>家人 {{ index + 1 }}</h4>
                  <button
                    v-if="familyForms.length > 1"
                    type="button"
                    class="remove-btn"
                    @click="removeMemberForm(index)"
                  >
                    删除
                  </button>
                </div>

                <div class="form-grid">
                  <div class="form-group">
                    <label class="form-label">
                      <span class="required">*</span>
                      姓名
                    </label>
                    <input
                      v-model="member.memberName"
                      type="text"
                      class="form-input"
                      placeholder="请输入家人姓名"
                      required
                      maxlength="20"
                    />
                  </div>

                  <div class="form-group">
                    <label class="form-label">
                      <span class="required">*</span>
                      关系
                    </label>
                    <select
                      v-model="member.relationship"
                      class="form-select"
                      required
                    >
                      <option value="">请选择关系</option>
                      <option value="1">配偶</option>
                      <option value="2">子女</option>
                      <option value="3">父母</option>
                      <option value="4">兄弟姐妹</option>
                      <option value="5">其他</option>
                    </select>
                  </div>

                  <div class="form-group">
                    <label class="form-label">
                      <span class="required">*</span>
                      身份证号
                    </label>
                    <input
                      v-model="member.idCard"
                      type="text"
                      class="form-input"
                      placeholder="请输入身份证号码"
                      required
                      maxlength="18"
                      @blur="validateIdCard(member, index)"
                    />
                    <div v-if="member.idCardError" class="form-error">
                      {{ member.idCardError }}
                    </div>
                  </div>

                  <div class="form-group">
                    <label class="form-label">
                      <span class="required">*</span>
                      联系电话
                    </label>
                    <input
                      v-model="member.contactPhone"
                      type="tel"
                      class="form-input"
                      placeholder="请输入联系电话"
                      required
                      maxlength="11"
                      @blur="validatePhone(member, index)"
                    />
                    <div v-if="member.phoneError" class="form-error">
                      {{ member.phoneError }}
                    </div>
                  </div>

                  <div class="form-group full-width">
                    <label class="form-label">
                      是否为主要联系人
                    </label>
                    <div class="radio-group">
                      <label class="radio-option">
                        <input
                          v-model="member.isPrimary"
                          type="radio"
                          :value="1"
                        />
                        <span class="radio-label">是</span>
                      </label>
                      <label class="radio-option">
                        <input
                          v-model="member.isPrimary"
                          type="radio"
                          :value="0"
                        />
                        <span class="radio-label">否</span>
                      </label>
                    </div>
                    <div class="form-hint">主要联系人将作为紧急联系人</div>
                  </div>
                </div>
              </div>

              <div class="form-actions">
                <button
                  type="button"
                  class="btn secondary"
                  @click="resetForms"
                >
                  重置
                </button>
                <button
                  type="submit"
                  class="btn primary"
                  :disabled="submittingFamily"
                >
                  <span v-if="submittingFamily">提交中...</span>
                  <span v-else>保存家人信息</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑家人信息弹窗 -->
    <div v-if="showEditModal" class="modal-overlay" @click="closeEditModal">
      <div class="modal-content large-modal" @click.stop>
        <div class="modal-header">
          <h3>编辑家人信息</h3>
          <button class="close-btn" @click="closeEditModal">×</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="submitEditForm" class="edit-form">
            <div class="form-grid">
              <div class="form-group">
                <label class="form-label">
                  <span class="required">*</span>
                  姓名
                </label>
                <input
                  v-model="editForm.memberName"
                  type="text"
                  class="form-input"
                  placeholder="请输入家人姓名"
                  required
                  maxlength="20"
                />
              </div>

              <div class="form-group">
                <label class="form-label">
                  <span class="required">*</span>
                  关系
                </label>
                <select
                  v-model="editForm.relationship"
                  class="form-select"
                  required
                >
                  <option value="">请选择关系</option>
                  <option value="1">配偶</option>
                  <option value="2">子女</option>
                  <option value="3">父母</option>
                  <option value="4">兄弟姐妹</option>
                  <option value="5">其他</option>
                </select>
              </div>

              <div class="form-group">
                <label class="form-label">
                  <span class="required">*</span>
                  身份证号
                </label>
                <input
                  v-model="editForm.idCard"
                  type="text"
                  class="form-input"
                  placeholder="请输入身份证号码"
                  required
                  maxlength="18"
                  @blur="validateEditIdCard"
                />
                <div v-if="editForm.idCardError" class="form-error">
                  {{ editForm.idCardError }}
                </div>
              </div>

              <div class="form-group">
                <label class="form-label">
                  <span class="required">*</span>
                  联系电话
                </label>
                <input
                  v-model="editForm.contactPhone"
                  type="tel"
                  class="form-input"
                  placeholder="请输入联系电话"
                  required
                  maxlength="11"
                  @blur="validateEditPhone"
                />
                <div v-if="editForm.phoneError" class="form-error">
                  {{ editForm.phoneError }}
                </div>
              </div>

              <div class="form-group full-width">
                <label class="form-label">
                  是否为主要联系人
                </label>
                <div class="radio-group">
                  <label class="radio-option">
                    <input
                      v-model="editForm.isPrimary"
                      type="radio"
                      :value="1"
                    />
                    <span class="radio-label">是</span>
                  </label>
                  <label class="radio-option">
                    <input
                      v-model="editForm.isPrimary"
                      type="radio"
                      :value="0"
                    />
                    <span class="radio-label">否</span>
                  </label>
                </div>
                <div class="form-hint">主要联系人将作为紧急联系人</div>
              </div>
            </div>

            <div class="form-actions">
              <button
                type="button"
                class="btn secondary"
                @click="closeEditModal"
              >
                取消
              </button>
              <button
                type="submit"
                class="btn primary"
                :disabled="submittingEdit"
              >
                <span v-if="submittingEdit">更新中...</span>
                <span v-else>保存更改</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 删除确认弹窗 -->
    <div v-if="showDeleteModal" class="modal-overlay" @click="closeDeleteModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>确认删除</h3>
          <button class="close-btn" @click="closeDeleteModal">×</button>
        </div>
        <div class="modal-body">
          <div class="delete-confirmation">
            <div class="warning-icon">⚠️</div>
            <h4>确定要删除这位家人吗？</h4>
            <div v-if="memberToDelete" class="member-info">
              <p><strong>姓名：</strong>{{ memberToDelete.memberName }}</p>
              <p><strong>关系：</strong>{{ getRelationshipText(memberToDelete.relationship) }}</p>
              <p><strong>联系电话：</strong>{{ memberToDelete.contactPhone }}</p>
            </div>
            <p class="warning-text">此操作不可撤销，请谨慎操作。</p>

            <div class="delete-actions">
              <button
                type="button"
                class="btn secondary"
                @click="closeDeleteModal"
              >
                取消
              </button>
              <button
                type="button"
                class="btn danger"
                :disabled="submittingDelete"
                @click="deleteFamilyMemberHandler"
              >
                <span v-if="submittingDelete">删除中...</span>
                <span v-else>确认删除</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 自定义弹窗 -->
  <CustomModal
    :visible="modal.visible"
    :title="modal.title"
    :message="modal.message"
    :type="modal.type"
    @close="closeModal"
    @confirm="closeModal"
  />
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { addFamilyMembers, getMyFamilyMembers, updateFamilyMember, deleteFamilyMember } from '../../services/familyApi.js';
import CustomModal from '../../components/CustomModal.vue';

// 响应式数据
const currentPage = ref('list');
const familyMembers = ref([]);
const loadingFamily = ref(false);
const submittingFamily = ref(false);

// 弹窗状态
const modal = ref({
  visible: false,
  title: '提示',
  message: '',
  type: 'info'
});

// 编辑相关数据
const showEditModal = ref(false);
const submittingEdit = ref(false);
const editForm = ref({
  id: null,
  ownerId: null,
  memberName: '',
  relationship: '',
  idCard: '',
  contactPhone: '',
  isPrimary: 0,
  isDeleted: 0,
  createTime: '',
  updateTime: '',
  idCardError: '',
  phoneError: ''
});

// 删除相关数据
const showDeleteModal = ref(false);
const submittingDelete = ref(false);
const memberToDelete = ref(null);

// 家人表单数据
const familyForms = ref([
  {
    memberName: '',
    relationship: '',
    idCard: '',
    contactPhone: '',
    isPrimary: 0,
    idCardError: '',
    phoneError: ''
  }
]);

// 方法
const switchPage = (page) => {
  currentPage.value = page;
  if (page === 'list') {
    loadFamilyMembers();
  }
};

const loadFamilyMembers = async () => {
  loadingFamily.value = true;
  try {
    const response = await getMyFamilyMembers();
    if (response.success) {
      familyMembers.value = response.data || [];
      console.log('✅ 家人列表加载成功:', familyMembers.value);
    }
  } catch (error) {
    console.error('❌ 家人列表加载失败:', error);
    showModal('加载失败', '加载家人列表失败: ' + error.message, 'error');
  } finally {
    loadingFamily.value = false;
  }
};

const addMemberForm = () => {
  familyForms.value.push({
    memberName: '',
    relationship: '',
    idCard: '',
    contactPhone: '',
    isPrimary: 0,
    idCardError: '',
    phoneError: ''
  });
};

const removeMemberForm = (index) => {
  if (familyForms.value.length > 1) {
    familyForms.value.splice(index, 1);
  }
};

// 弹窗相关方法
const showModal = (title, message, type = 'info') => {
  modal.value = {
    visible: true,
    title,
    message,
    type
  };
};

const closeModal = () => {
  modal.value.visible = false;
};

const validateIdCard = (member, index) => {
  const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
  if (!member.idCard) {
    member.idCardError = '';
    return true;
  }
  if (!idCardRegex.test(member.idCard)) {
    member.idCardError = '请输入正确的身份证号码';
    return false;
  }
  member.idCardError = '';
  return true;
};

const validatePhone = (member, index) => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!member.contactPhone) {
    member.phoneError = '';
    return true;
  }
  if (!phoneRegex.test(member.contactPhone)) {
    member.phoneError = '请输入正确的手机号码';
    return false;
  }
  member.phoneError = '';
  return true;
};

const submitFamilyMembers = async () => {
  // 验证所有表单
  let hasError = false;
  familyForms.value.forEach((member, index) => {
    if (!validateIdCard(member, index) || !validatePhone(member, index)) {
      hasError = true;
    }
  });

  if (hasError) {
    showModal('表单错误', '请检查并修正表单中的错误', 'warning');
    return;
  }

  submittingFamily.value = true;
  try {
    // 准备提交数据，移除错误字段
    const submitData = familyForms.value.map(member => ({
      memberName: member.memberName,
      relationship: parseInt(member.relationship),
      idCard: member.idCard,
      contactPhone: member.contactPhone,
      isPrimary: member.isPrimary
    }));

    const response = await addFamilyMembers(submitData);
    if (response.success) {
      showModal('添加成功', '家人信息添加成功！', 'success');
      resetForms();
      // 切换到家人列表页面并刷新数据
      currentPage.value = 'list';
      await loadFamilyMembers();
    }
  } catch (error) {
    console.error('❌ 家人信息添加失败:', error);
    showModal('添加失败', '家人信息添加失败: ' + error.message, 'error');
  } finally {
    submittingFamily.value = false;
  }
};

const resetForms = () => {
  familyForms.value = [
    {
      memberName: '',
      relationship: '',
      idCard: '',
      contactPhone: '',
      isPrimary: 0,
      idCardError: '',
      phoneError: ''
    }
  ];
};

// 编辑相关方法
const editFamilyMember = (member) => {
  console.log('🔧 编辑家人信息:', member);

  // 复制数据到编辑表单
  editForm.value = {
    id: member.id,
    ownerId: member.ownerId,
    memberName: member.memberName,
    relationship: member.relationship.toString(),
    idCard: member.idCard,
    contactPhone: member.contactPhone,
    isPrimary: member.isPrimary,
    isDeleted: member.isDeleted,
    createTime: member.createTime,
    updateTime: member.updateTime,
    idCardError: '',
    phoneError: ''
  };

  showEditModal.value = true;
};

const closeEditModal = () => {
  showEditModal.value = false;
  // 重置编辑表单
  editForm.value = {
    id: null,
    ownerId: null,
    memberName: '',
    relationship: '',
    idCard: '',
    contactPhone: '',
    isPrimary: 0,
    isDeleted: 0,
    createTime: '',
    updateTime: '',
    idCardError: '',
    phoneError: ''
  };
};

const validateEditIdCard = () => {
  const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
  if (!editForm.value.idCard) {
    editForm.value.idCardError = '';
    return true;
  }
  if (!idCardRegex.test(editForm.value.idCard)) {
    editForm.value.idCardError = '请输入正确的身份证号码';
    return false;
  }
  editForm.value.idCardError = '';
  return true;
};

const validateEditPhone = () => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!editForm.value.contactPhone) {
    editForm.value.phoneError = '';
    return true;
  }
  if (!phoneRegex.test(editForm.value.contactPhone)) {
    editForm.value.phoneError = '请输入正确的手机号码';
    return false;
  }
  editForm.value.phoneError = '';
  return true;
};

const submitEditForm = async () => {
  // 验证表单
  if (!validateEditIdCard() || !validateEditPhone()) {
    showModal('表单错误', '请检查并修正表单中的错误', 'warning');
    return;
  }

  submittingEdit.value = true;
  try {
    // 准备提交数据
    const submitData = {
      id: editForm.value.id,
      ownerId: editForm.value.ownerId,
      memberName: editForm.value.memberName,
      relationship: parseInt(editForm.value.relationship),
      idCard: editForm.value.idCard,
      contactPhone: editForm.value.contactPhone,
      isPrimary: editForm.value.isPrimary,
      isDeleted: editForm.value.isDeleted,
      createTime: editForm.value.createTime,
      updateTime: editForm.value.updateTime
    };

    console.log('🔧 提交更新数据:', submitData);

    const response = await updateFamilyMember(submitData);
    if (response.success) {
      showModal('更新成功', '家人信息更新成功！', 'success');
      closeEditModal();
      // 刷新家人列表
      await loadFamilyMembers();
    }
  } catch (error) {
    console.error('❌ 家人信息更新失败:', error);
    showModal('更新失败', '家人信息更新失败: ' + error.message, 'error');
  } finally {
    submittingEdit.value = false;
  }
};

// 删除相关方法
const confirmDeleteMember = (member) => {
  console.log('🔧 确认删除家人:', member);
  memberToDelete.value = member;
  showDeleteModal.value = true;
};

const closeDeleteModal = () => {
  showDeleteModal.value = false;
  memberToDelete.value = null;
};

const deleteFamilyMemberHandler = async () => {
  if (!memberToDelete.value) {
    return;
  }

  submittingDelete.value = true;
  try {
    console.log('🔧 删除家人，ID:', memberToDelete.value.id);

    const response = await deleteFamilyMember(memberToDelete.value.id);
    if (response.success) {
      alert('家人信息删除成功！');
      closeDeleteModal();
      // 刷新家人列表
      await loadFamilyMembers();
    }
  } catch (error) {
    console.error('❌ 家人信息删除失败:', error);
    alert('家人信息删除失败: ' + error.message);
  } finally {
    submittingDelete.value = false;
  }
};

// 辅助函数
const getRelationshipText = (relationship) => {
  const relationshipMap = {
    1: '配偶',
    2: '子女',
    3: '父母',
    4: '兄弟姐妹',
    5: '其他'
  };
  return relationshipMap[relationship] || '未知关系';
};

const formatIdCard = (idCard) => {
  if (!idCard) return '未填写';
  // 身份证号脱敏显示
  return idCard.replace(/^(.{6}).*(.{4})$/, '$1****$2');
};

const formatTime = (timeStr) => {
  if (!timeStr) return '未知时间';
  try {
    return new Date(timeStr).toLocaleString('zh-CN');
  } catch (error) {
    return timeStr;
  }
};

// 生命周期
onMounted(() => {
  loadFamilyMembers();
});
</script>

<style scoped>
.family-management {
  background: #f5f7fa;
  min-height: 100vh;
  padding: 20px;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 30px;
  text-align: center;
}

.page-header h1 {
  margin: 0 0 10px 0;
  font-size: 2.5rem;
  font-weight: 600;
}

.subtitle {
  margin: 0 0 30px 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.page-tabs {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.tab-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 12px 24px;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  font-weight: 500;
}

.tab-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

.tab-btn.active {
  background: white;
  color: #667eea;
  border-color: white;
}

.tab-icon {
  font-size: 1.2rem;
}

.management-container {
  max-width: 1200px;
  margin: 0 auto;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 25px;
}

.title-icon {
  font-size: 1.8rem;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.empty-state p {
  font-size: 1.1rem;
  margin-bottom: 25px;
}

/* 家人卡片 */
.family-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.family-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.family-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.member-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f8f9fa;
}

.member-name {
  font-size: 1.4rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
}

.primary-badge {
  background: #28a745;
  color: white;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

.member-details {
  margin-bottom: 20px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 8px 0;
}

.detail-row .label {
  font-weight: 600;
  color: #666;
}

.detail-row .value {
  color: #2c3e50;
}

.member-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f1f3f4;
}

/* 表单样式 */
.family-form-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.form-header {
  background: #f8f9fa;
  padding: 20px 25px;
  border-bottom: 1px solid #e1e8ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-header p {
  margin: 0;
  color: #666;
  font-size: 1rem;
}

.family-form {
  padding: 25px;
}

.member-form {
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  background: #fafbfc;
}

.member-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #dee2e6;
}

.member-form-header h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.2rem;
}

.remove-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background 0.3s ease;
}

.remove-btn:hover {
  background: #c82333;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.form-group {
  margin-bottom: 0;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2c3e50;
}

.required {
  color: #e74c3c;
  margin-right: 4px;
}

.form-input,
.form-select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #667eea;
}

.form-error {
  margin-top: 6px;
  color: #e74c3c;
  font-size: 0.9rem;
}

.form-hint {
  margin-top: 6px;
  font-size: 0.9rem;
  color: #666;
}

.radio-group {
  display: flex;
  gap: 20px;
  margin-bottom: 10px;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.radio-option input[type="radio"] {
  margin: 0;
}

.radio-label {
  font-weight: 500;
  color: #2c3e50;
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e1e8ed;
}

/* 按钮样式 */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn.primary {
  background: #667eea;
  color: white;
}

.btn.primary:hover:not(:disabled) {
  background: #5a6fd8;
  transform: translateY(-2px);
}

.btn.secondary {
  background: #6c757d;
  color: white;
}

.btn.secondary:hover:not(:disabled) {
  background: #5a6268;
  transform: translateY(-2px);
}

.btn.danger {
  background: #dc3545;
  color: white;
}

.btn.danger:hover:not(:disabled) {
  background: #c82333;
  transform: translateY(-2px);
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-content.large-modal {
  max-width: 700px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #e1e8ed;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  color: #333;
  background: #f1f3f4;
}

.modal-body {
  padding: 25px;
}

.edit-form {
  margin: 0;
}

/* 删除确认弹窗样式 */
.delete-confirmation {
  text-align: center;
  padding: 20px 0;
}

.warning-icon {
  font-size: 3rem;
  margin-bottom: 15px;
}

.delete-confirmation h4 {
  margin: 0 0 20px 0;
  color: #dc3545;
  font-size: 1.3rem;
  font-weight: 600;
}

.member-info {
  background: #f8f9fa;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  padding: 15px;
  margin: 20px 0;
  text-align: left;
}

.member-info p {
  margin: 8px 0;
  color: #2c3e50;
}

.member-info strong {
  color: #666;
  font-weight: 600;
}

.warning-text {
  color: #dc3545;
  font-weight: 500;
  margin: 20px 0;
  font-size: 0.95rem;
}

.delete-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 25px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .family-management {
    padding: 15px;
  }

  .page-header {
    padding: 20px;
  }

  .page-header h1 {
    font-size: 2rem;
  }

  .page-tabs {
    flex-direction: column;
    align-items: center;
  }

  .family-cards {
    grid-template-columns: 1fr;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .form-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .form-actions {
    flex-direction: column;
  }

  .radio-group {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
