import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', () => {
  // 用户基本信息
  const user = ref(null)
  const isAuthenticated = ref(false)
  
  // 用户类型：'resident' (居民) 或 'property' (物业)
  const userType = ref(null)
  
  // 计算属性
  const isResident = computed(() => userType.value === 'resident')
  const isProperty = computed(() => userType.value === 'property')
  
  // 登录方法
  const login = (userData, type) => {
    user.value = userData
    userType.value = type
    isAuthenticated.value = true
    
    // 存储到localStorage以保持登录状态
    localStorage.setItem('userToken', 'mock-token')
    localStorage.setItem('userType', type)
    localStorage.setItem('userData', JSON.stringify(userData))
  }
  
  // 登出方法
  const logout = () => {
    user.value = null
    userType.value = null
    isAuthenticated.value = false
    
    // 清除localStorage
    localStorage.removeItem('userToken')
    localStorage.removeItem('userType')
    localStorage.removeItem('userData')
  }
  
  // 初始化用户状态（从localStorage恢复）
  const initializeUser = () => {
    const token = localStorage.getItem('userToken')
    const type = localStorage.getItem('userType')
    const userData = localStorage.getItem('userData')

    console.log('🔍 userStore.initializeUser - localStorage数据:', {
      token,
      type,
      userData,
      'userData parsed': userData ? JSON.parse(userData) : null
    });

    if (token && type && userData) {
      user.value = JSON.parse(userData)
      userType.value = type
      isAuthenticated.value = true

      console.log('✅ userStore.initializeUser - 状态已恢复:', {
        'user.value': user.value,
        'userType.value': userType.value,
        'isAuthenticated.value': isAuthenticated.value,
        'isResident': isResident.value,
        'isProperty': isProperty.value
      });
    } else {
      console.log('⚠️ userStore.initializeUser - 缺少必要数据，无法恢复状态');
    }
  }
  
  return {
    user,
    isAuthenticated,
    userType,
    isResident,
    isProperty,
    login,
    logout,
    initializeUser
  }
})
