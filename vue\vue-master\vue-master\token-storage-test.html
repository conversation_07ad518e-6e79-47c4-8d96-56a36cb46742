<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Token存储功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button.danger {
            background-color: #dc3545;
        }
        button.danger:hover {
            background-color: #c82333;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .storage-display {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .storage-box {
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 15px;
            background-color: #fff;
        }
        .storage-box h3 {
            margin-top: 0;
            color: #007bff;
        }
        .storage-item {
            margin: 5px 0;
            padding: 5px;
            background-color: #f8f9fa;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Token存储功能测试</h1>
        <p>此页面用于测试前端token存储功能的一致性和正确性</p>
        
        <div class="test-section">
            <h2>📝 模拟登录数据设置</h2>
            <button onclick="setMockAuthData()">设置模拟认证数据 (localStorage)</button>
            <button onclick="setMockAuthDataSession()">设置模拟认证数据 (sessionStorage)</button>
            <button onclick="setLegacyTokenData()">设置旧版token数据</button>
            <div id="setResult" class="result"></div>
        </div>

        <div class="test-section">
            <h2>🔍 Token获取测试</h2>
            <button onclick="testTokenRetrieval()">测试Token获取</button>
            <button onclick="testAuthDataRetrieval()">测试认证数据获取</button>
            <button onclick="testTokenExpiry()">测试Token过期检查</button>
            <div id="retrievalResult" class="result"></div>
        </div>

        <div class="test-section">
            <h2>🧹 清除功能测试</h2>
            <button onclick="clearAllData()" class="danger">清除所有认证数据</button>
            <button onclick="clearSpecificData()" class="danger">清除特定数据</button>
            <div id="clearResult" class="result"></div>
        </div>

        <div class="test-section">
            <h2>💾 当前存储状态</h2>
            <button onclick="refreshStorageDisplay()">刷新存储状态</button>
            <div class="storage-display">
                <div class="storage-box">
                    <h3>localStorage</h3>
                    <div id="localStorageDisplay"></div>
                </div>
                <div class="storage-box">
                    <h3>sessionStorage</h3>
                    <div id="sessionStorageDisplay"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟tokenManager的功能
        function getToken() {
            // 优先从auth_data中获取token
            let token = null;
            
            // 1. 尝试从localStorage的auth_data获取
            const authDataLocal = localStorage.getItem('auth_data');
            if (authDataLocal) {
                try {
                    const authData = JSON.parse(authDataLocal);
                    if (authData.token) {
                        return authData.token;
                    }
                } catch (e) {
                    console.warn('解析localStorage auth_data失败:', e);
                }
            }
            
            // 2. 尝试从sessionStorage的auth_data获取
            const authDataSession = sessionStorage.getItem('auth_data');
            if (authDataSession) {
                try {
                    const authData = JSON.parse(authDataSession);
                    if (authData.token) {
                        return authData.token;
                    }
                } catch (e) {
                    console.warn('解析sessionStorage auth_data失败:', e);
                }
            }
            
            // 3. 兼容旧的token存储方式
            return localStorage.getItem('auth_token') || localStorage.getItem('userToken');
        }

        function getAuthData() {
            // 优先从localStorage获取
            const authDataLocal = localStorage.getItem('auth_data');
            if (authDataLocal) {
                try {
                    return JSON.parse(authDataLocal);
                } catch (e) {
                    console.warn('解析localStorage auth_data失败:', e);
                }
            }
            
            // 尝试从sessionStorage获取
            const authDataSession = sessionStorage.getItem('auth_data');
            if (authDataSession) {
                try {
                    return JSON.parse(authDataSession);
                } catch (e) {
                    console.warn('解析sessionStorage auth_data失败:', e);
                }
            }
            
            return null;
        }

        function isTokenExpired() {
            const authData = getAuthData();
            if (!authData || !authData.tokenExpiry) {
                return true;
            }
            
            const expiryTime = new Date(authData.tokenExpiry);
            const currentTime = new Date();
            return currentTime >= expiryTime;
        }

        function clearAllAuthData() {
            // 清除主要的认证数据
            localStorage.removeItem('auth_data');
            sessionStorage.removeItem('auth_data');
            
            // 清除兼容的旧存储
            localStorage.removeItem('auth_token');
            localStorage.removeItem('userToken');
            localStorage.removeItem('user_info');
            localStorage.removeItem('userType');
            localStorage.removeItem('userData');
        }

        // 测试函数
        function setMockAuthData() {
            const authData = {
                user: {
                    id: 1,
                    userName: 'testuser',
                    userType: 1,
                    role: 'resident'
                },
                token: 'mock-jwt-token-' + Date.now(),
                refreshToken: 'mock-refresh-token-' + Date.now(),
                tokenExpiry: new Date(Date.now() + 15 * 60 * 1000).toISOString(), // 15分钟后过期
                rememberMe: true
            };
            
            localStorage.setItem('auth_data', JSON.stringify(authData));
            document.getElementById('setResult').className = 'result success';
            document.getElementById('setResult').textContent = '✅ 已设置模拟认证数据到localStorage:\n' + JSON.stringify(authData, null, 2);
            refreshStorageDisplay();
        }

        function setMockAuthDataSession() {
            const authData = {
                user: {
                    id: 2,
                    userName: 'sessionuser',
                    userType: 2,
                    role: 'property'
                },
                token: 'session-jwt-token-' + Date.now(),
                refreshToken: 'session-refresh-token-' + Date.now(),
                tokenExpiry: new Date(Date.now() + 30 * 60 * 1000).toISOString(), // 30分钟后过期
                rememberMe: false
            };
            
            sessionStorage.setItem('auth_data', JSON.stringify(authData));
            document.getElementById('setResult').className = 'result success';
            document.getElementById('setResult').textContent = '✅ 已设置模拟认证数据到sessionStorage:\n' + JSON.stringify(authData, null, 2);
            refreshStorageDisplay();
        }

        function setLegacyTokenData() {
            localStorage.setItem('auth_token', 'legacy-auth-token-' + Date.now());
            localStorage.setItem('userToken', 'legacy-user-token-' + Date.now());
            localStorage.setItem('user_info', JSON.stringify({
                id: 3,
                userName: 'legacyuser',
                userType: 3
            }));
            
            document.getElementById('setResult').className = 'result success';
            document.getElementById('setResult').textContent = '✅ 已设置旧版token数据';
            refreshStorageDisplay();
        }

        function testTokenRetrieval() {
            const token = getToken();
            const result = document.getElementById('retrievalResult');
            
            if (token) {
                result.className = 'result success';
                result.textContent = '✅ 成功获取Token:\n' + token;
            } else {
                result.className = 'result error';
                result.textContent = '❌ 未找到有效Token';
            }
        }

        function testAuthDataRetrieval() {
            const authData = getAuthData();
            const result = document.getElementById('retrievalResult');
            
            if (authData) {
                result.className = 'result success';
                result.textContent = '✅ 成功获取认证数据:\n' + JSON.stringify(authData, null, 2);
            } else {
                result.className = 'result error';
                result.textContent = '❌ 未找到认证数据';
            }
        }

        function testTokenExpiry() {
            const authData = getAuthData();
            const isExpired = isTokenExpired();
            const result = document.getElementById('retrievalResult');
            
            if (authData) {
                const expiryTime = authData.tokenExpiry ? new Date(authData.tokenExpiry) : null;
                const currentTime = new Date();
                
                result.className = isExpired ? 'result error' : 'result success';
                result.textContent = `${isExpired ? '❌' : '✅'} Token过期检查:\n` +
                    `当前时间: ${currentTime.toISOString()}\n` +
                    `过期时间: ${expiryTime ? expiryTime.toISOString() : '未设置'}\n` +
                    `是否过期: ${isExpired}`;
            } else {
                result.className = 'result error';
                result.textContent = '❌ 无认证数据，无法检查过期状态';
            }
        }

        function clearAllData() {
            clearAllAuthData();
            document.getElementById('clearResult').className = 'result success';
            document.getElementById('clearResult').textContent = '✅ 已清除所有认证数据';
            refreshStorageDisplay();
        }

        function clearSpecificData() {
            localStorage.removeItem('auth_data');
            document.getElementById('clearResult').className = 'result success';
            document.getElementById('clearResult').textContent = '✅ 已清除localStorage中的auth_data';
            refreshStorageDisplay();
        }

        function refreshStorageDisplay() {
            // 显示localStorage内容
            const localDisplay = document.getElementById('localStorageDisplay');
            localDisplay.innerHTML = '';
            
            const authKeys = ['auth_data', 'auth_token', 'userToken', 'user_info', 'userType', 'userData'];
            authKeys.forEach(key => {
                const value = localStorage.getItem(key);
                if (value) {
                    const item = document.createElement('div');
                    item.className = 'storage-item';
                    item.innerHTML = `<strong>${key}:</strong><br>${value}`;
                    localDisplay.appendChild(item);
                }
            });
            
            if (localDisplay.children.length === 0) {
                localDisplay.innerHTML = '<div class="storage-item">无相关数据</div>';
            }
            
            // 显示sessionStorage内容
            const sessionDisplay = document.getElementById('sessionStorageDisplay');
            sessionDisplay.innerHTML = '';
            
            authKeys.forEach(key => {
                const value = sessionStorage.getItem(key);
                if (value) {
                    const item = document.createElement('div');
                    item.className = 'storage-item';
                    item.innerHTML = `<strong>${key}:</strong><br>${value}`;
                    sessionDisplay.appendChild(item);
                }
            });
            
            if (sessionDisplay.children.length === 0) {
                sessionDisplay.innerHTML = '<div class="storage-item">无相关数据</div>';
            }
        }

        // 页面加载时刷新存储显示
        window.addEventListener('load', refreshStorageDisplay);
    </script>
</body>
</html>
