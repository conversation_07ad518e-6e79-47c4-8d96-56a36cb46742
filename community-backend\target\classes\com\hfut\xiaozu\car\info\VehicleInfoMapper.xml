<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hfut.xiaozu.car.info.VehicleInfoMapper">

    <resultMap id="BaseResultMap" type="com.hfut.xiaozu.car.info.VehicleInfo">
            <id property="id" column="id" />
            <result property="userId" column="user_id" />
            <result property="plateNumber" column="plate_number" />
            <result property="brand" column="brand" />
            <result property="color" column="color" />
            <result property="isInCommunity" column="is_in_community" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,plate_number,brand,color,is_in_community,
        create_time,update_time
    </sql>

    <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from vehicle_info
        where  id = #{id}
    </select>

    <select id="getByUserId" resultType="com.hfut.xiaozu.car.info.VehicleInfo">
        select
        <include refid="Base_Column_List" />
        from vehicle_info
        where  user_id = #{userId}
    </select>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from vehicle_info
        where  id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.hfut.xiaozu.car.info.VehicleInfo" useGeneratedKeys="true">
        insert into vehicle_info
        ( user_id,plate_number,brand,color,is_in_community)
        values (#{userId},#{plateNumber},#{brand},#{color},#{isInCommunity})
    </insert>


    <update id="update" parameterType="com.hfut.xiaozu.car.info.VehicleInfo">
        update vehicle_info
        <set>
                <if test="plateNumber != null">
                    plate_number = #{plateNumber},
                </if>
                <if test="brand != null">
                    brand = #{brand},
                </if>
                <if test="color != null">
                    color = #{color},
                </if>
        </set>
        where   id = #{id}
    </update>

</mapper>
