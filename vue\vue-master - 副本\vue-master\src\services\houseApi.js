/**
 * 房屋管理API服务
 * 处理房屋选择、绑定等相关的后端API调用
 */

import axios from 'axios';
import { getToken } from '../utils/tokenManager.js';

// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:8080/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 添加JWT token到请求头
    const token = getToken();

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      console.log('🔑 房屋API已添加Authorization头');
    } else {
      console.log('⚠️ 房屋API未找到有效token');
    }

    console.log('🏠 房屋API请求:', config.method?.toUpperCase(), config.url);
    return config;
  },
  error => {
    console.error('❌ 房屋API请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  response => {
    console.log('📦 房屋API响应:', response.config.url, response.data);
    return response.data;
  },
  error => {
    console.error('❌ 房屋API请求错误:', error.response?.data || error.message);

    // 处理常见错误
    if (error.response?.status === 401) {
      console.error('🔒 房屋API认证失败，请重新登录');
    }

    throw error;
  }
);

/**
 * 房屋管理API
 */
export const houseApi = {
  /**
   * 获取所有社区
   */
  getCommunities: async () => {
    return await api.get('/grids/communities');
  },

  /**
   * 根据社区ID获取楼栋列表
   * @param {number} communityId - 社区ID
   */
  getBuildings: async (communityId) => {
    return await api.get(`/grids/buildings?communityId=${communityId}`);
  },

  /**
   * 根据楼栋ID获取单元列表
   * @param {number} buildingId - 楼栋ID
   */
  getUnits: async (buildingId) => {
    return await api.get(`/grids/units?buildingId=${buildingId}`);
  },

  /**
   * 根据单元ID获取房屋列表
   * @param {number} unitId - 单元ID
   */
  getHouses: async (unitId) => {
    return await api.get(`/grids/houses?unitId=${unitId}`);
  },

  /**
   * 获取房屋状态
   * @param {number} houseId - 房屋ID
   */
  getHouseStatus: async (houseId) => {
    return await api.get(`/grids/houses/status/${houseId}`);
  },

  /**
   * 绑定房屋
   * @param {number} houseId - 房屋ID
   * @param {number} relationType - 关系类型 (1-业主, 2-租客, 3-家属)
   */
  bindHouse: async (houseId, relationType) => {
    return await api.put(`/grids/houses/binding/${houseId}?relatioType=${relationType}`);
  }
};
