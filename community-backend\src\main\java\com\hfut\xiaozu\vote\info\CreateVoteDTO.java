package com.hfut.xiaozu.vote.info;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 投票主表
 * @TableName vote
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CreateVoteDTO {

    /**
     * 投票标题
     */
    @NotBlank(message = "投票标题不能为空")
    @Size(min = 2, max = 50, message = "标题长度必须在2-50个字符之间")
    private String title;

    /**
     * 投票描述
     */
    @Size(max = 500, message = "描述长度不能超过500个字符")
    private String description;

    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空")
//    @Future(message = "开始时间必须是未来时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空")
    @Future(message = "结束时间必须是未来时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 最多选择数
     */
    @NotNull(message = "最多选择数不能为空")
    @Min(value = 1, message = "最多选择数必须大于等于1")
    @Max(value = 100, message = "最多选择数不能超过100")
    private Integer maxChoices;

    /**
     * 是否匿名（0否1是）
     */
    @NotNull(message = "是否匿名不能为空")
    @Min(value = 0, message = "是否匿名参数无效")
    @Max(value = 1, message = "是否匿名参数无效")
    private Integer isAnonymous;

    @NotEmpty(message = "投票选项不能为空")
    @Size(min = 2, message = "至少需要2个投票选项")
    @Valid
    private List<Option> options;

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class Option {
        /**
         * 排序序号
         */
        @NotNull(message = "排序序号不能为空")
        @Min(value = 1, message = "排序序号必须大于等于1")
        private Integer sortOrder;

        /**
         * 选项内容
         */
        @NotBlank(message = "选项内容不能为空")
        @Size(max = 200, message = "选项内容长度不能超过200个字符")
        private String content;
    }

    @NotEmpty(message = "投票范围不能为空")
    @Valid
    private List<Scope> scopes;

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class Scope {
        /**
         * 目标类型 （1=社区 2=网格 3=楼栋）
         */
        @NotNull(message = "目标类型不能为空")
        @Min(value = 1, message = "目标类型参数无效")
        @Max(value = 3, message = "目标类型参数无效")
        private Integer targetType;

        /**
         * 目标ID
         */
        @NotNull(message = "目标ID不能为空")
        @Min(value = 1, message = "目标ID必须大于等于1")
        private Long targetId;
    }
}
