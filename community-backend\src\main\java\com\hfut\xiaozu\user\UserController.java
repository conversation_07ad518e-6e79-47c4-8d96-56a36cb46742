package com.hfut.xiaozu.user;

import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.PhoneUtil;
import com.hfut.xiaozu.common.Result;
import com.hfut.xiaozu.user.idv.IdentityVerificationDTO;
import com.hfut.xiaozu.user.account.UserLoginDTO;
import com.hfut.xiaozu.user.account.UserRegisterDTO;
import com.hfut.xiaozu.user.idv.IdentityVerifySubmitSingleVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-06-23
 */
@Tag(name = "用户操作及管理")
@RequestMapping("/api/auth")
@RestController
public class UserController {

    @Resource
    UserService userService;

    @Operation(summary = "用户注册")
    @PostMapping("/register")
    public Result<?> UserRegister(@Valid @RequestBody UserRegisterDTO userRegisterDTO, BindingResult bindingResult){
        List<String> errors = new ArrayList<>();
        if(bindingResult.hasErrors()){
            errors.addAll(bindingResult.getAllErrors()
                    .stream()
                    .map(DefaultMessageSourceResolvable::getDefaultMessage)
                    .collect(Collectors.toList()));
        }

        if(!PhoneUtil.isPhone(userRegisterDTO.getPhone())){
            errors.add("手机号格式错误");
        }

        // 验证用户类型
        if(userRegisterDTO.getUserType()==null){
            errors.add("用户类型不能为空");
        }
        else if(userRegisterDTO.getUserType() !=1 &&userRegisterDTO.getUserType() !=2 && userRegisterDTO.getUserType() !=3) {
            errors.add("用户类型必须为：1-居民，2-网格员，3-社区管理员");
        }

        if(!errors.isEmpty()){
            return Result.fail(errors.toString());
        }

        return userService.register(userRegisterDTO);
    }

    @Operation(summary = "用户登录")
    @PostMapping("/login")
    public Result<?> UserLogin(@Valid @RequestBody UserLoginDTO userLoginDTO, BindingResult bindingResult){
        List<String> errors = new ArrayList<>();

        if(bindingResult.hasErrors()){
            errors.addAll(bindingResult.getAllErrors()
                    .stream()
                    .map(DefaultMessageSourceResolvable::getDefaultMessage)
                    .collect(Collectors.toList()));
        }
        // 验证用户类型
        if(userLoginDTO.getUserType()==null){
            errors.add("用户类型不能为空");
        }
        else if(userLoginDTO.getUserType() !=1 &&userLoginDTO.getUserType() !=2 && userLoginDTO.getUserType() !=3) {
            errors.add("用户类型必须为：1-居民，2-网格员，3-社区管理员");
        }

        if(!errors.isEmpty()){
            return Result.fail(errors.toString());
        }

        return userService.login(userLoginDTO);
    }

    @Operation(summary = "查询自身信息")
    @GetMapping("/me")
    public Result<?> getMyselfInformation(){
        return userService.getMyselfInformation();
    }

    @Operation(summary = "社区管理员查询所有网格员")
    @GetMapping("/list/grid-workers")
    public Result<?> listAllGridWorkers(){
        return userService.listAllGridWorkers();
    }


    @Operation(summary = "实名认证")
    @PostMapping("/identity-verification")
    public Result<?> UserIdentityVerification(@Valid @RequestBody IdentityVerificationDTO identityDTO,
                                              BindingResult bindingResult){
        List<String> errors = new ArrayList<>();

        if (bindingResult.hasErrors()) {
            errors.addAll(bindingResult.getAllErrors()
                    .stream()
                    .map(DefaultMessageSourceResolvable::getDefaultMessage)
                    .collect(Collectors.toList()));
        }
        if(identityDTO==null){
            errors.add("认证请求不能为空");
        }
        else if(!IdcardUtil.isValidCard(identityDTO.getSubmittedIdCard())){
            errors.add("身份证号格式错误");
        }

        if (!errors.isEmpty()) {
            return Result.fail(errors.toString());
        }

        return userService.verifyIdentity(identityDTO);
    }

    @Operation(summary = "身份证OCR识别")
    @PostMapping("/ocr")
    public Result<?> processOCR(
            @RequestParam("session_id") String sessionId,
            @RequestParam("image") MultipartFile imageFile){
        return userService.processOCR(sessionId,imageFile);
    }

    @Operation(summary = "社区端查看审核记录")
    @GetMapping("/list")
    public Result<?> listIDVRecord(@RequestParam Integer status,
                                    @RequestParam(defaultValue = "1") Integer pageNum,
                                   @RequestParam(defaultValue = "10") Integer pageSize){

        if(status==null||(status!=0&&status!=1&&status!=2) ){
            return Result.fail("记录状态值错误");
        }

        if (pageNum <= 0 || pageSize <= 0) {
            return Result.fail("分页参数错误");
        }

        return userService.listIDVRecord(status,pageNum,pageSize);
    }

    @Operation(summary = "社区端查看单条审核记录详情")
    @GetMapping("/get/{id}")
    public Result<?> getIDVRecord(@PathVariable Long id){

        return userService.getIDVById(id);
    }

    @Operation(summary = "社区端更新单条记录状态")
    @PutMapping("/update/{id}")
    public Result<?> updateIDVRecord(@PathVariable Long id, @RequestBody IdentityVerifySubmitSingleVO dto){

        return userService.updateIDVById(dto);
    }

    @Operation(summary = "居民端查询自身实名认证状态")
    @PutMapping("/identity/me")
    public Result<?> updateIDVRecord(){

        return userService.getIDVRecordMyself();
    }


}
