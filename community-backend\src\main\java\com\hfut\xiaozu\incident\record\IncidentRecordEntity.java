package com.hfut.xiaozu.incident.record;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 事件记录主表
 * @TableName incident_record
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IncidentRecordEntity {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 上报人ID
     */
    private Long reporterId;

    /**
     * 事件标题
     */
    private String title;

    /**
     * 事件描述
     */
    private String description;

    /**
     * 分类类型: 1-环境卫生 2-设施损坏 3-安全问题 4-其他
     */
    private Integer categoryType;

    /**
     * 优先级: 1-紧急 2-高 3-中 4-低
     */
    private Integer priority;

    /**
     * 位置描述
     */
    private String locationDescription;

    /**
     * 位置GeoJSON
     */
    private String locationGeojson;

    /**
     * 所属小区ID
     */
    private Long communityId;

    /**
     * 状态: 1-待分派 2-处理中 3-已完成 4-已关闭
     */
    private Integer status;

    /**
     * 当前处理人ID
     */
    private Long handlerId;

    /**
     * 上报时间
     */
    private LocalDateTime createTime;

    /**
     * 分派时间
     */
    private LocalDateTime assignTime;

    /**
     * 完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
