package com.hfut.xiaozu.common;

import com.hfut.xiaozu.common.constant.HttpStatusConstant;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-06-22
 */
@Setter
@Getter
public class Result<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    public static final int SUCCESS = HttpStatusConstant.SUCCESS;

    public static final int FAIL = HttpStatusConstant.ERROR;

    private int code;

    private String msg;

    private T data;

    public static <T> Result<T> ok()
    {
        return new Result<T>(SUCCESS,"操作成功",null);
    }

    public static <T> Result<T> ok(T data)
    {
        return new Result<T>(SUCCESS,"操作成功",data);
    }

    public static <T> Result<T> ok(T data,String msg) { return new Result<T>(SUCCESS,msg,data); }

    public static <T> Result<T> fail()
    {
        return new Result<T>(FAIL,"操作失败",null);
    }

    public static <T> Result<T> fail(String msg)
    {
        return new Result<T>(FAIL,msg,null);
    }

    public static <T> Result<T> fail(int code,String msg) {return new Result<T>(code,msg,null);}


    public Result(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }
}
