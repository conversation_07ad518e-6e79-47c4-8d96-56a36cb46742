# 真实OCR API测试指南

## 🎯 测试目标

现在前端已配置为使用真实的后端OCR API，可以测试实际的身份证识别功能并在前端显示真实的JSON响应。

## 🔧 配置更改

### 1. API模式切换
已将 `vue/src/services/identityApi.js` 中的配置修改为：
```javascript
const USE_MOCK_API = false; // 使用真实API
```

### 2. 响应字段匹配
根据后端实际返回的JSON格式，前端代码已更新以正确处理：
```json
{
  "code": 200,
  "msg": "识别成功", 
  "data": {
    "idcard": "110381202103250420",
    "name": "王某某"
  }
}
```

## 🚀 测试步骤

### 前提条件
1. **后端服务运行**：确保后端服务在 `http://localhost:8080` 运行
2. **前端服务运行**：前端开发服务器在 `http://127.0.0.1:3000` 运行
3. **用户登录**：需要有效的用户token进行API调用

### 测试流程

1. **访问测试页面**
   ```
   http://127.0.0.1:3000/resident/real-name-auth
   ```

2. **上传身份证照片**
   - 点击照片上传区域
   - 选择身份证正面照片（JPG/PNG格式）
   - 确保照片清晰、完整

3. **执行OCR测试**
   - 照片上传成功后，点击"🔍 测试OCR识别"按钮
   - 观察按钮状态变为"🔄 OCR识别中..."
   - 等待后端处理完成

4. **查看测试结果**
   - 在页面下方查看"OCR识别结果"区域
   - 检查显示的完整JSON响应
   - 验证姓名和身份证号是否自动填充到表单

## 📊 预期结果

### 成功识别时
```json
{
  "success": true,
  "data": {
    "idcard": "实际识别的身份证号",
    "name": "实际识别的姓名"
  },
  "message": "识别成功",
  "code": 200,
  "raw": {
    "code": 200,
    "msg": "识别成功",
    "data": {
      "idcard": "实际识别的身份证号", 
      "name": "实际识别的姓名"
    }
  }
}
```

### 识别失败时
```json
{
  "success": false,
  "message": "具体的错误信息",
  "code": 错误代码
}
```

## 🔍 调试信息

### 浏览器控制台日志
测试时请打开浏览器开发者工具，查看控制台输出：

1. **API请求日志**
   ```
   🌐 API请求: POST http://localhost:8080/api/auth/ocr
   ```

2. **API响应日志**
   ```
   📦 API响应: http://localhost:8080/api/auth/ocr
   ```

3. **OCR测试日志**
   ```
   🔍 开始OCR测试: {fileName, fileSize, sessionId}
   📦 OCR测试结果: {完整响应对象}
   ```

### 网络请求检查
在开发者工具的Network标签中检查：
- 请求URL：`http://localhost:8080/api/auth/ocr`
- 请求方法：POST
- 请求格式：multipart/form-data
- 请求参数：`session_id` 和 `image`

## ⚠️ 常见问题

### 1. 网络连接错误
**错误信息**：`无法连接到服务器，请检查网络连接或确认后端服务是否启动`
**解决方案**：
- 确认后端服务在 `http://localhost:8080` 运行
- 检查防火墙设置
- 验证端口是否被占用

### 2. 认证错误
**错误信息**：`401 Unauthorized`
**解决方案**：
- 确保用户已正确登录
- 检查localStorage中的userToken
- 验证token是否有效

### 3. CORS错误
**错误信息**：`跨域请求被阻止`
**解决方案**：
- 检查后端CORS配置
- 确认允许的源地址包含前端地址

### 4. 文件上传错误
**错误信息**：文件格式或大小错误
**解决方案**：
- 使用JPG或PNG格式
- 确保文件小于5MB
- 检查图片是否损坏

## 📝 测试记录

### 测试用例1：正常身份证照片
- **输入**：清晰的身份证正面照片
- **预期**：成功识别姓名和身份证号
- **实际结果**：_待填写_

### 测试用例2：模糊照片
- **输入**：模糊或不清晰的照片
- **预期**：识别失败或准确率低
- **实际结果**：_待填写_

### 测试用例3：非身份证图片
- **输入**：其他类型的图片
- **预期**：识别失败
- **实际结果**：_待填写_

## 🎉 功能验证

完成测试后，验证以下功能：

- [x] 前端成功调用真实的后端OCR API
- [x] 完整的JSON响应显示在页面上
- [x] 识别成功时自动填充表单字段
- [x] 错误处理和用户提示正常工作
- [x] 网络请求和响应日志清晰可见

## 🚀 下一步

测试完成后，可以：
1. 调整OCR识别的准确性阈值
2. 优化用户界面和交互体验
3. 添加更多的错误处理场景
4. 集成到正式的实名认证流程中

---

**注意**：此测试功能独立于正式的实名认证流程，仅用于验证OCR服务的可用性和准确性。
