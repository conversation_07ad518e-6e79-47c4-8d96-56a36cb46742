package com.hfut.xiaozu.house.grid;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 全息档案主表
 * @TableName grid_item_archive
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GridItemArchive {

    public static final int ARCHIVE_TYPE_PERSON = 1;
    public static final int ARCHIVE_TYPE_HOUSE = 2;
    public static final int ARCHIVE_TYPE_VEHICLE = 3;
    public static final int ARCHIVE_TYPE_UNIT = 4;
    public static final int ARCHIVE_TYPE_BUILDING = 5;

    /**
     * 档案ID
     */
    private Long id;

    /**
     * 档案类型: 1-人员 2-房屋 3-车辆 4-单位 5-建筑物 6-党组织
     */
    private Integer archiveType;

    /**
     * 关联ID
     */
    private Long relatedId;

    /**
     * 所属网格ID
     */
    private Long gridId;

    /**
     * 名称
     */
    private String name;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 是否重点关注 0-否 1-是
     */
    private Integer isSpecial;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
