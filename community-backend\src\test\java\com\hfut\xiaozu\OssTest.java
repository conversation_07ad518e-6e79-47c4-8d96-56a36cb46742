package com.hfut.xiaozu;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.util.StrUtil;
import com.hfut.xiaozu.common.oss.AliOssUtil;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.InputStream;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-06-24
 */
@SpringBootTest
public class OssTest {

    @Resource
    AliOssUtil aliOssUtil;

    @Test
    public void UploadFileLocal(){

        ClassPathResource resource = new ClassPathResource("/pic/ocrTest01Front.jpg");

        // 2. 获取原始文件名并提取扩展名

        String fileName = resource.getName(); // 获取完整文件名
        String extension = FileUtil.getSuffix(fileName); // 直接获取扩展名

        try (InputStream inputStream = resource.getStream()) {
            byte[] bytes = IoUtil.readBytes(inputStream);

            // 生成带时间戳的文件名
            String objectName = StrUtil.format("images/{}{}",
                    DateUtil.format(new Date(), "yyyyMMddHHmmssSSS"),
                    StrUtil.isNotBlank(extension) ? "." + extension : "");

            // 调用OSS工具类上传文件
            String url = aliOssUtil.upload(bytes, objectName);

            System.out.println("文件上传成功！访问URL: " + url);
            System.out.println("文件路径: " + objectName);
        } catch (Exception e) {
            System.err.println("文件上传失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    }

