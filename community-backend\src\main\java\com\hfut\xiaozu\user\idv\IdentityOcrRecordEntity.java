package com.hfut.xiaozu.user.idv;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 身份证OCR识别记录表（每次OCR调用记录）
 * @TableName identity_ocr_record
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IdentityOcrRecordEntity {
    /**
     * OCR记录ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 认证会话ID（每次打开表单生成一个会话ID）
     */
    private String sessionId;

    /**
     * 身份证正面照URL
     */
    private String idCardFrontUrl;

    /**
     * OCR完整识别结果(JSON格式)
     */
    private Object ocrData;

    /**
     * OCR识别的姓名
     */
    private String ocrName;

    /**
     * OCR识别的身份证号
     */
    private String ocrIdNumber;

    /**
     * OCR处理状态: 0-处理中 1-成功 2-失败
     */
    private Integer ocrStatus;

    /**
     * OCR失败原因
     */
    private String errorMessage;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
