<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hfut.xiaozu.user.account.UserMapper">

    <insert id="save">
        INSERT INTO community.user_account(user_name, phone, password, user_type)
        VALUES(#{userName}, #{phone}, #{password}, #{userType});
    </insert>

    <update id="update">
        UPDATE community.user_account
        <set>
            <if test="userName != null and userName != ''">
                user_name = #{userName},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="password != null and password != ''">
                password = #{password},
            </if>
            <if test="realName != null">
                real_name = #{realName},
            </if>
            <if test="idCard != null">
                id_card = #{idCard},
            </if>
            <if test="avatarUrl != null">
                avatar_url = #{avatarUrl},
            </if>
            <if test="isVerified != null">
                is_verified = #{isVerified},
            </if>
        </set>
        WHERE id = #{id} AND is_deleted = 0
    </update>

    <select id="getByPhone" resultType="com.hfut.xiaozu.user.account.UserAccountEntity">
        SELECT * FROM community.user_account
                 WHERE phone=#{phone} AND is_deleted = 0;
    </select>

    <select id="getByUsername" resultType="com.hfut.xiaozu.user.account.UserAccountEntity">
        SELECT * FROM community.user_account
        WHERE user_name=#{userName} AND is_deleted = 0;
    </select>

    <select id="getByPhoneAndPasswordAndUserType" resultType="com.hfut.xiaozu.user.account.UserAccountEntity">
        SELECT * FROM community.user_account
        WHERE phone=#{userName} AND password = #{password} AND user_type= #{userType}
                AND is_deleted = 0;
    </select>

    <select id="getByUsernameAndPasswordAndUserType"
            resultType="com.hfut.xiaozu.user.account.UserAccountEntity">
        SELECT * FROM community.user_account
        WHERE user_name=#{userName} AND password = #{password} AND user_type= #{userType}
          AND is_deleted = 0;
    </select>

    <select id="getNameById" resultType="java.lang.String">
        SELECT user_name FROM community.user_account
        WHERE id=#{id} AND is_deleted = 0;
    </select>

    <select id="getById" resultType="com.hfut.xiaozu.user.account.UserAccountEntity">
        SELECT * FROM community.user_account
        WHERE id=#{userId} AND is_deleted = 0;
    </select>

</mapper>

