
# 智慧社区管理系统接口文档


## 1. 用户认证体系

### 1.1 用户注册

#### 1.1.1 基本信息

请求路径： /api/auth/register

请求方式：POST

接口描述：该接口用于用户注册

#### 1.1.2 请求参数

参数格式：application/json

参数说明：

参数名称						|类型		|是否必须	|备注  
:----						|:---		|:------	|:---	
userName				|string		|必须		|用户名（不能为纯数字）
phone	|string	|必须	|手机号码
password				|string		|必须			|密码


请求参数样例：
样例1：

```
{
    "userName": "年萍",
    "phone": "71636640662",
    "password": "7BerS6eI9KuKBos"
}
```
样例2：
```
{
    "userName": "+Y$UhU",
    "phone": "14975694918",
    "password": "yeuTRHD0EL07sfZ"
}
```

#### 1.1.3 响应数据

参数格式：application/json

参数说明：

参数名称						| 类型   | 是否必须 |备注  
:----						|:---		|:------	|:---	
code						|int		|必须			|响应码
msg							|string		|必须			|提示信息
data						|object[]		|必须			|返回的数据&nbsp;


响应数据样例：
样例1：

```
{
    "code": 200,
    "message": "注册成功",
    "data": null
}
```
样例2：
``` 
{
    "code": 200,
    "message": "注册成功",
    "data": null
}
```



### 1.2 用户登录
#### 1.2.1 基本信息

请求路径： /api/auth/login

请求方式：POST

接口描述：该接口用于用户登录

#### 1.2.2 请求参数

参数格式：application/json

参数说明：

参数名称						|类型		|是否必须	|备注  
:----						|:---		|:------	|:---	
&emsp;userName					|string		|必须			| 用户名 
&emsp;password				|string		|必须			|密码

请求参数样例：
样例1：

```
{
    "userName": "asjdjashdjad",
    "password": "bABnaAgWwhWagkp"
}
```
样例2：
```
{
	"userName": "sajndjahha",     
	"password": "6FnCyDbP8DK7SFB"
}
```



#### 1.2.3 响应数据

参数格式：application/json

参数说明：

参数名称						|类型		|是否必须	|备注  
:----						|:---		|:------	|:---	
code						|int		|必须		|响应码
msg							|string		|必须		|提示信息&nbsp;
data						|object[]		|必须	|返回的数据


响应数据样例：

```
{
    "code": 200,
    "message": "登录成功",
    "data": null
}
```

### 1.3 个人信息查询
#### 1.3.1 基本信息

请求路径：/api/users/me

请求方式：GET

接口描述：用于在登陆时返回前端所需的个人信息

#### 1.3.2 请求参数

参数格式：application/json

参数说明：

无

#### 1.3.3 响应数据

参数格式：application/json

参数说明：

参数名称						|类型		|是否必须	|备注  
:----						|:---		|:------	|:---	
code						|int		|必须			|响应码
msg							|string		|必须			|提示信息
data						|object[]		|必须		|返回的数据
| &emsp;\|-userName|string|必须|用户名
| &emsp;\|-avatarUrl|string|必须|头像URL
| &emsp;\|-isVerified|int|必须|是否实名认证 0-否 1-是
| &emsp;\|-houseId|array[int]|必须|房屋ID
| &emsp;\|-plateNumber|array[string]|必须|车牌号码
| &emsp;\|-spaceNumber|array[string]|必须|车位编号


响应数据样例：
```
{
    "code": 200,
    "msg": "查询成功",
    "data": {
        "userName": "愚义轩",
        "avatarUrl": "https://avatars.githubusercontent.com/u/61091788",
        "isVerified": 0,
        "houseId": [
            48,
            99
        ],
        "plateNumber": [
            "65",
            "94"
        ],
        "spaceNumber": [
            "26",
            "14",
            "52"
        ]
    }
}
```
### 1.4 实名认证
#### 1.4.1 基本信息

请求路径：/api/auth/identity-verification

请求方式：POST

接口描述：用于用户的实名认证

#### 1.4.2 请求参数

参数格式：application/json

参数说明：

参数名称						|类型		|是否必须	|备注  
:----						|:---		|:------	|:---	
submittedIdCard					|string	|必须			| 提交的身份证号
submittedName					|string	|必须			| 提交的姓名
sessionId					|string	|必须			| 认证会话ID（与OCR记录关联）

请求参数样例：
样例1：

```
{
	"submittedIdCard": "220100192606108944",
    "submittedName": "弭浩然",
    "sessionId": "41ae0fcf-244f-410e-8968-8d4d5d1c7d88"
}
```




#### 1.4.3 响应数据

参数格式：application/json

参数说明：

参数名称					     	|类型		|是否必须	|备注  
:----						      |:---		|:------	|:---	
code						|int		|必须			|响应码
msg							|string		|必须			|提示信息
data						|object[]		|必须		|返回的数据
| &emsp;\|-status|int	|必须	|当前状态：0-待审核，1-审核失败，2-审核通过
| &emsp;\|-reviewRemark|string|非必须|审核结果详情（如失败原因）



响应数据样例：
```
{
	"code": 200,
    "msg": "认证成功",
    "data": {
        "status": 0,
        "reviewRemark": "do"
    }
}
```
### 1.5  OCR识别
#### 1.5.1 基本信息

请求路径：/api/auth/identity-verification/ocr

请求方式：POST

接口描述：该接口用于用户在实名认证时使用OCR识别身份证信息

#### 1.5.2 请求参数

参数格式：application/json

参数说明：

参数名称						|类型		|是否必须	|备注  
:----						|:---		|:------	|:---	
sessionId					|long	|必须	| 认证会话ID（每次打开表单生成一个会话ID）
idCardFrontUrl					|string	|必须			| 身份证正面照URL

请求参数样例：
样例1：

```
{
    "sessionId": "86794730324849437495083100450331",
    "idCardFrontUrl": "https://boring-redesign.net/"
}
```




#### 1.5.3 响应数据

参数格式：application/json

参数说明：

参数名称						|类型		|是否必须	|备注  
:----						|:---		|:------	|:---	
code						|int		|必须			|响应码
msg							|string		|必须			|提示信息
data						|object[]		|必须		|返回的数据
&emsp;\|-OCRIdNumber			|string	|必须			| OCR识别的身份证号
&emsp;\|-OCRData				|string	|必须			| OCR识别的姓名

响应数据样例：
```
{
    "code": 200,
    "msg": "识别成功",
    "data": {
        "OCRIdNumber": "333005732107012432",
        "OCRName": "黄苡沫"
    }
}
```
### 1.6 物业审核通过用户实名认证

#### 1.6.1 基本信息

请求路径：/api/auth/{user-id}/approve

请求方式：PUT

接口描述：该接口用于物业审核通过用户的实名认证申请

#### 1.6.2 请求参数

参数格式：路径参数

参数说明：

| 参数名称         | 类型   | 是否必须 | 备注                                                      |
| ---------------- | ------ | -------- | ---------------------------------------------------------- |
|status     | int    | 必须     | 当前状态：0-待审核，1-审核失败，2-审核通过 |
|reviewRemark   | string   | 非必须     | 审核结果详情（如失败原因） |

请求参数样例：

```
{
    "status": 0,
    "reviewRemark": "est dolore"
}
```

#### 1.6.3 响应数据

参数格式：application/json

参数说明：

| 参数名称 | 类型     | 是否必须 | 备注                    |
| -------- | -------- | -------- | ----------------------- |
| code     | int      | 必须     | 响应码 |
| msg      | string   | 必须     | 提示信息                |
| data     | object[] | 非必须   | 返回的数据              |

响应数据样例：

```
{
  "code": 200,
  "msg": "审核通过"
  "data": null
}
```

### 1.7 物业审核驳回绑定申请

#### 1.7.1 基本信息

请求路径：/api/auth/{user-id}/reject

请求方式：PUT

接口描述：该接口用于物业审核驳回用户的实名认证申请

#### 1.7.2 请求参数

参数格式：路径参数

参数说明：

| 参数名称         | 类型   | 是否必须 | 备注                                                     |
| ---------------- | ------ | -------- | ----------------------------------------------------------|
|status     | int    | 必须     | 当前状态：0-待审核，1-审核失败，2-审核通过 |
|reviewRemark   | string   | 必须     | 审核结果详情（如失败原因） |


请求参数样例：

```
{
	"status": 0,
    "reviewRemark": "信息错误"
}
```

#### 1.7.3 响应数据

参数格式：application/json

参数说明：

| 参数名称 | 类型     | 是否必须 | 备注                    |
| -------- | -------- | -------- | ----------------------- |
| code     | int      | 必须     | 响应码 |
| msg      | string   | 必须     | 提示信息                |
| data     | object[] | 非必须   | 返回的数据              |

响应数据样例：

```
{
  "code": 200,
  "msg": "审核驳回"
  "data": null
}
```
### 1.8 实名认证信息查询
#### 1.8.1 基本信息

请求路径：/api/users/me/identity-verification

请求方式：GET

接口描述：该接口用于用户在个人信息页面，返回个人实名认证信息

#### 1.8.2 请求参数

参数格式：application/json

参数说明：

无

#### 1.8.3 响应数据

参数格式：application/json

参数说明：

参数名称						|类型		|是否必须	|备注  
:----						|:---		|:------	|:---	
code						|int		|必须			|响应码
msg							|string		|必须			|提示信息
data						|object[]		|必须		|返回的数据
| &emsp;\|-realName|string	|必须	|真实姓名
| &emsp;\|-idCard|string         |必须 |身份证号码



响应数据样例：
```
{
	"code": 200,
   	 "msg": "查询成功",
    "data": {
        "realName": "析紫林",
        "idCard": "35010220190311122X"
    }
}
```
## 2. 房屋管理

### 2.1 房屋绑定

#### 2.1.1 基本信息

请求路径：/api/houses/{house-id}/bindings

请求方式：POST

接口描述：该接口用于用户与其房屋的绑定

#### 2.1.2 请求参数

参数格式：路径参数

参数说明：

| 参数名称       | 类型 | 是否必须 | 备注                           |
| -------------- | ---- | -------- | ------------------------------ |
| houseId       | int  | 必须     | 房屋ID                         |
| relationType  | int  | 必须     | 关系类型: 1-业主 2-租客 3-家属 |
| submitUserId | int  | 必须     | 提交用户ID                     |

请求参数样例：
样例1：

```
{
	"houseId": 3,     
	"relationType": 1,     
	"submitUserId": 23
}
```
样例2：
```
{
    "houseId": 24,
    "relationType": 2,
    "submitUserId": 5
}
```
#### 2.1.3 响应数据

参数格式：application/json

参数说明：

| 参数名称            | 类型     | 是否必须 | 备注                                          |
| ------------------- | -------- | -------- | --------------------------------------------- |
| code                | int      | 必须     | 响应码                  |
| msg                 | string   | 必须     | 提示信息                                      |
| data                | object[] | 必须     | 返回的数据                                    |
| &emsp;\|-status     | int      | 必须     | 绑定状态: 1-待审核 2-已生效 3-已拒绝 4-已解绑 |
| &emsp;\|-bindingId | int      | 非必须   | 绑定的关系记录ID                              |

响应数据样例：
样例1：
```
{     
	"code": 200,     
	"message": "绑定成功",    
	"data": {        
		"status": 1,         
		"bindingId": 86     
	}
}
```
样例2：
```
{
    "code": 200,
    "message": "绑定成功",
    "data": {
        "status": 2,
        "bindingId": 90
    }
}
```

### 2.2 查询用户房屋绑定记录(居民端)

#### 2.2.1 基本信息

请求路径：/api/users/{user-id}/bindings

请求方式：GET

接口描述：该接口用于查询某用户的房屋绑定记录

#### 2.2.2 请求参数

参数格式：路径参数

参数说明：

| 参数名称      | 类型 | 是否必须 | 备注                                          |
| ------------- | ---- | -------- | --------------------------------------------- |
| bindingId | int  | 非必须   |提交时间           |
| houseId | int  | 非必须   |房屋ID           |
| relationType | int  | 非必须   | 关系类型: 1-业主 2-租客 3-家属                |
| status        | int  | 非必须   | 绑定状态: 1-待审核 2-已生效 3-已拒绝 4-已解绑 |
| submitTime | data-time  | 非必须   |绑定的关系记录ID            |
| realName | string  | 非必须   | 姓名                |
| page | int  | 必须   | 页码              |
|pageSize|int|必须|每页记录数

请求参数样例：

```
{
    "bindingId": 47,
    "houseId": 44,
    "relationType": 2,
    "status": 2,
    "submitTime": "2026-04-30 22:33:36",
	"realName":"张三",
	"page":1,
	"pageSize":10
}
```

#### 2.2.3 响应数据

参数格式：application/json

参数说明：

| 参数名称               | 类型     | 是否必须 | 备注                                          |
| ---------------------- | -------- | -------- | --------------------------------------------- |
| code                   | int      | 必须     | 响应码                   |
| msg                    | string   | 必须     | 提示信息                                      |
| data                   | object[] | 必须     | 返回的数据                                    |
| &emsp;\|-total    | int      | 必须     |记录结果总数                           |
| &emsp;\|-records    | array[object{6}]      | 必须     |记录                           |
| &emsp; &emsp;\|-bindingId    | int      | 必须     | 绑定的关系记录ID  |
| &emsp; &emsp;\|-houseId      | int      | 必须     | 房屋ID                    |
| &emsp; &emsp;\|-userId       | int      | 必须     | 用户ID                     |
| &emsp; &emsp;\|-relationType | int      | 必须     | 关系类型: 1-业主 2-租客 3-家属                |
| &emsp; &emsp;\|-status        | int      | 必须     | 绑定状态: 1-待审核 2-已生效 3-已拒绝 4-已解绑 |
| &emsp; &emsp;\|-submitTime   | datetime | 必须     | 提交时间                                      |

响应数据样例：

```
{
    "code": 200,
    "msg": "查询成功",
    "data": {
        "total": 2,
        "records": [
            {
                "userId": 60,
                "houseId": 43,
                "bindingId": 34,
                "relationType": 3,
                "status": 1,
                "submitTime": "2025-01-25 18:44:59"
            },
            {
                "userId": 61,
                "houseId": 95,
                "bindingId": 73,
                "relationType": 3,
                "status": 3,
                "submitTime": "2024-12-04 07:32:27"
            }
        ]
    }
}
```
### 2.3 查询用户房屋绑定记录（社区治理端）

#### 2.3.1 基本信息

请求路径：/api/users/{user-id}/bindings-governance

请求方式：GET

接口描述：该接口用于在社区治理端查询某用户的房屋绑定记录

#### 2.3.2 请求参数

参数格式：路径参数

参数说明：

| 参数名称      | 类型 | 是否必须 | 备注                                          |
| ------------- | ---- | -------- | --------------------------------------------- |
|userId					|int	|非必须			| 用户ID
| bindingId | int  | 非必须   |提交时间           |
| houseId | int  | 非必须   |房屋ID           |
| relationType | int  | 非必须   | 关系类型: 1-业主 2-租客 3-家属                |
| status        | int  | 非必须   | 绑定状态: 1-待审核 2-已生效 3-已拒绝 4-已解绑 |
| submitTime | data-time  | 非必须   |绑定的关系记录ID            |
| approvedBy| int  | 非必须   | 审核人ID|
| approvedTime|date-time  | 非必须   | 审核完成时间|
| approvedType| int  | 非必须   | 操作类型: 1-提交绑定 2-审核通过 3-审核拒绝 4-变更关系 5-解绑 |
| prevStatus| int  | 非必须   | 操作前状态|
| newStatus| int  | 非必须   | 操作后状态|
| realName | string  | 非必须   | 姓名                |
| page | int  | 必须   | 页码              |
|pageSize|int|必须|每页记录数
请求参数样例：

```
{
	"userId":"66"
    "houseId": 84,
    "bindingId": 46,
    "relationType": 3,
    "status": 1,
    "submitTime": "2026-05-13 08:10:37",
    "approvedBy": 23,
    "approvedTime": "2024-12-10 06:03:23",
    "operationType": 3,
    "prevStatus": 4,
    "newStatus": 1
	"realName":"张三",
	"page":1,
	"pageSize":10
}
```

#### 2.3.3 响应数据

参数格式：application/json

参数说明：

| 参数名称               | 类型     | 是否必须 | 备注                                          |
| ---------------------- | -------- | -------- | --------------------------------------------- |
| code                   | int      | 必须     | 响应码                      |
| msg                    | string   | 必须     | 提示信息                                      |
| data                   | object[] | 必须     | 返回的数据                                    |
| &emsp;\|-total    | int      | 必须     |记录结果总数                           |
| &emsp;\|-records    | array[object{11}]      | 必须     |记录                           |
| &emsp; &emsp;\|-bindingId    | int      | 必须     | 绑定的关系记录ID  |
| &emsp; &emsp;\|-houseId      | int      | 必须     | 房屋ID                    |
| &emsp; &emsp;\|-userId       | int      | 必须     | 用户ID                     |
| &emsp; &emsp;\|-relationType | int      | 必须     | 关系类型: 1-业主 2-租客 3-家属                |
| &emsp; &emsp;\|-status        | int      | 必须     | 绑定状态: 1-待审核 2-已生效 3-已拒绝 4-已解绑 |
| &emsp; &emsp;\|-submitTime   | datetime | 必须     | 提交时间           |
|&emsp; &emsp;\|- approvedBy| int  | 必须   | 审核人ID|
| &emsp; &emsp;\|-approvedTime|date-time  | 必须   | 审核完成时间|
|&emsp; &emsp;\|- approvedType| int  | 必须   | 操作类型: 1-提交绑定 2-审核通过 3-审核拒绝 4-变更关系 5-解绑 |
| &emsp; &emsp;\|-prevStatus| int  | 必须   | 操作前状态|
| &emsp; &emsp;\|- newStatus| int  | 必须   | 操作后状态|

响应数据样例：

```
{
    "code": 200,
    "msg": "查询成功",
    "data": {
        "total": 2,
        "record": [
            {
                "bindingId": 67,
                "houseId": 90,
                "userId": 11,
                "relationType": 1,
                "status": 3,
                "submitTime": "2025-08-27 09:50:51",
                "approvedBy": 55,
                "approvalTime": "2026-03-19 15:24:55",
                "operationType": 3,
                "prevStatus": 2,
                "newStatus": 4
            },
			      {
                "bindingId": 55,
                "houseId": 23,
                "userId": 15,
                "relationType": 1,
                "status": 3,
                "submitTime": "2025-08-27 09:50:51",
                "approvedBy": 20,
                "approvalTime": "2026-03-19 15:24:55",
                "operationType": 3,
                "prevStatus": 2,
                "newStatus": 4
            }
        ]
    }
}
```

### 2.4 物业审核通过绑定申请

#### 2.4.1 基本信息

请求路径：/api/bingdings/{binding-id}/approve

请求方式：PUT

接口描述：该接口用于物业审核通过用户与房屋的绑定申请

#### 2.4.2 请求参数

参数格式：路径参数

参数说明：

| 参数名称         | 类型   | 是否必须 | 备注                                                         |
| ---------------- | ------ | -------- | ------------------------------------------------------------ |
| bindingId       | int    | 必须     | 绑定的关系记录ID                 |
| operationType   | int    | 必须     | 操作类型: 1-提交绑定 2-审核通过 3-审核拒绝 4-变更关系 5-解绑 |
| operationRemark | string | 非必须   | 操作备注(审核意见/解绑原因等)                                |

请求参数样例：

```
{
    "bindingId": 80,
     "Authorization": "dadaFsfsDFrgrgthnytjyukui"
    "operationType": 5,
    "operationRemark": "esse laboris"
}
```

#### 2.4.3 响应数据

参数格式：application/json

参数说明：

| 参数名称 | 类型     | 是否必须 | 备注                    |
| -------- | -------- | -------- | ----------------------- |
| code     | int      | 必须     | 响应码 |
| msg      | string   | 必须     | 提示信息                |
| data     | object[] | 非必须   | 返回的数据              |

响应数据样例：

```
{
  "code": 200,
  "msg": "审核通过"
  "data": null
}
```

### 2.5 物业审核驳回绑定申请

#### 2.5.1 基本信息

请求路径：/api/bindings/{binding-id}/reject

请求方式：PUT

接口描述：该接口用于物业审核驳回用户与房屋的绑定申请

#### 2.5.2 请求参数

参数格式：路径参数

参数说明：

| 参数名称         | 类型   | 是否必须 | 备注                                                         |
| ---------------- | ------ | -------- | ------------------------------------------------------------ |
| bindingId       | int    | 必须     | 绑定的关系记录ID                                             |
| operationType   | int    | 必须     | 操作类型: 1-提交绑定 2-审核通过 3-审核拒绝 4-变更关系 5-解绑 |
| operationRemark | string | 必须   | 操作备注(审核意见/解绑原因等)    |

请求参数样例：

```
{
    "bindingId": 43,
    "operationType": 1,
    "operationRemark": "信息错误"
}
```

#### 2.5.3 响应数据

参数格式：application/json

参数说明：

| 参数名称 | 类型     | 是否必须 | 备注                    |
| -------- | -------- | -------- | ----------------------- |
| code     | int      | 必须     | 响应码, 1 成功 , 0 失败 |
| msg      | string   | 必须     | 提示信息                |
| data     | object[] | 非必须   | 返回的数据              |

响应数据样例：

```
{
  "code": 200,
  "msg": "审核驳回"
  "data": null
}
```
### 2.6 用户选择社区
#### 2.6.1 基本信息

请求路径：/api/communities

请求方式：GET

接口描述：该接口用于用户进行房屋绑定时，选择房屋所在的社区
#### 2.6.2 请求参数

参数格式：application/json

参数说明：

无

#### 2.6.3 响应数据

参数格式：application/json

参数说明：

参数名称						|类型		|是否必须	|备注  
:----						|:---		|:------	|:---	
code						|int		|必须			|响应码
msg							|string		|必须			|提示信息
data						|object[]		|必须		|返回的数据
| &emsp;\|-communityName|array[string]|必须|小区名称



响应数据样例：
```
{
    "code": 200,
    "msg": "选择社区成功",
    "data": {
        "communityName": [
            "芙蓉社区",
			"翡翠花园"
        ]
    }
}
```
### 2.7 用户选择楼栋
#### 2.7.1 基本信息

请求路径：/api/buildings

请求方式：GET

接口描述：该接口用于用户进行房屋绑定时，选择房屋所在的楼栋
#### 2.7.2 请求参数

参数格式：application/json

参数说明：

参数名称						|类型		|是否必须	|备注  
:----						|:---		|:------	|:---	
communityName					|string		|必须			|小区名称

#### 2.7.3 响应数据

参数格式：application/json

参数说明：

参数名称						|类型		|是否必须	|备注  
:----						|:---		|:------	|:---	
code						|int		|必须			|响应码
msg							|string		|必须			|提示信息
data						|object[]		|必须		|返回的数据
| &emsp;\|-buildingName|array[string]|必须|楼栋名称



响应数据样例：
```
{
    "code": 200,
    "msg": "选择楼栋成功",
    "data": {
        "buildingName": [
            "一号楼",
			"二号楼"
        ]
    }
}
```
### 2.8 用户选择单元
#### 2.8.1 基本信息

请求路径：/api/units

请求方式：GET

接口描述：该接口用于用户进行房屋绑定时，选择房屋所在的单元
#### 2.8.2 请求参数

参数格式：application/json

参数说明：

参数名称						|类型		|是否必须	|备注  
:----						|:---		|:------	|:---	
communityName					|string		|必须			|小区名称
buildingName					|string		|必须			|楼栋名称
#### 2.8.3 响应数据

参数格式：application/json

参数说明：

参数名称						|类型		|是否必须	|备注  
:----						|:---		|:------	|:---	
code						|int		|必须			|响应码
msg							|string		|必须			|提示信息
data						|object[]		|必须		|返回的数据
| &emsp;\|-unitCode|array[string]|必须|单元名称




响应数据样例：
```
{
    "code": 200,
    "msg": "选择单元成功",
    "data": {
        "unitCode": [
            "一单元",
			"二单元"
        ]
    }
}
```
### 2.9用户选择门牌号
#### 2.9.1 基本信息

请求路径：/api/houses

请求方式：GET

接口描述：该接口用于用户进行房屋绑定时，选择房屋的门牌号
#### 2.9.2 请求参数

参数格式：application/json

参数说明：

参数名称						|类型		|是否必须	|备注  
:----						|:---		|:------	|:---	
communityName					|string		|必须			|小区名称
buildingName					|string		|必须			|楼栋名称
unitCode						|string		|必须			|单元名称

#### 2.9.3 响应数据

参数格式：application/json

参数说明：

参数名称						|类型		|是否必须	|备注  
:----						|:---		|:------	|:---	
code						|int		|必须			|响应码
msg							|string		|必须			|提示信息
data						|object[]		|必须		|返回的数据
| &emsp;\|-houseNumber|array[string]|必须|门牌号




响应数据样例：
```
{
    "code": 200,
    "msg": "选择门牌号成功",
    "data": {
        "communityName": [
            "406",
			"501"
        ]
    }
}
```
## 3. 网格管理

### 3.1 创建网格

#### 3.1.1 基本信息

请求路径：/api/grids

请求方式：POST

接口描述：该接口用于物业来创建网格

#### 3.1.2 请求参数

参数格式：application/json

参数说明：

参数名称			|类型		|是否必须	|备注  
:----				|:---		|:------	|:---	
gridName		|string		|必须		|网格名称
responsibleId	|int	|必须	|负责人ID（网格员）
boundaryGeojson |object[]		|必须			|网格边界GeoJSON
| &emsp;\|-type		|string		|必须		|几何类型，固定为“Polygon”
| &emsp;\|-coordinates	|array[]		|必须		|坐标数组，三维
metadata	|object[]	|必须		|
| &emsp;\|-createTime	|data-time	|必须	|创建时间，ISO 8601格式


请求参数样例：
```
{
    "gridName": "网格",
    "responsibleId": 99,
    "boundaryGeojson": {
        "type": "Polygon",
        "coordinates": [
			[
				[116.4070, 39.9040], 
				[116.4080, 39.9040], 
				[116.4080, 39.9050],
				[116.4070, 39.9050],
				[116.4070, 39.9040]
			]
        ]
    },
    "metadata": {
        "createTime": "2025-06-23T09:53:03.513Z"
    }
}
```


#### 3.1.3 响应数据

参数格式：application/json

参数说明：

参数名称						| 类型   | 是否必须 |备注  
:----						|:---		|:------	|:---	
code						|int		|必须			|响应码，1 代表成功，0 代表失败
msg							|string		|必须			|提示信息
data						|object[]		|必须			|返回的数据&nbsp;


响应数据样例：
```
{
    "code": 200,
    "message": "创建成功",
    "data": {}
}
```
### 3.2 查询所有社区
#### 3.2.1 基本信息

请求路径：/api/grids/communities

请求方式：GET

接口描述：该接口用于查询数据库中的社区
#### 3.2.2 请求参数

参数格式：application/json

参数说明：

无

#### 3.2.3 响应数据

参数格式：application/json

参数说明：

参数名称						|类型		|是否必须	|备注  
:----						|:---		|:------	|:---	
code						|int		|必须			|响应码
msg							|string		|必须			|提示信息
data						|object[]		|必须		|返回的数据
|&emsp;\|-records|array[object{2}]|必须|记录
| &emsp;&emsp;\|-communityName|string|必须|小区名称
| &emsp;&emsp;\|-geoJSON|object[]|必须|中心点GeoJSON数据（三维数组）



响应数据样例：
```
{
    "code": 200,
    "msg": "查询社区成功",
    "data": {
        "records": [
            {
                "communityName": "芙蓉社区",
                "geoJSON": {
                    "coordinates": [
                  		[116.4070, 39.9040]
                    ]
                }
            },
            {
                "communityName": "翡翠新苑",
                "geoJSON": {
                    "coordinates": [
                    [116.4080, 39.9040]
                    ]
                }
            }
        ]
    }
}
```
### 3.3 查询所有楼栋
#### 3.3.1 基本信息

请求路径：/api/grids/buildings

请求方式：GET

接口描述：该接口用于查询数据库中的楼栋

#### 3.3.2 请求参数

参数格式：application/json

参数说明：

无

#### 3.3.3 响应数据

参数格式：application/json

参数说明：

参数名称						|类型		|是否必须	|备注  
:----						|:---		|:------	|:---	
code						|int		|必须			|响应码
msg							|string		|必须			|提示信息
data						|object[]		|必须		|返回的数据
|&emsp;\|-records|array[object{2}]|必须|记录
| &emsp;&emsp;\|-communityName|string|必须|楼栋名称
| &emsp;&emsp;\|-geoJSON|object[]|必须|中心点GeoJSON数据（三维数组）

响应数据样例：
```
{
    "code": 200,
    "msg": "查询楼栋成功",
    "data": {
        "records": [
            {
                "buildingName": "一号楼",
                "geoJSON": {
                    "coordinates": [
                         [116.4080, 39.9050]
                    ]
                }
            },
            {
                "buildingName": "二号楼",
                "geoJSON": {
                    "coordinates": [
                  [116.4080, 39.9040]
                    ]
                }
            }
        ]
    }
}
```
## 4. 车辆管理

### 4.1 用户注册

#### 4.1.1 车辆信息录入

请求路径：/api/cars

请求方式：POST

接口描述：该接口用于居民录入车辆信息

#### 4.1.2 请求参数

参数格式：application/json

参数说明：

参数名称						|类型		|是否必须	|备注  
:----						|:---		|:------	|:---	
plateNumber				|string		|必须		|车牌号码
brand	|string	|非必须	|车辆品牌
color				|string		|非必须			|车辆颜色
isInCommunity		|int		|必须			|是否小区内车辆 0-否 1-是


请求参数样例：
样例1：

```
{     
	"plateNumber": "皖A",     
	"brand": "宝马",     
	"color": "白色",     
	"isInCommunity": 1 
}
```
#### 4.1.3 响应数据

参数格式：application/json

参数说明：

参数名称						| 类型   | 是否必须 |备注  
:----						|:---		|:------	|:---	
code						|int		|必须			|响应码
msg							|string		|必须			|提示信息
data						|object[]		|非必须	|返回的数据


响应数据样例：
样例1：

```
{     
	"code": 200,     
	"msg": "录入成功",     
	"data": {} 
}
```
### 4.2 车辆房屋绑定

#### 4.2.1 基本信息

请求路径：/api/vehicles/{vehicle-id}/bindings

请求方式：POST

接口描述：该接口用于用户将车辆与其房屋绑定

#### 4.2.2 请求参数

参数格式：路径参数

参数说明：

| 参数名称       | 类型 | 是否必须 | 备注                           |
| -------------- | ---- | -------- | ------------------------------ |
| vehicleId       | int  | 必须     | 车辆ID                         |
| houseId       | array[int]  | 必须     | 房屋ID                         |
| spaceNumber  | string  | 必须     | 车位编号 |
| spaceType | int  | 非必须     | 车位类型: 1-固定车位 2-临时车位                |

请求参数样例：
```
{
    "vehicleId": 20,
    "houseId": [
        74,
        42
    ],
    "spaceNumber": "40",
    "spaceType": 1
}
```
#### 4.2.3 响应数据

参数格式：application/json

参数说明：

| 参数名称            | 类型     | 是否必须 | 备注                                          |
| ------------------- | -------- | -------- | --------------------------------------------- |
| code                | int      | 必须     | 响应码                  |
| msg                 | string   | 必须     | 提示信息                                      |
| data                | object[] | 必须     | 返回的数据                                    |
| &emsp;\|-status     | int      | 必须     | 绑定状态: 1-待审核 2-已生效 3-已拒绝 4-已解绑 |
| &emsp;\|-bindingId | int      | 必须   | 绑定的关系记录ID                              |

响应数据样例：
```
{     
	"code": 200,     
	"message": "绑定成功",    
	"data": {        
		"status": 1,         
		"bindingId": 86     
	}
}
```
### 4.3 查询用户车辆绑定记录(居民端)

#### 4.3.1 基本信息

请求路径：/api/vehicles/{vehicle-id}/bindings-resident

请求方式：GET

接口描述：该接口用于在用户端查询某用户的车辆房屋绑定记录

#### 4.3.2 请求参数

参数格式：路径参数

参数说明：

| 参数名称      | 类型 | 是否必须 | 备注                                          |
| ------------- | ---- | -------- | --------------------------------------------- |
| realName | string  | 非必须   |姓名          |
| bindingId | int  | 非必须   |绑定关系ID         |
| vehicleId | int  | 非必须   |车辆ID           |
| houseId | int  | 非必须   |房屋ID           |
| spaceType | int  | 非必须   | 车位类型: 1-固定车位 2-临时车位               |
| status        | int  | 非必须   | 绑定状态: 1-待审核 2-已生效 3-已拒绝 4-已解绑 |
| createTime | data-time  | 非必须   |创建时间           |


请求参数样例：

```
{
    "realName":"银月"
    "bindingId": 50,
    "vehicleId": 84,
    "houseId": 10,
    "spaceNumber": "37",
    "spaceType": 1,
    "status": 1,
    "createTime": "2024-11-19 19:42:53"
}
```

#### 4.3.3 响应数据

参数格式：application/json

参数说明：

| 参数名称                  | 类型     | 是否必须 | 备注                                          |
| -------------------------- | -------- | -------- | --------------------------------------------- |
| code                   | int      | 必须     | 响应码                   |
| msg                    | string   | 必须     | 提示信息                                      |
| data                   | object[] | 必须     | 返回的数据                                    |
| &emsp;\|-bindingId    | int      | 必须     |绑定关系ID                          |
| &emsp;\|-records    | array[object{2}]      | 必须     |记录                           |
| &emsp; &emsp;\|-vehicleId   | int      | 必须     | 车辆ID  |
| &emsp; &emsp;\|-houseId      | array[int]      | 必须     | 房屋ID                    |
| &emsp;\|-spaceNumber      | array[string]     | 必须     |车位编号                   |
| &emsp;\|-status | int      | 必须     | 绑定状态: 1-待审核 2-已生效 3-已拒绝 4-已解绑       |
| &emsp;\|-spaceType       | int      | 必须     | 车位类型: 1-固定车位 2-临时车位 |
|&emsp;\|-createTime  | datetime | 必须     | 创建时间                         |

响应数据样例：

```
{
    "code": 200,
    "msg": "查询成功",
    "data": {
        "bindingId": 18,
        "records": [
            {
                "vehicleId": 41,
                "houseId": [
                    89,
                    71,
                    15
                ]
            },
            {
                "vehicleId": 77,
                "houseId": [
                    77,
                    85
                ]
            }
        ],
        "spaceNumber": [
            "84",
            "83"
        ],
        "status": 1,
        "spaceType": 1,
        "createTime": "2025-08-05 13:08:16"
    }
}
```
### 4.4 查询用户车辆绑定记录（社区治理端）

#### 4.4.1 基本信息

请求路径：/api/vehicles/{vehicle-id}/bindings-governance

请求方式：GET

接口描述：该接口用于在社区治理端查询某用户的车辆房屋绑定记录

#### 4.4.2 请求参数

参数格式：application/json

参数说明：

| 参数名称      | 类型 | 是否必须 | 备注                                          |
| ------------- | ---- | -------- | --------------------------------------------- |
| bindingId | int  | 非必须   |绑定的关系记录ID           |
|userId					|int	|非必须			| 用户ID
| houseId | int  | 非必须   |房屋ID           |
| vehicleId | int  | 非必须   |车辆ID          |
|spaceNumber | int  | 非必须   | 车位编号               |
| spaceType | int  | 非必须   | 车位类型: 1-固定车位 2-临时车位              |
| status        | int  | 非必须   | 绑定状态: 1-待审核 2-已生效 3-已拒绝 4-已解绑 |
| operatorId| int  | 非必须   | 操作人ID|
| operationTime|date-time  | 非必须   | 操作时间|
|operationType| int  | 非必须   | 操作类型: 1-提交绑定 2-审核通过 3-审核拒绝 4-变更关系 5-解绑 |
| prevStatus| int  | 非必须   | 操作前状态|
| newStatus| int  | 非必须   | 操作后状态|
| realName | string  | 非必须   | 姓名                |
| page | int  | 必须   | 页码              |
|pageSize|int|必须|每页记录数
请求参数样例：

```
{
    "bindingId": 84,
    "userId": 87,
    "houseId": 36,
    "vehicleId": 55,
    "spaceNumber": 26,
    "spaceType": 2,
    "status": 3,
    "operatorId": 32,
    "operationTime": "2025-11-11 16:44:46",
    "operationType": 4,
    "prevStatus": 3,
    "newStatus": 3
	"realName":"张三",
	"page":1,
	"pageSize":10
}
```

#### 4.4.3 响应数据

参数格式：application/json

参数说明：

| 参数名称                  | 类型     | 是否必须 | 备注                                          |
| -------------------------- | -------- | -------- | --------------------------------------------- |
| code                   | int      | 必须     | 响应码                   |
| msg                    | string   | 必须     | 提示信息                                      |
| data                   | object[] | 必须     | 返回的数据                                    |
| &emsp;\|-total   | int      | 必须     |记录结果总数                         |
| &emsp;\|-records    | array[object{11}]      | 必须     |记录                           |
| &emsp; &emsp;\|-bindingId   | int      | 必须     | 绑定关系ID  |
| &emsp; &emsp;\|-userId     | int      | 必须     | 用户ID                    |
| &emsp; &emsp;\|-records    | array[object{2}]      | 必须     |记录             |
| &emsp;&emsp; &emsp;\|-vehicleId   | int      | 必须     | 车辆ID  |
| &emsp;&emsp; &emsp;\|-houseId    | array[int]      | 必须     | 房屋ID          |
| &emsp;&emsp;\|-spaceNumber      | array[string]     | 必须     |车位编号       |
| &emsp;&emsp;\|-status | int      | 必须     | 绑定状态: 1-待审核 2-已生效 3-已拒绝 4-已解绑       |
|  &emsp;&emsp;\|-spaceType       | int      | 必须     | 车位类型: 1-固定车位 2-临时车位 |
|  &emsp;&emsp;\|-operatorId| int  | 非必须   | 操作人ID|
|  &emsp;&emsp;\|-operationTime|date-time  | 非必须   | 操作时间|
|  &emsp;&emsp;\|-operationType| int  | 非必须   | 操作类型: 1-提交绑定 2-审核通过 3-审核拒绝 4-变更关系 5-解绑 |
|  &emsp;&emsp;\|-prevStatus| int  | 非必须   | 操作前状态|
|  &emsp;&emsp;\|-newStatus| int  | 非必须   | 操作后状态|

响应数据样例：

```
{
    "code": 200,
    "msg": "查询成功",
    "data": {
        "total": 56,
        "records": [
            {
                "bindingId": 8,
                "userId": 75,
                "records": [
                    {
                        "vehicleId": 95,
                        "houseId": [
                            37
                        ]
                    },
                    {
                        "vehicleId": 37,
                        "houseId": [
                            12,
                            96
                        ]
                    },
                    {
                        "vehicleId": 4,
                        "houseId": [
                            42,
                            29
                        ]
                    }
                ],
                "spaceNumber": [
                    "92",
                    "28"
                ],
                "status": 3,
                "spaceType": 1,
                "operatorId": 4,
                "operationTime": "2025-09-21 03:43:06",
                "operationType": 1,
                "prevStatus": 5,
                "newStatus": 29
            }
        ]
    }
}
```

## 5. 家人管理

### 5.1 家人信息录入

#### 5.1.1 基本信息

请求路径：api/family-member

请求方式：POST

接口描述：该接口用于录入家人信息

#### 5.1.2 请求参数

参数格式：application/json

参数说明：

参数名称						|类型		|是否必须	|备注  
:----						|:---		|:------	|:---	
houseId			|string		|必须		|业主ID
records			|array[object{4}]	|必须	|批量录入家人信息
| &emsp;\|-memberName				|string		|必须			|家人姓名
| &emsp;\|-relationShip		|int		|必须			|与业主关系:1-配偶 2-子女 3-父母 4-其他亲属
| &emsp;\|-idCard  |string |必须  |身份证号码
| &emsp;\|-contactPhone	|string	|非必须	|联系方式


请求参数样例：
样例1：

```
{     
	" houseId": "16",     
	"records": [         
		{             
			"memberName": "蒙辉",             
			"relationShip": 1,             
			"idCard": "360102194308116869",             							"contactPhone": "93681237362"         
		},         
		{             
			"memberName": "用雪",             
			"relationShip": 1,             
			"idCard": "540102194108235672",             							"contactPhone": "012 9145 5226"        
		}     
	] 
}
```
#### 5.1.3 响应数据

参数格式：application/json

参数说明：

参数名称						| 类型   | 是否必须 |备注  
:----						|:---		|:------	|:---	
code						|int		|必须			|响应码
msg							|string		|必须			|提示信息
data						|object[]		|非必须	|返回的数据


响应数据样例：
```
{     
	"code": 200,     
	"msg": "录入成功",     
	"data": {} 
}
```

## 6. 事件管理
### 6.1 事件录入
#### 6.1.1 基本信息

请求路径：/api/incident-records

请求方式：POST

接口描述：该接口用于记录上报事件

#### 6.1.2 请求参数

参数格式：application/json

参数说明：

参数名称						|类型		|是否必须	|备注  
:----						|:---		|:------	|:---	
title		|string		|必须		|事件标题
description		|text		|必须		|事件描述
categoryType		|int		|必须		|分类类型: 1-环境卫生 2-设施损坏 3-安全问题 4-其他
priority		|int		|非必须		|优先级: 1-紧急 2-高 3-中 4-低
locationDescription		|string		|必须		|位置描述
locationGeojson		|array		|必须	|位置GeoJSON
communityId		|string		|必须		|所属小区ID

响应数据样例：
```
{
    "title": "消防设施损坏",
    "description": "消防栓玻璃破裂",
    "categoryType": 3,
    "priority": 2,
    "locationDescription": "11号楼南楼三楼楼梯口。",
    "locationGeojson": [
          	[116.4070, 39.9040], 
        ]
    " communityId": "52"
}
```

#### 6.3.3 响应数据

参数格式：application/json

参数说明：

参数名称						|类型		|是否必须	|备注  
:----						|:---		|:------	|:---	
code						|int		|必须			|响应码
msg							|string		|必须			|提示信息
data						|object[]		|必须		|返回的数据
| &emsp;\|-status|int|必须|状态: 1-待分派 2-处理中 3-已完成 4-已关闭
| &emsp;\|-createTime|data-time|必须|上报时间


响应数据样例：
```
{
    "code": 200,
    "msg": "上报成功",
    "data": {
        "status": "1",
        "createTime": "2024-06-26 20:10:27"
    }
}
```

##  附录A 响应码说明

响应码	|说明  
:----	|:---
200		|处理成功
201		|解析报文错误
202		|无效调用凭证
204		|参数不正确
500		|系统内部错误
999		|处理失败
