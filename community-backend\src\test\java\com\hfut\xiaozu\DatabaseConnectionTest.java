package com.hfut.xiaozu;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;

import org.springframework.beans.factory.annotation.Autowired;

/**
 * 数据库连接测试类
 * 用于验证数据库连接是否正常
 */
@SpringBootTest
@TestPropertySource(locations = "classpath:application.yml")
public class DatabaseConnectionTest {

    @Autowired
    private DataSource dataSource;

    @Test
    public void testDatabaseConnection() {
        try {
            System.out.println("=== 开始测试数据库连接 ===");
            
            // 获取数据库连接
            Connection connection = dataSource.getConnection();
            System.out.println("✅ 数据库连接获取成功");
            
            // 测试连接是否有效
            boolean isValid = connection.isValid(5);
            System.out.println("连接有效性: " + (isValid ? "✅ 有效" : "❌ 无效"));
            
            // 执行简单查询
            Statement statement = connection.createStatement();
            ResultSet resultSet = statement.executeQuery("SELECT 1 as test");
            
            if (resultSet.next()) {
                int result = resultSet.getInt("test");
                System.out.println("✅ 查询测试成功，结果: " + result);
            }
            
            // 测试数据库信息
            System.out.println("数据库URL: " + connection.getMetaData().getURL());
            System.out.println("数据库用户: " + connection.getMetaData().getUserName());
            System.out.println("数据库产品: " + connection.getMetaData().getDatabaseProductName());
            System.out.println("数据库版本: " + connection.getMetaData().getDatabaseProductVersion());
            
            // 检查community数据库
            resultSet = statement.executeQuery("SELECT DATABASE() as current_db");
            if (resultSet.next()) {
                String currentDb = resultSet.getString("current_db");
                System.out.println("当前数据库: " + currentDb);
            }
            
            // 检查user_account表是否存在
            try {
                resultSet = statement.executeQuery("DESCRIBE user_account");
                System.out.println("✅ user_account表存在");
                System.out.println("表结构:");
                while (resultSet.next()) {
                    String field = resultSet.getString("Field");
                    String type = resultSet.getString("Type");
                    String nullable = resultSet.getString("Null");
                    String key = resultSet.getString("Key");
                    System.out.println("  " + field + " " + type + " " + nullable + " " + key);
                }
            } catch (Exception e) {
                System.out.println("❌ user_account表不存在: " + e.getMessage());
            }
            
            // 关闭连接
            resultSet.close();
            statement.close();
            connection.close();
            
            System.out.println("=== 数据库连接测试完成 ===");
            
        } catch (Exception e) {
            System.err.println("❌ 数据库连接测试失败:");
            e.printStackTrace();
            throw new RuntimeException("数据库连接失败", e);
        }
    }
    
    @Test
    public void testUserAccountTable() {
        try {
            System.out.println("=== 开始测试user_account表操作 ===");
            
            Connection connection = dataSource.getConnection();
            Statement statement = connection.createStatement();
            
            // 查询表中的数据数量
            ResultSet resultSet = statement.executeQuery("SELECT COUNT(*) as count FROM user_account");
            if (resultSet.next()) {
                int count = resultSet.getInt("count");
                System.out.println("user_account表中的记录数: " + count);
            }
            
            // 查询前5条记录
            resultSet = statement.executeQuery("SELECT id, user_name, phone, user_type, create_time FROM user_account LIMIT 5");
            System.out.println("前5条记录:");
            while (resultSet.next()) {
                System.out.println("ID: " + resultSet.getLong("id") + 
                                 ", 用户名: " + resultSet.getString("user_name") + 
                                 ", 手机号: " + resultSet.getString("phone") + 
                                 ", 用户类型: " + resultSet.getInt("user_type") + 
                                 ", 创建时间: " + resultSet.getTimestamp("create_time"));
            }
            
            resultSet.close();
            statement.close();
            connection.close();
            
            System.out.println("=== user_account表测试完成 ===");
            
        } catch (Exception e) {
            System.err.println("❌ user_account表测试失败:");
            e.printStackTrace();
        }
    }
}
