<template>
  <div class="login-container">
    <div class="login-form">
      <div class="form-header">
        <h2>物业管理系统登录</h2>
        <p class="subtitle">请输入您的账号和密码</p>
      </div>

      <form @submit.prevent="handleLogin" class="login-form-content">
        <!-- 用户名输入 -->
        <div class="form-group">
          <label for="username">用户名</label>
          <input
            id="username"
            v-model="loginForm.username"
            type="text"
            placeholder="请输入用户名"
            :disabled="isLoading"
            required
            autocomplete="username"
          />
          <span v-if="errors.username" class="error-message">{{ errors.username }}</span>
        </div>

        <!-- 密码输入 -->
        <div class="form-group">
          <label for="password">密码</label>
          <div class="password-input">
            <input
              id="password"
              v-model="loginForm.password"
              :type="showPassword ? 'text' : 'password'"
              placeholder="请输入密码"
              :disabled="isLoading"
              required
              autocomplete="current-password"
            />
            <button
              type="button"
              class="password-toggle"
              @click="showPassword = !showPassword"
              :disabled="isLoading"
            >
              {{ showPassword ? '👁️' : '👁️‍🗨️' }}
            </button>
          </div>
          <span v-if="errors.password" class="error-message">{{ errors.password }}</span>
        </div>

        <!-- 记住我 -->
        <div class="form-group checkbox-group">
          <label class="checkbox-label">
            <input
              v-model="loginForm.rememberMe"
              type="checkbox"
              :disabled="isLoading"
            />
            <span class="checkmark"></span>
            记住我
          </label>
        </div>

        <!-- 错误提示 -->
        <div v-if="loginError" class="error-alert">
          <span class="error-icon">⚠️</span>
          <span class="error-text">{{ loginError }}</span>
        </div>

        <!-- 登录按钮 -->
        <button
          type="submit"
          class="login-button"
          :disabled="isLoading || !isFormValid"
        >
          <span v-if="isLoading" class="loading-spinner"></span>
          {{ isLoading ? '登录中...' : '登录' }}
        </button>
      </form>

      <!-- 其他选项 -->
      <div class="form-footer">
        <a href="#" class="forgot-password">忘记密码？</a>
      </div>
    </div>

    <!-- 登录状态显示 -->
    <div v-if="showDebugInfo" class="debug-info">
      <h3>调试信息</h3>
      <div class="debug-item">
        <strong>登录状态:</strong> {{ isLoggedIn ? '已登录' : '未登录' }}
      </div>
      <div class="debug-item" v-if="currentUser">
        <strong>当前用户:</strong> {{ currentUser.username }}
      </div>
      <div class="debug-item" v-if="token">
        <strong>Token:</strong> {{ token.substring(0, 20) }}...
      </div>
      <div class="debug-item">
        <strong>Token存储:</strong> {{ loginForm.rememberMe ? 'localStorage' : 'sessionStorage' }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../stores/auth.js';

const router = useRouter();
const authStore = useAuthStore();

// 响应式数据
const loginForm = reactive({
  username: '',
  password: '',
  rememberMe: false
});

const errors = reactive({
  username: '',
  password: ''
});

const isLoading = ref(false);
const showPassword = ref(false);
const loginError = ref('');
const showDebugInfo = ref(process.env.NODE_ENV === 'development');

// 计算属性
const isFormValid = computed(() => {
  return loginForm.username.trim().length > 0 && 
         loginForm.password.length > 0;
});

const isLoggedIn = computed(() => authStore.isAuthenticated);
const currentUser = computed(() => authStore.user);
const token = computed(() => authStore.token);

// 生命周期
onMounted(() => {
  // 检查是否已经登录
  if (authStore.isAuthenticated) {
    router.push('/dashboard');
  }
  
  // 开发环境下填充测试数据
  if (process.env.NODE_ENV === 'development') {
    loginForm.username = 'admin';
    loginForm.password = '123456';
  }
});

// 表单验证
function validateForm() {
  errors.username = '';
  errors.password = '';
  
  if (!loginForm.username.trim()) {
    errors.username = '请输入用户名';
    return false;
  }
  
  if (loginForm.username.length < 3) {
    errors.username = '用户名至少3个字符';
    return false;
  }
  
  if (!loginForm.password) {
    errors.password = '请输入密码';
    return false;
  }
  
  if (loginForm.password.length < 6) {
    errors.password = '密码至少6个字符';
    return false;
  }
  
  return true;
}

// 处理登录
async function handleLogin() {
  // 清除之前的错误
  loginError.value = '';
  
  // 表单验证
  if (!validateForm()) {
    return;
  }
  
  try {
    isLoading.value = true;
    
    console.log('🔐 开始登录流程...');
    console.log('📝 登录数据:', {
      username: loginForm.username,
      rememberMe: loginForm.rememberMe
    });
    
    // 调用登录API
    await authStore.login({
      username: loginForm.username,
      password: loginForm.password,
      rememberMe: loginForm.rememberMe
    });
    
    console.log('✅ 登录成功!');
    
    // 登录成功后跳转
    const redirectPath = router.currentRoute.value.query.redirect || '/dashboard';
    router.push(redirectPath);
    
  } catch (error) {
    console.error('❌ 登录失败:', error);
    
    // 处理不同类型的错误
    if (error.code === 'INVALID_CREDENTIALS') {
      loginError.value = '用户名或密码错误';
    } else if (error.code === 'ACCOUNT_LOCKED') {
      loginError.value = '账户已被锁定，请联系管理员';
    } else if (error.code === 'ACCOUNT_DISABLED') {
      loginError.value = '账户已被禁用';
    } else if (error.message.includes('网络')) {
      loginError.value = '网络连接失败，请检查网络设置';
    } else {
      loginError.value = error.message || '登录失败，请稍后重试';
    }
    
    // 清空密码
    loginForm.password = '';
    
  } finally {
    isLoading.value = false;
  }
}

// 暴露方法给父组件
defineExpose({
  handleLogin
});
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-form {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 400px;
}

.form-header {
  text-align: center;
  margin-bottom: 30px;
}

.form-header h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.subtitle {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.login-form-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.form-group input {
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 2px rgba(255, 179, 102, 0.2);
}

.form-group input:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

.password-input {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input input {
  flex: 1;
  padding-right: 45px;
}

.password-toggle {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  padding: 4px;
  border-radius: 4px;
  transition: background 0.3s ease;
}

.password-toggle:hover {
  background: #f0f0f0;
}

.password-toggle:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.checkbox-group {
  flex-direction: row;
  align-items: center;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #666;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
  width: 16px;
  height: 16px;
}

.error-message {
  color: #dc3545;
  font-size: 12px;
  margin-top: 4px;
}

.error-alert {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  color: #721c24;
  font-size: 14px;
}

.error-icon {
  font-size: 16px;
}

.login-button {
  background: var(--primary-btn);
  color: #5B5347;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-height: 48px;
}

.login-button:hover:not(:disabled) {
  background: var(--primary-btn-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 179, 102, 0.3);
}

.login-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.form-footer {
  text-align: center;
  margin-top: 20px;
}

.forgot-password {
  color: var(--primary-btn);
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.forgot-password:hover {
  color: var(--primary-btn-hover);
  text-decoration: underline;
}

.debug-info {
  margin-top: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
}

.debug-info h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.debug-item {
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}

.debug-item strong {
  color: #333;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }
  
  .login-form {
    padding: 30px 20px;
  }
  
  .form-header h2 {
    font-size: 20px;
  }
}
</style>
