<template>
  <div class="house-management">
    <div class="page-header">
      <h1>房屋管理</h1>
      <p class="subtitle">管理您的房屋绑定信息</p>

      <!-- 页面切换按钮 -->
      <div class="page-tabs">
        <button
          class="tab-btn"
          :class="{ active: currentPage === 'applications' }"
          @click="switchPage('applications')"
        >
          <span class="tab-icon">📋</span>
          申请进度查询
        </button>
        <button
          class="tab-btn"
          :class="{ active: currentPage === 'binding' }"
          @click="switchPage('binding')"
        >
          <span class="tab-icon">🏠</span>
          申请房屋绑定
        </button>
      </div>
    </div>

    <div class="management-container">
      <!-- 申请进度查询页面 -->
      <div v-if="currentPage === 'applications'" class="applications-page">
        <div class="my-applications-section">
        <h2 class="section-title">
          <span class="title-icon">📋</span>
          我的房屋绑定申请
        </h2>

        <div v-if="loadingApplications" class="loading-state">
          <div class="loading-spinner"></div>
          <span>加载申请记录中...</span>
        </div>

        <div v-else-if="applications.length === 0" class="empty-state">
          <div class="empty-icon">📝</div>
          <p>暂无房屋绑定申请记录</p>
        </div>

        <div v-else class="applications-list">
          <div
            v-for="app in applications"
            :key="app.id"
            class="application-card"
            :class="getApplicationStatusClass(app.status)"
          >
            <div class="card-header">
              <div class="app-id">申请 #{{ app.id }}</div>
              <div class="app-status" :class="getStatusClass(app.status)">
                {{ getStatusText(app.status) }}
              </div>
            </div>

            <div class="card-body">
              <div class="app-info">
                <div class="info-row">
                  <span class="label">房屋ID：</span>
                  <span class="value">{{ app.houseId }}</span>
                </div>
                <div class="info-row">
                  <span class="label">关系类型：</span>
                  <span class="value">{{ getRelationTypeText(app.relationType) }}</span>
                </div>
                <div class="info-row">
                  <span class="label">申请时间：</span>
                  <span class="value">{{ formatDateTime(app.submitTime) }}</span>
                </div>
                <div v-if="app.updateTime" class="info-row">
                  <span class="label">更新时间：</span>
                  <span class="value">{{ formatDateTime(app.updateTime) }}</span>
                </div>
              </div>

              <div v-if="app.remark" class="app-remark">
                <div class="remark-label">备注：</div>
                <div class="remark-content">{{ app.remark }}</div>
              </div>
            </div>
          </div>
        </div>

        <button @click="loadMyApplications" class="refresh-btn" :disabled="loadingApplications">
          <span class="refresh-icon">🔄</span>
          刷新记录
        </button>
        </div>
      </div>

      <!-- 申请房屋绑定页面 -->
      <div v-if="currentPage === 'binding'" class="binding-page">
        <div class="selection-section">
        <h2 class="section-title">
          <span class="title-icon">🏠</span>
          房屋选择
        </h2>
        
        <div class="dropdown-container">
          <!-- 第一级：社区选择 -->
          <div class="dropdown-item">
            <label class="dropdown-label">选择社区</label>
            <select 
              v-model="selectedCommunity" 
              @change="onCommunityChange"
              class="dropdown-select"
              :disabled="loadingCommunities"
            >
              <option value="">请选择社区</option>
              <option 
                v-for="community in communities" 
                :key="community.communityId" 
                :value="community.communityId"
              >
                {{ community.communityName }}
              </option>
            </select>
            <div v-if="loadingCommunities" class="loading-indicator">加载中...</div>
          </div>

          <!-- 第二级：楼栋选择 -->
          <div class="dropdown-item">
            <label class="dropdown-label">选择楼栋</label>
            <select 
              v-model="selectedBuilding" 
              @change="onBuildingChange"
              class="dropdown-select"
              :disabled="!selectedCommunity || loadingBuildings"
            >
              <option value="">请选择楼栋</option>
              <option 
                v-for="building in buildings" 
                :key="building.buildingsId" 
                :value="building.buildingsId"
              >
                {{ building.buildingName }}
              </option>
            </select>
            <div v-if="loadingBuildings" class="loading-indicator">加载中...</div>
          </div>

          <!-- 第三级：单元选择 -->
          <div class="dropdown-item">
            <label class="dropdown-label">选择单元</label>
            <select 
              v-model="selectedUnit" 
              @change="onUnitChange"
              class="dropdown-select"
              :disabled="!selectedBuilding || loadingUnits"
            >
              <option value="">请选择单元</option>
              <option 
                v-for="unit in units" 
                :key="unit.id" 
                :value="unit.id"
              >
                {{ unit.unitCode }}
              </option>
            </select>
            <div v-if="loadingUnits" class="loading-indicator">加载中...</div>
          </div>

          <!-- 第四级：房屋选择 -->
          <div class="dropdown-item">
            <label class="dropdown-label">选择房屋</label>
            <select 
              v-model="selectedHouse" 
              @change="onHouseChange"
              class="dropdown-select"
              :disabled="!selectedUnit || loadingHouses"
            >
              <option value="">请选择房屋</option>
              <option 
                v-for="house in houses" 
                :key="house.id" 
                :value="house.id"
              >
                {{ house.houseNumber }}
              </option>
            </select>
            <div v-if="loadingHouses" class="loading-indicator">加载中...</div>
          </div>
        </div>

        <!-- 房屋状态显示 -->
        <div v-if="selectedHouse && houseStatus !== null" class="house-status-section">
          <h3 class="status-title">房屋状态</h3>
          <div class="status-display" :class="houseStatus === 0 ? 'vacant' : 'occupied'">
            <span class="status-icon">{{ houseStatus === 0 ? '🟢' : '🔴' }}</span>
            <span class="status-text">{{ houseStatus === 0 ? '无人居住' : '有人居住' }}</span>
          </div>
        </div>

        <!-- 关系类型选择 -->
        <div v-if="selectedHouse && houseStatus === 0" class="relation-section">
          <h3 class="relation-title">选择关系类型</h3>
          <div class="relation-options">
            <label class="relation-option">
              <input type="radio" v-model="relationType" value="1" />
              <span class="option-text">业主</span>
            </label>
            <label class="relation-option">
              <input type="radio" v-model="relationType" value="2" />
              <span class="option-text">租客</span>
            </label>
            <label class="relation-option">
              <input type="radio" v-model="relationType" value="3" />
              <span class="option-text">家属</span>
            </label>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div v-if="selectedHouse" class="action-section">
          <button 
            v-if="houseStatus === 0 && relationType"
            @click="bindHouse"
            class="bind-btn"
            :disabled="isBinding"
          >
            <span v-if="isBinding">绑定中...</span>
            <span v-else>申请绑定房屋</span>
          </button>
          
          <button @click="resetSelection" class="reset-btn">
            重新选择
          </button>
        </div>

        <!-- 错误信息显示 -->
        <div v-if="errorMessage" class="error-message">
          <span class="error-icon">❌</span>
          {{ errorMessage }}
        </div>

        <!-- 成功信息显示 -->
        <div v-if="successMessage" class="success-message">
          <span class="success-icon">✅</span>
          {{ successMessage }}
        </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { houseApi } from '../../services/houseApi.js';
import { getToken } from '../../utils/tokenManager.js';

// 响应式数据
const communities = ref([]);
const buildings = ref([]);
const units = ref([]);
const houses = ref([]);

const selectedCommunity = ref('');
const selectedBuilding = ref('');
const selectedUnit = ref('');
const selectedHouse = ref('');

const houseStatus = ref(null);
const relationType = ref('');

// 加载状态
const loadingCommunities = ref(false);
const loadingBuildings = ref(false);
const loadingUnits = ref(false);
const loadingHouses = ref(false);
const isBinding = ref(false);

// 消息状态
const errorMessage = ref('');
const successMessage = ref('');

// 申请记录相关
const applications = ref([]);
const loadingApplications = ref(false);

// 页面切换
const currentPage = ref('applications'); // 'applications' 或 'binding'

// 清除消息
const clearMessages = () => {
  errorMessage.value = '';
  successMessage.value = '';
};

// 页面切换函数
const switchPage = (page) => {
  currentPage.value = page;
  clearMessages();

  // 如果切换到申请记录页面，自动加载数据
  if (page === 'applications') {
    loadMyApplications();
  }

  console.log('📄 切换到页面:', page);
};

// 加载我的申请记录
const loadMyApplications = async () => {
  try {
    loadingApplications.value = true;
    clearMessages();

    console.log('🔍 加载我的房屋绑定申请记录...');

    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    const response = await fetch('http://localhost:8080/api/grids/houses/binding/me', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ 申请记录响应:', result);

    if (result.code === 200) {
      applications.value = result.data || [];
      console.log('✅ 申请记录加载成功:', applications.value.length, '条记录');
    } else {
      throw new Error(result.msg || '获取申请记录失败');
    }
  } catch (error) {
    console.error('❌ 加载申请记录失败:', error);
    errorMessage.value = error.message || '加载申请记录失败';
    applications.value = [];
  } finally {
    loadingApplications.value = false;
  }
};

// 状态映射
const statusMap = {
  1: '待审核',
  2: '已生效',
  3: '已拒绝',
  4: '已解绑'
};

const relationTypeMap = {
  1: '业主',
  2: '租客',
  3: '家属'
};

// 获取状态文本
const getStatusText = (status) => {
  return statusMap[status] || '未知状态';
};

// 获取关系类型文本
const getRelationTypeText = (relationType) => {
  return relationTypeMap[relationType] || '未知关系';
};

// 获取状态样式类
const getStatusClass = (status) => {
  const classMap = {
    1: 'status-pending',
    2: 'status-approved',
    3: 'status-rejected',
    4: 'status-unbound'
  };
  return classMap[status] || '';
};

// 获取申请卡片状态样式类
const getApplicationStatusClass = (status) => {
  const classMap = {
    1: 'app-pending',
    2: 'app-approved',
    3: 'app-rejected',
    4: 'app-unbound'
  };
  return classMap[status] || '';
};

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '-';
  try {
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (e) {
    return dateTimeStr;
  }
};

// 加载社区列表
const loadCommunities = async () => {
  try {
    loadingCommunities.value = true;
    clearMessages();
    
    console.log('🔍 加载社区列表...');
    const result = await houseApi.getCommunities();
    
    if (result.code === 200) {
      communities.value = result.data || [];
      console.log('✅ 社区列表加载成功:', communities.value.length, '个社区');
    } else {
      throw new Error(result.msg || '获取社区列表失败');
    }
  } catch (error) {
    console.error('❌ 加载社区列表失败:', error);
    errorMessage.value = error.message || '加载社区列表失败';
  } finally {
    loadingCommunities.value = false;
  }
};

// 社区选择变化
const onCommunityChange = async () => {
  console.log('🏘️ 选择社区:', selectedCommunity.value);
  
  // 重置下级选择
  selectedBuilding.value = '';
  selectedUnit.value = '';
  selectedHouse.value = '';
  buildings.value = [];
  units.value = [];
  houses.value = [];
  houseStatus.value = null;
  relationType.value = '';
  clearMessages();
  
  if (!selectedCommunity.value) return;
  
  try {
    loadingBuildings.value = true;
    
    const result = await houseApi.getBuildings(selectedCommunity.value);
    
    if (result.code === 200) {
      buildings.value = result.data || [];
      console.log('✅ 楼栋列表加载成功:', buildings.value.length, '栋楼');
    } else {
      throw new Error(result.msg || '获取楼栋列表失败');
    }
  } catch (error) {
    console.error('❌ 加载楼栋列表失败:', error);
    errorMessage.value = error.message || '加载楼栋列表失败';
  } finally {
    loadingBuildings.value = false;
  }
};

// 楼栋选择变化
const onBuildingChange = async () => {
  console.log('🏢 选择楼栋:', selectedBuilding.value);
  
  // 重置下级选择
  selectedUnit.value = '';
  selectedHouse.value = '';
  units.value = [];
  houses.value = [];
  houseStatus.value = null;
  relationType.value = '';
  clearMessages();
  
  if (!selectedBuilding.value) return;
  
  try {
    loadingUnits.value = true;
    
    const result = await houseApi.getUnits(selectedBuilding.value);
    
    if (result.code === 200) {
      units.value = result.data || [];
      console.log('✅ 单元列表加载成功:', units.value.length, '个单元');
    } else {
      throw new Error(result.msg || '获取单元列表失败');
    }
  } catch (error) {
    console.error('❌ 加载单元列表失败:', error);
    errorMessage.value = error.message || '加载单元列表失败';
  } finally {
    loadingUnits.value = false;
  }
};

// 单元选择变化
const onUnitChange = async () => {
  console.log('🏠 选择单元:', selectedUnit.value);
  
  // 重置下级选择
  selectedHouse.value = '';
  houses.value = [];
  houseStatus.value = null;
  relationType.value = '';
  clearMessages();
  
  if (!selectedUnit.value) return;
  
  try {
    loadingHouses.value = true;
    
    const result = await houseApi.getHouses(selectedUnit.value);
    
    if (result.code === 200) {
      houses.value = result.data || [];
      console.log('✅ 房屋列表加载成功:', houses.value.length, '套房屋');
    } else {
      throw new Error(result.msg || '获取房屋列表失败');
    }
  } catch (error) {
    console.error('❌ 加载房屋列表失败:', error);
    errorMessage.value = error.message || '加载房屋列表失败';
  } finally {
    loadingHouses.value = false;
  }
};

// 房屋选择变化
const onHouseChange = async () => {
  console.log('🏡 选择房屋:', selectedHouse.value);
  
  houseStatus.value = null;
  relationType.value = '';
  clearMessages();
  
  if (!selectedHouse.value) return;
  
  try {
    console.log('🔍 查询房屋状态...');
    const result = await houseApi.getHouseStatus(selectedHouse.value);
    
    if (result.code === 200) {
      houseStatus.value = result.data;
      console.log('✅ 房屋状态:', houseStatus.value === 0 ? '无人居住' : '有人居住');
    } else {
      throw new Error(result.msg || '获取房屋状态失败');
    }
  } catch (error) {
    console.error('❌ 查询房屋状态失败:', error);
    errorMessage.value = error.message || '查询房屋状态失败';
  }
};

// 绑定房屋
const bindHouse = async () => {
  if (!selectedHouse.value || !relationType.value) {
    errorMessage.value = '请选择房屋和关系类型';
    return;
  }
  
  try {
    isBinding.value = true;
    clearMessages();
    
    console.log('🔗 申请绑定房屋:', {
      houseId: selectedHouse.value,
      relationType: relationType.value
    });
    
    const result = await houseApi.bindHouse(selectedHouse.value, parseInt(relationType.value));
    
    if (result.code === 200) {
      successMessage.value = '房屋绑定申请提交成功，请等待审核';
      console.log('✅ 房屋绑定申请成功');
      
      // 重置选择
      setTimeout(() => {
        resetSelection();
      }, 2000);
    } else {
      throw new Error(result.msg || '房屋绑定申请失败');
    }
  } catch (error) {
    console.error('❌ 房屋绑定申请失败:', error);
    errorMessage.value = error.message || '房屋绑定申请失败';
  } finally {
    isBinding.value = false;
  }
};

// 重置选择
const resetSelection = () => {
  selectedCommunity.value = '';
  selectedBuilding.value = '';
  selectedUnit.value = '';
  selectedHouse.value = '';
  buildings.value = [];
  units.value = [];
  houses.value = [];
  houseStatus.value = null;
  relationType.value = '';
  clearMessages();
};

// 页面加载时初始化
onMounted(async () => {
  console.log('🏠 房屋管理页面已加载');

  // 默认加载申请记录
  if (currentPage.value === 'applications') {
    await loadMyApplications();
  }

  // 始终加载社区数据，以备房屋绑定页面使用
  await loadCommunities();
});
</script>

<style scoped>
.house-management {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 10px;
  font-weight: 700;
}

.subtitle {
  font-size: 1.1rem;
  color: #7f8c8d;
  margin: 0;
}

/* 页面切换标签样式 */
.page-tabs {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 20px;
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: 2px solid transparent;
  border-radius: 25px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.tab-btn:hover {
  background: linear-gradient(135deg, #5a67d8, #6b46c1);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.tab-btn.active {
  background: white;
  color: #667eea;
  border-color: #667eea;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.tab-btn.active:hover {
  background: white;
  color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.tab-icon {
  font-size: 18px;
}

.management-container {
  max-width: 800px;
  margin: 0 auto;
}

/* 页面容器样式 */
.applications-page,
.binding-page {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.selection-section {
  background: white;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 1.5rem;
  color: #2c3e50;
  margin-bottom: 25px;
  font-weight: 600;
}

.title-icon {
  font-size: 1.8rem;
  margin-right: 10px;
}

.dropdown-container {
  display: grid;
  gap: 20px;
  margin-bottom: 30px;
}

.dropdown-item {
  position: relative;
}

.dropdown-label {
  display: block;
  font-weight: 600;
  color: #34495e;
  margin-bottom: 8px;
  font-size: 1rem;
}

.dropdown-select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  transition: all 0.3s ease;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
}

.dropdown-select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.dropdown-select:disabled {
  background-color: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
}

.loading-indicator {
  position: absolute;
  right: 40px;
  top: 50%;
  transform: translateY(-50%);
  color: #3498db;
  font-size: 0.9rem;
  font-weight: 500;
}

.house-status-section {
  margin: 25px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  border-left: 4px solid #3498db;
}

.status-title {
  font-size: 1.2rem;
  color: #2c3e50;
  margin-bottom: 15px;
  font-weight: 600;
}

.status-display {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1.1rem;
}

.status-display.vacant {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-display.occupied {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.status-icon {
  font-size: 1.2rem;
  margin-right: 10px;
}

.relation-section {
  margin: 25px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  border-left: 4px solid #e74c3c;
}

.relation-title {
  font-size: 1.2rem;
  color: #2c3e50;
  margin-bottom: 15px;
  font-weight: 600;
}

.relation-options {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.relation-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 10px 15px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  background: white;
  transition: all 0.3s ease;
  min-width: 100px;
}

.relation-option:hover {
  border-color: #3498db;
  background: #f8f9fa;
}

.relation-option input[type="radio"] {
  margin-right: 8px;
  accent-color: #3498db;
}

.option-text {
  font-weight: 500;
  color: #2c3e50;
}

.action-section {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 30px;
}

.bind-btn {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 150px;
}

.bind-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #2980b9, #1f5f8b);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.bind-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.reset-btn {
  background: #95a5a6;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.reset-btn:hover {
  background: #7f8c8d;
  transform: translateY(-2px);
}

.error-message {
  display: flex;
  align-items: center;
  background: #f8d7da;
  color: #721c24;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #f5c6cb;
  margin-top: 20px;
  font-weight: 500;
}

.success-message {
  display: flex;
  align-items: center;
  background: #d4edda;
  color: #155724;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #c3e6cb;
  margin-top: 20px;
  font-weight: 500;
}

.error-icon,
.success-icon {
  font-size: 1.2rem;
  margin-right: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .house-management {
    padding: 15px;
  }

  .page-header h1 {
    font-size: 2rem;
  }

  .selection-section {
    padding: 20px;
  }

  .relation-options {
    flex-direction: column;
  }

  .action-section {
    flex-direction: column;
  }

  .bind-btn,
  .reset-btn {
    width: 100%;
  }
}

/* 我的申请记录样式 */
.my-applications-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 40px;
  color: #666;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e9ecef;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #666;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.applications-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.application-card {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s ease;
}

.application-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.application-card.app-pending {
  border-left: 4px solid #ffc107;
  background: linear-gradient(135deg, #fff9e6, #ffffff);
}

.application-card.app-approved {
  border-left: 4px solid #28a745;
  background: linear-gradient(135deg, #e8f5e8, #ffffff);
}

.application-card.app-rejected {
  border-left: 4px solid #dc3545;
  background: linear-gradient(135deg, #fde8e8, #ffffff);
}

.application-card.app-unbound {
  border-left: 4px solid #6c757d;
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e9ecef;
}

.app-id {
  font-weight: 600;
  font-size: 16px;
  color: #333;
}

.app-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.app-status.status-pending {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.app-status.status-approved {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.app-status.status-rejected {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.app-status.status-unbound {
  background: #e2e3e5;
  color: #383d41;
  border: 1px solid #d6d8db;
}

.card-body {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.app-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-row .label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
}

.info-row .value {
  color: #333;
  flex: 1;
}

.app-remark {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border-left: 3px solid #007bff;
}

.remark-label {
  font-weight: 500;
  color: #666;
  margin-bottom: 4px;
  font-size: 14px;
}

.remark-content {
  color: #333;
  line-height: 1.5;
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  align-self: flex-start;
}

.refresh-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #0056b3, #004085);
  transform: translateY(-1px);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.refresh-icon {
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-tabs {
    flex-direction: column;
    gap: 12px;
    margin-top: 16px;
  }

  .tab-btn {
    justify-content: center;
    padding: 14px 20px;
    font-size: 15px;
  }

  .my-applications-section {
    padding: 16px;
    margin-bottom: 16px;
  }

  .application-card {
    padding: 16px;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .info-row .label {
    min-width: auto;
  }
}
</style>
