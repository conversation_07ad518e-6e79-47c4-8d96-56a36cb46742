INSERT INTO building_info 
(id, community_id, building_code, building_name, geo_json, create_time, update_time)
VALUES 
(1, 1, 1, '1栋', '{"longitude": 117.198753, "latitude": 31.773422, "location": {"type": "Point", "coordinates": [117.198753, 31.773422]}}', NOW(), NOW()),
(2, 1, 2, '2栋', '{"longitude": 117.199651, "latitude": 31.773611, "location": {"type": "Point", "coordinates": [117.199651, 31.773611]}}', NOW(), NOW()),
(3, 1, 3, '3栋', '{"longitude": 117.20052, "latitude": 31.774106,"location": {"type": "Point", "coordinates": [117.20052, 31.774106]}}', NOW(), NOW()),
(4, 1, 4, '4栋', '{"longitude": 117.199409, "latitude": 31.774123, "location": {"type": "Point", "coordinates": [117.199409, 31.774123]}}', NOW(), NOW()),
(5, 1, 5, '5栋', '{"longitude": 117.197769, "latitude": 31.773506, "location": {"type": "Point", "coordinates": [117.197769, 31.773506]}}', NOW(), NOW()),
(6, 1, 6, '6栋', '{"longitude": 117.197829, "latitude": 31.773813, "location": {"type": "Point", "coordinates": [117.197829, 31.773813]}}', NOW(), NOW()),
(7, 1, 7, '7栋', '{"longitude": 117.197799, "latitude": 31.774174, "location": {"type": "Point", "coordinates": [117.197799, 31.774174]}}', NOW(), NOW()),
(8, 1, 8, '8栋', '{"longitude": 117.197819, "latitude": 31.774476, "location": {"type": "Point", "coordinates": [117.197819, 31.774476]}}', NOW(), NOW()),
(9, 1, 9, '9栋', '{"longitude": 117.198717, "latitude": 31.773855, "location": {"type": "Point", "coordinates": [117.198717, 31.773855]}}', NOW(), NOW()),
(10, 1, 10, '10栋', '{"longitude": 117.19853, "latitude": 31.774308, "location": {"type": "Point", "coordinates": [117.19853, 31.774308]}}', NOW(), NOW()),
(11, 1, 11, '11栋', '{"longitude": 117.198135, "latitude": 31.774677, "location": {"type": "Point", "coordinates": [117.198135, 31.774677]}}', NOW(), NOW()),
(12, 1, 12, '12栋', '{"longitude": 117.1985, "latitude": 31.774812, "location": {"type": "Point", "coordinates": [117.1985, 31.774812]}}', NOW(), NOW()),
(13, 1, 13, '13栋', '{"longitude": 117.200058, "latitude": 31.774125, "location": {"type": "Point", "coordinates": [117.200058, 31.774125]}}', NOW(), NOW()),
(14, 1, 14, '14栋', '{"longitude": 117.199491, "latitude": 31.774582, "location": {"type": "Point", "coordinates": [117.199491, 31.774582]}}', NOW(), NOW()),
(15, 1, 15, '15栋', '{"longitude": 117.199228, "latitude": 31.775007, "location": {"type": "Point", "coordinates": [117.199228, 31.775007]}}', NOW(), NOW());
(16, 2, 1, 'A1栋', '{"longitude": 117.211054, "latitude": 31.786371, "location": {"type": "Point", "coordinates": [117.211054, 31.786371]}}', NOW(), NOW()),
(17, 2, 2, 'A2栋', '{"longitude": 117.210578, "latitude": 31.786392, "location": {"type": "Point", "coordinates": [117.210578, 31.786392]}}', NOW(), NOW()),
(18, 2, 3, 'A3栋', '{"longitude": 117.211433, "latitude": 31.786131, "location": {"type": "Point", "coordinates": [117.211433, 31.786131]}}', NOW(), NOW()),
(19, 2, 4, 'A4栋', '{"longitude": 117.211048, "latitude": 31.786059, "location": {"type": "Point", "coordinates": [117.211048, 31.786059]}}', NOW(), NOW()),
(20, 2, 5, 'A5栋', '{"longitude": 117.210596, "latitude": 31.78608, "location": {"type": "Point", "coordinates": [117.210596, 31.78608]}}', NOW(), NOW()),
(21, 2, 6, 'A6栋', '{"longitude": 117.211583, "latitude": 31.78566, "location": {"type": "Point", "coordinates": [117.211583, 31.78566]}}', NOW(), NOW()),
(22, 2, 7, 'A7栋', '{"longitude": 117.21109, "latitude": 31.785681, "location": {"type": "Point", "coordinates": [117.21109, 31.785681]}}', NOW(), NOW()),
(23, 2, 8, 'A8栋', '{"longitude": 117.210584, "latitude": 31.785691, "location": {"type": "Point", "coordinates": [117.210584, 31.785691]}}', NOW(), NOW()),
(24, 2, 9, 'A9栋', '{"longitude": 117.211427, "latitude": 31.785328, "location": {"type": "Point", "coordinates": [117.211427, 31.785328]}}', NOW(), NOW()),
(25, 2, 10, 'A10栋', '{"longitude": 117.210789, "latitude": 31.785338, "location": {"type": "Point", "coordinates": [117.210789, 31.785338]}}', NOW(), NOW()),
(26, 2, 11, 'B1栋', '{"longitude": 117.213918, "latitude": 31.78633, "location": {"type": "Point", "coordinates": [117.213918, 31.78633]}}', NOW(), NOW()),
(27, 2, 12, 'B2栋', '{"longitude": 117.213478, "latitude": 31.786325, "location": {"type": "Point", "coordinates": [117.213478, 31.786325]}}', NOW(), NOW()),
(28, 2, 13, 'B3栋', '{"longitude": 117.212744, "latitude": 31.786371, "location": {"type": "Point", "coordinates": [117.212744, 31.786371]}}', NOW(), NOW()),
(29, 2, 14, 'B4栋', '{"longitude": 117.212221, "latitude": 31.786305, "location": {"type": "Point", "coordinates": [117.212221, 31.786305]}}', NOW(), NOW()),
(30, 2, 15, 'B5栋', '{"longitude": 117.213809, "latitude": 31.786029, "location": {"type": "Point", "coordinates": [117.213809, 31.786029]}}', NOW(), NOW()),
(31, 2, 16, 'B6栋', '{"longitude": 117.213166, "latitude": 31.785875, "location": {"type": "Point", "coordinates": [117.213166, 31.785875]}}', NOW(), NOW()),
(32, 2, 17, 'B7栋', '{"longitude": 117.212714, "latitude": 31.786013, "location": {"type": "Point", "coordinates": [117.212714, 31.786013]}}', NOW(), NOW()),
(33, 2, 18, 'B8栋', '{"longitude": 117.212275, "latitude": 31.785977, "location": {"type": "Point", "coordinates": [117.212275, 31.785977]}}', NOW(), NOW()),
(34, 2, 19, 'B9栋', '{"longitude": 117.213779, "latitude": 31.785681, "location": {"type": "Point", "coordinates": [117.213779, 31.785681]}}', NOW(), NOW()),
(35, 2, 20, 'B10栋', '{"longitude": 117.213406, "latitude": 31.785625, "location": {"type": "Point", "coordinates": [117.213406, 31.785625]}}', NOW(), NOW()),
(36, 2, 21, 'B11栋', '{"longitude": 117.212468, "latitude": 31.785655, "location": {"type": "Point", "coordinates": [117.212468, 31.785655]}}', NOW(), NOW()),
(37, 2, 22, 'B12栋', '{"longitude": 117.213893, "latitude": 31.785394, "location": {"type": "Point", "coordinates": [117.213893, 31.785394]}}', NOW(), NOW()),
(38, 2, 23, 'B13栋', '{"longitude": 117.213063, "latitude": 31.785348, "location": {"type": "Point", "coordinates": [117.213063, 31.785348]}}', NOW(), NOW()),
(39, 2, 24, 'C1栋', '{"longitude": 117.212052, "latitude": 31.787271, "location": {"type": "Point", "coordinates": [117.212052, 31.787271]}}', NOW(), NOW()),
(40, 2, 25, 'C2栋', '{"longitude": 117.212251, "latitude": 31.786985, "location": {"type": "Point", "coordinates": [117.212251, 31.786985]}}', NOW(), NOW()),
(41, 2, 26, 'C3栋', '{"longitude": 117.212113, "latitude": 31.786704, "location": {"type": "Point", "coordinates": [117.212113, 31.786704]}}', NOW(), NOW()),
(42, 2, 27, 'C4栋', '{"longitude": 117.211842, "latitude": 31.786499, "location": {"type": "Point", "coordinates": [117.211842, 31.786499]}}', NOW(), NOW()),
(43, 2, 28, 'C5栋', '{"longitude": 117.211523, "latitude": 31.786591, "location": {"type": "Point", "coordinates": [117.211523, 31.786591]}}', NOW(), NOW()),
(44, 2, 29, 'D1栋', '{"longitude": 117.213484, "latitude": 31.787747, "location": {"type": "Point", "coordinates": [117.213484, 31.787747]}}', NOW(), NOW()),
(45, 2, 30, 'D2栋', '{"longitude": 117.212462, "latitude": 31.787737, "location": {"type": "Point", "coordinates": [117.212462, 31.787737]}}', NOW(), NOW()),
(46, 2, 31, 'D3栋', '{"longitude": 117.211544, "latitude": 31.787693, "location": {"type": "Point", "coordinates": [117.211544, 31.787693]}}', NOW(), NOW()),
(47, 2, 32, 'D4栋', '{"longitude": 117.213882, "latitude": 31.78722, "location": {"type": "Point", "coordinates": [117.213882, 31.78722]}}', NOW(), NOW()),
(48, 2, 33, 'D5栋', '{"longitude": 117.212937, "latitude": 31.78721, "location": {"type": "Point", "coordinates": [117.212937, 31.78721]}}', NOW(), NOW()),
(49, 2, 34, 'D6栋', '{"longitude": 117.213883, "latitude": 31.786679, "location": {"type": "Point", "coordinates": [117.213883, 31.786679]}}', NOW(), NOW()),
(50, 2, 35, 'D7栋', '{"longitude": 117.21298, "latitude": 31.786633, "location": {"type": "Point", "coordinates": [117.21298, 31.786633]}}', NOW(), NOW()),
(51, 2, 36, 'E1栋', '{"longitude": 117.21379, "latitude": 31.788263, "location": {"type": "Point", "coordinates": [117.21379, 31.788263]}}', NOW(), NOW()),
(52, 2, 37, 'E2栋', '{"longitude": 117.212892, "latitude": 31.788277, "location": {"type": "Point", "coordinates": [117.212892, 31.788277]}}', NOW(), NOW()),
(53, 2, 38, 'E3栋', '{"longitude": 117.211822, "latitude": 31.788269, "location": {"type": "Point", "coordinates": [117.211822, 31.788269]}}', NOW(), NOW());