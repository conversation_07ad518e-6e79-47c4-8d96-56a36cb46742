<template>
  <div class="resident-profile">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p class="loading-text">正在加载用户信息...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <div class="error-icon">❌</div>
      <p class="error-text">{{ error }}</p>
      <button class="retry-btn" @click="fetchUserInfo">重试</button>
    </div>

    <!-- 正常内容 -->
    <div v-else>
    <div class="profile-header">
      <div class="header-content">
        <div class="avatar-section">
          <div class="avatar">
            <img v-if="userInfo.avatar" :src="userInfo.avatar" alt="用户头像" />
            <div v-else class="default-avatar">
              <span class="avatar-text">{{ getAvatarText() }}</span>
            </div>
          </div>
          <button class="change-avatar-btn" @click="changeAvatar">
            <span class="btn-icon">📷</span>
            更换头像
          </button>
        </div>
        <div class="basic-info">
          <h1 class="user-name">{{ userInfo.userName || '未设置' }}</h1>
          <p class="user-type">{{ getUserTypeText() }}</p>
          <div class="status-badges">
            <span class="badge verified" v-if="userInfo.isVerified">
              <span class="badge-icon">✓</span>
              已认证
            </span>
            <span class="badge unverified" v-else>
              <span class="badge-icon">⚠</span>
              未认证
            </span>
          </div>
        </div>
      </div>
    </div>

    <div class="profile-content">
      <!-- 基本信息卡片 -->
      <div class="info-card">
        <div class="card-header">
          <h2 class="card-title">
            <span class="title-icon">👤</span>
            基本信息
          </h2>
          <button class="edit-btn" @click="editBasicInfo">
            <span class="btn-icon">✏️</span>
            编辑
          </button>
        </div>
        <div class="card-content">
          <div class="info-grid">
            <div class="info-item">
              <label class="info-label">用户名</label>
              <span class="info-value">{{ userInfo.userName || '未设置' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">手机号</label>
              <span class="info-value">{{ userInfo.phone || '未设置' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">真实姓名</label>
              <span class="info-value">{{ userInfo.realName || '未设置' }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">身份证号</label>
              <span class="info-value">{{ maskIdCard(userInfo.idCard) }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">注册时间</label>
              <span class="info-value">{{ formatDate(userInfo.registerTime) }}</span>
            </div>
            <div class="info-item">
              <label class="info-label">最后登录</label>
              <span class="info-value">{{ formatDate(userInfo.lastLoginTime) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 房屋信息卡片 -->
      <div class="info-card">
        <div class="card-header">
          <h2 class="card-title">
            <span class="title-icon">🏠</span>
            房屋信息
          </h2>
          <button class="edit-btn" @click="editHouseInfo">
            <span class="btn-icon">✏️</span>
            编辑
          </button>
        </div>
        <div class="card-content">
          <div class="house-list">
            <div v-if="userInfo.houses && userInfo.houses.length > 0" class="house-items">
              <div v-for="house in userInfo.houses" :key="house.id" class="house-item">
                <div class="house-info">
                  <h4 class="house-address">{{ house.building }}</h4>
                  <p class="house-details">
                    <span class="detail-item">绑定ID: {{ house.id }}</span>
                    <span class="detail-item">关系: {{ house.relationTypeText }}</span>
                    <span class="detail-item">状态: {{ house.statusText }}</span>
                  </p>
                  <p class="house-time">
                    <span class="time-item">申请时间: {{ formatDate(house.submitTime) }}</span>
                    <span v-if="house.updateTime" class="time-item">更新时间: {{ formatDate(house.updateTime) }}</span>
                  </p>
                  <div v-if="house.remark" class="house-remark">
                    <span class="remark-label">备注:</span>
                    <span class="remark-text">{{ house.remark }}</span>
                  </div>
                </div>
                <div class="house-status-badge">
                  <span class="status-badge" :class="getHouseStatusClass(house.status)">
                    {{ house.statusText }}
                  </span>
                  <span class="relation-badge" :class="getRelationClass(house.relationType)">
                    {{ house.relationTypeText }}
                  </span>
                </div>
              </div>
            </div>
            <div v-else class="empty-state">
              <span class="empty-icon">🏠</span>
              <p class="empty-text">暂无绑定房屋</p>
              <button class="bind-house-btn" @click="bindHouse">绑定房屋</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 车辆信息卡片 -->
      <div class="info-card">
        <div class="card-header">
          <h2 class="card-title">
            <span class="title-icon">🚗</span>
            车辆信息
          </h2>
          <button class="edit-btn" @click="editVehicleInfo">
            <span class="btn-icon">✏️</span>
            编辑
          </button>
        </div>
        <div class="card-content">
          <div class="vehicle-list">
            <div v-if="userInfo.vehicles && userInfo.vehicles.length > 0" class="vehicle-items">
              <div v-for="vehicle in userInfo.vehicles" :key="vehicle.id" class="vehicle-item">
                <div class="vehicle-info">
                  <h4 class="vehicle-number">{{ vehicle.plateNumber }}</h4>
                  <p class="vehicle-details">
                    <span class="detail-item">品牌: {{ vehicle.brand }}</span>
                    <span class="detail-item">颜色: {{ vehicle.color }}</span>
                    <span class="detail-item">车位: {{ vehicle.parkingSpace || '未分配' }}</span>
                  </p>
                </div>
                <span class="vehicle-status" :class="vehicle.status">
                  {{ getVehicleStatusText(vehicle.status) }}
                </span>
              </div>
            </div>
            <div v-else class="empty-state">
              <span class="empty-icon">🚗</span>
              <p class="empty-text">暂无绑定车辆</p>
              <button class="bind-vehicle-btn" @click="bindVehicle">绑定车辆</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 家庭成员卡片 -->
      <div class="info-card">
        <div class="card-header">
          <h2 class="card-title">
            <span class="title-icon">👨‍👩‍👧‍👦</span>
            家庭成员
          </h2>
          <button class="edit-btn" @click="editFamilyInfo">
            <span class="btn-icon">✏️</span>
            编辑
          </button>
        </div>
        <div class="card-content">
          <div class="family-list">
            <div v-if="userInfo.familyMembers && userInfo.familyMembers.length > 0" class="family-items">
              <div v-for="member in userInfo.familyMembers" :key="member.id" class="family-item">
                <div class="member-info">
                  <h4 class="member-name">{{ member.name }}</h4>
                  <p class="member-details">
                    <span class="detail-item">关系: {{ member.relationship }}</span>
                    <span class="detail-item">手机: {{ member.phone || '未设置' }}</span>
                  </p>
                </div>
                <span class="member-status" :class="member.status">
                  {{ member.status === 'verified' ? '已认证' : '待认证' }}
                </span>
              </div>
            </div>
            <div v-else class="empty-state">
              <span class="empty-icon">👨‍👩‍👧‍👦</span>
              <p class="empty-text">暂无家庭成员</p>
              <button class="add-member-btn" @click="addFamilyMember">添加成员</button>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useUserStore } from '../stores/userStore';
import { getToken } from '../utils/tokenManager.js';

const userStore = useUserStore();

// 用户信息数据
const userInfo = ref({
  userName: '',
  phone: '',
  realName: '',
  idCard: '',
  userType: 1,
  isVerified: false,
  avatar: null,
  registerTime: '',
  lastLoginTime: '',
  houses: [],
  vehicles: [],
  familyMembers: []
});

// 加载状态
const isLoading = ref(false);
const error = ref('');

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    isLoading.value = true;
    error.value = '';

    console.log('🔍 获取用户信息...');

    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    const response = await fetch('http://localhost:8080/api/auth/me', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ 用户信息响应:', result);

    if (result.code === 200 && result.data) {
      const data = result.data;

      // 映射API返回的数据到本地数据结构
      userInfo.value = {
        id: data.id,
        userName: data.userName || '',
        phone: data.phone || '',
        realName: data.realName || '',
        idCard: data.idCard || '',
        userType: data.userType || 1,
        isVerified: data.isDeleted === 0, // 根据API字段判断认证状态
        avatar: data.avatarUrl || null,
        registerTime: data.createTime || '',
        lastLoginTime: data.updateTime || '',
        // 暂时保留空数组，后续可以添加其他API调用获取房屋、车辆等信息
        houses: [],
        vehicles: [],
        familyMembers: []
      };

      console.log('✅ 用户信息加载成功:', userInfo.value);
    } else {
      throw new Error(result.msg || '获取用户信息失败');
    }
  } catch (err) {
    console.error('❌ 获取用户信息失败:', err);
    error.value = err.message || '获取用户信息失败';
  } finally {
    isLoading.value = false;
  }
};

// 获取房屋绑定信息
const fetchHouseInfo = async () => {
  try {
    console.log('🔍 获取房屋绑定信息...');

    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    const response = await fetch('http://localhost:8080/api/grids/houses/binding/me', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ 房屋绑定信息响应:', result);

    if (result.code === 200 && result.data) {
      // 处理房屋绑定数据
      const houseBindings = result.data.map(binding => ({
        id: binding.id,
        userId: binding.userId,
        houseId: binding.houseId,
        relationType: binding.relationType,
        relationTypeText: getRelationTypeText(binding.relationType),
        status: binding.status,
        statusText: getHouseStatusText(binding.status),
        submitTime: binding.submitTime,
        updateTime: binding.updateTime,
        remark: binding.remark || '',
        // 房屋详细信息需要额外获取，这里先用houseId作为标识
        building: `房屋ID: ${binding.houseId}`,
        unit: '',
        room: '',
        area: 0,
        type: '待获取',
        isOwner: binding.relationType === 1
      }));

      // 更新用户信息中的房屋数据
      userInfo.value.houses = houseBindings;
      console.log('✅ 房屋绑定信息加载成功:', houseBindings.length, '条记录');
    } else {
      console.log('ℹ️ 暂无房屋绑定记录');
      userInfo.value.houses = [];
    }
  } catch (err) {
    console.error('❌ 获取房屋绑定信息失败:', err);
    // 不抛出错误，只是记录日志，避免影响主要的用户信息加载
    userInfo.value.houses = [];
  }
};

// 获取关系类型文本
const getRelationTypeText = (relationType) => {
  const relationMap = {
    1: '业主',
    2: '租客',
    3: '家属'
  };
  return relationMap[relationType] || '未知关系';
};

// 获取房屋绑定状态文本
const getHouseStatusText = (status) => {
  const statusMap = {
    1: '待审核',
    2: '已生效',
    3: '已拒绝',
    4: '已解绑'
  };
  return statusMap[status] || '未知状态';
};

// 获取房屋状态样式类
const getHouseStatusClass = (status) => {
  const classMap = {
    1: 'status-pending',
    2: 'status-approved',
    3: 'status-rejected',
    4: 'status-unbound'
  };
  return classMap[status] || '';
};

// 获取关系类型样式类
const getRelationClass = (relationType) => {
  const classMap = {
    1: 'relation-owner',
    2: 'relation-tenant',
    3: 'relation-family'
  };
  return classMap[relationType] || '';
};

// 获取头像文字
const getAvatarText = () => {
  const name = userInfo.value.realName || userInfo.value.userName || '用户';
  return name.charAt(name.length - 1);
};

// 获取用户类型文本
const getUserTypeText = () => {
  const typeMap = {
    1: '社区居民',
    2: '网格员',
    3: '社区管理员'
  };
  return typeMap[userInfo.value.userType] || '未知';
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未设置';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '未设置';
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (e) {
    return '未设置';
  }
};

// 身份证号脱敏
const maskIdCard = (idCard) => {
  if (!idCard) return '未设置';
  if (idCard.length < 18) return idCard; // 如果长度不够，直接返回
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2');
};

// 获取车辆状态文本
const getVehicleStatusText = (status) => {
  const statusMap = {
    active: '正常',
    pending: '待审核',
    expired: '已过期'
  };
  return statusMap[status] || '未知';
};

// 事件处理函数
const changeAvatar = () => {
  alert('更换头像功能开发中...');
};

const editBasicInfo = () => {
  alert('编辑基本信息功能开发中...');
};

const editHouseInfo = () => {
  alert('编辑房屋信息功能开发中...');
};

const editVehicleInfo = () => {
  alert('编辑车辆信息功能开发中...');
};

const editFamilyInfo = () => {
  alert('编辑家庭成员功能开发中...');
};

const bindHouse = () => {
  alert('绑定房屋功能开发中...');
};

const bindVehicle = () => {
  alert('绑定车辆功能开发中...');
};

const addFamilyMember = () => {
  alert('添加家庭成员功能开发中...');
};

onMounted(async () => {
  console.log('个人信息页面加载完成');
  await fetchUserInfo();
  // 获取用户基本信息后，再获取房屋绑定信息
  await fetchHouseInfo();
});
</script>

<style scoped>
.resident-profile {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 16px;
  color: #666;
  margin: 0;
}

/* 错误状态样式 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-text {
  font-size: 16px;
  color: #dc3545;
  margin: 0 0 20px 0;
}

.retry-btn {
  padding: 10px 20px;
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: linear-gradient(135deg, #0056b3, #004085);
  transform: translateY(-1px);
}

/* 个人信息头部 */
.profile-header {
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-dark));
  color: white;
  border-radius: 12px;
  margin-bottom: 30px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(33, 150, 243, 0.3);
}

.header-content {
  display: flex;
  align-items: center;
  padding: 40px;
  gap: 30px;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-avatar {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--accent-green), var(--accent-green-light));
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-text {
  font-size: 48px;
  font-weight: bold;
  color: white;
}

.change-avatar-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.change-avatar-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.basic-info {
  flex: 1;
}

.user-name {
  font-size: 36px;
  font-weight: 700;
  margin: 0 0 10px 0;
  color: white;
}

.user-type {
  font-size: 18px;
  margin: 0 0 20px 0;
  opacity: 0.9;
}

.status-badges {
  display: flex;
  gap: 10px;
}

.badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.badge.verified {
  background: var(--accent-green);
  color: white;
}

.badge.unverified {
  background: var(--accent-yellow);
  color: var(--text-dark);
}

.badge-icon {
  font-size: 12px;
}

/* 内容区域 */
.profile-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* 信息卡片 */
.info-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid var(--light-gray);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px 30px;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-bottom: 1px solid var(--light-gray);
}

.card-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 600;
  color: var(--text-dark);
  margin: 0;
}

.title-icon {
  font-size: 28px;
}

.edit-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: var(--primary-blue);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.edit-btn:hover {
  background: var(--primary-blue-dark);
  transform: translateY(-2px);
}

.card-content {
  padding: 30px;
}

/* 信息网格 */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-light);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  font-size: 18px;
  font-weight: 500;
  color: var(--text-dark);
  padding: 12px 0;
  border-bottom: 2px solid var(--light-gray);
}

/* 房屋列表 */
.house-list,
.vehicle-list,
.family-list {
  width: 100%;
}

.house-items,
.vehicle-items,
.family-items {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.house-item,
.vehicle-item,
.family-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border: 1px solid var(--light-gray);
  border-radius: 10px;
  transition: all 0.3s ease;
}

.house-item:hover,
.vehicle-item:hover,
.family-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: var(--primary-blue);
}

.house-info h4,
.vehicle-info h4,
.member-info h4 {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-dark);
  margin: 0 0 8px 0;
}

.house-details,
.vehicle-details,
.member-details {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin: 0;
  font-size: 14px;
  color: var(--text-light);
}

.detail-item {
  padding: 4px 12px;
  background: var(--light-gray);
  border-radius: 15px;
  font-weight: 500;
}

/* 房屋时间信息 */
.house-time {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin: 8px 0 0 0;
  font-size: 13px;
  color: #666;
}

.time-item {
  padding: 3px 8px;
  background: #e3f2fd;
  border-radius: 12px;
  font-weight: 500;
}

/* 房屋备注信息 */
.house-remark {
  margin-top: 12px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #007bff;
}

.remark-label {
  font-weight: 600;
  color: #666;
  font-size: 13px;
}

.remark-text {
  color: #333;
  font-size: 14px;
  margin-left: 8px;
}

/* 房屋状态徽章容器 */
.house-status-badge {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-end;
}

/* 状态徽章样式 */
.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.status-pending {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.status-badge.status-approved {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-badge.status-rejected {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.status-badge.status-unbound {
  background: #e2e3e5;
  color: #383d41;
  border: 1px solid #d6d8db;
}

/* 关系类型徽章样式 */
.relation-badge {
  padding: 4px 10px;
  border-radius: 15px;
  font-size: 11px;
  font-weight: 500;
}

.relation-badge.relation-owner {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
}

.relation-badge.relation-tenant {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
}

.relation-badge.relation-family {
  background: linear-gradient(135deg, #6f42c1, #5a2d91);
  color: white;
}

.house-badge,
.vehicle-status,
.member-status {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
}

.house-badge.owner {
  background: var(--accent-green);
  color: white;
}

.house-badge.resident {
  background: var(--primary-blue);
  color: white;
}

.vehicle-status.active,
.member-status.verified {
  background: var(--accent-green);
  color: white;
}

.vehicle-status.pending,
.member-status.pending {
  background: var(--accent-yellow);
  color: var(--text-dark);
}

.vehicle-status.expired {
  background: var(--error);
  color: white;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 64px;
  opacity: 0.5;
}

.empty-text {
  font-size: 18px;
  color: var(--text-light);
  margin: 0;
}

.bind-house-btn,
.bind-vehicle-btn,
.add-member-btn {
  padding: 12px 24px;
  background: var(--primary-blue);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.bind-house-btn:hover,
.bind-vehicle-btn:hover,
.add-member-btn:hover {
  background: var(--primary-blue-dark);
  transform: translateY(-2px);
}

.btn-icon {
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    text-align: center;
    padding: 30px 20px;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .card-header {
    padding: 20px;
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .card-content {
    padding: 20px;
  }

  .house-item,
  .vehicle-item,
  .family-item {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .house-details,
  .vehicle-details,
  .member-details {
    justify-content: center;
  }
}
</style>
