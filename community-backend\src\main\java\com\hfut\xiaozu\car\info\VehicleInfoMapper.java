package com.hfut.xiaozu.car.info;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【vehicle_info(车辆信息表)】的数据库操作Mapper
* @createDate 2025-06-27 16:54:23
* @Entity com.hfut.xiaozu.car.VehicleInfo
*/
@Mapper
public interface VehicleInfoMapper {

    int deleteById(Long id);

    int insert(VehicleInfo record);

    VehicleInfo getById(Long id);

    int update(VehicleInfo record);

    List<VehicleInfo> getByUserId(Long userId);
}
