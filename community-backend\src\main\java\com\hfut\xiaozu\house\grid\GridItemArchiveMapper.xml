<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hfut.xiaozu.house.grid.GridItemArchiveMapper">

    <resultMap id="BaseResultMap" type="com.hfut.xiaozu.house.grid.GridItemArchive">
            <id property="id" column="id" />
            <result property="archiveType" column="archive_type" />
            <result property="relatedId" column="related_id" />
            <result property="gridId" column="grid_id" />
            <result property="name" column="name" />
            <result property="description" column="description" />
            <result property="isSpecial" column="is_special" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,archive_type,related_id,grid_id,name,description,
        is_special,create_time,update_time
    </sql>

    <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from grid_item_archive
        where  id = #{id}
    </select>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from grid_item_archive
        where  id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.hfut.xiaozu.house.grid.GridItemArchive" useGeneratedKeys="true">
        insert into grid_item_archive
        (archive_type,related_id,grid_id,name,description,is_special)
        values (#{archiveType},#{relatedId},#{gridId},#{name},#{description},#{isSpecial})
    </insert>


    <update id="updateByIdSelective" parameterType="com.hfut.xiaozu.house.grid.GridItemArchive">
        update grid_item_archive
        <set>
                <if test="archiveType != null">
                    archive_type = #{archiveType},
                </if>
                <if test="relatedId != null">
                    related_id = #{relatedId},
                </if>
                <if test="gridId != null">
                    grid_id = #{gridId},
                </if>
                <if test="name != null">
                    name = #{name},
                </if>
                <if test="description != null">
                    description = #{description},
                </if>
                <if test="isSpecial != null">
                    is_special = #{isSpecial},
                </if>
        </set>
        where   id = #{id}
    </update>

    <update id="updateById" parameterType="com.hfut.xiaozu.house.grid.GridItemArchive">
        update grid_item_archive
        set
            archive_type =  #{archiveType},
            related_id =  #{relatedId},
            grid_id =  #{gridId},
            name =  #{name},
            description =  #{description},
            is_special =  #{isSpecial}
        where   id = #{id}
    </update>
</mapper>
