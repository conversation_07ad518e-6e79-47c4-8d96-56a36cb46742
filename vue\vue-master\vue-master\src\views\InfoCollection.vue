<template>
  <div class="info-collection">
    <div class="page-header">
      <h1>信息采集</h1>
      <p class="subtitle">实名认证申请管理与审核</p>
    </div>

    <div class="content-container">
      <!-- 筛选控制区域 -->
      <div class="filter-section">
        <div class="filter-controls">
          <div class="filter-group">
            <label>申请状态：</label>
            <select v-model="selectedStatus" @change="loadApplications">
              <option value="0">待审核</option>
              <option value="1">审核失败</option>
              <option value="2">审核通过</option>
            </select>
          </div>
          
          <div class="filter-actions">
            <button 
              class="refresh-btn" 
              @click="refreshData"
              :disabled="isLoading"
            >
              <span class="btn-icon" :class="{ 'spinning': isLoading }">🔄</span>
              {{ isLoading ? '刷新中...' : '刷新数据' }}
            </button>
          </div>
        </div>

        <!-- 统计信息 -->
        <div class="stats-bar">
          <div class="stat-item">
            <span class="stat-label">总申请数：</span>
            <span class="stat-value">{{ applications.length }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">当前状态：</span>
            <span class="stat-value status" :class="getStatusClass(selectedStatus)">
              {{ getStatusText(selectedStatus) }}
            </span>
          </div>
        </div>
      </div>

      <!-- 申请列表 -->
      <div class="applications-section">
        <div v-if="isLoading" class="loading-state">
          <div class="loading-spinner"></div>
          <p>正在加载申请数据...</p>
        </div>

        <div v-else-if="error" class="error-state">
          <div class="error-icon">❌</div>
          <p class="error-message">{{ error }}</p>
          <button class="retry-btn" @click="loadApplications">重试</button>
        </div>

        <div v-else-if="applications.length === 0" class="empty-state">
          <div class="empty-icon">📝</div>
          <p>暂无{{ getStatusText(selectedStatus) }}的申请</p>
        </div>

        <div v-else class="applications-table">
          <table>
            <thead>
              <tr>
                <th>申请ID</th>
                <th>用户ID</th>
                <th>用户名</th>
                <th>提交姓名</th>
                <th>身份证号</th>
                <th>申请状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="app in applications" :key="app.id" class="application-row">
                <td>{{ app.id }}</td>
                <td>{{ app.userId }}</td>
                <td>{{ app.userName }}</td>
                <td>{{ app.submittedName }}</td>
                <td>{{ formatIdCard(app.submittedIdCard) }}</td>
                <td>
                  <span class="status-badge" :class="getStatusClass(app.status)">
                    {{ getStatusText(app.status) }}
                  </span>
                </td>
                <td>
                  <div class="action-buttons">
                    <button
                      class="view-detail-btn"
                      @click="viewApplicationDetail(app)"
                      title="查看详情"
                    >
                      👁️ 查看详情
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- 详情弹窗 -->
    <div v-if="showDetailModal" class="modal-overlay" @click="closeDetailModal">
      <div class="modal-content detail-modal" @click.stop>
        <div class="modal-header">
          <h3>实名认证申请详情</h3>
          <button class="close-btn" @click="closeDetailModal">×</button>
        </div>
        <div class="modal-body">
          <div v-if="selectedApplication" class="detail-info">
            <div class="info-section">
              <h4 class="section-title">基本信息</h4>
              <div class="info-grid">
                <div class="info-row">
                  <label>申请ID：</label>
                  <span>{{ selectedApplication.id }}</span>
                </div>
                <div class="info-row">
                  <label>用户ID：</label>
                  <span>{{ selectedApplication.userId }}</span>
                </div>
                <div class="info-row">
                  <label>用户名：</label>
                  <span>{{ selectedApplication.userName }}</span>
                </div>
                <div class="info-row">
                  <label>申请状态：</label>
                  <span class="status-badge" :class="getStatusClass(selectedApplication.status)">
                    {{ getStatusText(selectedApplication.status) }}
                  </span>
                </div>
              </div>
            </div>

            <div class="info-section">
              <h4 class="section-title">认证信息</h4>
              <div class="info-grid">
                <div class="info-row">
                  <label>提交姓名：</label>
                  <span class="highlight-text">{{ selectedApplication.submittedName }}</span>
                </div>
                <div class="info-row">
                  <label>身份证号：</label>
                  <span class="highlight-text">{{ selectedApplication.submittedIdCard }}</span>
                </div>
              </div>
            </div>

            <!-- 审核操作区域 -->
            <div v-if="selectedApplication.status === 0" class="review-section">
              <h4 class="section-title">审核操作</h4>
              <div class="review-actions">
                <button
                  class="approve-action-btn"
                  @click="approveApplicationInModal"
                  :disabled="isProcessing"
                >
                  <span class="btn-icon">✅</span>
                  审核通过
                </button>
                <button
                  class="reject-action-btn"
                  @click="rejectApplicationInModal"
                  :disabled="isProcessing"
                >
                  <span class="btn-icon">❌</span>
                  审核驳回
                </button>
              </div>
              <p class="review-tip">请仔细核对申请人的身份信息后进行审核操作</p>
            </div>

            <!-- 已审核信息显示 -->
            <div v-else class="reviewed-section">
              <h4 class="section-title">审核结果</h4>
              <div class="review-result">
                <span class="result-status" :class="getStatusClass(selectedApplication.status)">
                  {{ getStatusText(selectedApplication.status) }}
                </span>
                <p class="review-note">该申请已完成审核</p>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="cancel-btn" @click="closeDetailModal">关闭</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 审核通过确认模态框 -->
  <div v-if="showApproveModal" class="modal-overlay" @click="showApproveModal = false">
    <div class="modal-content confirm-modal" @click.stop>
      <div class="modal-header">
        <h3 class="modal-title">
          <span class="title-icon">✅</span>
          确认审核通过
        </h3>
        <button class="close-btn" @click="showApproveModal = false">×</button>
      </div>
      <div class="modal-body">
        <div class="confirm-content">
          <div class="confirm-icon">
            <span class="icon">✅</span>
          </div>
          <div class="confirm-text">
            <p class="confirm-title">确定要通过以下用户的实名认证申请吗？</p>
            <div class="user-info">
              <p><strong>用户名：</strong>{{ selectedApplication?.userName }}</p>
              <p><strong>提交姓名：</strong>{{ selectedApplication?.submittedName }}</p>
              <p><strong>身份证号：</strong>{{ selectedApplication?.submittedIdCard }}</p>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="cancel-btn" @click="showApproveModal = false">取消</button>
        <button class="approve-btn" @click="confirmApprove" :disabled="isProcessing">
          <span v-if="isProcessing">处理中...</span>
          <span v-else>确认通过</span>
        </button>
      </div>
    </div>
  </div>

  <!-- 审核驳回模态框 -->
  <div v-if="showRejectModal" class="modal-overlay" @click="showRejectModal = false">
    <div class="modal-content reject-modal" @click.stop>
      <div class="modal-header">
        <h3 class="modal-title">
          <span class="title-icon">❌</span>
          审核驳回
        </h3>
        <button class="close-btn" @click="showRejectModal = false">×</button>
      </div>
      <div class="modal-body">
        <div class="reject-content">
          <div class="user-info">
            <p><strong>用户名：</strong>{{ selectedApplication?.userName }}</p>
            <p><strong>提交姓名：</strong>{{ selectedApplication?.submittedName }}</p>
          </div>
          <div class="form-group">
            <label for="rejectReason" class="form-label">
              <span class="required">*</span>
              驳回理由：
            </label>
            <textarea
              id="rejectReason"
              v-model="rejectReason"
              class="form-textarea"
              placeholder="请详细说明驳回的原因，以便用户了解并重新提交..."
              rows="4"
              maxlength="200"
            ></textarea>
            <div class="char-count">{{ rejectReason.length }}/200</div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="cancel-btn" @click="showRejectModal = false">取消</button>
        <button class="reject-btn" @click="confirmReject" :disabled="isProcessing || !rejectReason.trim()">
          <span v-if="isProcessing">处理中...</span>
          <span v-else>确认驳回</span>
        </button>
      </div>
    </div>
  </div>

  <!-- 消息提示 -->
  <div v-if="showMessage" class="message-toast" :class="messageType">
    <div class="message-content">
      <span class="message-icon">
        <span v-if="messageType === 'success'">✅</span>
        <span v-else-if="messageType === 'error'">❌</span>
        <span v-else-if="messageType === 'warning'">⚠️</span>
      </span>
      <span class="message-text">{{ messageText }}</span>
    </div>
    <button class="message-close" @click="showMessage = false">×</button>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { getToken } from '../utils/tokenManager';

// 响应式数据
const applications = ref([]);
const selectedStatus = ref(0); // 默认显示待审核
const isLoading = ref(false);
const isProcessing = ref(false); // 审核处理中状态
const error = ref('');
const showDetailModal = ref(false);
const selectedApplication = ref(null);

// 审核确认模态框
const showApproveModal = ref(false);
const showRejectModal = ref(false);
const rejectReason = ref('');

// 消息提示
const showMessage = ref(false);
const messageType = ref(''); // 'success', 'error', 'warning'
const messageText = ref('');

// 状态映射
const statusMap = {
  0: '待审核',
  1: '审核失败', 
  2: '审核通过'
};

// 获取状态文本
const getStatusText = (status) => {
  return statusMap[status] || '未知状态';
};

// 显示消息提示
const showMessageToast = (type, text) => {
  messageType.value = type;
  messageText.value = text;
  showMessage.value = true;

  // 3秒后自动隐藏
  setTimeout(() => {
    showMessage.value = false;
  }, 3000);
};

// 获取状态样式类
const getStatusClass = (status) => {
  const classMap = {
    0: 'pending',
    1: 'failed',
    2: 'approved'
  };
  return classMap[status] || '';
};

// 格式化身份证号（隐藏中间部分）
const formatIdCard = (idCard) => {
  if (!idCard || idCard.length < 8) return idCard;
  return idCard.substring(0, 4) + '****' + idCard.substring(idCard.length - 4);
};

// 加载申请数据
const loadApplications = async () => {
  isLoading.value = true;
  error.value = '';
  
  try {
    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    console.log('🔍 查询实名申请，状态:', selectedStatus.value);
    
    const response = await fetch(`http://localhost:8080/api/auth/list?status=${selectedStatus.value}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('📋 API响应:', result);

    if (result.code === 200) {
      applications.value = result.data || [];
      console.log('✅ 成功加载申请数据:', applications.value.length, '条记录');
    } else {
      throw new Error(result.msg || '获取申请数据失败');
    }
  } catch (err) {
    console.error('❌ 加载申请数据失败:', err);
    error.value = err.message;
    applications.value = [];
  } finally {
    isLoading.value = false;
  }
};

// 刷新数据
const refreshData = () => {
  loadApplications();
};

// 查看申请详情
const viewApplicationDetail = (app) => {
  selectedApplication.value = app;
  showDetailModal.value = true;
};

// 关闭详情弹窗
const closeDetailModal = () => {
  showDetailModal.value = false;
  selectedApplication.value = null;
};



// 显示审核通过确认模态框
const approveApplicationInModal = () => {
  if (!selectedApplication.value) return;
  showApproveModal.value = true;
};

// 确认审核通过
const confirmApprove = async () => {
  if (!selectedApplication.value) return;

  showApproveModal.value = false;
  isProcessing.value = true;

  try {
    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    console.log('🔍 审核通过申请 ID:', selectedApplication.value.id);

    const response = await fetch(`http://localhost:8080/api/auth/update/${selectedApplication.value.id}`, {
      method: 'PUT',
      headers: {
        'Authorization': token,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        id: selectedApplication.value.id,
        status: 2, // 审核通过
        reviewRemark: '审核通过'
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ 审核通过响应:', result);

    if (result.code === 200) {
      showMessageToast('success', '审核通过成功！');
      // 关闭弹窗
      closeDetailModal();
      // 刷新数据
      loadApplications();
    } else {
      throw new Error(result.msg || '审核通过失败');
    }
  } catch (err) {
    console.error('❌ 审核通过失败:', err);
    showMessageToast('error', '审核通过失败: ' + err.message);
  } finally {
    isProcessing.value = false;
  }
};

// 显示审核驳回模态框
const rejectApplicationInModal = () => {
  if (!selectedApplication.value) return;
  rejectReason.value = '';
  showRejectModal.value = true;
};

// 确认审核驳回
const confirmReject = async () => {
  if (!selectedApplication.value) return;

  if (!rejectReason.value || rejectReason.value.trim() === '') {
    showMessageToast('warning', '请输入驳回理由');
    return;
  }

  showRejectModal.value = false;
  isProcessing.value = true;

  try {
    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    console.log('🔍 审核驳回申请 ID:', selectedApplication.value.id, '理由:', rejectReason.value);

    const response = await fetch(`http://localhost:8080/api/auth/update/${selectedApplication.value.id}`, {
      method: 'PUT',
      headers: {
        'Authorization': token,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        id: selectedApplication.value.id,
        status: 1, // 审核驳回
        reviewRemark: rejectReason.value.trim()
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ 审核驳回响应:', result);

    if (result.code === 200) {
      showMessageToast('success', '审核驳回成功！');
      // 关闭弹窗
      closeDetailModal();
      // 刷新数据
      loadApplications();
    } else {
      throw new Error(result.msg || '审核驳回失败');
    }
  } catch (err) {
    console.error('❌ 审核驳回失败:', err);
    showMessageToast('error', '审核驳回失败: ' + err.message);
  } finally {
    isProcessing.value = false;
  }
};

// 页面加载时获取数据
onMounted(() => {
  loadApplications();
});
</script>

<style scoped>
.info-collection {
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #f4d837 0%, #efc443 100%);
  color: white;
  padding: 30px;
  text-align: center;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 2.5em;
  font-weight: 600;
}

.subtitle {
  margin: 0;
  font-size: 1.1em;
  opacity: 0.9;
}

.content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.filter-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.filter-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-group label {
  font-weight: 500;
  color: #333;
}

.filter-group select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.refresh-btn, .retry-btn, .query-btn, .approve-btn {
  background: #FFF7E2 ;
  color: #5B5347 ;
  border: 1.5px solid #FDE1B6 ;
  border-radius: 8px;
  font-weight: 500;
  transition: background 0.2s;
}
.refresh-btn:hover, .retry-btn:hover, .query-btn:hover, .approve-btn:hover {
  background: #FDE1B6 ;
  color: #5B5347 ;
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-icon.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.stats-bar {
  display: flex;
  gap: 20px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.stat-value {
  font-weight: 600;
  color: #333;
}

.stat-value.status.pending { color: #ffa500; }
.stat-value.status.failed { color: #dc3545; }
.stat-value.status.approved { color: #28a745; }

.applications-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.loading-state, .error-state, .empty-state {
  text-align: center;
  padding: 40px;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #e6c623;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

.error-icon, .empty-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.retry-btn {
  padding: 8px 16px;
  background: #f2eb26;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

.applications-table {
  overflow-x: auto;
}

.applications-table table {
  width: 100%;
  border-collapse: collapse;
}

.applications-table th,
.applications-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.applications-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.pending {
  background: #fff3cd;
  color: #856404;
}

.status-badge.failed {
  background: #f8d7da;
  color: #721c24;
}

.status-badge.approved {
  background: #d4edda;
  color: #155724;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-buttons button {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.view-btn {
  background: #f3e013;
  color: white;
}

.approve-btn {
  background: #28a745;
  color: white;
}

.reject-btn {
  background: #dc3545;
  color: white;
}

.action-buttons button:hover:not(:disabled) {
  opacity: 0.8;
}

.action-buttons button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.view-detail-btn {
  background: #d1c21d;
  color: white;
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
}

.detail-modal {
  max-width: 600px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.modal-body {
  padding: 20px;
}

.detail-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-section {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
}

.section-title {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #ecc528;
  padding-bottom: 6px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-row {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-row label {
  font-weight: 500;
  color: #666;
  font-size: 14px;
}

.info-row span {
  color: #333;
  font-weight: 500;
}

.highlight-text {
  color: #fce00a !important;
  font-weight: 600 !important;
  font-size: 15px;
}

.review-section {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  padding: 16px;
}

.review-actions {
  display: flex;
  gap: 12px;
  margin: 12px 0;
}

.approve-action-btn, .reject-action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.approve-action-btn {
  background: #28a745;
  color: white;
}

.approve-action-btn:hover:not(:disabled) {
  background: #218838;
}

.reject-action-btn {
  background: #dc3545;
  color: white;
}

.reject-action-btn:hover:not(:disabled) {
  background: #c82333;
}

.approve-action-btn:disabled,
.reject-action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.review-tip {
  margin: 8px 0 0 0;
  font-size: 13px;
  color: #856404;
  font-style: italic;
}

.reviewed-section {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 6px;
  padding: 16px;
  text-align: center;
}

.review-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.result-status {
  font-size: 18px;
  font-weight: 600;
  padding: 8px 16px;
  border-radius: 20px;
}

.review-note {
  margin: 0;
  color: #155724;
  font-size: 14px;
}

.modal-footer {
  padding: 20px;
  border-top: 1px solid #eee;
  text-align: right;
}

.cancel-btn {
  padding: 8px 16px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-modal {
    max-width: 95%;
    margin: 10px;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .review-actions {
    flex-direction: column;
  }

  .approve-action-btn, .reject-action-btn {
    justify-content: center;
  }
}

/* 确认模态框样式 */
.confirm-modal {
  max-width: 500px;
}

.confirm-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  text-align: left;
}

.confirm-icon {
  flex-shrink: 0;
}

.confirm-icon .icon {
  font-size: 48px;
  display: block;
}

.confirm-text {
  flex: 1;
}

.confirm-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
}

.user-info {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #ffd900;
}

.user-info p {
  margin: 4px 0;
  font-size: 14px;
  color: #555;
}

.approve-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.approve-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #218838, #1ea085);
  transform: translateY(-1px);
}

.approve-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 驳回模态框样式 */
.reject-modal {
  max-width: 600px;
}

.reject-content .user-info {
  margin-bottom: 20px;
  border-left-color: #dc3545;
}

.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.required {
  color: #dc3545;
  margin-right: 4px;
}

.form-textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.char-count {
  text-align: right;
  font-size: 12px;
  color: #6c757d;
  margin-top: 4px;
}

.reject-btn {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.reject-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #c82333, #bd2130);
  transform: translateY(-1px);
}

.reject-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 消息提示样式 */
.message-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  z-index: 10000;
  min-width: 300px;
  animation: slideInRight 0.3s ease;
}

.message-toast.success {
  border-left: 4px solid #28a745;
}

.message-toast.error {
  border-left: 4px solid #dc3545;
}

.message-toast.warning {
  border-left: 4px solid #ffc107;
}

.message-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.message-icon {
  font-size: 18px;
}

.message-text {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.message-close {
  background: none;
  border: none;
  font-size: 18px;
  color: #999;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.message-close:hover {
  color: #666;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
</style>
