<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hfut.xiaozu.vote.record.VoteRecordMapper">

    <resultMap id="BaseResultMap" type="com.hfut.xiaozu.vote.record.VoteRecordEntity">
            <id property="id" column="id" />
            <result property="voteId" column="vote_id" />
            <result property="userId" column="user_id" />
            <result property="optionId" column="option_id" />
            <result property="createTime" column="create_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,vote_id,user_id,option_id,create_time
    </sql>

    <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from vote_record
        where  id = #{id}
    </select>

    <select id="listByVoteId" resultType="com.hfut.xiaozu.vote.record.VoteRecordEntity">
        select
        <include refid="Base_Column_List" />
        from vote_record
        where  vote_id = #{voteId}
    </select>

    <select id="countByOptionId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM vote_record
        WHERE option_id = #{optionId}
    </select>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from vote_record
        where  id = #{id}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.hfut.xiaozu.vote.record.VoteRecordEntity" useGeneratedKeys="true">
        insert into vote_record
        ( vote_id,user_id,option_id)
        values (#{voteId},#{userId},#{optionId})
    </insert>


</mapper>
