import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router.js' // 明确引入router.js文件
import './style.css' // 保留或根据需要调整全局样式
import { useAuthStore } from './stores/auth.js'
import { useUserStore } from './stores/userStore.js'

// 创建应用实例
const app = createApp(App)

// 创建Pinia实例
const pinia = createPinia()
app.use(pinia) // 使用pinia状态管理

// 恢复认证状态
const authStore = useAuthStore()
const userStore = useUserStore()
console.log('🔄 main.js: 开始恢复认证状态...')
authStore.restoreAuthState()
userStore.initializeUser()
console.log('✅ main.js: 认证状态恢复完成')
console.log('🔍 main.js: 用户状态:', {
  userType: userStore.userType,
  isResident: userStore.isResident,
  isProperty: userStore.isProperty,
  isAuthenticated: userStore.isAuthenticated
})

// 移除这里的路由守卫，使用 router.js 中定义的路由守卫

// 路由后置守卫
router.afterEach((to, from) => {
  console.log(`✅ 路由导航完成: ${to.path}`)

  // 更新页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 物业管理系统`
  } else {
    document.title = '物业管理系统'
  }
})

app.use(router) // 使用router

app.mount('#app')

console.log('🚀 应用启动完成')