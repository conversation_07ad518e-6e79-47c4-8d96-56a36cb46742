<template>
  <div class="property-dashboard">
    <div class="dashboard-header">
      <h1>社区治理端</h1>
      <p class="subtitle">物业管理系统 - 数据总览与功能导航</p>
    </div>
    
    <div class="dashboard-content">
      <!-- 社区治理端功能模块 -->
      <section class="function-section">
        <h2 class="section-title">社区治理功能</h2>
        <div class="grid-container">
          <div class="grid-item" @click="navigateTo('/property/overview')">
            <div class="item-icon">📊</div>
            <h3>数据总览</h3>
            <p>总体情况展示, GIS地图展示</p>
          </div>
          
          <div class="grid-item" @click="navigateTo('/property/full-archive')">
            <div class="item-icon">📁</div>
            <h3>全息档案</h3>
            <p>档案信息管理与分类, 档案查询与展示, 重点监控人群, 档案导出</p>
          </div>
          
          <div class="grid-item" @click="navigateTo('/property/event-management')">
            <div class="item-icon">🎯</div>
            <h3>事件管理</h3>
            <p>多源事件整合, 事件查询与状态跟踪, 超时预警</p>
          </div>
          
          <div class="grid-item" @click="navigateTo('/property/patrol-management')">
            <div class="item-icon">🚶</div>
            <h3>巡查管理</h3>
            <p>巡查任务编排, 巡查任务发布, 巡查任务执行情况</p>
          </div>
          
          <div class="grid-item" @click="navigateTo('/property/gis-management')">
            <div class="item-icon">🗺️</div>
            <h3>GIS网格管理</h3>
            <p>网格边界编辑, 网格信息维护与绑定, 网格合并与拆分</p>
          </div>
          
          <div class="grid-item" @click="navigateTo('/property/info-collection')">
            <div class="item-icon">📝</div>
            <h3>信息采集</h3>
            <p>信息查询与浏览, 信息新增与维护, 纠错审核流</p>
          </div>
        </div>
      </section>

      <!-- 公共模块 -->
      <section class="function-section">
        <h2 class="section-title">公共模块</h2>
        <div class="grid-container">
          <div class="grid-item" @click="navigateTo('/common/user-permission')">
            <div class="item-icon">👥</div>
            <h3>用户与权限服务</h3>
            <p>登录鉴权, 账号体系</p>
          </div>
          
          <div class="grid-item" @click="navigateTo('/common/file-storage')">
            <div class="item-icon">💾</div>
            <h3>文件存储服务</h3>
            <p>阿里OSS</p>
          </div>
          
          <div class="grid-item" @click="navigateTo('/common/message-notification')">
            <div class="item-icon">📢</div>
            <h3>消息通知服务</h3>
            <p>系统内通知</p>
          </div>
          
          <div class="grid-item" @click="navigateTo('/common/log-monitor')">
            <div class="item-icon">📈</div>
            <h3>日志与监控</h3>
            <p>操作日志, 错误告警, 系统性能监控</p>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';

const router = useRouter();

const navigateTo = (path) => {
  // 跳转到对应的页面
  router.push(path);
};
</script>

<style scoped>
.property-dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px 0;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.dashboard-header h1 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 2.2em;
  font-weight: bold;
}

.subtitle {
  margin: 0;
  color: #666;
  font-size: 1.1em;
}

.dashboard-content {
  padding: 0 20px;
}

.function-section {
  margin-bottom: 50px;
}

.section-title {
  margin: 0 0 25px 0;
  padding-bottom: 10px;
  border-bottom: 3px solid #d4a574;
  color: #333;
  font-size: 1.5em;
  font-weight: bold;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 25px;
}

.grid-item {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  text-align: center;
}

.grid-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  border-color: #d4a574;
}

.item-icon {
  font-size: 2.5em;
  margin-bottom: 15px;
  display: block;
}

.grid-item h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.2em;
  font-weight: bold;
}

.grid-item p {
  margin: 0;
  color: #666;
  font-size: 0.95em;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-container {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .dashboard-header h1 {
    font-size: 1.8em;
  }
  
  .grid-item {
    padding: 20px;
  }
}
</style>
