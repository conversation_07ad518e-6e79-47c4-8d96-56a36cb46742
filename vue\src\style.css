/* 治愈温馨暖黄色配色方案 - 替换原蓝色系 */
:root {
  /* 主色调 - 暖黄色系 */
  --primary-yellow: #FFD180; /* 主暖黄 */
  --primary-yellow-dark: #FFB300; /* 深暖黄 */
  --primary-yellow-light: #FFF8E1; /* 浅暖黄 */
  --primary-yellow-lighter: #FFFDE7; /* 更浅暖黄 */

  /* 辅助橙色 */
  --secondary-orange: #FFAB40;
  --secondary-orange-dark: #FF9100;
  --secondary-orange-light: #FFE0B2;

  /* 中性色 - 白灰系 */
  --white: #FFFFFF;
  --light-gray: #FFF8E1;
  --gray: #FFE0B2;
  --medium-gray: #FFD180;
  --dark-gray: #BCAAA4;
  --text-dark: #222;
  --text-light: #8D6E63;

  /* 点缀色 - 柔和绿 */
  --accent-green: #AED581;
  --accent-green-light: #DCE775;
  --accent-green-lighter: #F0F4C3;
  --accent-yellow: #FFD54F;
  --accent-yellow-light: #FFF59D;
  --accent-yellow-lighter: #FFFDE7;

  /* 状态色 */
  --success: var(--accent-green);
  --warning: var(--accent-yellow);
  --error: #F44336;
  --info: var(--primary-yellow);

  /* 全局暖黄色色系变量 */
  --main-yellow: #FFE0B2;
  --card-yellow: #FFF8E1;
  --line-orange: #FFCC80;
  --btn-yellow: #FFD180;
  --btn-yellow-dark: #FFB300;
  --text-gray: #333;
}

body {
  color: var(--text-dark);
  background: #fff;
  background: linear-gradient(
    135deg,
    var(--white) 0%,
    var(--light-gray) 40%,
    var(--primary-yellow-lighter) 70%,
    var(--white) 100%
  );
  min-height: 100vh;
}

/* 确保自定义弹窗容器在最顶层 */
#custom-dialog-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 10000;
}

#custom-dialog-container > * {
  pointer-events: auto;
}