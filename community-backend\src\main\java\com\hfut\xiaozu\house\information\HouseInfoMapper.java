package com.hfut.xiaozu.house.information;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【house_info(房屋信息表)】的数据库操作Mapper
* @createDate 2025-06-25 21:44:49
* @Entity com.hfut.xiaozu.house.HouseInfo
*/
@Mapper
public interface HouseInfoMapper {

    int insert(HouseInfo record);

    HouseInfo getById(Long id);

    int update(HouseInfo record);

    List<HouseInfo> listByUnitId(Long unitId);

    Integer getHouseStatusById(Long houseId);

    void updateStatusById(Long houseId, Integer status);

    Long getCommunityIdByHouseId(Long houseId);
}
