package com.hfut.xiaozu.user.idv;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IdentityVerifySubmitVListVO {

    /**
     * 认证提交ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /*
      用户姓名
     */
    private String userName;

    /**
     * 提交的姓名
     */
    private String submittedName;

    /**
     * 提交的身份证号
     */
    private String submittedIdCard;

    /**
     * 当前状态: 0-待审核,1-审核失败,2-审核通过
     */
    private Integer status;

}
