<template>
  <div class="data-overview">
    <div class="page-header">
      <h1>数据总览</h1>
      <p class="subtitle">社区综合数据展示与GIS地图可视化</p>
    </div>

    <div class="overview-content">
      <!-- 统计卡片区域 -->
      <div class="stats-section">
        <div class="stats-grid">
          <div class="stat-card primary">
            <div class="stat-icon">🏠</div>
            <div class="stat-info">
              <h3>总户数</h3>
              <p class="stat-number">{{ statistics.totalHouseholds }}</p>
              <span class="stat-change positive">+{{ statistics.householdChange }} 本月</span>
            </div>
          </div>
          
          <div class="stat-card success">
            <div class="stat-icon">👥</div>
            <div class="stat-info">
              <h3>总人数</h3>
              <p class="stat-number">{{ statistics.totalPopulation }}</p>
              <span class="stat-change positive">+{{ statistics.populationChange }} 本月</span>
            </div>
          </div>
          
          <div class="stat-card warning">
            <div class="stat-icon">📋</div>
            <div class="stat-info">
              <h3>待处理事件</h3>
              <p class="stat-number">{{ statistics.pendingEvents }}</p>
              <span class="stat-change negative">-{{ statistics.eventChange }} 今日</span>
            </div>
          </div>
          
          <div class="stat-card info">
            <div class="stat-icon">🗺️</div>
            <div class="stat-info">
              <h3>网格数量</h3>
              <p class="stat-number">{{ statistics.totalGrids }}</p>
              <span class="stat-change neutral">{{ statistics.activeGrids }} 活跃</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 地图和图表区域 -->
      <div class="main-content">
        <div class="content-grid">
          <!-- GIS地图区域 -->
          <div class="map-section">
            <div class="section-header">
              <h2>社区GIS地图</h2>
              <div class="map-controls">
                <select v-model="selectedMapView" @change="changeMapView">
                  <option value="overview">总览视图</option>
                  <option value="grid">网格视图</option>
                  <option value="events">事件视图</option>
                  <option value="patrol">巡查视图</option>
                </select>
                <button
                  class="control-btn"
                  :class="{ 'refreshing': isRefreshing }"
                  :disabled="isRefreshing"
                  @click="refreshMapData"
                >
                  <span class="btn-icon" :class="{ 'spinning': isRefreshing }">
                    {{ isRefreshing ? '⏳' : '🔄' }}
                  </span>
                  {{ isRefreshing ? '刷新中...' : '刷新' }}
                </button>
              </div>
            </div>
            <div class="map-container">
              <MapComponent
                ref="mapComponent"
                :height="'450px'"
                :center="mapCenter"
                :zoom="17"
                :markers="mapMarkers"
                :polygons="mapPolygons"
                :show-controls="true"
                :show-drawing-tools="false"
                @marker-click="onMarkerClick"
                @polygon-click="onPolygonClick"
                @layer-toggle="onLayerToggle"
                @map-ready="onMapReady"
              />
            </div>
          </div>

          <!-- 事件分布图表 -->
          <div class="chart-section">
            <div class="section-header">
              <h2>事件分布统计</h2>
            </div>
            <div class="chart-container">
              <canvas ref="eventChart" width="400" height="300"></canvas>
            </div>
          </div>
        </div>

        <!-- 详细数据表格 -->
        <div class="data-tables">
          <div class="table-section">
            <div class="section-header">
              <h2>最新事件</h2>
              <router-link to="/property/event-management" class="view-all-link">
                查看全部 →
              </router-link>
            </div>
            <div class="table-container">
              <table class="data-table">
                <thead>
                  <tr>
                    <th>事件编号</th>
                    <th>事件类型</th>
                    <th>发生位置</th>
                    <th>状态</th>
                    <th>创建时间</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="event in recentEvents" :key="event.id">
                    <td>{{ event.id }}</td>
                    <td>
                      <span :class="['event-type', event.type]">{{ getEventTypeName(event.type) }}</span>
                    </td>
                    <td>{{ event.location }}</td>
                    <td>
                      <span :class="['status-badge', event.status]">{{ getStatusName(event.status) }}</span>
                    </td>
                    <td>{{ formatDate(event.createdAt) }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <div class="table-section">
            <div class="section-header">
              <h2>网格状态</h2>
              <router-link to="/property/gis-grid" class="view-all-link">
                查看全部 →
              </router-link>
            </div>
            <div class="table-container">
              <table class="data-table">
                <thead>
                  <tr>
                    <th>网格名称</th>
                    <th>负责人</th>
                    <th>面积(m²)</th>
                    <th>状态</th>
                    <th>最后更新</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="grid in gridStatus" :key="grid.id">
                    <td>{{ grid.name }}</td>
                    <td>{{ grid.manager }}</td>
                    <td>{{ grid.area.toLocaleString() }}</td>
                    <td>
                      <span :class="['status-badge', grid.status]">{{ getGridStatusName(grid.status) }}</span>
                    </td>
                    <td>{{ formatDate(grid.updatedAt) }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, computed } from 'vue';
import { useRouter } from 'vue-router';
import MapComponent from '../components/MapComponent.vue';
import Chart from 'chart.js/auto';
import { getAllGrids } from '../services/gridApi.js';

const router = useRouter();

// 响应式数据
const mapComponent = ref(null);
const eventChart = ref(null);
const selectedMapView = ref('overview');
const isRefreshing = ref(false);

// 统计数据
const statistics = reactive({
  totalHouseholds: 1248,
  householdChange: 12,
  totalPopulation: 3567,
  populationChange: 28,
  pendingEvents: 23,
  eventChange: 5,
  totalGrids: 12,
  activeGrids: 10
});

// 地图配置
const mapCenter = [117.198612, 31.774164]; // 社区管理系统地图中心点，高德地图格式：[经度, 纬度]

// 基础地图数据
const baseMapData = {
  overview: {
    markers: [
      {
        lat: 31.774164,
        lng: 117.198612,
        title: '社区服务中心',
        popup: '芙蓉社区服务中心',
        properties: {
          type: '服务设施',
          status: '正常运营'
        }
      },
      {
        lat: 31.775164,
        lng: 117.199612,
        title: '紧急事件',
        popup: '水管爆裂',
        properties: {
          type: '紧急事件',
          status: '处理中'
        }
      }
    ],
    polygons: [
      {
        id: 1,
        title: '芙蓉社区A区',
        coordinates: [[116.4070, 39.9040], [116.4080, 39.9040], [116.4080, 39.9050], [116.4070, 39.9050]],
        color: '#28a745',
        fillColor: '#28a745',
        fillOpacity: 0.2,
        strokeWeight: 2,
        popup: '<div><h4>芙蓉社区A区</h4><p>面积：15000m²</p><p>负责人：张三</p><p>状态：活跃</p></div>',
        properties: {
          gridId: 1,
          name: '芙蓉社区A区',
          area: 15000,
          manager: '张三',
          status: 'active'
        }
      },
      {
        id: 2,
        title: '芙蓉社区B区',
        coordinates: [[116.4080, 39.9040], [116.4090, 39.9040], [116.4090, 39.9048], [116.4080, 39.9048]],
        color: '#28a745',
        fillColor: '#28a745',
        fillOpacity: 0.2,
        strokeWeight: 2,
        popup: '<div><h4>芙蓉社区B区</h4><p>面积：12000m²</p><p>负责人：李四</p><p>状态：活跃</p></div>',
        properties: {
          gridId: 2,
          name: '芙蓉社区B区',
          area: 12000,
          manager: '李四',
          status: 'active'
        }
      },
      {
        id: 3,
        title: '芙蓉社区C区',
        coordinates: [[116.4060, 39.9050], [116.4075, 39.9050], [116.4075, 39.9065], [116.4060, 39.9065]],
        color: '#6c757d',
        fillColor: '#6c757d',
        fillOpacity: 0.2,
        strokeWeight: 2,
        popup: '<div><h4>芙蓉社区C区</h4><p>面积：18000m²</p><p>负责人：王五</p><p>状态：非活跃</p></div>',
        properties: {
          gridId: 3,
          name: '芙蓉社区C区',
          area: 18000,
          manager: '王五',
          status: 'inactive'
        }
      }
    ]
  },
  grid: {
    markers: [],
    polygons: [
      {
        id: 1,
        title: '芙蓉社区A区',
        coordinates: [[117.201184, 31.769427], [117.203184, 31.769427], [117.203184, 31.771427], [117.201184, 31.771427]],
        color: '#28a745',
        fillColor: '#28a745',
        fillOpacity: 0.3,
        strokeWeight: 2,
        popup: '<div><h4>芙蓉社区A区</h4><p>面积：15000m²</p><p>负责人：张三</p><p>状态：活跃</p></div>',
        properties: {
          gridId: 1,
          name: '芙蓉社区A区',
          area: 15000,
          manager: '张三',
          status: 'active'
        }
      },
      {
        id: 2,
        title: '芙蓉社区B区',
        coordinates: [[117.203184, 31.769427], [117.205184, 31.769427], [117.205184, 31.770927], [117.203184, 31.770927]],
        color: '#28a745',
        fillColor: '#28a745',
        fillOpacity: 0.3,
        strokeWeight: 2,
        popup: '<div><h4>芙蓉社区B区</h4><p>面积：12000m²</p><p>负责人：李四</p><p>状态：活跃</p></div>',
        properties: {
          gridId: 2,
          name: '芙蓉社区B区',
          area: 12000,
          manager: '李四',
          status: 'active'
        }
      },
      {
        id: 3,
        title: '芙蓉社区C区',
        coordinates: [[117.200184, 31.771427], [117.202684, 31.771427], [117.202684, 31.773927], [117.200184, 31.773927]],
        color: '#6c757d',
        fillColor: '#6c757d',
        fillOpacity: 0.3,
        strokeWeight: 2,
        popup: '<div><h4>芙蓉社区C区</h4><p>面积：18000m²</p><p>负责人：王五</p><p>状态：非活跃</p></div>',
        properties: {
          gridId: 3,
          name: '芙蓉社区C区',
          area: 18000,
          manager: '王五',
          status: 'inactive'
        }
      }
    ]
  },
  events: {
    markers: [
      {
        lat: 39.9042,
        lng: 116.4074,
        title: '维修事件',
        popup: 'A区1号楼电梯故障',
        properties: {
          type: '维修事件',
          status: '待处理'
        }
      },
      {
        lat: 39.9052,
        lng: 116.4084,
        title: '安全事件',
        popup: 'B区停车场可疑人员',
        properties: {
          type: '安全事件',
          status: '处理中'
        }
      },
      {
        lat: 39.9032,
        lng: 116.4064,
        title: '环境事件',
        popup: 'C区花园垃圾清理',
        properties: {
          type: '环境事件',
          status: '已完成'
        }
      }
    ],
    polygons: [
      {
        id: 1,
        title: '芙蓉社区A区',
        coordinates: [[116.4070, 39.9040], [116.4080, 39.9040], [116.4080, 39.9050], [116.4070, 39.9050]],
        color: '#28a745',
        fillColor: '#28a745',
        fillOpacity: 0.1,
        strokeWeight: 1,
        popup: '<div><h4>芙蓉社区A区</h4><p>面积：15000m²</p><p>负责人：张三</p></div>'
      },
      {
        id: 2,
        title: '芙蓉社区B区',
        coordinates: [[116.4080, 39.9040], [116.4090, 39.9040], [116.4090, 39.9048], [116.4080, 39.9048]],
        color: '#28a745',
        fillColor: '#28a745',
        fillOpacity: 0.1,
        strokeWeight: 1,
        popup: '<div><h4>芙蓉社区B区</h4><p>面积：12000m²</p><p>负责人：李四</p></div>'
      },
      {
        id: 3,
        title: '芙蓉社区C区',
        coordinates: [[116.4060, 39.9050], [116.4075, 39.9050], [116.4075, 39.9065], [116.4060, 39.9065]],
        color: '#6c757d',
        fillColor: '#6c757d',
        fillOpacity: 0.1,
        strokeWeight: 1,
        popup: '<div><h4>芙蓉社区C区</h4><p>面积：18000m²</p><p>负责人：王五</p></div>'
      }
    ]
  },
  patrol: {
    markers: [
      {
        lat: 39.9042,
        lng: 116.4074,
        title: '巡查点1',
        popup: '主入口检查点',
        properties: {
          type: '巡查点',
          status: '正常'
        }
      },
      {
        lat: 39.9045,
        lng: 116.4077,
        title: '巡查点2',
        popup: '停车场检查点',
        properties: {
          type: '巡查点',
          status: '正常'
        }
      }
    ],
    polygons: [
      {
        id: 1,
        title: '芙蓉社区A区',
        coordinates: [[116.4070, 39.9040], [116.4080, 39.9040], [116.4080, 39.9050], [116.4070, 39.9050]],
        color: '#28a745',
        fillColor: '#28a745',
        fillOpacity: 0.1,
        strokeWeight: 1,
        popup: '<div><h4>芙蓉社区A区</h4><p>面积：15000m²</p><p>负责人：张三</p></div>'
      },
      {
        id: 2,
        title: '芙蓉社区B区',
        coordinates: [[116.4080, 39.9040], [116.4090, 39.9040], [116.4090, 39.9048], [116.4080, 39.9048]],
        color: '#28a745',
        fillColor: '#28a745',
        fillOpacity: 0.1,
        strokeWeight: 1,
        popup: '<div><h4>芙蓉社区B区</h4><p>面积：12000m²</p><p>负责人：李四</p></div>'
      },
      {
        id: 3,
        title: '芙蓉社区C区',
        coordinates: [[116.4060, 39.9050], [116.4075, 39.9050], [116.4075, 39.9065], [116.4060, 39.9065]],
        color: '#6c757d',
        fillColor: '#6c757d',
        fillOpacity: 0.1,
        strokeWeight: 1,
        popup: '<div><h4>芙蓉社区C区</h4><p>面积：18000m²</p><p>负责人：王五</p></div>'
      }
    ]
  }
};

// 工具函数：计算多边形面积（简单估算）
const calculatePolygonArea = (coordinates) => {
  if (!coordinates || coordinates.length < 3) return 0;

  let area = 0;
  const n = coordinates.length;

  for (let i = 0; i < n; i++) {
    const j = (i + 1) % n;
    area += coordinates[i][0] * coordinates[j][1];
    area -= coordinates[j][0] * coordinates[i][1];
  }

  return Math.abs(area / 2) * 111000 * 111000; // 粗略转换为平方米
};

/**
 * 转换后端网格数据为数据总览页面格式
 * @param {Object} backendGrid - 后端网格数据
 * @returns {Object} 数据总览页面网格数据格式
 */
const transformBackendGridToOverview = (backendGrid) => {
  try {
    // 新格式：后端直接返回坐标对象数组
    const coordinateObjects = backendGrid.coordinates;

    // 转换为前端期望的坐标数组格式 [lng, lat]
    const coordinates = coordinateObjects.map(coord => [
      coord.longitude,
      coord.latitude
    ]);

    // 计算多边形面积
    const area = calculatePolygonArea(coordinates);

    return {
      id: backendGrid.id,
      name: backendGrid.gridName,
      manager: '待分配', // 后端暂时没有负责人信息
      area: Math.round(area),
      status: 'active', // 后端暂时没有状态信息，使用默认值
      coordinates: coordinates,
      communityId: backendGrid.communityId,
      responsibleId: backendGrid.responsibleId,
      createdAt: new Date(backendGrid.createTime),
      updatedAt: new Date(backendGrid.updateTime)
    };
  } catch (error) {
    console.error('转换网格数据失败:', error, backendGrid);
    return null;
  }
};

/**
 * 从后端加载网格数据
 */
const loadGridsFromBackend = async () => {
  try {
    console.log('🔧 DataOverview: 开始从后端加载网格数据...');

    const response = await getAllGrids();

    if (response.success && response.data) {
      console.log('✅ DataOverview: 后端网格数据获取成功:', response.data.length, '个网格');

      // 转换数据格式
      const transformedGrids = response.data
        .map(transformBackendGridToOverview)
        .filter(grid => grid !== null); // 过滤掉转换失败的数据

      console.log('✅ DataOverview: 网格数据转换完成:', transformedGrids.length, '个有效网格');
      return transformedGrids;
    } else {
      console.warn('⚠️ DataOverview: 后端返回数据为空，使用空数组');
      return [];
    }
  } catch (error) {
    console.error('❌ DataOverview: 从后端加载网格数据失败:', error);
    return [];
  }
};

// 将网格数据转换为地图多边形格式
const convertGridsToPolygons = (grids) => {
  return grids.map(grid => ({
    id: grid.id,
    title: grid.name,
    coordinates: grid.coordinates,
    color: grid.status === 'active' ? '#28a745' : '#6c757d',
    fillColor: grid.status === 'active' ? '#28a745' : '#6c757d',
    fillOpacity: 0.3,
    strokeWeight: 2,
    popup: `<div><h4>${grid.name}</h4><p>面积：${grid.area}m²</p><p>负责人：${grid.manager}</p><p>状态：${grid.status === 'active' ? '活跃' : '非活跃'}</p></div>`,
    properties: {
      gridId: grid.id,
      name: grid.name,
      area: grid.area,
      manager: grid.manager,
      status: grid.status
    }
  }));
};

// 响应式网格数据
const actualGrids = ref([]);
const isLoadingGrids = ref(false);

// 计算属性：动态生成地图数据
const dynamicMapData = computed(() => {
  const gridPolygons = convertGridsToPolygons(actualGrids.value);

  return {
    overview: {
      markers: baseMapData.overview.markers,
      polygons: baseMapData.overview.polygons.concat(gridPolygons) // 合并静态和动态网格
    },
    grid: {
      markers: [],
      polygons: gridPolygons // 只显示实际创建的网格
    },
    events: baseMapData.events,
    patrol: baseMapData.patrol
  };
});

// 地图标记数据
const mapMarkers = ref(baseMapData.overview.markers);

// 地图多边形数据（网格）- 使用动态数据
const mapPolygons = ref(dynamicMapData.value.overview.polygons);

// 最新事件数据
const recentEvents = ref([
  {
    id: 'E001',
    type: 'maintenance',
    location: 'A区1号楼',
    status: 'pending',
    createdAt: new Date('2024-01-15T10:30:00')
  },
  {
    id: 'E002',
    type: 'security',
    location: 'B区停车场',
    status: 'processing',
    createdAt: new Date('2024-01-15T09:15:00')
  },
  {
    id: 'E003',
    type: 'environment',
    location: 'C区花园',
    status: 'completed',
    createdAt: new Date('2024-01-14T16:45:00')
  }
]);

// 网格状态数据 - 使用计算属性从actualGrids生成
const gridStatus = computed(() => {
  return actualGrids.value.slice(0, 5); // 只显示前5个网格
});

/**
 * 刷新网格数据
 */
const refreshGridData = async () => {
  isLoadingGrids.value = true;
  try {
    const gridData = await loadGridsFromBackend();
    actualGrids.value = gridData;

    // 更新统计数据
    statistics.totalGrids = gridData.length;
    statistics.activeGrids = gridData.filter(grid => grid.status === 'active').length;

    console.log('✅ DataOverview: 网格数据刷新完成:', gridData.length, '个网格');
  } catch (error) {
    console.error('❌ DataOverview: 刷新网格数据失败:', error);
  } finally {
    isLoadingGrids.value = false;
  }
};

// 方法
const changeMapView = async () => {
  console.log('切换地图视图:', selectedMapView.value);

  // 刷新网格数据
  await refreshGridData();

  // 根据选择的视图更新地图数据
  const viewData = dynamicMapData.value[selectedMapView.value];
  if (viewData) {
    mapMarkers.value = [...viewData.markers];
    mapPolygons.value = [...viewData.polygons];

    // 如果地图组件已经加载，刷新地图
    if (mapComponent.value) {
      // 可以在这里添加地图刷新逻辑
      console.log('地图数据已更新，网格数量:', mapPolygons.value.length);
    }
  }
};

const refreshMapData = async () => {
  if (isRefreshing.value) return; // 防止重复刷新

  console.log('开始刷新地图数据...');
  isRefreshing.value = true;

  try {
    // 模拟异步操作延迟
    await new Promise(resolve => setTimeout(resolve, 500));

    // 重置地图视图到默认状态
    selectedMapView.value = 'overview';

    // 刷新网格数据
    await refreshGridData();

    // 重新加载当前视图的地图数据
    const viewData = dynamicMapData.value[selectedMapView.value];
    if (viewData) {
      mapMarkers.value = [...viewData.markers];
      mapPolygons.value = [...viewData.polygons];
    }

    // 如果地图组件已加载，执行完整重置
    if (mapComponent.value) {
      // 重置地图视图到初始位置和缩放级别
      if (mapComponent.value.resetView) {
        mapComponent.value.resetView();
      }

      // 刷新地图数据
      if (mapComponent.value.refreshData) {
        mapComponent.value.refreshData();
      }

      console.log('地图状态已重置到初始状态');
    }

    // 重新加载统计数据（模拟数据刷新）
    refreshStatistics();

    console.log('地图数据刷新完成');
  } catch (error) {
    console.error('地图刷新过程中出现错误:', error);
  } finally {
    isRefreshing.value = false;
  }
};



// 刷新统计数据
const refreshStatistics = () => {
  // 模拟从服务器重新获取数据
  console.log('刷新统计数据');

  // 这里可以添加实际的API调用
  // 目前使用模拟数据更新
  Object.assign(statistics, {
    totalHouseholds: 1248 + Math.floor(Math.random() * 10),
    householdChange: Math.floor(Math.random() * 20),
    totalPopulation: 3567 + Math.floor(Math.random() * 50),
    populationChange: Math.floor(Math.random() * 40),
    pendingEvents: Math.floor(Math.random() * 30),
    eventChange: Math.floor(Math.random() * 10),
    totalGrids: 12,
    activeGrids: 10 + Math.floor(Math.random() * 2)
  });
};

const onMarkerClick = (marker) => {
  console.log('点击标记:', marker);
};

const onPolygonClick = (polygon) => {
  console.log('点击多边形:', polygon);
};

const onMapReady = () => {
  console.log('数据总览 - 地图初始化完成');

  // 确保建筑物图层在地图加载完成后启用
  setTimeout(() => {
    if (mapComponent.value && mapComponent.value.toggleLayer) {
      console.log('强制启用建筑物图层...');
      mapComponent.value.toggleLayer('buildings', true);
    }
  }, 1000);
};

const onLayerToggle = (layerKey, visible) => {
  console.log(`数据总览 - 图层切换: ${layerKey}`, visible);

  if (layerKey === 'buildings') {
    console.log(`建筑物图层${visible ? '启用' : '禁用'}`);

    if (visible) {
      // 当启用建筑物图层时，确保缩放级别足够高
      if (mapComponent.value && mapComponent.value.getMap) {
        const map = mapComponent.value.getMap();
        if (map && map.getZoom() < 16) {
          console.log('缩放级别较低，自动放大以显示建筑物');
          map.setZoom(17);
        }
      }
    }
  }
};

const getEventTypeName = (type) => {
  const typeMap = {
    'maintenance': '维修',
    'security': '安全',
    'environment': '环境'
  };
  return typeMap[type] || type;
};

const getStatusName = (status) => {
  const statusMap = {
    'pending': '待处理',
    'processing': '处理中',
    'completed': '已完成'
  };
  return statusMap[status] || status;
};

const getGridStatusName = (status) => {
  const statusMap = {
    'active': '活跃',
    'inactive': '非活跃'
  };
  return statusMap[status] || status;
};

const formatDate = (date) => {
  return new Date(date).toLocaleString('zh-CN');
};

// 初始化图表
const initEventChart = () => {
  nextTick(() => {
    if (eventChart.value) {
      new Chart(eventChart.value, {
        type: 'doughnut',
        data: {
          labels: ['维修事件', '安全事件', '环境事件', '其他事件'],
          datasets: [{
            data: [12, 8, 5, 3],
            backgroundColor: [
              '#FF6384',
              '#36A2EB',
              '#FFCE56',
              '#4BC0C0'
            ]
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom'
            }
          }
        }
      });
    }
  });
};

// 生命周期
onMounted(async () => {
  initEventChart();

  // 页面加载时刷新网格数据
  await refreshGridData();

  // 更新地图数据
  const viewData = dynamicMapData.value[selectedMapView.value];
  if (viewData) {
    mapMarkers.value = [...viewData.markers];
    mapPolygons.value = [...viewData.polygons];
  }
});
</script>

<style scoped>
.data-overview {
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  text-align: center;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 2.5em;
  font-weight: 600;
}

.subtitle {
  margin: 0;
  opacity: 0.9;
  font-size: 1.2em;
}

.overview-content {
  padding: 30px;
}

.stats-section {
  margin-bottom: 30px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.stat-card.primary {
  border-left: 4px solid #007bff;
}

.stat-card.success {
  border-left: 4px solid #28a745;
}

.stat-card.warning {
  border-left: 4px solid #ffc107;
}

.stat-card.info {
  border-left: 4px solid #17a2b8;
}

.stat-icon {
  font-size: 3em;
  opacity: 0.8;
}

.stat-info h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #666;
  font-weight: 600;
}

.stat-number {
  margin: 0 0 5px 0;
  font-size: 2.2em;
  font-weight: bold;
  color: #333;
}

.stat-change {
  font-size: 12px;
  font-weight: 600;
}

.stat-change.positive {
  color: #28a745;
}

.stat-change.negative {
  color: #dc3545;
}

.stat-change.neutral {
  color: #6c757d;
}

.main-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
  overflow: hidden;
}

.content-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 0;
}

.map-section, .chart-section {
  padding: 25px;
}

.chart-section {
  border-left: 1px solid #e1e8ed;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h2 {
  margin: 0;
  font-size: 1.4em;
  color: #333;
}

.map-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.map-controls select {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.control-btn {
  background: #4a90e2;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.control-btn:hover {
  background: #357abd;
}

.control-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.7;
}

.control-btn.refreshing {
  background: #17a2b8;
  color: white;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.btn-icon {
  font-size: 14px;
}

.map-container {
  border-radius: 8px;
  overflow: hidden;
}

.chart-container {
  height: 300px;
  position: relative;
}

.data-tables {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  padding: 25px;
  border-top: 1px solid #e1e8ed;
}

.table-section {
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
}

.table-section .section-header {
  padding: 15px 20px;
  background: white;
  border-bottom: 1px solid #e1e8ed;
  margin-bottom: 0;
}

.view-all-link {
  color: #4a90e2;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
}

.view-all-link:hover {
  text-decoration: underline;
}

.table-container {
  padding: 20px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.data-table th {
  background: #f1f3f4;
  padding: 12px 8px;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #e1e8ed;
}

.data-table td {
  padding: 12px 8px;
  border-bottom: 1px solid #f1f3f4;
  color: #666;
}

.data-table tr:hover {
  background: #f8f9fa;
}

.event-type, .status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.event-type.maintenance {
  background: #fff3cd;
  color: #856404;
}

.event-type.security {
  background: #f8d7da;
  color: #721c24;
}

.event-type.environment {
  background: #d4edda;
  color: #155724;
}

.status-badge.pending {
  background: #fff3cd;
  color: #856404;
}

.status-badge.processing {
  background: #cce5ff;
  color: #004085;
}

.status-badge.completed {
  background: #d4edda;
  color: #155724;
}

.status-badge.active {
  background: #d4edda;
  color: #155724;
}

.status-badge.inactive {
  background: #f8d7da;
  color: #721c24;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-grid {
    grid-template-columns: 1fr;
  }

  .chart-section {
    border-left: none;
    border-top: 1px solid #e1e8ed;
  }

  .data-tables {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .overview-content {
    padding: 15px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .map-section, .chart-section {
    padding: 15px;
  }

  .data-tables {
    padding: 15px;
  }
}
</style>
