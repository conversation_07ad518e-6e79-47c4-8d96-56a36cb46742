// 高德地图配置
export const AMAP_CONFIG = {
  // 高德地图API密钥
  apiKey: import.meta.env.VITE_AMAP_API_KEY || '6654ed14b4cb7aae5003dc379fd3f134',
  
  // 安全密钥
  securityJsCode: import.meta.env.VITE_AMAP_SECURITY_KEY || '830ff4b97ad96c81625c4d44049c8452',
  
  // 默认地图中心点 - 高德地图使用GCJ02坐标系
  defaultCenter: [117.198612, 31.774164],
  
  // 默认缩放级别
  defaultZoom: 13,
  
  // 地图类型
  mapTypes: {
    NORMAL: 'normal',
    SATELLITE: 'satellite',
    HYBRID: 'hybrid'
  }
};

// 动态加载高德地图API
export const loadAmapAPI = () => {
  return new Promise((resolve, reject) => {
    console.log('检查AMap是否已存在:', typeof AMap);

    if (typeof AMap !== 'undefined') {
      console.log('AMap已存在，直接返回');
      resolve(AMap);
      return;
    }

    console.log('开始加载高德地图API...');
    console.log('API Key:', AMAP_CONFIG.apiKey);
    console.log('安全密钥:', AMAP_CONFIG.securityJsCode);

    // 设置安全密钥
    window._AMapSecurityConfig = {
      securityJsCode: AMAP_CONFIG.securityJsCode,
    };

    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = `https://webapi.amap.com/maps?v=2.0&key=${AMAP_CONFIG.apiKey}`;

    console.log('加载脚本URL:', script.src);

    script.onload = () => {
      console.log('脚本加载完成，检查AMap:', typeof AMap);

      if (typeof AMap !== 'undefined') {
        console.log('高德地图API加载成功');
        resolve(AMap);
      } else {
        console.error('脚本加载完成但AMap未定义');
        reject(new Error('高德地图API加载失败'));
      }
    };

    script.onerror = (error) => {
      console.error('脚本加载失败:', error);
      reject(new Error('高德地图API脚本加载失败: ' + error.message));
    };

    document.head.appendChild(script);
    console.log('脚本已添加到页面');
  });
};
