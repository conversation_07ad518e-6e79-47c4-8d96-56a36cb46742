package com.hfut.xiaozu.user.idv;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-06-24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IdentityVerificationDTO {

    @NotBlank(message = "身份证号不能为空")
    private String submittedIdCard;

    @NotBlank(message = "姓名不能为空")
    private String submittedName;

    @NotBlank(message = "会话ID不能为空")
    private String sessionId;
}
