/**
 * 投票管理API服务
 * 处理投票创建、查询等相关的后端API调用
 */

import axios from 'axios';
import { getToken } from '../utils/tokenManager.js';

// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:8080/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 添加JWT token到请求头
    const token = getToken();

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      console.log('🔑 投票API已添加Authorization头');
    } else {
      console.log('⚠️ 投票API未找到有效token');
    }

    console.log('🗳️ 投票API请求:', config.method?.toUpperCase(), config.url);
    return config;
  },
  error => {
    console.error('❌ 投票API请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  response => {
    console.log('✅ 投票API响应成功:', response.status, response.data);
    return response;
  },
  error => {
    console.error('❌ 投票API响应错误:', error);
    
    if (error.response) {
      console.error('错误状态码:', error.response.status);
      console.error('错误数据:', error.response.data);
    }
    
    return Promise.reject(error);
  }
);

/**
 * 创建投票
 * @param {Object} voteData - 投票数据
 * @returns {Promise<Object>} API响应
 */
export const createVote = async (voteData) => {
  console.log('🔧 API: 创建投票', voteData);

  try {
    const response = await api.post('/vote', voteData);

    console.log('✅ API: 投票创建成功', response.data);
    return {
      success: true,
      data: response.data,
      message: response.data.msg || '投票创建成功'
    };
  } catch (error) {
    console.error('❌ API: 投票创建失败', error);
    
    let errorMessage = '投票创建失败';
    if (error.response?.data?.msg) {
      errorMessage = error.response.data.msg;
    } else if (error.message) {
      errorMessage = error.message;
    }
    
    throw new Error(errorMessage);
  }
};

/**
 * 获取投票列表
 * @returns {Promise<Object>} API响应
 */
export const getVoteList = async () => {
  console.log('🔧 API: 获取投票列表');

  // 检查token
  const token = getToken();
  if (!token) {
    console.error('❌ API: 未找到认证令牌');
    throw new Error('未找到认证令牌，请重新登录');
  }

  try {
    console.log('📡 发送请求到: /vote/list');
    const response = await api.get('/vote/list');

    console.log('✅ API: 投票列表获取成功', response.data);

    // 检查响应格式
    if (!response.data) {
      throw new Error('服务器响应格式错误：缺少data字段');
    }

    return {
      success: true,
      data: response.data.data || [],
      message: response.data.msg || '投票列表获取成功'
    };
  } catch (error) {
    console.error('❌ API: 投票列表获取失败', error);

    let errorMessage = '投票列表获取失败';

    if (error.response) {
      // 服务器响应了错误状态码
      const status = error.response.status;
      const data = error.response.data;

      console.error('❌ HTTP错误状态:', status);
      console.error('❌ 错误响应数据:', data);

      if (status === 401) {
        errorMessage = '认证失败，请重新登录';
      } else if (status === 403) {
        errorMessage = '权限不足，无法访问投票数据';
      } else if (status === 404) {
        errorMessage = '投票接口不存在';
      } else if (status === 500) {
        errorMessage = '服务器内部错误';
      } else if (data?.msg) {
        errorMessage = data.msg;
      } else {
        errorMessage = `请求失败 (HTTP ${status})`;
      }
    } else if (error.request) {
      // 请求已发出但没有收到响应
      console.error('❌ 网络错误:', error.request);
      errorMessage = '网络连接失败，请检查网络或确认后端服务是否启动';
    } else {
      // 其他错误
      console.error('❌ 其他错误:', error.message);
      errorMessage = error.message || '未知错误';
    }

    throw new Error(errorMessage);
  }
};

/**
 * 获取投票详情
 * @param {number} voteId - 投票ID
 * @returns {Promise<Object>} API响应
 */
export const getVoteDetail = async (voteId) => {
  console.log('🔧 API: 获取投票详情', voteId);

  try {
    const response = await api.get(`/vote/${voteId}`);

    console.log('✅ API: 投票详情获取成功', response.data);
    return {
      success: true,
      data: response.data.data,
      message: response.data.msg || '投票详情获取成功'
    };
  } catch (error) {
    console.error('❌ API: 投票详情获取失败', error);
    
    let errorMessage = '投票详情获取失败';
    if (error.response?.data?.msg) {
      errorMessage = error.response.data.msg;
    } else if (error.message) {
      errorMessage = error.message;
    }
    
    throw new Error(errorMessage);
  }
};

/**
 * 提交投票
 * @param {number} voteId - 投票ID
 * @param {Array} optionIds - 选择的选项ID数组
 * @returns {Promise<Object>} API响应
 */
export const submitVote = async (voteId, optionIds) => {
  console.log('🔧 API: 提交投票', { voteId, optionIds });

  // 检查token
  const token = getToken();
  if (!token) {
    console.error('❌ API: 未找到认证令牌');
    throw new Error('未找到认证令牌，请重新登录');
  }

  try {
    // 根据后端错误信息，API期望直接接收选项ID数组
    console.log('📡 发送投票请求到:', `/vote/${voteId}`);
    console.log('📦 请求体 (选项ID数组):', optionIds);

    const response = await api.post(`/vote/${voteId}`, optionIds);

    console.log('✅ API: 投票提交成功', response.data);
    return {
      success: true,
      data: response.data.data,
      message: response.data.msg || '投票提交成功'
    };
  } catch (error) {
    console.error('❌ API: 投票提交失败', error);

    let errorMessage = '投票提交失败';

    if (error.response) {
      // 服务器响应了错误状态码
      const status = error.response.status;
      const data = error.response.data;

      console.error('❌ HTTP错误状态:', status);
      console.error('❌ 错误响应数据:', data);

      if (status === 401) {
        errorMessage = '认证失败，请重新登录';
      } else if (status === 403) {
        errorMessage = '权限不足，无法投票';
      } else if (status === 404) {
        errorMessage = '投票不存在或已结束';
      } else if (status === 400) {
        errorMessage = data?.msg || '投票参数错误';
      } else if (status === 409) {
        errorMessage = '您已经投过票了';
      } else if (status === 500) {
        errorMessage = '服务器内部错误';
      } else if (data?.msg) {
        errorMessage = data.msg;
      } else {
        errorMessage = `投票失败 (HTTP ${status})`;
      }
    } else if (error.request) {
      // 请求已发出但没有收到响应
      console.error('❌ 网络错误:', error.request);
      errorMessage = '网络连接失败，请检查网络或确认后端服务是否启动';
    } else {
      // 其他错误
      console.error('❌ 其他错误:', error.message);
      errorMessage = error.message || '未知错误';
    }

    throw new Error(errorMessage);
  }
};

/**
 * 验证投票数据
 * @param {Object} voteData - 投票数据
 * @returns {Object} 验证结果
 */
export const validateVoteData = (voteData) => {
  const errors = [];

  // 验证标题
  if (!voteData.title || voteData.title.trim().length === 0) {
    errors.push('投票标题不能为空');
  } else if (voteData.title.trim().length > 100) {
    errors.push('投票标题不能超过100个字符');
  }

  // 验证描述
  if (!voteData.description || voteData.description.trim().length === 0) {
    errors.push('投票描述不能为空');
  } else if (voteData.description.trim().length > 500) {
    errors.push('投票描述不能超过500个字符');
  }

  // 验证时间
  if (!voteData.startTime) {
    errors.push('开始时间不能为空');
  }
  if (!voteData.endTime) {
    errors.push('结束时间不能为空');
  }
  if (voteData.startTime && voteData.endTime) {
    const startTime = new Date(voteData.startTime);
    const endTime = new Date(voteData.endTime);
    if (startTime >= endTime) {
      errors.push('开始时间必须早于结束时间');
    }
  }

  // 验证最大选择数
  if (!voteData.maxChoices || voteData.maxChoices < 1) {
    errors.push('最大选择数必须大于0');
  }

  // 验证选项
  if (!voteData.options || voteData.options.length < 2) {
    errors.push('至少需要2个投票选项');
  } else {
    voteData.options.forEach((option, index) => {
      if (!option.content || option.content.trim().length === 0) {
        errors.push(`选项${index + 1}内容不能为空`);
      } else if (option.content.trim().length > 100) {
        errors.push(`选项${index + 1}内容不能超过100个字符`);
      }
    });
  }

  // 验证投票范围
  if (!voteData.scopes || voteData.scopes.length === 0) {
    errors.push('必须选择至少一个投票范围');
  }

  return {
    isValid: errors.length === 0,
    errors: errors
  };
};

export default {
  createVote,
  getVoteList,
  getVoteDetail,
  submitVote,
  validateVoteData
};
