/**
 * HTTP请求拦截器
 * 自动在请求中添加token，处理token过期等情况
 */

import { useAuthStore } from '../stores/auth.js';

// API基础配置
// 在开发环境下使用Vite代理，生产环境使用完整URL
const API_BASE_URL = import.meta.env.DEV
  ? '/api'  // 开发环境使用Vite代理
  : (process.env.VUE_APP_API_BASE_URL || 'http://localhost:8080/api');

/**
 * 创建带有拦截器的fetch包装器
 */
class HttpInterceptor {
  constructor() {
    this.requestQueue = new Map(); // 请求队列，用于防止重复请求
    this.isRefreshing = false; // 是否正在刷新token
    this.refreshPromise = null; // token刷新Promise
  }

  /**
   * 发起HTTP请求
   * @param {string} url - 请求URL
   * @param {Object} options - 请求选项
   * @returns {Promise<Object>} 响应结果
   */
  async request(url, options = {}) {
    // 获取认证store
    const authStore = useAuthStore();
    
    // 构建完整URL
    const fullUrl = url.startsWith('http') ? url : `${API_BASE_URL}${url}`;
    
    // 生成请求ID用于去重
    const requestId = this.generateRequestId(url, options);
    
    try {
      // 检查是否有重复请求
      if (this.requestQueue.has(requestId)) {
        console.log(`⏳ 等待重复请求完成: ${url}`);
        return await this.requestQueue.get(requestId);
      }
      
      // 准备请求配置
      const config = await this.prepareRequestConfig(options, authStore);
      
      // 创建请求Promise并加入队列
      const requestPromise = this.executeRequest(fullUrl, config, authStore);
      this.requestQueue.set(requestId, requestPromise);
      
      // 执行请求
      const result = await requestPromise;
      
      // 请求完成，从队列中移除
      this.requestQueue.delete(requestId);
      
      return result;
      
    } catch (error) {
      // 请求失败，从队列中移除
      this.requestQueue.delete(requestId);
      throw error;
    }
  }

  /**
   * 准备请求配置
   * @param {Object} options - 原始请求选项
   * @param {Object} authStore - 认证store
   * @returns {Object} 处理后的请求配置
   */
  async prepareRequestConfig(options, authStore) {
    const config = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      },
      ...options
    };

    // 合并headers
    config.headers = {
      ...config.headers,
      ...options.headers
    };

    // 处理请求体数据
    if (options.data && (config.method === 'POST' || config.method === 'PUT' || config.method === 'DELETE' || config.method === 'PATCH')) {
      config.body = JSON.stringify(options.data);
      console.log(`📤 请求体数据:`, options.data);
    }

    // 添加认证token
    if (authStore.isAuthenticated && authStore.token) {
      // 检查token是否即将过期
      if (authStore.isTokenExpired) {
        console.log('🔄 Token已过期，尝试刷新...');
        await this.handleTokenRefresh(authStore);
      }

      config.headers['Authorization'] = `Bearer ${authStore.token}`;
      console.log(`🔑 已添加Authorization头: Bearer ${authStore.token.substring(0, 20)}...`);
    }

    // 添加请求ID用于追踪
    config.headers['X-Request-ID'] = this.generateUniqueId();

    return config;
  }

  /**
   * 执行HTTP请求
   * @param {string} url - 请求URL
   * @param {Object} config - 请求配置
   * @param {Object} authStore - 认证store
   * @returns {Promise<Object>} 响应结果
   */
  async executeRequest(url, config, authStore) {
    const startTime = Date.now();
    
    try {
      console.log(`🌐 发起请求: ${config.method} ${url}`);
      
      const response = await fetch(url, config);
      const duration = Date.now() - startTime;
      
      console.log(`📥 收到响应: ${response.status} ${response.statusText} (${duration}ms)`);
      
      // 处理响应
      return await this.handleResponse(response, url, config, authStore);
      
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`❌ 请求失败: ${config.method} ${url} (${duration}ms)`, error);
      
      // 处理网络错误
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        const networkError = new Error('网络连接失败，请检查网络设置');
        networkError.code = 'NETWORK_ERROR';
        throw networkError;
      }
      
      throw error;
    }
  }

  /**
   * 处理HTTP响应
   * @param {Response} response - fetch响应对象
   * @param {string} url - 请求URL
   * @param {Object} config - 请求配置
   * @param {Object} authStore - 认证store
   * @returns {Promise<Object>} 处理后的响应数据
   */
  async handleResponse(response, url, config, authStore) {
    // 解析响应内容
    const contentType = response.headers.get('content-type');
    let data;
    
    try {
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        data = { message: await response.text() };
      }
    } catch (error) {
      console.error('❌ 解析响应内容失败:', error);
      data = { message: '响应格式错误' };
    }

    // 处理成功响应
    if (response.ok) {
      return data;
    }

    // 处理错误响应
    const error = new Error(data.message || `HTTP ${response.status}: ${response.statusText}`);
    error.status = response.status;
    error.code = data.error?.code || 'HTTP_ERROR';
    error.details = data.error?.details;

    // 处理认证相关错误
    if (response.status === 401) {
      console.log('🔐 收到401响应，处理认证错误...');
      await this.handleAuthError(error, url, config, authStore);
    }

    throw error;
  }

  /**
   * 处理认证错误
   * @param {Error} error - 错误对象
   * @param {string} url - 请求URL
   * @param {Object} config - 请求配置
   * @param {Object} authStore - 认证store
   */
  async handleAuthError(error, url, config, authStore) {
    // 如果是登录接口的401错误，直接抛出
    if (url.includes('/auth/login')) {
      error.code = 'INVALID_CREDENTIALS';
      return;
    }

    // 如果是token刷新接口的401错误，清除认证信息
    if (url.includes('/auth/refresh')) {
      console.log('❌ 刷新token失败，清除认证信息');
      await authStore.clearAuthData();
      this.redirectToLogin();
      return;
    }

    // 尝试刷新token
    if (authStore.refreshToken && !this.isRefreshing) {
      try {
        console.log('🔄 尝试刷新token...');
        await this.handleTokenRefresh(authStore);
        
        // token刷新成功，可以重试原请求
        console.log('✅ Token刷新成功，可以重试请求');
        error.code = 'TOKEN_REFRESHED';
        error.canRetry = true;
        
      } catch (refreshError) {
        console.error('❌ Token刷新失败:', refreshError);
        await authStore.clearAuthData();
        this.redirectToLogin();
      }
    } else {
      // 没有刷新token或正在刷新中，直接跳转登录
      console.log('❌ 无法刷新token，跳转到登录页');
      await authStore.clearAuthData();
      this.redirectToLogin();
    }
  }

  /**
   * 处理token刷新
   * @param {Object} authStore - 认证store
   * @returns {Promise<void>}
   */
  async handleTokenRefresh(authStore) {
    // 如果已经在刷新中，等待刷新完成
    if (this.isRefreshing && this.refreshPromise) {
      console.log('⏳ 等待token刷新完成...');
      return await this.refreshPromise;
    }

    // 开始刷新token
    this.isRefreshing = true;
    this.refreshPromise = authStore.refreshAuthToken();

    try {
      await this.refreshPromise;
      console.log('✅ Token刷新完成');
    } finally {
      this.isRefreshing = false;
      this.refreshPromise = null;
    }
  }

  /**
   * 跳转到登录页
   */
  redirectToLogin() {
    if (typeof window !== 'undefined') {
      const currentPath = window.location.pathname;
      const loginUrl = `/login?redirect=${encodeURIComponent(currentPath)}`;
      
      console.log(`🔄 跳转到登录页: ${loginUrl}`);
      
      // 使用replace避免在历史记录中留下记录
      window.location.replace(loginUrl);
    }
  }

  /**
   * 生成请求ID用于去重
   * @param {string} url - 请求URL
   * @param {Object} options - 请求选项
   * @returns {string} 请求ID
   */
  generateRequestId(url, options) {
    const method = options.method || 'GET';
    const body = options.body || '';
    return `${method}:${url}:${this.hashCode(body)}`;
  }

  /**
   * 生成唯一ID
   * @returns {string} 唯一ID
   */
  generateUniqueId() {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 计算字符串哈希值
   * @param {string} str - 输入字符串
   * @returns {number} 哈希值
   */
  hashCode(str) {
    let hash = 0;
    if (str.length === 0) return hash;
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    
    return hash;
  }

  /**
   * GET请求
   * @param {string} url - 请求URL
   * @param {Object} options - 请求选项
   * @returns {Promise<Object>} 响应结果
   */
  get(url, options = {}) {
    return this.request(url, { ...options, method: 'GET' });
  }

  /**
   * POST请求
   * @param {string} url - 请求URL
   * @param {Object} data - 请求数据
   * @param {Object} options - 请求选项
   * @returns {Promise<Object>} 响应结果
   */
  post(url, data, options = {}) {
    return this.request(url, {
      ...options,
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  /**
   * PUT请求
   * @param {string} url - 请求URL
   * @param {Object} data - 请求数据
   * @param {Object} options - 请求选项
   * @returns {Promise<Object>} 响应结果
   */
  put(url, data, options = {}) {
    return this.request(url, {
      ...options,
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }

  /**
   * DELETE请求
   * @param {string} url - 请求URL
   * @param {Object} options - 请求选项
   * @returns {Promise<Object>} 响应结果
   */
  delete(url, options = {}) {
    return this.request(url, { ...options, method: 'DELETE' });
  }
}

// 创建全局HTTP实例
const http = new HttpInterceptor();

// 导出HTTP实例
export default http;

// 也可以导出类，供其他地方创建新实例
export { HttpInterceptor };
