# OCR功能完善总结

## 🎯 完善目标

根据提供的API文档示例，完善前端OCR功能，确保请求格式完全符合后端要求：

### 📋 API要求
- **请求头**: `Authorization: Bearer {token}`
- **请求体**: `multipart/form-data` 格式
  - `session_id`: 文本字段，会话ID
  - `image`: 文件字段，身份证图片

## ✅ 已完成的改进

### 1. 更新identityApi.js中的OCR方法

**文件**: `vue/src/services/identityApi.js`

**主要改进**:
- 直接使用fetch API而不是通用的apiRequest函数
- 确保Authorization头格式正确：`Bearer {token}`
- 使用正确的FormData字段名：`session_id` 和 `image`
- 增强错误处理和日志记录
- 统一返回格式

**关键代码**:
```javascript
ocrIdentityCardFile: async (file, sessionId) => {
  const formData = new FormData();
  formData.append('image', file);
  formData.append('session_id', sessionId);

  const token = getAuthToken();
  
  const response = await fetch(`${API_BASE_URL}/auth/ocr`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: formData
  });
  
  // 处理响应...
}
```

### 2. 创建增强版OCR测试工具

**文件**: `vue/ocr-test-enhanced.html`

**功能特点**:
- 完整的Token管理（自动获取 + 手动设置）
- 详细的请求预览
- 实时的文件信息显示
- 完整的错误处理和调试信息
- 符合API文档的请求格式

### 3. 创建简化版OCR测试工具

**文件**: `vue/simple-ocr-test.html`

**功能特点**:
- 简洁的界面设计
- 快速测试OCR功能
- 自动加载存储的Token
- 清晰的请求/响应显示

## 🔧 请求格式验证

### 请求头
```
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

### 请求体 (multipart/form-data)
```
session_id: qweqweqweqw
image: [文件对象]
```

### 完整请求示例
```javascript
const formData = new FormData();
formData.append('session_id', 'qweqweqweqw');
formData.append('image', fileObject);

fetch('http://localhost:8080/api/auth/ocr', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...'
  },
  body: formData
});
```

## 🧪 测试工具使用指南

### 1. 增强版测试工具 (ocr-test-enhanced.html)

**适用场景**: 完整的功能测试和调试

**使用步骤**:
1. 打开页面，自动检查当前Token状态
2. 可选：手动设置自定义Token
3. 输入Session ID (默认: test-session-123)
4. 选择身份证图片文件
5. 点击"开始OCR识别"
6. 查看详细的请求信息和响应结果

### 2. 简化版测试工具 (simple-ocr-test.html)

**适用场景**: 快速验证OCR功能

**使用步骤**:
1. 页面自动加载存储的Token
2. 输入Session ID
3. 选择图片文件
4. 点击"测试OCR"
5. 查看结果

## 📊 Token获取优先级

测试工具按以下优先级获取Token:

1. **手动设置的Token** (最高优先级)
2. **localStorage中的auth_data.token**
3. **sessionStorage中的auth_data.token**
4. **localStorage中的auth_token**
5. **localStorage中的userToken**

## 🔍 调试信息

### 浏览器控制台日志
- 请求详情 (URL, 方法, 参数)
- Token状态和预览
- 响应状态和内容
- 错误详情

### 网络面板检查
- 请求URL: `http://localhost:8080/api/auth/ocr`
- 请求方法: `POST`
- Content-Type: `multipart/form-data`
- Authorization头: `Bearer {token}`

## ⚠️ 常见问题解决

### 1. Token相关问题
- **问题**: 401 Unauthorized
- **解决**: 检查Token是否有效，确保格式为`Bearer {token}`

### 2. 文件上传问题
- **问题**: 400 Bad Request
- **解决**: 确保使用正确的字段名`session_id`和`image`

### 3. CORS问题
- **问题**: 跨域请求被阻止
- **解决**: 检查后端CORS配置

### 4. 网络连接问题
- **问题**: 无法连接到服务器
- **解决**: 确认后端服务运行在`http://localhost:8080`

## 📝 测试建议

### 测试用例
1. **正常流程**: 有效Token + 正确Session ID + 清晰身份证图片
2. **Token错误**: 无效或过期Token
3. **参数错误**: 缺少Session ID或图片
4. **文件格式**: 不同格式的图片文件
5. **网络异常**: 后端服务停止时的错误处理

### 验证要点
- [ ] 请求头包含正确的Authorization
- [ ] FormData包含session_id和image字段
- [ ] 响应正确解析和显示
- [ ] 错误情况得到妥善处理
- [ ] 日志信息完整清晰

## 🚀 部署建议

1. **开发环境**: 使用测试工具验证功能
2. **集成测试**: 在实名认证页面中测试OCR功能
3. **生产部署**: 确保Token管理和错误处理完善

## 📈 后续优化

1. **用户体验**: 添加上传进度显示
2. **错误处理**: 更友好的错误提示
3. **性能优化**: 图片压缩和格式转换
4. **安全增强**: Token加密存储
