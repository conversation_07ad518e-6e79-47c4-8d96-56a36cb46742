# 注意事项

整合了druid连接池后，要启动数据库，才能启动程序，否则启动程序后无法使用任何接口

idea热部署，要在debug模式才可以，run模式不行 
默认build快捷键是 ctrl+F9

jwt用过滤器实现，在Servlet层实现，时机更早
过滤器能直接返回响应
过滤器能完整控制请求，响应的预处理和后处理，清理ThreadLocal资源方便安全
用了一堆方法，最后使用朴素的Servlet的过滤器+spring的bean管理可以了
hutool的jwt校验有点乱，JwtUtils等待重构

数据库默认是UTC时间，比北京时间晚了8小时，所以你插入数据，发现数据显示的时间比你电脑早八小时是正常的）

百度身份证OCR，我将apikey和secretkey存放在电脑环境变量
然后需要去测试类发送一次post请求获取一个有效期为30天的accesstoken，然后可以调用ocr接口
![img.png](src/main/resources/pic/img.png)


image方式适合后端预处理，用户直接上传
url适合已经存在oss，图床什么的
![img2.png](src/main/resources/pic/img2.png)

阿里云oss，有四个变量，两个在配置文件，两个api和apikey电脑本地环境变量


高德地图使用GCJ-02坐标系
