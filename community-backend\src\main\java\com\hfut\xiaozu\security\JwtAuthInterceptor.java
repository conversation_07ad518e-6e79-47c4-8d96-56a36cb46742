package com.hfut.xiaozu.security;

import cn.hutool.core.util.StrUtil;
import com.hfut.xiaozu.common.constant.HttpStatusConstant;
import com.hfut.xiaozu.user.context.CurrentUser;
import com.hfut.xiaozu.user.context.UserContextHolder;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;


/**
 * <AUTHOR>
 * @date 2025-06-23
 */
@Slf4j
@Component
public class JwtAuthInterceptor implements HandlerInterceptor {

    @Resource
    private JwtUtils jwtUtils;

    @Resource
    private JwtConfigProperties jwtConfigProperties;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        String requestUri = request.getRequestURI();
        log.error("进入JWT拦截器，请求路径: {} {}", request.getMethod(), requestUri);

        // 放行OPTIONS预检请求
        if ("OPTIONS".equalsIgnoreCase(request.getMethod())) {
            return true;
        }

        String token = request.getHeader(jwtConfigProperties.getHeader());

        if(StrUtil.isBlank(token)){
            log.error("请求 {} {} 缺少Authorization头部", request.getMethod(), requestUri);

            responseError(response,"缺少有效的jwt请求头", HttpStatusConstant.UNAUTHORIZED);
            return false;
        }

        //RFC 6750标准，JWT应通过Authorization: Bearer <token>格式传递（Bearer后有一个空格）
        if(token.startsWith("Bearer ")){
            token=token.substring(7);
        }

        CurrentUser currentUser = jwtUtils.parseToken(token);
        if(currentUser==null){
            log.error("请求 {} {} token无效", request.getMethod(), requestUri);

            responseError(response,"令牌无效或者已过期，请重新登录", HttpStatusConstant.UNAUTHORIZED);
            return false;
        }

        UserContextHolder.setCurrentUser(currentUser);

        return true;
    }

    @Override
    public  void afterCompletion(HttpServletRequest request, HttpServletResponse response,
                                 Object handler, @Nullable Exception ex) throws Exception {
        UserContextHolder.clear();
    }

    private void responseError(HttpServletResponse response, String message, int code) throws IOException {

        response.setStatus(code);
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        String jsonResponse = String.format("{\"code\":%d,\"msg\":\"%s\",\"data\":null}", code, message);
        response.getWriter().write(jsonResponse);

        response.getWriter().flush();
    }

}
