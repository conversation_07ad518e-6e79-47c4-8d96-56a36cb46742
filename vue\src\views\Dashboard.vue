<template>
  <div class="dashboard-container">
    <h1>社区治理端</h1>
    <div class="grid-container">
      <div class="grid-item" @click="navigateTo('home-overview')">
        <h3>首页 (数据总览)</h3>
        <p>总体情况展示, GIS地图展示</p>
      </div>
      <div class="grid-item" @click="navigateTo('full-archive')">
        <h3>全息档案</h3>
        <p>档案信息管理与分类, 档案查询与展示, 重点监控人群, 档案导出</p>
      </div>
      <div class="grid-item" @click="navigateTo('event-management')">
        <h3>事件管理</h3>
        <p>多源事件整合, 事件查询与状态跟踪, 超时预警</p>
      </div>
      <div class="grid-item" @click="navigateTo('patrol-management')">
        <h3>巡查管理</h3>
        <p>巡查任务编排, 巡查任务发布, 巡查任务执行情况</p>
      </div>
      <div class="grid-item" @click="navigateTo('gis-grid-management')">
        <h3>GIS网格管理</h3>
        <p>网格边界编辑, 网格信息维护与绑定, 网格合并与拆分</p>
      </div>
      <div class="grid-item" @click="navigateTo('info-collection')">
        <h3>信息采集</h3>
        <p>信息查询与浏览, 信息新增与维护, 纠错审核流</p>
      </div>
    </div>

    <h1>居民端</h1>
    <div class="grid-container">
       <div class="grid-item" @click="navigateTo('real-name-auth')">
        <h3>实名认证</h3>
        <p>表单填写, OCR识别, 认证状态管理, 真实性校验</p>
      </div>
      <div class="grid-item" @click="navigateTo('house-management')">
        <h3>房屋管理</h3>
        <p>房屋绑定, 二维码绑定, 档案信息同步, 绑定审核</p>
      </div>
      <div class="grid-item" @click="navigateTo('vehicle-management')">
        <h3>车辆管理</h3>
        <p>车辆车位绑定, 车辆房屋绑定, 车辆信息管理, 车辆验证</p>
      </div>
      <div class="grid-item" @click="navigateTo('family-management')">
        <h3>家人管理</h3>
        <p>家人信息录入, 家人绑定业主</p>
      </div>
      <div class="grid-item" @click="navigateTo('issue-report')">
        <h3>问题上报</h3>
        <p>问题反馈, 事件分类, 事件流转与处理跟踪, 进度通知</p>
      </div>
      <div class="grid-item" @click="navigateTo('budget-vote')">
        <h3>预算支出投票</h3>
        <p>投票管理, 投票结果展示, 投票鉴权与投票</p>
      </div>
    </div>

    <h1>公共模块</h1>
    <div class="grid-container">
      <!-- 已删除四个无用模块：用户与权限服务、文件存储服务、消息通知服务、日志与监控 -->
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';

const router = useRouter();

const navigateTo = (routeName) => {
  console.log(`🔗 导航到: ${routeName}`);
  // 对于居民端功能，需要添加 /resident 前缀
  if (['real-name-auth', 'house-management', 'vehicle-management', 'family-management', 'issue-report', 'budget-vote'].includes(routeName)) {
    router.push(`/resident/${routeName}`);
  } else {
    // 其他路由保持原样或根据需要调整
    router.push(`/${routeName}`);
  }
};
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
}

.dashboard-container h1 {
  margin-top: 30px;
  margin-bottom: 15px;
  border-bottom: 2px solid #eee;
  padding-bottom: 10px;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

.grid-item {
  border: 1px solid #ddd;
  padding: 15px;
  border-radius: 8px;
  background-color: #f9f9f9;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.grid-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.grid-item h3 {
  margin-top: 0;
  color: #333;
}

.grid-item p {
  font-size: 0.9em;
  color: #666;
  line-height: 1.4;
}
</style>