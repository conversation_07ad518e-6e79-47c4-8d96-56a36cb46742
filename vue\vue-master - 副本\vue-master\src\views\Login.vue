<template>
  <div class="login-page">
    <div class="login-background">
      <div class="background-pattern"></div>
    </div>

    <div class="login-container">
      <div class="login-card">
        <div class="login-header">
          <div class="system-logo">🏘️</div>
          <h1 class="system-title">芙蓉社区管理系统</h1>
          <p class="system-subtitle">Community Management System</p>
        </div>

        <div class="login-form-container">
          <h2 class="form-title">用户登录</h2>

          <!-- 错误和成功消息显示 -->
          <div v-if="errorMessage" class="message error-message">
            ❌ {{ errorMessage }}
          </div>
          <div v-if="successMessage" class="message success-message">
            ✅ {{ successMessage }}
          </div>

          <form @submit.prevent="handleLogin" class="login-form">
            <div class="form-group">
              <label for="userName" class="form-label">
                <span class="label-icon">👤</span>
                用户名/手机号
              </label>
              <input
                type="text"
                id="userName"
                v-model="userName"
                required
                class="form-input"
                placeholder="请输入用户名或手机号"
              >
            </div>

            <div class="form-group">
              <label for="password" class="form-label">
                <span class="label-icon">🔒</span>
                密码
              </label>
              <input
                type="password"
                id="password"
                v-model="password"
                required
                class="form-input"
                placeholder="请输入密码"
              >
            </div>

            <div class="form-group">
              <label for="userType" class="form-label">
                <span class="label-icon">👥</span>
                用户类型
              </label>
              <select
                id="userType"
                v-model="userType"
                required
                class="form-input form-select"
              >
                <option value="">请选择用户类型</option>
                <option value="1">居民</option>
                <option value="2">网格员</option>
                <option value="3">社区管理员</option>
              </select>
            </div>

            <button type="submit" :disabled="isLoading" class="login-btn">
              <span v-if="isLoading" class="loading-spinner"></span>
              <span class="btn-text">{{ isLoading ? '登录中...' : '立即登录' }}</span>
            </button>

            <div class="form-footer">
              <p class="register-link">
                还没有账户？
                <router-link to="/register" class="register-btn">立即注册</router-link>
              </p>
            </div>
          </form>
        </div>
      </div>

      <div class="login-info">
        <div class="info-card">
          <h3>🎯 登录说明</h3>
          <ul>
            <li>支持用户名或手机号登录</li>
            <li>登录后可享受完整功能</li>
            <li>忘记密码请联系管理员</li>
            <li>首次使用请先注册账户</li>
          </ul>
        </div>
        <div class="info-card">
          <h3>🔐 安全提示</h3>
          <ul>
            <li>请妥善保管您的账户信息</li>
            <li>不要在公共设备上保存密码</li>
            <li>发现异常请及时联系客服</li>
            <li>定期更换密码保障安全</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import { authApi } from '../services/authApi.js';
import { useAuthStore } from '../stores/auth.js';
import { useUserStore } from '../stores/userStore.js';

const router = useRouter();
const authStore = useAuthStore();
const userStore = useUserStore();

// 表单数据
const userName = ref('');
const password = ref('');
const userType = ref('');
const isLoading = ref(false);

// 错误和成功信息
const errorMessage = ref('');
const successMessage = ref('');

const handleLogin = async () => {
  // 清除之前的错误信息
  errorMessage.value = '';
  successMessage.value = '';

  // 前端验证
  if (!userName.value.trim()) {
    errorMessage.value = '请输入用户名或手机号';
    return;
  }

  if (!password.value) {
    errorMessage.value = '请输入密码';
    return;
  }

  if (password.value.length < 6) {
    errorMessage.value = '密码长度至少6位字符';
    return;
  }

  if (!userType.value) {
    errorMessage.value = '请选择用户类型';
    return;
  }

  isLoading.value = true;

  try {
    console.log('🔐 开始登录流程...');

    // 调试用户类型值
    console.log('🔍 调试用户类型:', {
      'userType.value': userType.value,
      'typeof userType.value': typeof userType.value,
      'parseInt(userType.value)': parseInt(userType.value),
      'isNaN(parseInt(userType.value))': isNaN(parseInt(userType.value))
    });

    // 构造请求数据（使用驼峰命名法）
    const loginData = {
      userName: userName.value.trim(),
      password: password.value,
      userType: parseInt(userType.value)
    };

    console.log('🚀 发送登录请求:', loginData);

    // 调用登录API
    const result = await authApi.login(loginData);

    console.log('📦 登录结果:', result);

    if (result.success) {
      successMessage.value = result.message || '登录成功！';

      // 设置认证状态
      console.log('💾 设置认证状态...');
      const expiresAt = new Date(Date.now() + 15 * 60 * 1000);
      console.log('⏰ Token过期时间:', expiresAt.toISOString());

      await authStore.setAuthData({
        user: {
          id: result.data?.id,
          userName: result.data?.userName,
          userType: result.data?.userType,
          role: result.data?.userType === 1 ? 'resident' : 'property'
        },
        token: result.data?.token,
        refreshToken: result.data?.refreshToken || '',
        expiresAt: expiresAt.toISOString(),
        rememberMe: false
      });

      // 同时更新 userStore 以兼容路由守卫
      // 优先使用API返回的userType，如果没有则使用表单中的userType
      const apiUserType = result.data?.userType;
      const formUserType = parseInt(userType.value);
      const finalUserTypeNumber = apiUserType || formUserType;
      const userTypeString = finalUserTypeNumber === 1 ? 'resident' : 'property';

      console.log('🔍 准备更新 userStore:', {
        'result.data.userType': result.data?.userType,
        'userType.value': userType.value,
        'formUserType': formUserType,
        'finalUserTypeNumber': finalUserTypeNumber,
        'userTypeString': userTypeString,
        'userData': {
          id: result.data?.id,
          userName: result.data?.userName,
          userType: finalUserTypeNumber
        }
      });

      userStore.login({
        id: result.data?.id,
        userName: result.data?.userName,
        userType: finalUserTypeNumber
      }, userTypeString);

      console.log('🔄 已同步更新 userStore:', {
        userType: userStore.userType,
        isAuthenticated: userStore.isAuthenticated,
        user: userStore.user
      });

      // 验证认证状态
      console.log('🔍 验证认证状态:', {
        isAuthenticated: authStore.isAuthenticated,
        hasToken: !!authStore.token,
        hasUser: !!authStore.user,
        isTokenExpired: authStore.isTokenExpired,
        tokenExpiry: authStore.tokenExpiry
      });

      // 等待响应式更新完成
      await nextTick();

      // 多次等待确保认证状态完全更新
      await new Promise(resolve => setTimeout(resolve, 100));
      await nextTick();

      // 再次确认认证状态
      console.log('🔍 跳转前最终认证状态:', {
        isAuthenticated: authStore.isAuthenticated,
        hasToken: !!authStore.token,
        hasUser: !!authStore.user,
        tokenExpiry: authStore.tokenExpiry,
        isTokenExpired: authStore.isTokenExpired
      });

      // 如果认证状态还是不正确，强制更新
      if (!authStore.isAuthenticated) {
        console.log('⚠️ 认证状态异常，强制更新...');
        authStore.restoreAuthState();
        await nextTick();
        console.log('🔍 强制更新后认证状态:', {
          isAuthenticated: authStore.isAuthenticated,
          hasToken: !!authStore.token,
          hasUser: !!authStore.user
        });
      }

      // 根据用户类型跳转到不同页面
      // 首先尝试从API响应中获取用户类型
      let finalUserType = result.data?.userType;

      // 如果API响应中没有用户类型，从localStorage中获取
      if (!finalUserType) {
        try {
          const userInfo = localStorage.getItem('user_info');
          if (userInfo) {
            const parsedUserInfo = JSON.parse(userInfo);
            finalUserType = parsedUserInfo.userType;
          }
        } catch (error) {
          console.error('❌ 解析localStorage用户信息失败:', error);
        }
      }

      // 如果还是没有用户类型，使用表单中选择的用户类型
      if (!finalUserType) {
        finalUserType = parseInt(userType.value);
      }

      console.log('🔍 检查用户类型:', {
        'result.data': result.data,
        'result.data.userType': result.data?.userType,
        'localStorage.userType': (() => {
          try {
            const userInfo = localStorage.getItem('user_info');
            return userInfo ? JSON.parse(userInfo).userType : null;
          } catch { return null; }
        })(),
        'userType.value': userType.value,
        'finalUserType': finalUserType
      });

      // 确定目标路径
      let targetPath;
      if (finalUserType === 1) {
        targetPath = '/resident-home';
        console.log('🏠 准备跳转到居民页面');
      } else if (finalUserType === 2 || finalUserType === 3) {
        targetPath = '/property-home';
        console.log('🏢 准备跳转到物业页面');
      } else {
        targetPath = '/dashboard';
        console.log('📊 准备跳转到默认页面，用户类型:', finalUserType);
      }

      console.log('🚀 准备跳转到:', targetPath);
      console.log('🔍 当前路径:', window.location.pathname);

      // 等待一下，确保所有状态都已更新
      await nextTick();

      // 检查localStorage中的数据
      console.log('🔍 localStorage数据检查:', {
        'userToken': localStorage.getItem('userToken'),
        'userType': localStorage.getItem('userType'),
        'userData': localStorage.getItem('userData')
      });

      // 再次确认状态
      console.log('🔍 跳转前最终状态确认:', {
        'authStore.isAuthenticated': authStore.isAuthenticated,
        'userStore.isAuthenticated': userStore.isAuthenticated,
        'userStore.userType': userStore.userType,
        'targetPath': targetPath
      });

      try {
        // 使用Vue Router replace进行跳转，避免历史记录问题
        console.log('🧭 使用Vue Router replace跳转...');
        await router.replace(targetPath);
        console.log('✅ Vue Router replace跳转成功');
      } catch (error) {
        console.error('❌ Vue Router replace跳转失败:', error);
        // 如果Vue Router失败，使用window.location
        console.log('🔄 使用window.location备用跳转...');
        window.location.replace(targetPath);
      }
    } else {
      errorMessage.value = result.message || '登录失败，请重试';
    }
  } catch (error) {
    console.error('❌ 登录失败:', error);
    errorMessage.value = error.message || '登录失败，请检查网络连接';
  } finally {
    isLoading.value = false;
  }
};
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.background-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 2px, transparent 2px);
  background-size: 50px 50px;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(1deg); }
}

.login-container {
  position: relative;
  z-index: 2;
  display: flex;
  gap: 40px;
  align-items: flex-start;
  max-width: 1200px;
  width: 100%;
  padding: 40px 20px;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  border: 1px solid rgba(255,255,255,0.2);
  min-width: 450px;
  max-width: 500px;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.system-logo {
  font-size: 64px;
  margin-bottom: 20px;
  display: block;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.system-title {
  font-size: 32px;
  font-weight: 700;
  color: var(--text-dark);
  margin: 0 0 12px 0;
  background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.system-subtitle {
  font-size: 16px;
  color: var(--text-light);
  margin: 0;
  font-weight: 500;
  letter-spacing: 1px;
}

.login-form-container {
  position: relative;
}

.form-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-dark);
  margin: 0 0 35px 0;
  text-align: center;
  position: relative;
}

.form-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
  border-radius: 2px;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.message {
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
}

.error-message {
  background-color: #fee;
  color: #c53030;
  border: 1px solid #feb2b2;
}

.success-message {
  background-color: #f0fff4;
  color: #38a169;
  border: 1px solid #9ae6b4;
}

.form-group {
  position: relative;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
}

.label-icon {
  font-size: 16px;
}

.form-input {
  width: 100%;
  padding: 18px 22px;
  border: 2px solid var(--gray);
  border-radius: 12px;
  font-size: 18px;
  transition: all 0.3s ease;
  background: rgba(255,255,255,0.9);
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
  background: white;
}

.form-input::placeholder {
  color: #adb5bd;
}

.form-select {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 16px center;
  background-size: 16px;
  padding-right: 50px;
}

.form-select:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
  background: white;
}

.login-btn {
  width: 100%;
  padding: 20px;
  background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-top: 12px;
}

.login-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(210, 105, 30, 0.3);
}

.login-btn:active {
  transform: translateY(0);
}

.login-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255,255,255,0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.btn-text {
  font-weight: 600;
}

.form-footer {
  text-align: center;
  margin-top: 20px;
}

.register-link {
  color: var(--text-light);
  font-size: 16px;
  margin: 0;
}

.register-btn {
  color: var(--accent-green);
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.register-btn:hover {
  color: var(--accent-green-light);
  text-decoration: underline;
}

.login-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-width: 350px;
}

.info-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  border: 1px solid rgba(255,255,255,0.2);
  transition: transform 0.3s ease;
}

.info-card:hover {
  transform: translateY(-5px);
}

.info-card h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 15px 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.info-card p {
  font-size: 14px;
  color: #6c757d;
  margin: 0;
  line-height: 1.6;
}

.info-card ul {
  font-size: 14px;
  color: #6c757d;
  margin: 0;
  padding-left: 20px;
  line-height: 1.6;
}

.info-card li {
  margin-bottom: 8px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .login-container {
    flex-direction: column;
    align-items: center;
    gap: 30px;
  }

  .login-info {
    flex-direction: row;
    max-width: 100%;
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .login-container {
    padding: 20px 15px;
  }

  .login-card {
    min-width: auto;
    width: 100%;
    max-width: 400px;
    padding: 30px 25px;
  }

  .system-title {
    font-size: 24px;
  }

  .system-logo {
    font-size: 48px;
  }

  .login-info {
    flex-direction: column;
  }

  .info-card {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .login-card {
    padding: 25px 20px;
  }

  .form-input {
    padding: 14px 16px;
    font-size: 14px;
  }

  .login-btn {
    padding: 16px;
    font-size: 14px;
  }
}
</style>