<template>
  <div class="gis-management">
    <div class="page-header">
      <h1>GIS网格管理系统</h1>
      <p class="subtitle">地理信息系统 - 网格管理与空间数据可视化</p>
    </div>

    <div class="gis-content">
      <!-- 功能导航区域 -->
      <div class="function-nav">
        <div class="nav-tabs">
          <button 
            v-for="tab in tabs" 
            :key="tab.key"
            :class="['tab-btn', { active: activeTab === tab.key }]"
            @click="activeTab = tab.key"
          >
            <span class="tab-icon">{{ tab.icon }}</span>
            {{ tab.name }}
          </button>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 地图概览 -->
        <div v-if="activeTab === 'overview'" class="content-panel">
          <div class="panel-header">
            <h2>地图概览</h2>
            <div class="panel-actions">
              <button class="action-btn" @click="refreshMap">
                <span class="btn-icon">🔄</span>
                刷新地图
              </button>
              <button class="action-btn" @click="toggleFullscreen">
                <span class="btn-icon">⛶</span>
                全屏显示
              </button>
            </div>
          </div>
          <div class="map-container">
            <MapComponent
              ref="mapComponent"
              map-id="gis-overview-map"
              :height="'600px'"
              :center="mapCenter"
              :zoom="15"
              :markers="overviewMarkers"
              :polygons="overviewPolygons"
              :show-controls="true"
              :show-drawing-tools="false"
              @map-ready="onMapReady"
              @marker-click="onMarkerClick"
              @polygon-click="onPolygonClick"
              @layer-toggle="onLayerToggle"
            />
          </div>
        </div>

        <!-- 网格管理 -->
        <div v-if="activeTab === 'grid'" class="content-panel">
          <div class="panel-header">
            <h2>网格管理</h2>
            <div class="panel-actions">
              <button
                :class="['action-btn', 'primary', { active: isDrawingMode }]"
                @click="toggleDrawingMode"
              >
                <span class="btn-icon">{{ isDrawingMode ? '✏️' : '➕' }}</span>
                {{ isDrawingMode ? '退出绘制' : '新建网格' }}
              </button>
              <button class="action-btn" @click="importGrid">
                <span class="btn-icon">📥</span>
                导入网格
              </button>
              <button class="action-btn" @click="exportGrids">
                <span class="btn-icon">📤</span>
                导出网格
              </button>
            </div>
          </div>

          <div class="grid-management-layout">
            <!-- 左侧：网格列表 -->
            <div class="grid-list-section">
              <div class="section-header">
                <h3>网格列表</h3>
                <div class="list-controls">
                  <input
                    type="text"
                    v-model="gridSearchQuery"
                    placeholder="搜索网格..."
                    class="search-input"
                  >
                  <select v-model="gridStatusFilter" class="status-filter">
                    <option value="">全部状态</option>
                    <option value="active">活跃</option>
                    <option value="inactive">非活跃</option>
                  </select>
                </div>
              </div>

              <div class="grid-list">
                <!-- 加载状态 -->
                <div v-if="isLoadingGrids" class="loading-message">
                  <div class="loading-spinner"></div>
                  <p>正在加载网格数据...</p>
                </div>

                <!-- 空状态 -->
                <div v-else-if="filteredGrids.length === 0" class="empty-message">
                  <p>暂无网格数据</p>
                  <button class="btn primary" @click="toggleDrawingMode">创建第一个网格</button>
                </div>

                <!-- 网格列表 -->
                <div
                  v-else
                  v-for="grid in filteredGrids"
                  :key="grid.id"
                  :class="['grid-item', { selected: selectedGrid?.id === grid.id }]"
                  @click="selectGrid(grid)"
                >
                  <div class="grid-info">
                    <h4>{{ grid.name }}</h4>
                    <p>面积: {{ grid.area }}m²</p>
                    <p>负责人: {{ grid.manager }}</p>
                    <p>状态: <span :class="['status', grid.status]">{{ getStatusText(grid.status) }}</span></p>
                  </div>
                  <div class="grid-actions">
                    <button class="btn-small" @click.stop="editGridInfo(grid)">编辑</button>
                    <button class="btn-small secondary" @click.stop="viewGridOnMap(grid)">定位</button>
                    <button class="btn-small danger" @click.stop="deleteGrid(grid)">删除</button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 右侧：地图区域 -->
            <div class="map-section">
              <div class="map-header">
                <h3>网格地图</h3>
                <div class="map-tools">
                  <!-- 自动绘制提示 -->
                  <div v-if="showAutoDrawTip" class="auto-draw-tip">
                    <span class="tip-icon">🎯</span>
                    <div class="tip-content">
                      <div class="tip-title">已自动激活绘制模式</div>
                      <div class="tip-message">您可以开始在地图上绘制新的网格边界</div>
                      <button class="close-tip-btn" @click="showAutoDrawTip = false">×</button>
                    </div>
                  </div>

                  <div v-if="isDrawingMode" class="drawing-tips">
                    <span class="tip-icon">💡</span>
                    <div class="tip-content">
                      <div class="tip-title">绘制网格边界</div>
                      <div class="tip-steps">
                        <div>1. 在地图上点击多个点绘制多边形边界</div>
                        <div>2. 至少需要3个点才能形成有效区域</div>
                        <div>3. 点击"完成绘制"按钮完成绘制</div>
                        <div class="current-points">当前已绘制 {{ drawingPoints.length }} 个点</div>
                      </div>
                    </div>
                  </div>

                  <button
                    v-if="isDrawingMode && drawingPoints.length >= 3"
                    class="tool-btn complete-drawing-btn"
                    @click="completeDrawing"
                  >
                    <span class="btn-icon">✅</span>
                    完成绘制 ({{ drawingPoints.length }}个点)
                  </button>

                  <button class="tool-btn" @click="clearMapSelection">
                    <span class="btn-icon">🧹</span>
                    清除选择
                  </button>
                  <button class="tool-btn" @click="fitAllGrids">
                    <span class="btn-icon">🎯</span>
                    显示全部
                  </button>
                  <button class="tool-btn" @click="refreshMap">
                    <span class="btn-icon">🔄</span>
                    刷新地图
                  </button>
                  <button class="tool-btn" @click="debugGridData">
                    <span class="btn-icon">🐛</span>
                    调试网格
                  </button>
                </div>
              </div>

              <div class="map-container">
                <MapComponent
                  ref="mapComponent"
                  map-id="grid-management-map"
                  :height="'500px'"
                  :center="mapCenter"
                  :zoom="16"
                  :markers="gridMarkers"
                  :polygons="gridPolygons"
                  :show-controls="true"
                  :show-drawing-tools="false"
                  @map-ready="onMapReady"
                  @marker-click="onGridMarkerClick"
                  @polygon-click="onGridPolygonClick"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 数据统计 -->
        <div v-if="activeTab === 'statistics'" class="content-panel">
          <div class="panel-header">
            <h2>数据统计</h2>
          </div>
          <div class="statistics-content">
            <div class="stats-grid">
              <div class="stat-card">
                <div class="stat-icon">🏘️</div>
                <div class="stat-info">
                  <h3>总网格数</h3>
                  <p class="stat-number">{{ statistics.totalGrids }}</p>
                </div>
              </div>
              <div class="stat-card">
                <div class="stat-icon">📍</div>
                <div class="stat-info">
                  <h3>活跃事件</h3>
                  <p class="stat-number">{{ statistics.activeEvents }}</p>
                </div>
              </div>
              <div class="stat-card">
                <div class="stat-icon">🚶</div>
                <div class="stat-info">
                  <h3>巡查路线</h3>
                  <p class="stat-number">{{ statistics.patrolRoutes }}</p>
                </div>
              </div>
              <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-info">
                  <h3>网格员</h3>
                  <p class="stat-number">{{ statistics.gridManagers }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 工具箱 -->
        <div v-if="activeTab === 'tools'" class="content-panel">
          <div class="panel-header">
            <h2>GIS工具箱</h2>
          </div>
          <div class="tools-content">
            <div class="tool-groups">
              <div class="tool-group">
                <h3>绘制工具</h3>
                <div class="tool-buttons">
                  <button class="tool-btn" @click="activateTool('polygon')">
                    <span class="tool-icon">⬟</span>
                    绘制多边形
                  </button>
                  <button class="tool-btn" @click="activateTool('line')">
                    <span class="tool-icon">📏</span>
                    绘制线条
                  </button>
                  <button class="tool-btn" @click="activateTool('marker')">
                    <span class="tool-icon">📍</span>
                    添加标记
                  </button>
                </div>
              </div>
              <div class="tool-group">
                <h3>测量工具</h3>
                <div class="tool-buttons">
                  <button class="tool-btn" @click="activateTool('distance')">
                    <span class="tool-icon">📐</span>
                    距离测量
                  </button>
                  <button class="tool-btn" @click="activateTool('area')">
                    <span class="tool-icon">📊</span>
                    面积测量
                  </button>
                </div>
              </div>
              <div class="tool-group">
                <h3>数据工具</h3>
                <div class="tool-buttons">
                  <button class="tool-btn" @click="exportData">
                    <span class="tool-icon">📤</span>
                    导出数据
                  </button>
                  <button class="tool-btn" @click="importData">
                    <span class="tool-icon">📥</span>
                    导入数据
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 网格编辑对话框 -->
    <div v-if="showGridEditDialog" class="dialog-overlay" @click="closeGridEditDialog">
      <div class="dialog" @click.stop>
        <div class="dialog-header">
          <h3>{{ editingGrid.id ? '编辑网格' : '新建网格' }}</h3>
          <button class="close-btn" @click="closeGridEditDialog">×</button>
        </div>
        <div class="dialog-content">
          <form @submit.prevent="saveGrid">
            <div class="form-grid">
              <div class="form-group">
                <label>网格名称 <span class="required">*</span></label>
                <input
                  type="text"
                  v-model="editingGrid.name"
                  placeholder="请输入网格名称"
                  required
                >
              </div>
              <div class="form-group">
                <label>所属社区 <span class="required">*</span></label>
                <select v-model="editingGrid.communityId" required>
                  <option value="">请选择社区</option>
                  <option
                    v-for="community in communities"
                    :key="community.communityId"
                    :value="community.communityId"
                  >
                    {{ community.communityName }}
                  </option>
                </select>
              </div>
              <div class="form-group">
                <label>负责人 <span class="required">*</span></label>
                <input
                  type="text"
                  v-model="editingGrid.manager"
                  placeholder="请输入负责人姓名"
                  required
                >
              </div>
              <div class="form-group">
                <label>状态</label>
                <select v-model="editingGrid.status">
                  <option value="active">活跃</option>
                  <option value="inactive">非活跃</option>
                </select>
              </div>
              <div class="form-group">
                <label>面积</label>
                <input
                  type="number"
                  v-model="editingGrid.area"
                  placeholder="平方米"
                  readonly
                >
                <small class="form-hint">面积由系统根据绘制的多边形自动计算</small>
              </div>
            </div>
            <div class="form-actions">
              <button type="button" class="btn secondary" @click="closeGridEditDialog">取消</button>
              <button type="submit" class="btn primary">保存</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import MapComponent from '../components/MapComponent.vue';
import { createGrid, getCommunities, getAllGrids } from '../services/gridApi.js';

const router = useRouter();

// 响应式数据
const activeTab = ref('overview');
const mapComponent = ref(null);
const layers = reactive({
  grid: true,
  buildings: true,
  events: true,
  patrol: false
});

// 网格管理相关状态
const isDrawingMode = ref(false);
const drawingPoints = ref([]);
const selectedGrid = ref(null);
const gridSearchQuery = ref('');
const gridStatusFilter = ref('');
const showGridEditDialog = ref(false);
const showAutoDrawTip = ref(false);
const editingGrid = reactive({
  id: null,
  name: '',
  manager: '',
  status: 'active',
  coordinates: [],
  area: 0,
  communityId: '',
  responsibleId: 0
});

// 社区数据
const communities = ref([]);
const loadingCommunities = ref(false);

// 地图配置
const mapCenter = [117.198612, 31.774164]; // 地图中心点坐标 [经度, 纬度]

// 地图数据
const overviewMarkers = ref([
  {
    lng: 117.198612,
    lat: 31.774164,
    title: '芙蓉社区A区',
    popup: '<div><h4>芙蓉社区A区</h4><p>负责人：张三</p><p>状态：正常</p></div>',
    properties: { type: 'grid', status: 'active' }
  },
  {
    lng: 117.199612,
    lat: 31.775164,
    title: '芙蓉社区B区',
    popup: '<div><h4>芙蓉社区B区</h4><p>负责人：李四</p><p>状态：正常</p></div>',
    properties: { type: 'grid', status: 'active' }
  }
]);

const overviewPolygons = ref([
  {
    title: '芙蓉社区A区边界',
    coordinates: [[117.197612, 31.773664], [117.197612, 31.774664], [117.199612, 31.774664], [117.199612, 31.773664]],
    color: '#3388ff',
    fillColor: '#3388ff',
    fillOpacity: 0.2,
    popup: '<div><h4>芙蓉社区A区</h4><p>面积：15000m²</p></div>',
    properties: { name: '芙蓉社区A区', area: 15000 }
  }
]);

// 标签页配置
const tabs = [
  { key: 'overview', name: '地图概览', icon: '🗺️' },
  { key: 'grid', name: '网格管理', icon: '🏘️' },
  { key: 'statistics', name: '数据统计', icon: '📊' },
  { key: 'tools', name: 'GIS工具', icon: '🛠️' }
];

/**
 * 转换后端网格数据为前端格式
 * @param {Object} backendGrid - 后端网格数据
 * @returns {Object} 前端网格数据格式
 */
const transformBackendGridData = (backendGrid) => {
  try {
    // 解析boundaryGeojson字符串
    const geoJson = JSON.parse(backendGrid.boundaryGeojson);
    const coordinates = geoJson.coordinates[0]; // 获取外环坐标

    // 计算多边形中心点
    const center = calculatePolygonCenter(coordinates);

    // 计算多边形面积（简单估算）
    const area = calculatePolygonArea(coordinates);

    return {
      id: backendGrid.id,
      name: backendGrid.gridName,
      area: Math.round(area),
      manager: '待分配', // 后端暂时没有负责人信息，使用默认值
      status: 'active', // 后端暂时没有状态信息，使用默认值
      coordinates: coordinates,
      communityId: backendGrid.communityId,
      responsibleId: backendGrid.responsibleId,
      centerLat: center.lat,
      centerLng: center.lng,
      createdAt: new Date(backendGrid.createTime),
      updatedAt: new Date(backendGrid.updateTime)
    };
  } catch (error) {
    console.error('转换网格数据失败:', error, backendGrid);
    return null;
  }
};

/**
 * 从后端加载网格数据
 */
const loadGridsFromBackend = async () => {
  try {
    console.log('🔧 开始从后端加载网格数据...');

    const response = await getAllGrids();

    if (response.success && response.data) {
      console.log('✅ 后端网格数据获取成功:', response.data.length, '个网格');

      // 转换数据格式
      const transformedGrids = response.data
        .map(transformBackendGridData)
        .filter(grid => grid !== null); // 过滤掉转换失败的数据

      console.log('✅ 网格数据转换完成:', transformedGrids.length, '个有效网格');
      return transformedGrids;
    } else {
      console.warn('⚠️ 后端返回数据为空，使用空数组');
      return [];
    }
  } catch (error) {
    console.error('❌ 从后端加载网格数据失败:', error);

    // 如果后端加载失败，返回空数组而不是默认数据
    console.log('🔄 后端加载失败，返回空数组');
    return [];
  }
};

// 网格数据
const grids = ref([]);
const isLoadingGrids = ref(false);

// 统计数据
const statistics = reactive({
  totalGrids: 12,
  activeEvents: 8,
  patrolRoutes: 5,
  gridManagers: 15
});

// 计算属性
const filteredGrids = computed(() => {
  let filtered = grids.value;

  if (gridSearchQuery.value) {
    filtered = filtered.filter(grid =>
      grid.name.toLowerCase().includes(gridSearchQuery.value.toLowerCase()) ||
      grid.manager.toLowerCase().includes(gridSearchQuery.value.toLowerCase())
    );
  }

  if (gridStatusFilter.value) {
    filtered = filtered.filter(grid => grid.status === gridStatusFilter.value);
  }

  return filtered;
});

const gridMarkers = computed(() => {
  return grids.value.map(grid => ({
    lat: grid.centerLat,
    lng: grid.centerLng,
    title: grid.name,
    popup: `<div><h4>${grid.name}</h4><p>负责人：${grid.manager}</p><p>面积：${grid.area}m²</p><p>状态：${getStatusText(grid.status)}</p></div>`,
    properties: {
      gridId: grid.id,
      type: 'grid',
      status: grid.status
    }
  }));
});

// 根据社区ID获取颜色
const getCommunityColor = (communityId) => {
  const colors = [
    '#28a745', // 绿色
    '#007bff', // 蓝色
    '#ffc107', // 黄色
    '#dc3545', // 红色
    '#6f42c1', // 紫色
    '#fd7e14', // 橙色
    '#20c997', // 青色
    '#e83e8c'  // 粉色
  ];

  if (!communityId) return '#6c757d'; // 默认灰色

  // 根据社区ID计算颜色索引
  const index = (communityId - 1) % colors.length;
  return colors[index];
};

const gridPolygons = computed(() => {
  const polygons = grids.value.map(grid => {
    const color = grid.status === 'active' ? getCommunityColor(grid.communityId) : '#6c757d';
    const communityName = communities.value.find(c => c.communityId === grid.communityId)?.communityName || '未知社区';

    return {
      id: grid.id,
      title: grid.name,
      coordinates: grid.coordinates,
      color: color,
      fillColor: color,
      fillOpacity: selectedGrid.value?.id === grid.id ? 0.4 : 0.2,
      strokeWeight: selectedGrid.value?.id === grid.id ? 3 : 2,
      popup: `<div><h4>${grid.name}</h4><p>所属社区：${communityName}</p><p>面积：${grid.area}m²</p><p>负责人：${grid.manager}</p></div>`,
      properties: {
        gridId: grid.id,
        name: grid.name,
        area: grid.area,
        manager: grid.manager,
        status: grid.status,
        communityId: grid.communityId
      }
    };
  });

  console.log('计算网格多边形数据:', polygons);
  return polygons;
});

/**
 * 刷新网格数据
 */
const refreshGridData = async () => {
  isLoadingGrids.value = true;
  try {
    const gridData = await loadGridsFromBackend();
    grids.value = gridData;

    // 更新统计数据
    statistics.totalGrids = gridData.length;

    console.log('✅ 网格数据刷新完成:', gridData.length, '个网格');
  } catch (error) {
    console.error('❌ 刷新网格数据失败:', error);
  } finally {
    isLoadingGrids.value = false;
  }
};

// 地图相关方法
const refreshMap = async () => {
  // 刷新网格数据
  await refreshGridData();

  // 刷新地图显示
  if (mapComponent.value) {
    mapComponent.value.refreshData();
  }
};

const toggleFullscreen = () => {
  console.log('切换全屏');
};

// 地图事件处理
const onMapReady = (map) => {
  console.log('地图初始化完成:', map);
  // 确保地图实例可以通过mapComponent访问
  if (mapComponent.value) {
    console.log('地图组件引用已建立');
    // 测试地图实例访问
    const testMapInstance = mapComponent.value.getMap();
    console.log('测试地图实例访问:', testMapInstance);
  }
};

const onMarkerClick = (markerData) => {
  console.log('标记点击:', markerData);
};

const onPolygonClick = (polygonData) => {
  console.log('多边形点击:', polygonData);
};

// 网格地图事件处理
const onGridMarkerClick = (markerData) => {
  const gridId = markerData.properties.gridId;
  const grid = grids.value.find(g => g.id === gridId);
  if (grid) {
    selectGrid(grid);
  }
};

const onGridPolygonClick = (polygonData) => {
  const gridId = polygonData.properties.gridId;
  const grid = grids.value.find(g => g.id === gridId);
  if (grid) {
    selectGrid(grid);
  }
};

// 坐标点击事件处理
const onCoordinateClicked = (coordinateData) => {
  console.log('🎯 坐标点击事件触发:', coordinateData);
  console.log('📍 获取到的坐标信息:');
  console.log('  经度:', coordinateData.lng);
  console.log('  纬度:', coordinateData.lat);
  console.log('  点击时间:', coordinateData.clickTime);
  console.log('  时间戳:', coordinateData.timestamp);

  // 可以在这里添加更多的处理逻辑
  // 比如保存到历史记录、发送到后端、显示通知等

  // 显示简单的通知
  const message = `坐标已获取！\n经度: ${coordinateData.lng.toFixed(6)}\n纬度: ${coordinateData.lat.toFixed(6)}`;
  console.log('坐标获取通知:', message);
};

const onGridDrawn = async (feature) => {
  console.log('绘制完成:', feature);

  if (feature.type === 'polygon' && feature.coordinates && feature.coordinates.length > 0) {
    // 计算多边形面积（简单估算）
    const area = calculatePolygonArea(feature.coordinates);

    // 计算多边形中心点
    const center = calculatePolygonCenter(feature.coordinates);

    // 设置编辑网格的基本信息，准备让用户填写详细信息
    Object.assign(editingGrid, {
      id: null,
      name: `新网格_${new Date().toLocaleString('zh-CN')}`,
      manager: '待分配',
      status: 'active',
      coordinates: feature.coordinates,
      area: Math.round(area),
      communityId: '',
      responsibleId: 0
    });

    // 显示编辑对话框让用户选择社区和填写详细信息
    showGridEditDialog.value = true;
  }
};

const onLayerToggle = (layerKey, visible) => {
  console.log(`图层切换: ${layerKey}`, visible);
  if (layers[layerKey] !== undefined) {
    layers[layerKey] = visible;
  }
};

// 网格管理方法
const toggleDrawingMode = () => {
  console.log('切换绘制模式，当前状态:', isDrawingMode.value);
  isDrawingMode.value = !isDrawingMode.value;
  console.log('新的绘制模式状态:', isDrawingMode.value);

  if (isDrawingMode.value) {
    console.log('进入绘制模式...');
    // 进入绘制模式，激活多边形绘制工具
    if (mapComponent.value) {
      console.log('地图组件存在，获取地图实例...');
      const mapInstance = mapComponent.value.getMap();
      if (mapInstance) {
        console.log('地图实例获取成功，激活绘制功能...');
        activatePolygonDrawing();
      } else {
        console.error('无法获取地图实例');
        alert('地图未初始化，请稍后再试');
        isDrawingMode.value = false;
      }
    } else {
      console.error('地图组件不存在');
      alert('地图组件未加载，请刷新页面重试');
      isDrawingMode.value = false;
    }
  } else {
    console.log('退出绘制模式...');
    // 退出绘制模式时清除选择和绘制状态
    selectedGrid.value = null;
    if (mapComponent.value) {
      clearPolygonDrawing();
    }
  }
};

const selectGrid = (grid) => {
  selectedGrid.value = grid;
  // 在地图上高亮显示选中的网格
  if (mapComponent.value) {
    // 可以添加地图定位到选中网格的逻辑
    console.log('选中网格:', grid.name);
  }
};

const editGridInfo = (grid) => {
  // 复制网格信息到编辑对象
  Object.assign(editingGrid, {
    id: grid.id,
    name: grid.name,
    manager: grid.manager,
    status: grid.status,
    coordinates: [...grid.coordinates],
    area: grid.area
  });
  showGridEditDialog.value = true;
};

const viewGridOnMap = (grid) => {
  selectedGrid.value = grid;
  // 地图定位到网格中心
  if (mapComponent.value) {
    // 这里可以调用地图组件的方法来定位到网格
    console.log('定位到网格:', grid.name);
  }
};

const deleteGrid = async (grid) => {
  if (confirm(`确定要删除网格 "${grid.name}" 吗？\n删除后将无法恢复。`)) {
    try {
      // TODO: 调用后端删除API
      // await deleteGridApi(grid.id);

      // 暂时只是从本地数组中删除，等后端提供删除API后再完善
      const index = grids.value.findIndex(g => g.id === grid.id);
      if (index > -1) {
        grids.value.splice(index, 1);

        // 更新统计数据
        statistics.totalGrids = grids.value.length;

        // 如果删除的是当前选中的网格，清除选择
        if (selectedGrid.value?.id === grid.id) {
          selectedGrid.value = null;
        }

        // 刷新地图显示
        refreshMap();

        console.log('网格删除成功:', grid.name);
        alert('网格删除成功！');
      }
    } catch (error) {
      console.error('删除网格失败:', error);
      alert('删除网格失败: ' + error.message);
    }
  }
};

const clearMapSelection = () => {
  selectedGrid.value = null;
};

const fitAllGrids = () => {
  if (mapComponent.value && grids.value.length > 0) {
    // 这里可以调用地图组件的方法来适配显示所有网格
    console.log('显示所有网格');
  }
};

const debugGridData = () => {
  console.log('=== 网格数据调试信息 ===');
  console.log('网格总数:', grids.value.length);
  console.log('网格数据:', grids.value);
  console.log('网格多边形数据:', gridPolygons.value);
  console.log('地图组件引用:', mapComponent.value);

  if (mapComponent.value) {
    const mapInstance = mapComponent.value.getMap();
    console.log('地图实例:', mapInstance);

    if (mapInstance) {
      const allOverlays = mapInstance.getAllOverlays();
      console.log('地图上的所有覆盖物:', allOverlays);
      console.log('覆盖物数量:', allOverlays.length);
    }
  }

  // 强制触发地图刷新
  refreshMap();
};

const importGrid = () => {
  console.log('导入网格');
};

const exportGrids = () => {
  console.log('导出网格数据');
};

// 辅助函数
const calculatePolygonArea = (coordinates) => {
  // 简单的多边形面积计算（使用鞋带公式的近似版本）
  // 注意：这是一个简化的计算，实际应用中应该考虑地球曲率
  if (coordinates.length < 3) return 0;

  let area = 0;
  const n = coordinates.length;

  for (let i = 0; i < n; i++) {
    const j = (i + 1) % n;
    area += coordinates[i][0] * coordinates[j][1];
    area -= coordinates[j][0] * coordinates[i][1];
  }

  area = Math.abs(area) / 2;

  // 转换为平方米（粗略估算，1度约等于111km）
  const metersPerDegree = 111000;
  return area * metersPerDegree * metersPerDegree;
};

const calculatePolygonCenter = (coordinates) => {
  // 计算多边形的几何中心
  if (coordinates.length === 0) return { lat: 0, lng: 0 };

  let sumLng = 0;
  let sumLat = 0;

  coordinates.forEach(coord => {
    sumLng += coord[0]; // 经度
    sumLat += coord[1]; // 纬度
  });

  return {
    lng: sumLng / coordinates.length,
    lat: sumLat / coordinates.length
  };
};

// 计算多边形周长
const calculatePolygonPerimeter = (coordinates) => {
  if (coordinates.length < 2) return 0;

  let perimeter = 0;
  const metersPerDegree = 111000; // 粗略估算，1度约等于111km

  for (let i = 0; i < coordinates.length; i++) {
    const current = coordinates[i];
    const next = coordinates[(i + 1) % coordinates.length];

    // 计算两点间距离（简化版本）
    const deltaLng = next[0] - current[0];
    const deltaLat = next[1] - current[1];
    const distance = Math.sqrt(deltaLng * deltaLng + deltaLat * deltaLat) * metersPerDegree;

    perimeter += distance;
  }

  return Math.round(perimeter);
};

// 计算边界框
const calculateBoundingBox = (coordinates) => {
  if (coordinates.length === 0) {
    return { minLng: 0, maxLng: 0, minLat: 0, maxLat: 0 };
  }

  let minLng = coordinates[0][0];
  let maxLng = coordinates[0][0];
  let minLat = coordinates[0][1];
  let maxLat = coordinates[0][1];

  coordinates.forEach(coord => {
    minLng = Math.min(minLng, coord[0]);
    maxLng = Math.max(maxLng, coord[0]);
    minLat = Math.min(minLat, coord[1]);
    maxLat = Math.max(maxLat, coord[1]);
  });

  return {
    minLng,
    maxLng,
    minLat,
    maxLat,
    width: maxLng - minLng,
    height: maxLat - minLat
  };
};

// 加载社区列表
const loadCommunities = async () => {
  try {
    loadingCommunities.value = true;
    console.log('🔍 加载社区列表...');

    const response = await getCommunities();

    if (response.success && response.data) {
      communities.value = response.data;
      console.log('✅ 社区列表加载成功:', communities.value);
    } else {
      throw new Error(response.message || '获取社区列表失败');
    }
  } catch (error) {
    console.error('❌ 加载社区列表失败:', error);
    alert('加载社区列表失败: ' + error.message);
  } finally {
    loadingCommunities.value = false;
  }
};

// 发送网格数据到后端
const sendGridDataToBackend = async (gridData) => {
  console.log('发送网格数据到后端...', gridData);

  try {
    // 使用API服务发送数据
    const response = await createGrid(gridData);
    console.log('后端响应:', response);
    return response;
  } catch (error) {
    console.error('API调用失败:', error);
    throw error;
  }
};

// 多边形绘制功能
const activatePolygonDrawing = () => {
  console.log('🎯 开始激活多边形绘制模式...');

  const mapInstance = mapComponent.value?.getMap();
  if (!mapInstance) {
    console.error('❌ 无法获取地图实例！');
    alert('地图未初始化，请稍后再试');
    isDrawingMode.value = false;
    return;
  }

  console.log('✅ 地图实例获取成功');

  // 禁用地图拖拽和双击缩放
  mapInstance.setStatus({
    dragEnable: false,
    doubleClickZoom: false
  });
  console.log('✅ 地图拖拽已禁用');

  // 改变鼠标样式
  const mapContainer = document.getElementById('grid-management-map');
  if (mapContainer) {
    mapContainer.style.cursor = 'crosshair';
    console.log('✅ 鼠标样式已设置为十字光标');
  }

  // 初始化绘制状态
  const localDrawingPoints = [];
  let tempMarkers = [];

  // 重置全局绘制点数
  drawingPoints.value = [];

  console.log('✅ 绘制状态初始化完成');

  // 点击事件处理
  const clickHandler = (event) => {
    console.log('🎯 地图点击事件触发!');

    const { lng, lat } = event.lnglat;
    localDrawingPoints.push([lng, lat]);

    // 更新全局绘制点数
    drawingPoints.value = [...localDrawingPoints];

    console.log(`✅ 添加绘制点: ${lng.toFixed(6)}, ${lat.toFixed(6)}`);
    console.log(`📍 当前总点数: ${localDrawingPoints.length}`);

    // 创建标记
    const marker = new AMap.Marker({
      position: [lng, lat],
      title: `点 ${localDrawingPoints.length}`
    });

    console.log('✅ 标记对象创建成功');

    // 添加到地图
    mapInstance.add(marker);
    console.log('✅ 标记已添加到地图');

    // 保存引用
    tempMarkers.push(marker);
    console.log(`📝 临时标记总数: ${tempMarkers.length}`);
  };

  // 清理函数
  const cleanupDrawing = () => {
    // 清理临时标记
    tempMarkers.forEach(marker => {
      mapInstance.remove(marker);
    });

    // 移除事件监听
    mapInstance.off('click', clickHandler);

    // 恢复地图状态
    mapInstance.setStatus({
      dragEnable: true,
      doubleClickZoom: true
    });

    // 恢复鼠标样式
    const mapContainer = document.getElementById('grid-management-map');
    if (mapContainer) {
      mapContainer.style.cursor = '';
    }

    // 重置绘制模式和点数
    isDrawingMode.value = false;
    drawingPoints.value = [];
  };

  // 绑定事件
  console.log('绑定地图点击事件...');
  mapInstance.on('click', clickHandler);
  console.log('事件绑定完成');

  // 存储清理函数和本地点数据以便外部调用
  mapInstance.drawingCleanup = cleanupDrawing;
  mapInstance.localDrawingPoints = localDrawingPoints;
};

const clearPolygonDrawing = () => {
  const mapInstance = mapComponent.value?.getMap();
  if (mapInstance && mapInstance.drawingCleanup) {
    mapInstance.drawingCleanup();
  }
};

// 完成绘制方法
const completeDrawing = () => {
  console.log('🎯 按钮完成绘制');

  const mapInstance = mapComponent.value?.getMap();
  if (!mapInstance || !mapInstance.localDrawingPoints) {
    console.error('❌ 无法获取绘制数据');
    return;
  }

  const localPoints = mapInstance.localDrawingPoints;

  if (localPoints.length >= 3) {
    console.log('✅ 绘制完成，点数:', localPoints.length);

    // 触发网格创建
    onGridDrawn({
      type: 'polygon',
      coordinates: [...localPoints],
      area: calculatePolygonArea(localPoints)
    });

    // 清理绘制状态
    if (mapInstance.drawingCleanup) {
      mapInstance.drawingCleanup();
    }
  } else {
    alert('请至少点击3个点来绘制多边形');
  }
};

// 对话框处理方法
const closeGridEditDialog = () => {
  showGridEditDialog.value = false;
  // 清空编辑数据
  Object.assign(editingGrid, {
    id: null,
    name: '',
    manager: '',
    status: 'active',
    coordinates: [],
    area: 0,
    communityId: '',
    responsibleId: 0
  });
};

const saveGrid = async () => {
  if (!editingGrid.name.trim() || !editingGrid.manager.trim()) {
    alert('请填写完整的网格信息');
    return;
  }

  if (!editingGrid.communityId) {
    alert('请选择所属社区');
    return;
  }

  if (editingGrid.id) {
    // 更新现有网格
    const index = grids.value.findIndex(g => g.id === editingGrid.id);
    if (index > -1) {
      grids.value[index] = {
        ...grids.value[index],
        name: editingGrid.name,
        manager: editingGrid.manager,
        status: editingGrid.status,
        area: editingGrid.area,
        communityId: editingGrid.communityId,
        updatedAt: new Date()
      };
      selectedGrid.value = grids.value[index];

      // 保存到localStorage以保持数据同步
      localStorage.setItem('community_grids', JSON.stringify(grids.value));
      console.log('网格编辑后数据已保存到localStorage');

      // 刷新地图显示
      refreshMap();
    }
  } else {
    // 创建新网格
    await createNewGrid();
  }

  closeGridEditDialog();
};

// 创建新网格
const createNewGrid = async () => {
  try {
    console.log('🔧 开始创建新网格...');

    // 准备发送给后端的数据
    const gridData = {
      name: editingGrid.name,
      manager: editingGrid.manager,
      coordinates: editingGrid.coordinates,
      communityId: editingGrid.communityId,
      responsibleId: editingGrid.responsibleId
    };

    console.log('准备发送的网格数据:', JSON.stringify(gridData, null, 2));

    // 发送数据到后端
    const response = await sendGridDataToBackend(gridData);

    if (response.success) {
      console.log('✅ 网格创建成功:', response);

      // 创建本地网格对象
      // 注意：后端返回的data可能为null，所以使用时间戳生成ID
      const newGrid = {
        id: (response.data && response.data.id) ? response.data.id : `grid_${Date.now()}`,
        name: editingGrid.name,
        manager: editingGrid.manager,
        status: editingGrid.status,
        coordinates: editingGrid.coordinates,
        area: editingGrid.area,
        communityId: editingGrid.communityId,
        centerLat: calculatePolygonCenter(editingGrid.coordinates).lat,
        centerLng: calculatePolygonCenter(editingGrid.coordinates).lng,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // 刷新网格数据从后端重新加载
      await refreshGridData();

      // 退出绘制模式
      isDrawingMode.value = false;

      // 刷新地图显示
      refreshMap();

      // 显示成功消息
      alert(`网格创建成功！\n网格ID: ${newGrid.id}\n面积: ${editingGrid.area}平方米`);

    } else {
      throw new Error(response.message || '创建网格失败');
    }
  } catch (error) {
    console.error('❌ 创建网格失败:', error);
    alert(`创建网格失败: ${error.message}`);
  }
};

const getStatusText = (status) => {
  return status === 'active' ? '活跃' : '非活跃';
};

// 工具方法
const activateTool = (tool) => {
  console.log(`激活工具: ${tool}`);
};

const exportData = () => {
  console.log('导出数据');
};

const importData = () => {
  console.log('导入数据');
};

// 生命周期
onMounted(async () => {
  // 初始化地图
  console.log('初始化GIS管理系统');

  // 加载社区列表
  loadCommunities();

  // 加载网格数据
  await refreshGridData();

  // 检查URL参数，自动激活相应功能
  const route = router.currentRoute.value;
  if (route.query.tab === 'grid' && (route.query.action === 'draw' || route.query.action === 'create')) {
    // 自动切换到网格管理标签页
    activeTab.value = 'grid';

    // 显示自动绘制提示
    showAutoDrawTip.value = true;

    // 延迟激活绘制模式，确保地图已初始化
    setTimeout(() => {
      console.log('自动激活绘制模式...');
      if (!isDrawingMode.value) {
        toggleDrawingMode();
      }

      // 3秒后自动隐藏提示
      setTimeout(() => {
        showAutoDrawTip.value = false;
      }, 3000);
    }, 1000);
  }
});

onUnmounted(() => {
  // 清理资源
  console.log('清理GIS资源');
});
</script>

<style scoped>
.gis-management {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 30px;
  text-align: center;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 2.2em;
  font-weight: 600;
}

.subtitle {
  margin: 0;
  opacity: 0.9;
  font-size: 1.1em;
}

.gis-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.function-nav {
  background: white;
  border-bottom: 1px solid #e1e8ed;
  padding: 0 30px;
}

.nav-tabs {
  display: flex;
  gap: 0;
}

.tab-btn {
  background: none;
  border: none;
  padding: 15px 25px;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tab-btn:hover {
  color: #4a90e2;
  background: #f8f9fa;
}

.tab-btn.active {
  color: #4a90e2;
  border-bottom-color: #4a90e2;
  background: #f8f9fa;
}

.tab-icon {
  font-size: 16px;
}

.main-content {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
}

.content-panel {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #e1e8ed;
  background: #fafbfc;
}

.panel-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.5em;
}

.panel-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  background: #4a90e2;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: background 0.3s ease;
}

.action-btn:hover {
  background: #357abd;
}

.action-btn.primary {
  background: #28a745;
}

.action-btn.primary:hover {
  background: #218838;
}

.btn-icon {
  font-size: 14px;
}

/* 地图相关样式 */
.map-container {
  position: relative;
  height: 600px;
}

.map-view {
  width: 100%;
  height: 100%;
  background: #e8f4f8;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 18px;
}

.map-controls {
  position: absolute;
  top: 20px;
  right: 20px;
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  min-width: 200px;
}

.control-group label {
  display: block;
  margin-bottom: 10px;
  font-weight: 600;
  color: #333;
}

.layer-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: normal;
  cursor: pointer;
  margin-bottom: 0 !important;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
}

/* 网格管理样式 */
.grid-management-layout {
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: 20px;
  padding: 20px;
  height: 600px;
}

.grid-list-section {
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.section-header {
  padding: 15px 20px;
  background: white;
  border-bottom: 1px solid #e1e8ed;
}

.section-header h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 16px;
}

.list-controls {
  display: flex;
  gap: 10px;
}

.search-input {
  flex: 1;
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.status-filter {
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  min-width: 100px;
}

.grid-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.grid-item {
  background: white;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.grid-item:hover {
  border-color: #4a90e2;
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.1);
}

.grid-item.selected {
  border-color: #4a90e2;
  background: #f0f7ff;
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.2);
}

.grid-info h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

.grid-info p {
  margin: 4px 0;
  color: #666;
  font-size: 12px;
}

.status {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 600;
}

.status.active {
  background: #d4edda;
  color: #155724;
}

.status.inactive {
  background: #f8d7da;
  color: #721c24;
}

.grid-actions {
  display: flex;
  gap: 5px;
  margin-top: 10px;
}

.btn-small {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  transition: background 0.3s ease;
}

.btn-small:not(.secondary):not(.danger) {
  background: #4a90e2;
  color: white;
}

.btn-small:not(.secondary):not(.danger):hover {
  background: #357abd;
}

.btn-small.secondary {
  background: #6c757d;
  color: white;
}

.btn-small.secondary:hover {
  background: #545b62;
}

.btn-small.danger {
  background: #dc3545;
  color: white;
}

.btn-small.danger:hover {
  background: #c82333;
}

.map-section {
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e8ed;
}

.map-header h3 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.map-tools {
  display: flex;
  gap: 10px;
  align-items: center;
}

.drawing-tips {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 12px 15px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  font-size: 12px;
  color: #856404;
  max-width: 300px;
}

.tip-icon {
  font-size: 16px;
  margin-top: 2px;
}

.tip-content {
  flex: 1;
}

.tip-title {
  font-weight: 600;
  margin-bottom: 4px;
  color: #664d03;
}

.tip-steps {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.tip-steps div {
  font-size: 11px;
  line-height: 1.3;
}

.current-points {
  font-weight: 600;
  color: #4a90e2 !important;
  margin-top: 4px;
}

.complete-drawing-btn {
  background: #28a745 !important;
  color: white !important;
  border: 2px solid #28a745 !important;
  font-weight: 600;
  animation: pulse 2s infinite;
}

.complete-drawing-btn:hover {
  background: #218838 !important;
  border-color: #218838 !important;
  transform: scale(1.05);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
  }
}

.auto-draw-tip {
  display: flex;
  align-items: center;
  gap: 12px;
  background: linear-gradient(135deg, #4a90e2, #357abd);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 12px;
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
  animation: slideInDown 0.5s ease-out;
  position: relative;
}

.auto-draw-tip .tip-icon {
  font-size: 20px;
  animation: bounce 2s infinite;
}

.auto-draw-tip .tip-content {
  flex: 1;
}

.auto-draw-tip .tip-title {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
}

.auto-draw-tip .tip-message {
  font-size: 12px;
  opacity: 0.9;
}

.close-tip-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease;
}

.close-tip-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

@keyframes slideInDown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-5px);
  }
  60% {
    transform: translateY(-3px);
  }
}

.tool-btn {
  background: #f8f9fa;
  border: 1px solid #e1e8ed;
  padding: 6px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.3s ease;
}

.tool-btn:hover {
  background: #e9ecef;
  border-color: #4a90e2;
}

.action-btn.active {
  background: #fd7e14 !important;
  color: white;
}

.action-btn.active:hover {
  background: #e8690b !important;
}

/* 对话框样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #e1e8ed;
  background: #f8f9fa;
}

.dialog-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background 0.3s ease;
}

.close-btn:hover {
  background: #e9ecef;
}

.dialog-content {
  padding: 25px;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-group:nth-child(4) {
  grid-column: 1 / -1;
}

.form-group label {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.required {
  color: #dc3545;
}

.form-group input,
.form-group select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.form-group input[readonly] {
  background: #f8f9fa;
  color: #6c757d;
}

.form-hint {
  font-size: 12px;
  color: #6c757d;
  margin-top: 2px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 25px;
  padding-top: 20px;
  border-top: 1px solid #e1e8ed;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background 0.3s ease;
}

.btn.primary {
  background: #4a90e2;
  color: white;
}

.btn.primary:hover {
  background: #357abd;
}

.btn.secondary {
  background: #6c757d;
  color: white;
}

.btn.secondary:hover {
  background: #545b62;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .grid-management-layout {
    grid-template-columns: 1fr;
    height: auto;
  }

  .grid-list-section {
    order: 2;
    height: 300px;
  }

  .map-section {
    order: 1;
  }
}

@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }

  .list-controls {
    flex-direction: column;
  }

  .map-tools {
    flex-wrap: wrap;
  }

  .drawing-tips {
    order: -1;
    width: 100%;
  }
}

/* 网格管理样式 */
.grid-management-content {
  padding: 30px;
}

.grid-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.grid-item {
  background: #f8f9fa;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  padding: 20px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.grid-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.grid-info h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.grid-info p {
  margin: 5px 0;
  color: #666;
  font-size: 14px;
}

.status {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.status.active {
  background: #d4edda;
  color: #155724;
}

.status.inactive {
  background: #f8d7da;
  color: #721c24;
}

.grid-actions {
  margin-top: 15px;
  display: flex;
  gap: 8px;
}

.btn-small {
  background: #6c757d;
  color: white;
  border: none;
  padding: 4px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background 0.3s ease;
}

.btn-small:hover {
  background: #5a6268;
}

.btn-small.danger {
  background: #dc3545;
}

.btn-small.danger:hover {
  background: #c82333;
}

/* 统计样式 */
.statistics-content {
  padding: 30px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 25px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  font-size: 2.5em;
  opacity: 0.8;
}

.stat-info h3 {
  margin: 0 0 5px 0;
  font-size: 14px;
  opacity: 0.9;
}

.stat-number {
  margin: 0;
  font-size: 2em;
  font-weight: bold;
}

/* 工具样式 */
.tools-content {
  padding: 30px;
}

.tool-groups {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.tool-group h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.2em;
}

.tool-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.tool-btn {
  background: #f8f9fa;
  border: 1px solid #e1e8ed;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
  text-align: left;
}

.tool-btn:hover {
  background: #e9ecef;
  border-color: #4a90e2;
  transform: translateY(-1px);
}

.tool-icon {
  font-size: 18px;
  color: #4a90e2;
}

/* 加载状态样式 */
.loading-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #666;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #666;
}

.empty-message p {
  margin-bottom: 16px;
  font-size: 16px;
}
</style>
