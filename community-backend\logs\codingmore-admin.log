2025-07-01 09:22:26.317 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 53 ] - Starting XiaozuApplication using Java 17.0.2 with PID 24220 (D:\gitlab\xiangmu\community-backend\target\classes started by joy0531 in D:\gitlab\xiangmu\community-backend)
2025-07-01 09:22:26.328 [ restartedMain ] - [ DEBUG ] [ com.hfut.xiaozu.XiaozuApplication : 54 ] - Running with Spring Boot v3.4.7, Spring v6.2.8
2025-07-01 09:22:26.328 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 652 ] - No active profile set, falling back to 1 default profile: "default"
2025-07-01 09:22:26.367 [ restartedMain ] - [ INFO  ] [ o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor : 252 ] - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-01 09:22:26.367 [ restartedMain ] - [ INFO  ] [ o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor : 252 ] - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-01 09:22:26.953 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 247 ] - Searching for mappers annotated with @Mapper
2025-07-01 09:22:26.954 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 251 ] - Using auto-configuration base package 'com.hfut.xiaozu'
2025-07-01 09:22:27.104 [ restartedMain ] - [ DEBUG ] [ org.apache.ibatis.logging.LogFactory : 112 ] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
2025-07-01 09:22:27.115 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.class]
2025-07-01 09:22:27.115 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.class]
2025-07-01 09:22:27.115 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.class]
2025-07-01 09:22:27.115 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.class]
2025-07-01 09:22:27.115 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.class]
2025-07-01 09:22:27.116 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.class]
2025-07-01 09:22:27.116 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.class]
2025-07-01 09:22:27.116 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.class]
2025-07-01 09:22:27.116 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.class]
2025-07-01 09:22:27.116 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.class]
2025-07-01 09:22:27.116 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.class]
2025-07-01 09:22:27.116 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.class]
2025-07-01 09:22:27.116 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.class]
2025-07-01 09:22:27.116 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.class]
2025-07-01 09:22:27.116 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.class]
2025-07-01 09:22:27.117 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleHouseBindingMapper' and 'com.hfut.xiaozu.car.bind.VehicleHouseBindingMapper' mapperInterface
2025-07-01 09:22:27.118 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleInfoMapper' and 'com.hfut.xiaozu.car.info.VehicleInfoMapper' mapperInterface
2025-07-01 09:22:27.119 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'familyMemberMapper' and 'com.hfut.xiaozu.family.info.FamilyMemberMapper' mapperInterface
2025-07-01 09:22:27.119 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userHouseBindingMapper' and 'com.hfut.xiaozu.house.binding.UserHouseBindingMapper' mapperInterface
2025-07-01 09:22:27.119 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityGridMapper' and 'com.hfut.xiaozu.house.grid.CommunityGridMapper' mapperInterface
2025-07-01 09:22:27.119 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'gridItemArchiveMapper' and 'com.hfut.xiaozu.house.grid.GridItemArchiveMapper' mapperInterface
2025-07-01 09:22:27.121 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'buildingInfoMapper' and 'com.hfut.xiaozu.house.information.BuildingInfoMapper' mapperInterface
2025-07-01 09:22:27.121 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityInfoMapper' and 'com.hfut.xiaozu.house.information.CommunityInfoMapper' mapperInterface
2025-07-01 09:22:27.121 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'houseInfoMapper' and 'com.hfut.xiaozu.house.information.HouseInfoMapper' mapperInterface
2025-07-01 09:22:27.121 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'unitInfoMapper' and 'com.hfut.xiaozu.house.information.UnitInfoMapper' mapperInterface
2025-07-01 09:22:27.122 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentProcessRecordMapper' and 'com.hfut.xiaozu.incident.process.IncidentProcessRecordMapper' mapperInterface
2025-07-01 09:22:27.122 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentRecordMapper' and 'com.hfut.xiaozu.incident.record.IncidentRecordMapper' mapperInterface
2025-07-01 09:22:27.123 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userMapper' and 'com.hfut.xiaozu.user.account.UserMapper' mapperInterface
2025-07-01 09:22:27.123 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityOcrRecordMapper' and 'com.hfut.xiaozu.user.idv.IdentityOcrRecordMapper' mapperInterface
2025-07-01 09:22:27.123 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityVerifySubmitMapper' and 'com.hfut.xiaozu.user.idv.IdentityVerifySubmitMapper' mapperInterface
2025-07-01 09:22:27.475 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 111 ] - Tomcat initialized with port 8080 (http)
2025-07-01 09:22:27.485 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardService : 168 ] - Starting service [Tomcat]
2025-07-01 09:22:27.487 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardEngine : 168 ] - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-01 09:22:27.529 [ restartedMain ] - [ INFO  ] [ o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] : 168 ] - Initializing Spring embedded WebApplicationContext
2025-07-01 09:22:27.530 [ restartedMain ] - [ INFO  ] [ o.s.b.w.s.c.ServletWebServerApplicationContext : 301 ] - Root WebApplicationContext: initialization completed in 1162 ms
2025-07-01 09:22:27.591 [ restartedMain ] - [ INFO  ] [ c.a.d.s.b.a.DruidDataSourceAutoConfigure : 55 ] - Init DruidDataSource
2025-07-01 09:22:27.936 [ restartedMain ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 1009 ] - {dataSource-1} inited
2025-07-01 09:22:28.087 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.xml]'
2025-07-01 09:22:28.096 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.xml]'
2025-07-01 09:22:28.106 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.xml]'
2025-07-01 09:22:28.115 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.xml]'
2025-07-01 09:22:28.123 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.xml]'
2025-07-01 09:22:28.130 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.xml]'
2025-07-01 09:22:28.143 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.xml]'
2025-07-01 09:22:28.151 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.xml]'
2025-07-01 09:22:28.160 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.xml]'
2025-07-01 09:22:28.167 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.xml]'
2025-07-01 09:22:28.174 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.xml]'
2025-07-01 09:22:28.183 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.xml]'
2025-07-01 09:22:28.191 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.xml]'
2025-07-01 09:22:28.196 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.xml]'
2025-07-01 09:22:28.201 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.xml]'
2025-07-01 09:22:28.714 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 319 ] - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-01 09:22:28.879 [ restartedMain ] - [ INFO  ] [ o.s.b.d.autoconfigure.OptionalLiveReloadServer : 59 ] - LiveReload server is running on port 35729
2025-07-01 09:22:28.924 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 243 ] - Tomcat started on port 8080 (http) with context path '/'
2025-07-01 09:22:28.932 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 59 ] - Started XiaozuApplication in 2.973 seconds (process running for 4.177)
2025-07-01 09:36:52.209 [ http-nio-8080-exec-1 ] - [ INFO  ] [ o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] : 168 ] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-01 09:36:52.209 [ http-nio-8080-exec-1 ] - [ INFO  ] [ org.springframework.web.servlet.DispatcherServlet : 532 ] - Initializing Servlet 'dispatcherServlet'
2025-07-01 09:36:52.210 [ http-nio-8080-exec-1 ] - [ INFO  ] [ org.springframework.web.servlet.DispatcherServlet : 554 ] - Completed initialization in 1 ms
2025-07-01 09:36:54.624 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/houses/binding/me
2025-07-01 09:36:54.633 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 09:36:54.674 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/communities
2025-07-01 09:36:54.680 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-07-01 09:36:58.889 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/buildings
2025-07-01 09:36:58.893 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/buildings
2025-07-01 09:37:02.705 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/buildings
2025-07-01 09:37:02.710 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/buildings
2025-07-01 09:37:04.384 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/units
2025-07-01 09:37:04.390 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/units
2025-07-01 09:37:05.947 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/buildings
2025-07-01 09:37:05.950 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/buildings
2025-07-01 09:37:07.355 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/units
2025-07-01 09:37:07.359 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/units
2025-07-01 09:37:08.545 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/houses
2025-07-01 09:37:08.549 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses
2025-07-01 09:37:10.622 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/houses
2025-07-01 09:37:10.626 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses
2025-07-01 09:37:13.689 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/units
2025-07-01 09:37:13.694 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/units
2025-07-01 09:37:15.092 [ http-nio-8080-exec-3 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/buildings
2025-07-01 09:37:16.566 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/units
2025-07-01 09:37:16.570 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/units
2025-07-01 09:37:17.860 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/houses
2025-07-01 09:37:17.864 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses
2025-07-01 09:37:19.157 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/houses/status/2613
2025-07-01 09:37:19.161 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/status/2613
2025-07-01 09:37:22.308 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: OPTIONS /api/grids/houses/binding/2613
2025-07-01 09:37:22.312 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: PUT /api/grids/houses/binding/2613
2025-07-01 09:37:24.041 [ http-nio-8080-exec-2 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 09:39:57.323 [ http-nio-8080-exec-4 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-01 09:39:57.855 [ http-nio-8080-exec-5 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-01 09:39:58.202 [ http-nio-8080-exec-7 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 09:39:58.215 [ http-nio-8080-exec-6 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-07-01 09:39:59.432 [ http-nio-8080-exec-8 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/family/list/me
2025-07-01 09:39:59.822 [ http-nio-8080-exec-9 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/car/list/me
2025-07-01 09:40:00.176 [ http-nio-8080-exec-10 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/houses/binding/me
2025-07-01 09:40:00.187 [ http-nio-8080-exec-1 ] - [ ERROR ] [ com.hfut.xiaozu.security.JwtAuthInterceptor : 36 ] - 进入JWT拦截器，请求路径: GET /api/grids/communities
2025-07-01 09:52:51.080 [ File Watcher ] - [ INFO  ] [ o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener : 211 ] - Restarting due to 100 class path changes (0 additions, 100 deletions, 0 modifications)
2025-07-01 09:52:51.089 [ Thread-5 ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 54 ] - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-01 09:52:51.239 [ tomcat-shutdown ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 76 ] - Graceful shutdown complete
2025-07-01 09:52:51.266 [ Thread-5 ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2174 ] - {dataSource-1} closing ...
2025-07-01 09:52:51.282 [ Thread-5 ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2247 ] - {dataSource-1} closed
2025-07-01 09:52:57.133 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 53 ] - Starting XiaozuApplication using Java 17.0.2 with PID 24220 (D:\gitlab\xiangmu\community-backend\target\classes started by joy0531 in D:\gitlab\xiangmu\community-backend)
2025-07-01 09:52:57.134 [ restartedMain ] - [ DEBUG ] [ com.hfut.xiaozu.XiaozuApplication : 54 ] - Running with Spring Boot v3.4.7, Spring v6.2.8
2025-07-01 09:52:57.134 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 652 ] - No active profile set, falling back to 1 default profile: "default"
2025-07-01 09:52:57.633 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 247 ] - Searching for mappers annotated with @Mapper
2025-07-01 09:52:57.633 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 251 ] - Using auto-configuration base package 'com.hfut.xiaozu'
2025-07-01 09:52:57.791 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.class]
2025-07-01 09:52:57.791 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.class]
2025-07-01 09:52:57.792 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.class]
2025-07-01 09:52:57.792 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.class]
2025-07-01 09:52:57.792 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.class]
2025-07-01 09:52:57.792 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.class]
2025-07-01 09:52:57.792 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.class]
2025-07-01 09:52:57.792 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.class]
2025-07-01 09:52:57.792 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.class]
2025-07-01 09:52:57.792 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.class]
2025-07-01 09:52:57.793 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.class]
2025-07-01 09:52:57.793 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.class]
2025-07-01 09:52:57.793 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.class]
2025-07-01 09:52:57.793 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.class]
2025-07-01 09:52:57.795 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 476 ] - Identified candidate component class: file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.class]
2025-07-01 09:52:57.795 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleHouseBindingMapper' and 'com.hfut.xiaozu.car.bind.VehicleHouseBindingMapper' mapperInterface
2025-07-01 09:52:57.796 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'vehicleInfoMapper' and 'com.hfut.xiaozu.car.info.VehicleInfoMapper' mapperInterface
2025-07-01 09:52:57.797 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'familyMemberMapper' and 'com.hfut.xiaozu.family.info.FamilyMemberMapper' mapperInterface
2025-07-01 09:52:57.797 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userHouseBindingMapper' and 'com.hfut.xiaozu.house.binding.UserHouseBindingMapper' mapperInterface
2025-07-01 09:52:57.798 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityGridMapper' and 'com.hfut.xiaozu.house.grid.CommunityGridMapper' mapperInterface
2025-07-01 09:52:57.799 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'gridItemArchiveMapper' and 'com.hfut.xiaozu.house.grid.GridItemArchiveMapper' mapperInterface
2025-07-01 09:52:57.799 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'buildingInfoMapper' and 'com.hfut.xiaozu.house.information.BuildingInfoMapper' mapperInterface
2025-07-01 09:52:57.800 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'communityInfoMapper' and 'com.hfut.xiaozu.house.information.CommunityInfoMapper' mapperInterface
2025-07-01 09:52:57.800 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'houseInfoMapper' and 'com.hfut.xiaozu.house.information.HouseInfoMapper' mapperInterface
2025-07-01 09:52:57.801 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'unitInfoMapper' and 'com.hfut.xiaozu.house.information.UnitInfoMapper' mapperInterface
2025-07-01 09:52:57.803 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentProcessRecordMapper' and 'com.hfut.xiaozu.incident.process.IncidentProcessRecordMapper' mapperInterface
2025-07-01 09:52:57.803 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'incidentRecordMapper' and 'com.hfut.xiaozu.incident.record.IncidentRecordMapper' mapperInterface
2025-07-01 09:52:57.804 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'userMapper' and 'com.hfut.xiaozu.user.account.UserMapper' mapperInterface
2025-07-01 09:52:57.805 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityOcrRecordMapper' and 'com.hfut.xiaozu.user.idv.IdentityOcrRecordMapper' mapperInterface
2025-07-01 09:52:57.806 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.mapper.ClassPathMapperScanner : 49 ] - Creating MapperFactoryBean with name 'identityVerifySubmitMapper' and 'com.hfut.xiaozu.user.idv.IdentityVerifySubmitMapper' mapperInterface
2025-07-01 09:52:57.937 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 111 ] - Tomcat initialized with port 8080 (http)
2025-07-01 09:52:57.938 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardService : 168 ] - Starting service [Tomcat]
2025-07-01 09:52:57.938 [ restartedMain ] - [ INFO  ] [ org.apache.catalina.core.StandardEngine : 168 ] - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-07-01 09:52:57.975 [ restartedMain ] - [ INFO  ] [ o.a.c.c.ContainerBase.[Tomcat-2].[localhost].[/] : 168 ] - Initializing Spring embedded WebApplicationContext
2025-07-01 09:52:57.975 [ restartedMain ] - [ INFO  ] [ o.s.b.w.s.c.ServletWebServerApplicationContext : 301 ] - Root WebApplicationContext: initialization completed in 837 ms
2025-07-01 09:52:58.009 [ restartedMain ] - [ INFO  ] [ c.a.d.s.b.a.DruidDataSourceAutoConfigure : 55 ] - Init DruidDataSource
2025-07-01 09:52:58.226 [ restartedMain ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 1009 ] - {dataSource-2} inited
2025-07-01 09:52:58.290 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\bind\VehicleHouseBindingMapper.xml]'
2025-07-01 09:52:58.299 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\car\info\VehicleInfoMapper.xml]'
2025-07-01 09:52:58.306 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\family\info\FamilyMemberMapper.xml]'
2025-07-01 09:52:58.314 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\binding\UserHouseBindingMapper.xml]'
2025-07-01 09:52:58.322 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\CommunityGridMapper.xml]'
2025-07-01 09:52:58.329 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\grid\GridItemArchiveMapper.xml]'
2025-07-01 09:52:58.335 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\BuildingInfoMapper.xml]'
2025-07-01 09:52:58.345 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\CommunityInfoMapper.xml]'
2025-07-01 09:52:58.355 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\HouseInfoMapper.xml]'
2025-07-01 09:52:58.362 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\house\information\UnitInfoMapper.xml]'
2025-07-01 09:52:58.379 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\process\IncidentProcessRecordMapper.xml]'
2025-07-01 09:52:58.390 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\incident\record\IncidentRecordMapper.xml]'
2025-07-01 09:52:58.398 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\account\UserMapper.xml]'
2025-07-01 09:52:58.405 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityOcrRecordMapper.xml]'
2025-07-01 09:52:58.408 [ restartedMain ] - [ DEBUG ] [ org.mybatis.spring.SqlSessionFactoryBean : 49 ] - Parsed mapper file: 'file [D:\gitlab\xiangmu\community-backend\target\classes\com\hfut\xiaozu\user\idv\IdentityVerifySubmitMapper.xml]'
2025-07-01 09:52:58.707 [ restartedMain ] - [ DEBUG ] [ o.m.s.boot.autoconfigure.MybatisAutoConfiguration : 319 ] - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-01 09:52:58.794 [ restartedMain ] - [ INFO  ] [ o.s.b.d.autoconfigure.OptionalLiveReloadServer : 59 ] - LiveReload server is running on port 35729
2025-07-01 09:52:58.849 [ restartedMain ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.TomcatWebServer : 243 ] - Tomcat started on port 8080 (http) with context path '/'
2025-07-01 09:52:58.855 [ restartedMain ] - [ INFO  ] [ com.hfut.xiaozu.XiaozuApplication : 59 ] - Started XiaozuApplication in 1.797 seconds (process running for 1834.101)
2025-07-01 09:52:58.859 [ restartedMain ] - [ INFO  ] [ o.s.b.d.a.ConditionEvaluationDeltaLoggingListener : 63 ] - Condition evaluation unchanged
2025-07-01 09:53:13.239 [ File Watcher ] - [ INFO  ] [ o.s.b.d.a.LocalDevToolsAutoConfiguration$RestartingClassPathChangeChangedEventListener : 211 ] - Restarting due to 100 class path changes (0 additions, 100 deletions, 0 modifications)
2025-07-01 09:53:13.250 [ Thread-7 ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 54 ] - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-01 09:53:13.405 [ tomcat-shutdown ] - [ INFO  ] [ o.s.boot.web.embedded.tomcat.GracefulShutdown : 76 ] - Graceful shutdown complete
2025-07-01 09:53:13.422 [ Thread-7 ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2174 ] - {dataSource-2} closing ...
2025-07-01 09:53:13.427 [ Thread-7 ] - [ INFO  ] [ com.alibaba.druid.pool.DruidDataSource : 2247 ] - {dataSource-2} closed
