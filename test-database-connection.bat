@echo off
echo ========================================
echo 数据库连接测试脚本
echo ========================================
echo.

echo 1. 测试MySQL服务状态...
sc query mysql >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ MySQL服务已安装
    sc query mysql | find "RUNNING" >nul
    if %errorlevel% == 0 (
        echo ✅ MySQL服务正在运行
    ) else (
        echo ❌ MySQL服务未运行，尝试启动...
        net start mysql
    )
) else (
    echo ❌ MySQL服务未安装或名称不同
    echo 请检查MySQL安装情况
)

echo.
echo 2. 测试数据库连接...
echo 尝试连接到MySQL数据库...

mysql -u root -pa13716842307 -e "SELECT 'MySQL连接成功!' as status;" 2>nul
if %errorlevel% == 0 (
    echo ✅ MySQL数据库连接成功
) else (
    echo ❌ MySQL数据库连接失败
    echo 请检查:
    echo - 用户名密码是否正确
    echo - MySQL服务是否启动
    echo - 端口3306是否开放
)

echo.
echo 3. 检查community数据库...
mysql -u root -pa13716842307 -e "USE community; SELECT 'community数据库存在' as status;" 2>nul
if %errorlevel% == 0 (
    echo ✅ community数据库存在
) else (
    echo ❌ community数据库不存在，正在创建...
    mysql -u root -pa13716842307 -e "CREATE DATABASE IF NOT EXISTS community CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    if %errorlevel% == 0 (
        echo ✅ community数据库创建成功
    ) else (
        echo ❌ community数据库创建失败
    )
)

echo.
echo 4. 检查user_account表...
mysql -u root -pa13716842307 -e "USE community; DESCRIBE user_account;" 2>nul
if %errorlevel% == 0 (
    echo ✅ user_account表存在
    echo 表结构:
    mysql -u root -pa13716842307 -e "USE community; DESCRIBE user_account;"
) else (
    echo ❌ user_account表不存在，正在创建...
    mysql -u root -pa13716842307 community < community-backend\init-database.sql
    if %errorlevel% == 0 (
        echo ✅ user_account表创建成功
    ) else (
        echo ❌ user_account表创建失败
    )
)

echo.
echo 5. 测试后端服务连接...
curl -s http://localhost:8080/api/auth/test >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ 后端服务连接成功
    echo 测试数据库连接接口...
    curl -s http://localhost:8080/api/auth/test-db
) else (
    echo ❌ 后端服务连接失败
    echo 请确保后端服务已启动在端口8080
)

echo.
echo ========================================
echo 测试完成！
echo ========================================
echo.
echo 如果所有测试都通过，数据库连接应该正常工作。
echo 如果有失败项，请根据提示进行修复。
echo.
pause
