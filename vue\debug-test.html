<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        .btn-danger { background-color: #dc3545; color: white; }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API调试测试工具</h1>
        
        <div class="test-section">
            <h3>1. 基础连接测试</h3>
            <button class="btn-primary" onclick="testBasicConnection()">测试基础连接</button>
            <button class="btn-success" onclick="testCors()">测试CORS</button>
            <div id="basic-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. API端点测试</h3>
            <button class="btn-primary" onclick="testApiEndpoint()">测试API端点</button>
            <button class="btn-warning" onclick="testSwagger()">测试Swagger文档</button>
            <div id="api-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. 注册功能测试</h3>
            <button class="btn-success" onclick="testRegisterAPI()">测试注册API</button>
            <button class="btn-danger" onclick="testInvalidData()">测试无效数据</button>
            <div id="register-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. 网络诊断</h3>
            <button class="btn-warning" onclick="diagnoseNetwork()">网络诊断</button>
            <button class="btn-info" onclick="showBrowserInfo()">浏览器信息</button>
            <div id="network-result" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080';
        
        // 1. 基础连接测试
        async function testBasicConnection() {
            const resultDiv = document.getElementById('basic-result');
            resultDiv.textContent = '正在测试基础连接...';
            resultDiv.className = 'result info';
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/auth/test`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                resultDiv.textContent = `✅ 连接成功！\n状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = `❌ 连接失败！\n错误: ${error.message}\n\n可能原因:\n1. 后端服务未启动\n2. 端口8080被占用\n3. 防火墙阻止连接`;
                resultDiv.className = 'result error';
            }
        }

        // 2. CORS测试
        async function testCors() {
            const resultDiv = document.getElementById('basic-result');
            resultDiv.textContent = '正在测试CORS配置...';
            resultDiv.className = 'result info';
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/auth/test`, {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': window.location.origin,
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type'
                    }
                });
                
                resultDiv.textContent = `✅ CORS配置正常！\n状态码: ${response.status}`;
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = `❌ CORS配置有问题！\n错误: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // 3. API端点测试
        async function testApiEndpoint() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.textContent = '正在测试API端点...';
            resultDiv.className = 'result info';
            
            const endpoints = [
                '/api/auth/test',
                '/api/auth/register'
            ];
            
            let results = [];
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
                        method: endpoint.includes('register') ? 'POST' : 'GET',
                        headers: { 'Content-Type': 'application/json' },
                        body: endpoint.includes('register') ? JSON.stringify({}) : undefined
                    });
                    
                    results.push(`✅ ${endpoint}: ${response.status}`);
                } catch (error) {
                    results.push(`❌ ${endpoint}: ${error.message}`);
                }
            }
            
            resultDiv.textContent = results.join('\n');
            resultDiv.className = 'result info';
        }

        // 4. Swagger文档测试
        async function testSwagger() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.textContent = '正在测试Swagger文档...';
            resultDiv.className = 'result info';
            
            try {
                const response = await fetch(`${API_BASE_URL}/doc.html`);
                if (response.ok) {
                    resultDiv.textContent = `✅ Swagger文档可访问！\n地址: ${API_BASE_URL}/doc.html`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = `⚠️ Swagger文档访问异常: ${response.status}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `❌ Swagger文档不可访问: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // 5. 注册API测试
        async function testRegisterAPI() {
            const resultDiv = document.getElementById('register-result');
            resultDiv.textContent = '正在测试注册API...';
            resultDiv.className = 'result info';
            
            const testData = {
                userName: 'testuser' + Date.now(),
                phone: '13800138' + String(Date.now()).slice(-3),
                password: '123456',
                userType: 1
            };
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                
                const data = await response.json();
                resultDiv.textContent = `✅ 注册API响应！\n状态码: ${response.status}\n请求数据: ${JSON.stringify(testData, null, 2)}\n响应数据: ${JSON.stringify(data, null, 2)}`;
                resultDiv.className = response.ok ? 'result success' : 'result error';
            } catch (error) {
                resultDiv.textContent = `❌ 注册API失败！\n错误: ${error.message}\n请求数据: ${JSON.stringify(testData, null, 2)}`;
                resultDiv.className = 'result error';
            }
        }

        // 6. 无效数据测试
        async function testInvalidData() {
            const resultDiv = document.getElementById('register-result');
            resultDiv.textContent = '正在测试无效数据处理...';
            resultDiv.className = 'result info';
            
            const invalidData = {
                userName: '',
                phone: '123',
                password: '1',
                userType: 999
            };
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(invalidData)
                });
                
                const data = await response.json();
                resultDiv.textContent = `✅ 无效数据处理正常！\n状态码: ${response.status}\n错误信息: ${JSON.stringify(data, null, 2)}`;
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = `❌ 无效数据处理失败！\n错误: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // 7. 网络诊断
        async function diagnoseNetwork() {
            const resultDiv = document.getElementById('network-result');
            resultDiv.textContent = '正在进行网络诊断...';
            resultDiv.className = 'result info';
            
            let diagnosis = [];
            
            // 检查当前页面协议
            diagnosis.push(`当前页面: ${window.location.href}`);
            diagnosis.push(`协议: ${window.location.protocol}`);
            diagnosis.push(`主机: ${window.location.host}`);
            
            // 检查目标服务器
            diagnosis.push(`目标服务器: ${API_BASE_URL}`);
            
            // 检查网络连接
            diagnosis.push(`在线状态: ${navigator.onLine ? '在线' : '离线'}`);
            
            resultDiv.textContent = diagnosis.join('\n');
            resultDiv.className = 'result info';
        }

        // 8. 浏览器信息
        function showBrowserInfo() {
            const resultDiv = document.getElementById('network-result');
            
            const info = [
                `浏览器: ${navigator.userAgent}`,
                `语言: ${navigator.language}`,
                `平台: ${navigator.platform}`,
                `Cookie启用: ${navigator.cookieEnabled}`,
                `Java启用: ${navigator.javaEnabled()}`,
                `屏幕分辨率: ${screen.width}x${screen.height}`,
                `时区: ${Intl.DateTimeFormat().resolvedOptions().timeZone}`
            ];
            
            resultDiv.textContent = info.join('\n');
            resultDiv.className = 'result info';
        }
    </script>
</body>
</html>
