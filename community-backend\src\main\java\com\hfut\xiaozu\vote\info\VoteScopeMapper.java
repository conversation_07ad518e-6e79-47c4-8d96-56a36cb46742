package com.hfut.xiaozu.vote.info;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【vote_scope(投票范围关联表)】的数据库操作Mapper
* @createDate 2025-07-01 10:35:26
* @Entity com.hfut.xiaozu.vote.info.VoteScope
*/
@Mapper
public interface VoteScopeMapper {

    int deleteById(Long id);

    int insert(VoteScopeEntity record);

    VoteScopeEntity getById(Long id);

    int updateById(VoteScopeEntity record);

    List<VoteScopeEntity> listByVoteId(Long voteId);
}
