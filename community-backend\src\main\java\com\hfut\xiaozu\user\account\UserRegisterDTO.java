package com.hfut.xiaozu.user.account;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-06-23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserRegisterDTO {

    @NotBlank(message = "用户名不能为空")
    @Size(min = 2, max = 50, message = "用户名长度需在2-50个字符之间")
    @Pattern(regexp = "^(?!\\d+$).+$", message = "用户名不能为纯数字")
    private String userName;

    /**
     * 手机号码
     * 手机号格式比较复杂多样，放到控制层校验
     */
    @NotBlank(message = "手机号不能为空")
    private String phone;

    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 200, message = "密码长度需在6-200个字符之间")
    private String password;

    /**
     * 用户类型（1-居民 2-网格员 3-社区管理员）
     * 放到控制层校验
     */
    private Integer userType;
}
