/**
 * Token管理工具
 * 统一管理token的存储、获取和清除，确保整个应用的一致性
 */

/**
 * 获取当前有效的token
 * 按优先级从不同存储位置获取token
 * @returns {string|null} token字符串或null
 */
export function getToken() {
  // 优先从auth_data中获取token（与authStore一致）
  let token = null;
  
  // 1. 尝试从localStorage的auth_data获取
  const authDataLocal = localStorage.getItem('auth_data');
  if (authDataLocal) {
    try {
      const authData = JSON.parse(authDataLocal);
      if (authData.token) {
        token = authData.token;
        console.log('🔑 从localStorage auth_data获取token');
        return token;
      }
    } catch (e) {
      console.warn('⚠️ 解析localStorage auth_data失败:', e);
    }
  }
  
  // 2. 尝试从sessionStorage的auth_data获取
  const authDataSession = sessionStorage.getItem('auth_data');
  if (authDataSession) {
    try {
      const authData = JSON.parse(authDataSession);
      if (authData.token) {
        token = authData.token;
        console.log('🔑 从sessionStorage auth_data获取token');
        return token;
      }
    } catch (e) {
      console.warn('⚠️ 解析sessionStorage auth_data失败:', e);
    }
  }
  
  // 3. 兼容旧的token存储方式
  token = localStorage.getItem('auth_token') || localStorage.getItem('userToken');
  if (token) {
    console.log('🔑 从兼容存储获取token');
    return token;
  }
  
  console.log('⚠️ 未找到有效token');
  return null;
}

/**
 * 获取完整的认证数据
 * @returns {Object|null} 认证数据对象或null
 */
export function getAuthData() {
  // 优先从localStorage获取
  const authDataLocal = localStorage.getItem('auth_data');
  if (authDataLocal) {
    try {
      const authData = JSON.parse(authDataLocal);
      console.log('📱 从localStorage获取认证数据');
      return authData;
    } catch (e) {
      console.warn('⚠️ 解析localStorage auth_data失败:', e);
    }
  }
  
  // 尝试从sessionStorage获取
  const authDataSession = sessionStorage.getItem('auth_data');
  if (authDataSession) {
    try {
      const authData = JSON.parse(authDataSession);
      console.log('💾 从sessionStorage获取认证数据');
      return authData;
    } catch (e) {
      console.warn('⚠️ 解析sessionStorage auth_data失败:', e);
    }
  }
  
  console.log('ℹ️ 未找到认证数据');
  return null;
}

/**
 * 检查token是否过期
 * @returns {boolean} true表示已过期，false表示未过期
 */
export function isTokenExpired() {
  const authData = getAuthData();
  if (!authData || !authData.tokenExpiry) {
    return true;
  }
  
  const expiryTime = new Date(authData.tokenExpiry);
  const currentTime = new Date();
  const isExpired = currentTime >= expiryTime;
  
  console.log('⏰ Token过期检查:', {
    expiryTime: expiryTime.toISOString(),
    currentTime: currentTime.toISOString(),
    isExpired
  });
  
  return isExpired;
}

/**
 * 清除所有认证相关数据
 * 确保清除所有可能的存储位置
 */
export function clearAllAuthData() {
  // 清除主要的认证数据
  localStorage.removeItem('auth_data');
  sessionStorage.removeItem('auth_data');
  
  // 清除兼容的旧存储
  localStorage.removeItem('auth_token');
  localStorage.removeItem('userToken');
  localStorage.removeItem('user_info');
  localStorage.removeItem('userType');
  localStorage.removeItem('userData');
  
  console.log('🗑️ 已清除所有认证数据');
}

/**
 * 获取用户信息
 * @returns {Object|null} 用户信息对象或null
 */
export function getUserInfo() {
  const authData = getAuthData();
  if (authData && authData.user) {
    return authData.user;
  }
  
  // 兼容旧的用户信息存储
  const userDataStr = localStorage.getItem('userData');
  if (userDataStr) {
    try {
      return JSON.parse(userDataStr);
    } catch (e) {
      console.warn('⚠️ 解析userData失败:', e);
    }
  }
  
  return null;
}

/**
 * 检查用户是否已认证
 * @returns {boolean} true表示已认证，false表示未认证
 */
export function isAuthenticated() {
  const token = getToken();
  const userInfo = getUserInfo();
  const tokenNotExpired = !isTokenExpired();
  
  const result = !!(token && userInfo && tokenNotExpired);
  
  console.log('🔍 认证状态检查:', {
    hasToken: !!token,
    hasUserInfo: !!userInfo,
    tokenNotExpired,
    result
  });
  
  return result;
}

/**
 * 获取用户角色
 * @returns {string} 用户角色
 */
export function getUserRole() {
  const userInfo = getUserInfo();
  if (userInfo) {
    // 优先使用role字段
    if (userInfo.role) {
      return userInfo.role;
    }
    
    // 根据userType推断角色
    if (userInfo.userType === 1) {
      return 'resident';
    } else if (userInfo.userType === 2 || userInfo.userType === 3) {
      return 'property';
    }
  }
  
  return 'guest';
}

/**
 * 获取用户类型
 * @returns {number|null} 用户类型数字或null
 */
export function getUserType() {
  const userInfo = getUserInfo();
  return userInfo ? userInfo.userType : null;
}
