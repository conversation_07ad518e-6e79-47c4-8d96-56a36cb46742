package com.hfut.xiaozu.house.information;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【building_info(楼栋信息表)】的数据库操作Mapper
* @createDate 2025-06-25 21:44:49
* @Entity com.hfut.xiaozu.house.BuildingInfo
*/
@Mapper
public interface BuildingInfoMapper {

    int insert(BuildingInfo record);

    BuildingInfo getById(Long id);

    int update(BuildingInfo record);

    List<BuildingInfo> listByCommunityId(Long communityId);

    Long getIdByHouseId(Long houseId);
}
