package com.hfut.xiaozu.car.bind;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 车辆绑定多个房屋的申请DTO
 * @TableName vehicle_house_binding
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class VehicleHouseBindingDTO {
    /**
     * 车辆ID
     */
    @NotNull(message = "请选择绑定的车辆")
    private Long vehicleId;

    /**
     * 房屋ID
     */
    @NotNull(message = "请选择绑定的房屋(们)")
    private List<Long> houseIds;

    /**
     * 车位编号
     */
    @NotBlank(message = "请选择车辆绑定的车位")
    private String spaceNumber;

    /**
     * 车位类型: 1-固定车位 2-临时车位
     */
    @NotNull(message = "请选择车辆绑定的车位类型")
    private Integer spaceType;

}
