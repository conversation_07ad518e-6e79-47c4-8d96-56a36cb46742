/**
 * 用户认证API服务
 * 处理用户注册、登录等认证相关的后端API调用
 */

import axios from 'axios';
import { getToken, clearAllAuthData } from '../utils/tokenManager.js';

// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:8080/api', // 后端API基础URL
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 添加JWT token到请求头
    const token = getToken();

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      console.log('🔑 已添加Authorization头');
    } else {
      console.log('⚠️ 未找到有效token');
    }

    console.log('🚀 API请求:', config.method?.toUpperCase(), config.url, config.data);
    return config;
  },
  error => {
    console.error('❌ 请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  response => {
    console.log('📦 API响应:', response.config.url, response.data);
    return response.data;
  },
  error => {
    console.error('❌ API请求错误:', error.response?.data || error.message);

    // 处理常见错误
    if (error.response?.status === 401) {
      // 未授权，清除所有认证相关数据并跳转到登录页
      clearAllAuthData();
      console.log('🗑️ 401错误：已清除所有认证数据');
      window.location.href = '/login';
    }

    return Promise.reject(error);
  }
);

/**
 * 用户认证相关API
 */
export const authApi = {
  /**
   * 用户注册
   * @param {Object} userData - 用户注册数据
   * @param {string} userData.userName - 用户名（驼峰命名法）
   * @param {string} userData.phone - 手机号
   * @param {string} userData.password - 密码
   * @param {number} userData.userType - 用户类型（1-居民 2-网格员 3-社区管理员）
   * @returns {Promise<Object>} API响应
   */
  register: async (userData) => {
    try {
      console.log('📝 开始用户注册:', userData);

      // 数据验证
      if (!userData.userName || userData.userName.trim().length < 2) {
        throw new Error('用户名长度需在2-50个字符之间');
      }

      // 检查用户名是否为纯数字
      if (/^\d+$/.test(userData.userName.trim())) {
        throw new Error('用户名不能为纯数字');
      }

      if (!userData.phone || !/^1[3-9]\d{9}$/.test(userData.phone)) {
        throw new Error('请输入正确的手机号码');
      }

      if (!userData.password || userData.password.length < 6) {
        throw new Error('密码长度需在6-200个字符之间');
      }

      // 验证用户类型
      if (!userData.userType || userData.userType < 1 || userData.userType > 3) {
        throw new Error('请选择正确的用户类型');
      }

      // 构造请求数据，使用驼峰命名法
      const requestData = {
        userName: userData.userName.trim(),
        phone: userData.phone.trim(),
        password: userData.password,
        userType: userData.userType
      };

      console.log('🚀 发送注册请求:', requestData);
      const response = await api.post('/auth/register', requestData);

      console.log('📦 注册响应:', response);

      // 处理API规范的响应格式 (code: 200成功/500失败)
      if (response.code === 200) {
        return {
          success: true,
          message: response.msg || response.message || '注册成功',
          data: response.data
        };
      } else {
        return {
          success: false,
          message: response.msg || response.message || '注册失败',
          data: response.data
        };
      }
    } catch (error) {
      console.error('❌ 注册失败:', error);
      throw error;
    }
  },

  /**
   * 测试API连接
   * @returns {Promise<Object>} 连接测试结果
   */
  testConnection: async () => {
    try {
      console.log('🔗 测试API连接...');
      const response = await api.get('/auth/test');

      return {
        success: response.code === 1,
        message: response.message || response.msg || '连接测试完成',
        data: response.data
      };
    } catch (error) {
      console.error('❌ 连接测试失败:', error);
      throw new Error(`连接测试失败: ${error.message}`);
    }
  },

  /**
   * 测试数据库连接
   * @returns {Promise<Object>} 数据库连接测试结果
   */
  testDatabase: async () => {
    try {
      console.log('🗄️ 测试数据库连接...');
      const response = await api.get('/auth/test-db');

      return {
        success: response.code === 200,
        message: response.msg || response.message || '数据库测试完成',
        data: response.data
      };
    } catch (error) {
      console.error('❌ 数据库测试失败:', error);
      throw new Error(`数据库测试失败: ${error.message}`);
    }
  },

  /**
   * 用户登录
   * @param {Object} loginData - 登录数据
   * @param {string} loginData.userName - 用户名（驼峰命名法）
   * @param {string} loginData.password - 密码
   * @param {number} loginData.userType - 用户类型（1-居民 2-网格员 3-社区管理员）
   * @returns {Promise<Object>} API响应
   */
  login: async (loginData) => {
    try {
      console.log('🔐 开始用户登录:', loginData);

      // 数据验证
      if (!loginData.userName || loginData.userName.trim().length < 2) {
        throw new Error('用户名长度需在2-50个字符之间');
      }

      if (!loginData.password || loginData.password.length < 6) {
        throw new Error('密码长度需在6-200个字符之间');
      }

      // 验证用户类型
      if (!loginData.userType || loginData.userType < 1 || loginData.userType > 3) {
        throw new Error('请选择正确的用户类型');
      }

      // 构造请求数据，使用驼峰命名法
      const requestData = {
        userName: loginData.userName.trim(),
        password: loginData.password,
        userType: loginData.userType
      };

      console.log('🚀 发送登录请求:', requestData);
      const response = await api.post('/auth/login', requestData);

      console.log('📦 登录响应:', response);

      // 处理API规范的响应格式 (code: 200成功/500失败)
      if (response.code === 200) {
        // 登录成功，保存token和用户信息
        if (response.data && response.data.token) {
          localStorage.setItem('auth_token', response.data.token);

          // 由于后端只返回token，我们需要从请求数据中获取用户信息
          localStorage.setItem('user_info', JSON.stringify({
            userName: requestData.userName,
            userType: requestData.userType
          }));

          console.log('💾 Token已保存到localStorage:', {
            token: response.data.token.substring(0, 20) + '...',
            userName: requestData.userName,
            userType: requestData.userType
          });
        } else {
          console.warn('⚠️ 登录响应中没有token数据:', response);
        }

        // 返回包含完整用户信息的数据
        return {
          success: true,
          message: response.msg || response.message || '登录成功',
          data: {
            token: response.data?.token,
            userName: requestData.userName,
            userType: requestData.userType,
            // 为了兼容前端其他地方的代码
            id: null,
            refreshToken: null
          }
        };
      } else {
        return {
          success: false,
          message: response.msg || response.message || '登录失败',
          data: response.data
        };
      }
    } catch (error) {
      console.error('❌ 登录失败:', error);
      throw error;
    }
  },

  /**
   * 获取用户信息
   * @returns {Promise<Object>} 用户信息
   */
  getUserInfo: async () => {
    try {
      const response = await api.get('/auth/user');
      return response;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw error;
    }
  },

  /**
   * 刷新token
   * @returns {Promise<Object>} 新的token
   */
  refreshToken: async () => {
    try {
      const response = await api.post('/auth/refresh');
      
      if (response.code === 200 && response.data?.token) {
        localStorage.setItem('userToken', response.data.token);
      }
      
      return response;
    } catch (error) {
      console.error('刷新token失败:', error);
      throw error;
    }
  },

  /**
   * 退出登录
   * @returns {Promise<Object>} API响应
   */
  logout: async () => {
    try {
      const response = await api.post('/auth/logout');

      // 清除本地存储
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user_info');

      return response;
    } catch (error) {
      console.error('❌ 退出登录失败:', error);

      // 即使API调用失败，也要清除本地存储
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user_info');

      throw error;
    }
  },

  /**
   * 获取当前用户信息
   * @returns {Object|null} 用户信息
   */
  getCurrentUser: () => {
    try {
      const userInfo = localStorage.getItem('user_info');
      return userInfo ? JSON.parse(userInfo) : null;
    } catch (error) {
      console.error('❌ 获取用户信息失败:', error);
      return null;
    }
  },

  /**
   * 获取当前token
   * @returns {string|null} JWT token
   */
  getToken: () => {
    return localStorage.getItem('auth_token');
  },

  /**
   * 检查是否已登录
   * @returns {boolean} 是否已登录
   */
  isLoggedIn: () => {
    const token = localStorage.getItem('auth_token');
    const userInfo = localStorage.getItem('user_info');
    return !!(token && userInfo);
  },

  /**
   * 获取网格员列表
   * @returns {Promise<Object>} 网格员列表
   */
  getGridWorkers: async () => {
    try {
      console.log('🔧 获取网格员列表...');
      const response = await api.get('/auth/list/grid-workers');

      console.log('📦 网格员列表响应:', response);

      if (response.code === 200) {
        return {
          success: true,
          message: response.msg || '获取网格员列表成功',
          data: response.data || []
        };
      } else {
        return {
          success: false,
          message: response.msg || '获取网格员列表失败',
          data: []
        };
      }
    } catch (error) {
      console.error('❌ 获取网格员列表失败:', error);
      throw error;
    }
  }
};

export default authApi;
