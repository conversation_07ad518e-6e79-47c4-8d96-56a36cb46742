package com.hfut.xiaozu.incident.record;

import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【incident_record(事件记录主表)】的数据库操作Mapper
* @createDate 2025-06-29 22:54:45
* @Entity com.hfut.xiaozu.incident.record.IncidentRecord
*/
@Mapper
public interface IncidentRecordMapper {

    int insert(IncidentRecordEntity record);

    IncidentRecordEntity getById(Long id);

    int updateById(IncidentRecordEntity record);

    List<IncidentRecordEntity> listByStatus(Integer status);

    List<IncidentRecordEntity> listByStatusAndReporter(Integer status, Long reporterId);

    List<IncidentRecordEntity> listByStatusForGridWorker(Integer status, Long userId);

    List<IncidentRecordEntity> listAll();

    List<IncidentRecordEntity> listByReporter(Long reporterId);

    List<IncidentRecordEntity> listForGridWorker(Long userId);

}
