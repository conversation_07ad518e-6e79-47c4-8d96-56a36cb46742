-- 中环城云邸小区、楼栋、单元和房屋信息SQL脚本
-- 数据库表插入语句

-- 插入小区信息
INSERT INTO community_info (community_name, address_detail, geo_json)
VALUES (
    '中环城云邸', 
    '安徽省合肥市蜀山区棋秤路',
    '{"latitude": 117.203444, "longitude": 31.780863}'
);

-- 获取小区ID
SET @community_id = LAST_INSERT_ID();

-- 插入9栋楼信息
INSERT INTO building_info (community_id, building_code, building_name, geo_json)
VALUES 
(@community_id, '1', '一幢', '{"latitude": 117.202988, "longitude": 31.781688}'),
(@community_id, '2', '二幢', '{"latitude": 117.203449, "longitude": 31.781716}'),
(@community_id, '3', '三幢', '{"latitude": 117.203937, "longitude": 31.781688}'),
(@community_id, '4', '四幢', '{"latitude": 117.203042, "longitude": 31.781123}'),
(@community_id, '5', '五幢', '{"latitude": 117.203921, "longitude": 31.78115}'),
(@community_id, '6', '六幢', '{"latitude": 117.203031, "longitude": 31.780576}'),
(@community_id, '7', '七幢', '{"latitude": 117.203943, "longitude": 31.780566}'),
(@community_id, '8', '八幢', '{"latitude": 117.202988, "longitude": 31.78011}'),
(@community_id, '9', '九幢', '{"latitude": 117.203728, "longitude": 31.78001}');

-- 插入单元信息（每栋楼2个单元，5层）
INSERT INTO unit_info (building_id, unit_code, floor_count) VALUES 
-- 一幢
(1, '1幢1单元', 5),
(1, '1幢2单元', 5),
-- 二幢
(2, '2幢1单元', 5),
(2, '2幢2单元', 5),
-- 三幢
(3, '3幢1单元', 5),
(3, '3幢2单元', 5),
-- 四幢
(4, '4幢1单元', 5),
(4, '4幢2单元', 5),
-- 五幢
(5, '5幢1单元', 5),
(5, '5幢2单元', 5),
-- 六幢
(6, '6幢1单元', 5),
(6, '6幢2单元', 5),
-- 七幢
(7, '7幢1单元', 5),
(7, '7幢2单元', 5),
-- 八幢
(8, '8幢1单元', 5),
(8, '8幢2单元', 5),
-- 九幢
(9, '9幢1单元', 5),
(9, '9幢2单元', 5);

-- 插入房屋信息（每个单元5层，每层2户）
-- 一幢单元1
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(1, '1幢1单元0101', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(1, '1幢1单元0102', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(1, '1幢1单元0201', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(1, '1幢1单元0202', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(1, '1幢1单元0301', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(1, '1幢1单元0302', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(1, '1幢1单元0401', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(1, '1幢1单元0402', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(1, '1幢1单元0501', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(1, '1幢1单元0502', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2));

-- 一幢单元2
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(2, '1幢2单元0101', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(2, '1幢2单元0102', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(2, '1幢2单元0201', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(2, '1幢2单元0202', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(2, '1幢2单元0301', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(2, '1幢2单元0302', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(2, '1幢2单元0401', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(2, '1幢2单元0402', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(2, '1幢2单元0501', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(2, '1幢2单元0502', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2));

-- 二幢单元1
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(3, '2幢1单元0101', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(3, '2幢1单元0102', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(3, '2幢1单元0201', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(3, '2幢1单元0202', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(3, '2幢1单元0301', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(3, '2幢1单元0302', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(3, '2幢1单元0401', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(3, '2幢1单元0402', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(3, '2幢1单元0501', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(3, '2幢1单元0502', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2));

-- 二幢单元2
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(4, '2幢2单元0101', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(4, '2幢2单元0102', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(4, '2幢2单元0201', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(4, '2幢2单元0202', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(4, '2幢2单元0301', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(4, '2幢2单元0302', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(4, '2幢2单元0401', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(4, '2幢2单元0402', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(4, '2幢2单元0501', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(4, '2幢2单元0502', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2));

-- 三幢单元1
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(5, '3幢1单元0101', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(5, '3幢1单元0102', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(5, '3幢1单元0201', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(5, '3幢1单元0202', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(5, '3幢1单元0301', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(5, '3幢1单元0302', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(5, '3幢1单元0401', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(5, '3幢1单元0402', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(5, '3幢1单元0501', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(5, '3幢1单元0502', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2));

-- 三幢单元2
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(6, '3幢2单元0101', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(6, '3幢2单元0102', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(6, '3幢2单元0201', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(6, '3幢2单元0202', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(6, '3幢2单元0301', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(6, '3幢2单元0302', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(6, '3幢2单元0401', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(6, '3幢2单元0402', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(6, '3幢2单元0501', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(6, '3幢2单元0502', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2));

-- 四幢单元1
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(7, '4幢1单元0101', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(7, '4幢1单元0102', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(7, '4幢1单元0201', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(7, '4幢1单元0202', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(7, '4幢1单元0301', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(7, '4幢1单元0302', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(7, '4幢1单元0401', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(7, '4幢1单元0402', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(7, '4幢1单元0501', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(7, '4幢1单元0502', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2));

-- 四幢单元2
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(8, '4幢2单元0101', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(8, '4幢2单元0102', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(8, '4幢2单元0201', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(8, '4幢2单元0202', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(8, '4幢2单元0301', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(8, '4幢2单元0302', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(8, '4幢2单元0401', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(8, '4幢2单元0402', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(8, '4幢2单元0501', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(8, '4幢2单元0502', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2));

-- 五幢单元1
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(9, '5幢1单元0101', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(9, '5幢1单元0102', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(9, '5幢1单元0201', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(9, '5幢1单元0202', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(9, '5幢1单元0301', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(9, '5幢1单元0302', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(9, '5幢1单元0401', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(9, '5幢1单元0402', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(9, '5幢1单元0501', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(9, '5幢1单元0502', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2));

-- 五幢单元2
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(10, '5幢2单元0101', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(10, '5幢2单元0102', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(10, '5幢2单元0201', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(10, '5幢2单元0202', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(10, '5幢2单元0301', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(10, '5幢2单元0302', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(10, '5幢2单元0401', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(10, '5幢2单元0402', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(10, '5幢2单元0501', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(10, '5幢2单元0502', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2));

-- 六幢单元1
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(11, '6幢1单元0101', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(11, '6幢1单元0102', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(11, '6幢1单元0201', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(11, '6幢1单元0202', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(11, '6幢1单元0301', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(11, '6幢1单元0302', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(11, '6幢1单元0401', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(11, '6幢1单元0402', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(11, '6幢1单元0501', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(11, '6幢1单元0502', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2));

-- 六幢单元2
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(12, '6幢2单元0101', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(12, '6幢2单元0102', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(12, '6幢2单元0201', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(12, '6幢2单元0202', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(12, '6幢2单元0301', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(12, '6幢2单元0302', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(12, '6幢2单元0401', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(12, '6幢2单元0402', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(12, '6幢2单元0501', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(12, '6幢2单元0502', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2));

-- 七幢单元1
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(13, '7幢1单元0101', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(13, '7幢1单元0102', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(13, '7幢1单元0201', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(13, '7幢1单元0202', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(13, '7幢1单元0301', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(13, '7幢1单元0302', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(13, '7幢1单元0401', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(13, '7幢1单元0402', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(13, '7幢1单元0501', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(13, '7幢1单元0502', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2));

-- 七幢单元2
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(14, '7幢2单元0101', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(14, '7幢2单元0102', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(14, '7幢2单元0201', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(14, '7幢2单元0202', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(14, '7幢2单元0301', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(14, '7幢2单元0302', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(14, '7幢2单元0401', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(14, '7幢2单元0402', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(14, '7幢2单元0501', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(14, '7幢2单元0502', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2));

-- 八幢单元1
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(15, '8幢1单元0101', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(15, '8幢1单元0102', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(15, '8幢1单元0201', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(15, '8幢1单元0202', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(15, '8幢1单元0301', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(15, '8幢1单元0302', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(15, '8幢1单元0401', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(15, '8幢1单元0402', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(15, '8幢1单元0501', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(15, '8幢1单元0502', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2));

-- 八幢单元2
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(16, '8幢2单元0101', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(16, '8幢2单元0102', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(16, '8幢2单元0201', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(16, '8幢2单元0202', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(16, '8幢2单元0301', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(16, '8幢2单元0302', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(16, '8幢2单元0401', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(16, '8幢2单元0402', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(16, '8幢2单元0501', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(16, '8幢2单元0502', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2));

-- 九幢单元1
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(17, '9幢1单元0101', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(17, '9幢1单元0102', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(17, '9幢1单元0201', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(17, '9幢1单元0202', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(17, '9幢1单元0301', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(17, '9幢1单元0302', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(17, '9幢1单元0401', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(17, '9幢1单元0402', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(17, '9幢1单元0501', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(17, '9幢1单元0502', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2));

-- 九幢单元2
INSERT INTO house_info (unit_id, house_number, area_size, is_occupied) VALUES 
(18, '9幢2单元0101', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(18, '9幢2单元0102', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(18, '9幢2单元0201', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(18, '9幢2单元0202', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(18, '9幢2单元0301', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(18, '9幢2单元0302', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(18, '9幢2单元0401', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(18, '9幢2单元0402', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(18, '9幢2单元0501', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2)),
(18, '9幢2单元0502', ROUND(RAND() * 15 + 40, 2), FLOOR(RAND() * 2));