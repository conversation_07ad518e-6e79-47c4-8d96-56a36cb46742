package com.hfut.xiaozu;

import com.hfut.xiaozu.user.idv.IdentityVerifySubmitMapper;
import com.hfut.xiaozu.user.idv.IdentityVerifySubmitEntity;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * <AUTHOR>
 * @date 2025-06-24
 */
@SpringBootTest
public class IdentityVerifyTest {

    @Resource
    private IdentityVerifySubmitMapper identityVerifySubmitMapper;

    @Test
//    @Transactional // 使用事务注解确保测试数据不会影响数据库
    public void testInsertIdentityVerification() {
        // 创建测试实体
        IdentityVerifySubmitEntity entity = new IdentityVerifySubmitEntity();
        entity.setUserId(1L);
        entity.setSessionId("test-session-123");
        entity.setSubmittedName("测试用户");
        entity.setSubmittedIdCard("123456789012345678");
        entity.setStatus(0);

        System.out.println(entity);
        // 执行插入操作
        identityVerifySubmitMapper.insert(entity);

        // 验证 id 是否自增生成
        assertNotNull(entity.getId());
        assertTrue(entity.getId() > 0);

        System.out.println(entity);

        System.out.println("插入成功，生成的ID为: " + entity.getId());
    }
}
