<template>
  <div class="voice-text-converter">
    <div class="converter-header">
      <h3 class="converter-title">语音文字互转</h3>
      <div class="converter-tabs">
        <button 
          :class="['tab-btn', { active: activeTab === 'text-to-speech' }]"
          @click="activeTab = 'text-to-speech'"
        >
          文字转语音
        </button>
        <button 
          :class="['tab-btn', { active: activeTab === 'speech-to-text' }]"
          @click="activeTab = 'speech-to-text'"
        >
          语音转文字
        </button>
      </div>
    </div>

    <!-- 文字转语音 -->
    <div v-if="activeTab === 'text-to-speech'" class="tts-section">
      <div class="input-section">
        <label class="input-label">输入要转换的文字：</label>
        <textarea
          v-model="textToSpeak"
          class="text-input"
          placeholder="请输入要转换为语音的文字..."
          rows="4"
          maxlength="500"
        ></textarea>
        <div class="char-count">{{ textToSpeak.length }}/500</div>
      </div>

      <div class="voice-settings">
        <div class="setting-group">
          <label class="setting-label">语音选择：</label>
          <select v-model="selectedVoice" class="voice-select">
            <option value="">默认语音</option>
            <option 
              v-for="voice in availableVoices" 
              :key="voice.name" 
              :value="voice.name"
            >
              {{ voice.name }} ({{ voice.lang }})
            </option>
          </select>
        </div>

        <div class="setting-group">
          <label class="setting-label">语速：</label>
          <input 
            v-model="speechRate" 
            type="range" 
            min="0.5" 
            max="2" 
            step="0.1" 
            class="range-input"
          >
          <span class="range-value">{{ speechRate }}x</span>
        </div>

        <div class="setting-group">
          <label class="setting-label">音调：</label>
          <input 
            v-model="speechPitch" 
            type="range" 
            min="0" 
            max="2" 
            step="0.1" 
            class="range-input"
          >
          <span class="range-value">{{ speechPitch }}</span>
        </div>

        <div class="setting-group">
          <label class="setting-label">音量：</label>
          <input 
            v-model="speechVolume" 
            type="range" 
            min="0" 
            max="1" 
            step="0.1" 
            class="range-input"
          >
          <span class="range-value">{{ Math.round(speechVolume * 100) }}%</span>
        </div>
      </div>

      <div class="control-buttons">
        <button 
          @click="speakText" 
          :disabled="!textToSpeak.trim() || isSpeaking"
          class="control-btn primary"
        >
          <span v-if="isSpeaking">🔊 播放中...</span>
          <span v-else>🎵 开始播放</span>
        </button>
        <button 
          @click="stopSpeaking" 
          :disabled="!isSpeaking"
          class="control-btn secondary"
        >
          ⏹️ 停止播放
        </button>
        <button 
          @click="pauseSpeaking" 
          :disabled="!isSpeaking || isPaused"
          class="control-btn secondary"
        >
          ⏸️ 暂停
        </button>
        <button 
          @click="resumeSpeaking" 
          :disabled="!isPaused"
          class="control-btn secondary"
        >
          ▶️ 继续
        </button>
      </div>
    </div>

    <!-- 语音转文字 -->
    <div v-if="activeTab === 'speech-to-text'" class="stt-section">
      <div class="recognition-status">
        <div class="status-indicator" :class="{ active: isListening, error: recognitionError }">
          <span v-if="isListening">🎤 正在监听...</span>
          <span v-else-if="recognitionError">❌ 识别错误</span>
          <span v-else>🎤 点击开始语音识别</span>
        </div>
      </div>

      <div class="recognition-settings">
        <div class="setting-group">
          <label class="setting-label">识别语言：</label>
          <select v-model="recognitionLang" class="voice-select">
            <option value="zh-CN">中文（普通话）</option>
            <option value="en-US">英语（美国）</option>
            <option value="ja-JP">日语</option>
            <option value="ko-KR">韩语</option>
          </select>
        </div>

        <div class="setting-group">
          <label class="setting-label">连续识别：</label>
          <input 
            v-model="continuousRecognition" 
            type="checkbox" 
            class="checkbox-input"
          >
        </div>

        <div class="setting-group">
          <label class="setting-label">临时结果：</label>
          <input 
            v-model="interimResults" 
            type="checkbox" 
            class="checkbox-input"
          >
        </div>
      </div>

      <div class="control-buttons">
        <button 
          @click="startRecognition" 
          :disabled="isListening || !speechRecognitionSupported"
          class="control-btn primary"
        >
          🎤 开始识别
        </button>
        <button 
          @click="stopRecognition" 
          :disabled="!isListening"
          class="control-btn secondary"
        >
          ⏹️ 停止识别
        </button>
        <button 
          @click="clearRecognitionText" 
          class="control-btn secondary"
        >
          🗑️ 清空文字
        </button>
      </div>

      <div class="recognition-result">
        <label class="input-label">识别结果：</label>
        <div class="result-container">
          <div v-if="interimText" class="interim-text">{{ interimText }}</div>
          <textarea
            v-model="recognizedText"
            class="text-input result-text"
            placeholder="语音识别结果将显示在这里..."
            rows="6"
            readonly
          ></textarea>
        </div>
        <div class="result-actions">
          <button @click="copyToClipboard" class="action-btn">📋 复制文字</button>
          <button @click="speakRecognizedText" class="action-btn">🔊 朗读文字</button>
        </div>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="errorMessage" class="error-message">
      ❌ {{ errorMessage }}
    </div>

    <!-- 浏览器兼容性提示 -->
    <div v-if="!speechSynthesisSupported || !speechRecognitionSupported" class="compatibility-warning">
      <h4>⚠️ 浏览器兼容性提示</h4>
      <ul>
        <li v-if="!speechSynthesisSupported">您的浏览器不支持语音合成功能</li>
        <li v-if="!speechRecognitionSupported">您的浏览器不支持语音识别功能</li>
      </ul>
      <p>建议使用最新版本的 Chrome、Edge 或 Safari 浏览器</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

// 响应式数据
const activeTab = ref('text-to-speech')

// TTS 相关
const textToSpeak = ref('')
const selectedVoice = ref('')
const speechRate = ref(1)
const speechPitch = ref(1)
const speechVolume = ref(1)
const availableVoices = ref([])
const isSpeaking = ref(false)
const isPaused = ref(false)

// STT 相关
const recognizedText = ref('')
const interimText = ref('')
const isListening = ref(false)
const recognitionLang = ref('zh-CN')
const continuousRecognition = ref(true)
const interimResults = ref(true)
const recognitionError = ref(false)

// 通用
const errorMessage = ref('')
const speechSynthesisSupported = ref(false)
const speechRecognitionSupported = ref(false)

// 语音合成和识别对象
let speechSynthesis = null
let speechRecognition = null
let currentUtterance = null

// 检查浏览器支持
const checkBrowserSupport = () => {
  speechSynthesisSupported.value = 'speechSynthesis' in window
  speechRecognitionSupported.value = 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window

  console.log('语音合成支持:', speechSynthesisSupported.value)
  console.log('语音识别支持:', speechRecognitionSupported.value)
}

// 初始化语音合成
const initSpeechSynthesis = () => {
  if (!speechSynthesisSupported.value) return

  speechSynthesis = window.speechSynthesis

  // 获取可用语音列表
  const loadVoices = () => {
    const voices = speechSynthesis.getVoices()
    availableVoices.value = voices.filter(voice =>
      voice.lang.includes('zh') || voice.lang.includes('en')
    )
    console.log('可用语音:', availableVoices.value)
  }

  // 语音列表可能异步加载
  if (speechSynthesis.getVoices().length > 0) {
    loadVoices()
  } else {
    speechSynthesis.addEventListener('voiceschanged', loadVoices)
  }
}

// 初始化语音识别
const initSpeechRecognition = () => {
  if (!speechRecognitionSupported.value) return

  const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
  speechRecognition = new SpeechRecognition()

  // 配置语音识别
  speechRecognition.continuous = continuousRecognition.value
  speechRecognition.interimResults = interimResults.value
  speechRecognition.lang = recognitionLang.value

  // 识别结果事件
  speechRecognition.onresult = (event) => {
    let finalTranscript = ''
    let interimTranscript = ''

    for (let i = event.resultIndex; i < event.results.length; i++) {
      const transcript = event.results[i][0].transcript
      if (event.results[i].isFinal) {
        finalTranscript += transcript
      } else {
        interimTranscript += transcript
      }
    }

    if (finalTranscript) {
      recognizedText.value += finalTranscript + ' '
    }

    if (interimResults.value) {
      interimText.value = interimTranscript
    }
  }

  // 识别开始事件
  speechRecognition.onstart = () => {
    isListening.value = true
    recognitionError.value = false
    errorMessage.value = ''
    console.log('语音识别开始')
  }

  // 识别结束事件
  speechRecognition.onend = () => {
    isListening.value = false
    interimText.value = ''
    console.log('语音识别结束')
  }

  // 识别错误事件
  speechRecognition.onerror = (event) => {
    recognitionError.value = true
    isListening.value = false
    interimText.value = ''

    let errorMsg = '语音识别出错'
    switch (event.error) {
      case 'no-speech':
        errorMsg = '未检测到语音输入'
        break
      case 'audio-capture':
        errorMsg = '无法访问麦克风'
        break
      case 'not-allowed':
        errorMsg = '麦克风权限被拒绝'
        break
      case 'network':
        errorMsg = '网络连接错误'
        break
      default:
        errorMsg = `识别错误: ${event.error}`
    }

    errorMessage.value = errorMsg
    console.error('语音识别错误:', event.error)
  }
}

// TTS 功能函数
const speakText = () => {
  if (!speechSynthesisSupported.value || !textToSpeak.value.trim()) return

  // 停止当前播放
  speechSynthesis.cancel()

  currentUtterance = new SpeechSynthesisUtterance(textToSpeak.value)

  // 设置语音参数
  currentUtterance.rate = speechRate.value
  currentUtterance.pitch = speechPitch.value
  currentUtterance.volume = speechVolume.value

  // 设置选定的语音
  if (selectedVoice.value) {
    const voice = availableVoices.value.find(v => v.name === selectedVoice.value)
    if (voice) {
      currentUtterance.voice = voice
    }
  }

  // 事件监听
  currentUtterance.onstart = () => {
    isSpeaking.value = true
    isPaused.value = false
    errorMessage.value = ''
    console.log('开始播放语音')
  }

  currentUtterance.onend = () => {
    isSpeaking.value = false
    isPaused.value = false
    console.log('语音播放结束')
  }

  currentUtterance.onerror = (event) => {
    isSpeaking.value = false
    isPaused.value = false
    errorMessage.value = `语音播放错误: ${event.error}`
    console.error('语音播放错误:', event.error)
  }

  currentUtterance.onpause = () => {
    isPaused.value = true
    console.log('语音播放暂停')
  }

  currentUtterance.onresume = () => {
    isPaused.value = false
    console.log('语音播放继续')
  }

  // 开始播放
  speechSynthesis.speak(currentUtterance)
}

const stopSpeaking = () => {
  if (speechSynthesis) {
    speechSynthesis.cancel()
    isSpeaking.value = false
    isPaused.value = false
  }
}

const pauseSpeaking = () => {
  if (speechSynthesis && isSpeaking.value) {
    speechSynthesis.pause()
  }
}

const resumeSpeaking = () => {
  if (speechSynthesis && isPaused.value) {
    speechSynthesis.resume()
  }
}

// STT 功能函数
const startRecognition = () => {
  if (!speechRecognitionSupported.value || isListening.value) return

  // 更新识别配置
  speechRecognition.continuous = continuousRecognition.value
  speechRecognition.interimResults = interimResults.value
  speechRecognition.lang = recognitionLang.value

  try {
    speechRecognition.start()
  } catch (error) {
    errorMessage.value = '启动语音识别失败: ' + error.message
    console.error('启动语音识别失败:', error)
  }
}

const stopRecognition = () => {
  if (speechRecognition && isListening.value) {
    speechRecognition.stop()
  }
}

const clearRecognitionText = () => {
  recognizedText.value = ''
  interimText.value = ''
  errorMessage.value = ''
}

// 工具函数
const copyToClipboard = async () => {
  if (!recognizedText.value.trim()) {
    errorMessage.value = '没有可复制的文字'
    return
  }

  try {
    await navigator.clipboard.writeText(recognizedText.value)
    // 显示成功提示
    const originalError = errorMessage.value
    errorMessage.value = '✅ 文字已复制到剪贴板'
    setTimeout(() => {
      errorMessage.value = originalError
    }, 2000)
  } catch (error) {
    errorMessage.value = '复制失败: ' + error.message
    console.error('复制失败:', error)
  }
}

const speakRecognizedText = () => {
  if (!recognizedText.value.trim()) {
    errorMessage.value = '没有可朗读的文字'
    return
  }

  // 切换到文字转语音标签页并播放
  activeTab.value = 'text-to-speech'
  textToSpeak.value = recognizedText.value

  // 延迟一下再播放，确保界面更新完成
  setTimeout(() => {
    speakText()
  }, 100)
}

// 生命周期钩子
onMounted(() => {
  checkBrowserSupport()
  initSpeechSynthesis()
  initSpeechRecognition()
})

onUnmounted(() => {
  // 清理资源
  if (speechSynthesis) {
    speechSynthesis.cancel()
  }
  if (speechRecognition && isListening.value) {
    speechRecognition.stop()
  }
})

// 暴露给父组件的方法
defineExpose({
  speakText: (text) => {
    textToSpeak.value = text
    speakText()
  },
  stopSpeaking,
  startRecognition,
  stopRecognition,
  getRecognizedText: () => recognizedText.value,
  clearAll: () => {
    textToSpeak.value = ''
    recognizedText.value = ''
    interimText.value = ''
    errorMessage.value = ''
    stopSpeaking()
    stopRecognition()
  }
})
</script>

<style scoped>
.voice-text-converter {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.converter-header {
  margin-bottom: 30px;
}

.converter-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 20px 0;
  text-align: center;
}

.converter-tabs {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.tab-btn {
  padding: 12px 24px;
  border: 2px solid #20B2AA;
  background: white;
  color: #20B2AA;
  border-radius: 25px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-btn.active {
  background: #20B2AA;
  color: white;
}

.tab-btn:hover:not(.active) {
  background: rgba(32, 178, 170, 0.1);
}

.input-section {
  margin-bottom: 25px;
}

.input-label {
  display: block;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.text-input {
  width: 100%;
  padding: 15px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  line-height: 1.5;
  resize: vertical;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.text-input:focus {
  outline: none;
  border-color: #20B2AA;
  box-shadow: 0 0 0 3px rgba(32, 178, 170, 0.1);
}

.char-count {
  text-align: right;
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.voice-settings, .recognition-settings {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 25px;
}

.setting-group {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.setting-group:last-child {
  margin-bottom: 0;
}

.setting-label {
  min-width: 80px;
  font-weight: 500;
  color: #333;
}

.voice-select {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.range-input {
  flex: 1;
  margin: 0 10px;
}

.range-value {
  min-width: 50px;
  font-weight: 500;
  color: #666;
}

.checkbox-input {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.control-buttons {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 25px;
}

.control-btn {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-btn.primary {
  background: #20B2AA;
  color: white;
}

.control-btn.primary:hover:not(:disabled) {
  background: #1a9a93;
  transform: translateY(-2px);
}

.control-btn.secondary {
  background: #6c757d;
  color: white;
}

.control-btn.secondary:hover:not(:disabled) {
  background: #5a6268;
  transform: translateY(-2px);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.recognition-status {
  text-align: center;
  margin-bottom: 25px;
}

.status-indicator {
  display: inline-block;
  padding: 15px 25px;
  border-radius: 25px;
  font-weight: 500;
  font-size: 16px;
  transition: all 0.3s ease;
}

.status-indicator.active {
  background: rgba(32, 178, 170, 0.1);
  color: #20B2AA;
  border: 2px solid #20B2AA;
  animation: pulse 2s infinite;
}

.status-indicator.error {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border: 2px solid #dc3545;
}

.status-indicator:not(.active):not(.error) {
  background: #f8f9fa;
  color: #666;
  border: 2px solid #e1e5e9;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.recognition-result {
  margin-top: 25px;
}

.result-container {
  position: relative;
}

.interim-text {
  position: absolute;
  top: 15px;
  left: 15px;
  right: 15px;
  color: #999;
  font-style: italic;
  pointer-events: none;
  z-index: 1;
}

.result-text {
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.9);
}

.result-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 15px;
}

.action-btn {
  padding: 8px 16px;
  background: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 6px;
  color: #333;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.action-btn:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 12px 16px;
  border-radius: 6px;
  margin: 15px 0;
  border: 1px solid #f5c6cb;
}

.compatibility-warning {
  background: #fff3cd;
  color: #856404;
  padding: 20px;
  border-radius: 8px;
  margin-top: 20px;
  border: 1px solid #ffeaa7;
}

.compatibility-warning h4 {
  margin: 0 0 10px 0;
  color: #856404;
}

.compatibility-warning ul {
  margin: 10px 0;
  padding-left: 20px;
}

.compatibility-warning p {
  margin: 10px 0 0 0;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .voice-text-converter {
    padding: 15px;
  }

  .converter-tabs {
    flex-direction: column;
    align-items: center;
  }

  .tab-btn {
    width: 200px;
    text-align: center;
  }

  .setting-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .setting-label {
    min-width: auto;
  }

  .voice-select, .range-input {
    width: 100%;
  }

  .control-buttons {
    flex-direction: column;
    align-items: center;
  }

  .control-btn {
    width: 200px;
    justify-content: center;
  }

  .result-actions {
    flex-direction: column;
    align-items: center;
  }

  .action-btn {
    width: 150px;
    text-align: center;
  }
}
</style>
