package com.hfut.xiaozu.vote;

import com.hfut.xiaozu.common.Result;
import com.hfut.xiaozu.vote.info.CreateVoteDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-07-01
 */
@Tag(name = "投票管理")
@RestController
@RequestMapping("/api/vote")
public class VoteController {

    @Resource
    private VoteService voteService;

    @Operation(summary = "创建投票")
    @PostMapping
    public Result<?> createVote(@Valid @RequestBody CreateVoteDTO dto, BindingResult bindingResult){

        List<String> errors = new ArrayList<>();
        if(bindingResult.hasErrors()){
            errors.addAll(bindingResult.getAllErrors()
                    .stream()
                    .map(DefaultMessageSourceResolvable::getDefaultMessage)
                    .collect(Collectors.toList()));
        }

        if(dto.getStartTime().isAfter(dto.getEndTime())){
            errors.add("开始时间不能晚于结束时间");
        }

        if(!errors.isEmpty()){
            return Result.fail(errors.toString());
        }

        return voteService.createVote(dto);
    }

    @Operation(summary = "查看所有投票(管理员和居民用一个接口）")
    @GetMapping("/list")
    public Result<?> listVote(){
        return voteService.listVote();
    }

    @Operation(summary = "居民投票")
    @PostMapping("/{voteId}")
    public Result<?> vote(@PathVariable Long voteId, @RequestBody List<Integer> chooses){

        return voteService.vote(voteId,chooses);
    }


}
