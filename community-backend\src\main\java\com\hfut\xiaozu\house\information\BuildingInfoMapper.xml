<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hfut.xiaozu.house.information.BuildingInfoMapper">

    <resultMap id="BaseResultMap" type="com.hfut.xiaozu.house.information.BuildingInfo">
            <id property="id" column="id" />
            <result property="communityId" column="community_id" />
            <result property="buildingCode" column="building_code" />
            <result property="buildingName" column="building_name" />
            <result property="geoJson" column="geo_json" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,community_id,building_code,building_name,geo_json,create_time,
        update_time
    </sql>

    <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from building_info
        where  id = #{id}
    </select>


    <select id="listByCommunityId" resultType="com.hfut.xiaozu.house.information.BuildingInfo">
        select
        <include refid="Base_Column_List" />
        from building_info
        where  community_id = #{communityId}
    </select>

    <select id="getIdByHouseId" resultType="java.lang.Long">
        SELECT id FROM building_info
                join unit_info unit on building_info.id = unit.building_id
                join house_info house on unit.id = house.unit_id
                where house.id = #{houseId}
    </select>


    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.hfut.xiaozu.house.information.BuildingInfo" useGeneratedKeys="true">
        insert into building_info
        ( community_id,building_code,building_name,geo_json)
        values (#{communityId},#{buildingCode},#{buildingName},#{geoJson})
    </insert>


    <update id="update" parameterType="com.hfut.xiaozu.house.information.BuildingInfo">
        update building_info
        <set>
                <if test="communityId != null">
                    community_id = #{communityId},
                </if>
                <if test="buildingCode != null">
                    building_code = #{buildingCode},
                </if>
                <if test="buildingName != null">
                    building_name = #{buildingName},
                </if>
                <if test="geoJson != null">
                    geo_json = #{geoJson},
                </if>
        </set>
        where   id = #{id}
    </update>

</mapper>
