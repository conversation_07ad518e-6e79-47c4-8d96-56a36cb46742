package com.hfut.xiaozu.car.bind;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【vehicle_house_binding(车辆-房屋绑定关系表（支持多房屋绑定）)】的数据库操作Mapper
* @createDate 2025-06-27 23:38:59
* @Entity com.hfut.xiaozu.car.VehicleHouseBinding
*/
@Mapper
public interface VehicleHouseBindingMapper {

    int deleteById(Long id);

    int insert(VehicleHouseBindingEntity record);

    VehicleHouseBindingEntity getById(Long id);

    int updateById(VehicleHouseBindingEntity record);

    List<VehicleHouseBindingEntity> ListByVehicleId(Long id);

    List<VehicleHouseBindingEntity> listByStatus(Integer status);
}
