package com.hfut.xiaozu.house.information;

import java.time.LocalDateTime;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @TableName community_info
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class CommunityInfo {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 小区名称
     */
    private String communityName;

    /**
     * 详细地址
     */
    private String addressDetail;

    /**
     * 中心点GeoJSON数据
     */
    private String geoJson;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
