package com.hfut.xiaozu.incident.process;

import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【incident_process_record(事件处理记录表)】的数据库操作Mapper
* @createDate 2025-06-29 22:54:45
* @Entity com.hfut.xiaozu.incident.process.IncidentProcessRecord
*/
@Mapper
public interface IncidentProcessRecordMapper {

    int insert(IncidentProcessRecordEntity record);

    IncidentProcessRecordEntity getById(Long id);

    int updateById(IncidentProcessRecordEntity record);

    List<IncidentProcessRecordEntity> listByIncidentId(Long incidentId);

}
